<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>transport-domain</module>
        <module>transport-infrastructure</module>
        <module>transport-application</module>
        <module>transport-start</module>
        <module>transport-interfaces</module>
    </modules>

    <parent>
        <groupId>com.ctrip.dcs.pom</groupId>
        <artifactId>dcs-super-pom</artifactId>
        <version>7.3.0</version>
    </parent>

    <groupId>com.ctrip.dcs.tms</groupId>
    <artifactId>tms-transport-service</artifactId>
    <packaging>pom</packaging>
    <version>1.4.2</version>

    <name>tms-transport-service</name>

    <dependencies>
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework</groupId>
            <artifactId>canal-json</artifactId>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- spock依赖的groovy1 -->
        <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-all</artifactId>
        <type>pom</type>
        <exclusions>
            <exclusion>
                <artifactId>groovy-test-junit5</artifactId>
                <groupId>org.codehaus.groovy</groupId>
            </exclusion>
            <exclusion>
                <artifactId>groovy-testng</artifactId>
                <groupId>org.codehaus.groovy</groupId>
            </exclusion>
            <exclusion>
                <artifactId>hamcrest-core</artifactId>
                <groupId>org.hamcrest</groupId>
            </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.frt.framework</groupId>
            <artifactId>frt-xresource-framework-utility</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jsr305</artifactId>
                    <groupId>com.google.code.findbugs</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-math3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                    <exclusion>
                        <groupId>com.google.code.findbugs</groupId>
                        <artifactId>annotations</artifactId>
                    </exclusion>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-math3</artifactId>
            <version>3.6.1</version>
        </dependency>

    </dependencies>

    <properties>
        <api.version>1.6.29-gyj-SNAPSHOT</api.version>
        <infrastructure.service.version>1.3.4</infrastructure.service.version>
        <pms.product.api.version>1.0.22</pms.product.api.version>
        <jnt.price.version>1.0.20</jnt.price.version>
        <dcsscmmerchantservice.version>1.0.42</dcsscmmerchantservice.version>
        <open.pub.client.version>1.0.20</open.pub.client.version>
        <mockito.version>2.0.2-beta</mockito.version>
        <jacoco.version>0.8.5</jacoco.version>
        <sonar.jacoco.reportPaths>${project.basedir}/target/jacoco.exec</sonar.jacoco.reportPaths>
        <sonar.jacoco.csvPath>${project.basedir}/target/static</sonar.jacoco.csvPath>
        <tmsconnectservice.version>1.5.6</tmsconnectservice.version>
        <hutool.version>5.5.8</hutool.version>
        <canal.json.version>1.0.0</canal.json.version>
        <location.query.service.version>1.0.1</location.query.service.version>
        <distlock.version>1.1.0</distlock.version>
        <im.service>1.0.15</im.service>
        <request.filter.version>1.0.3</request.filter.version>
        <log4j2.version>2.17.1</log4j2.version>
        <sso-client.version>0.1.3</sso-client.version>
        <sonar.exclusions>
            **/*,**/common/**,src/main/java/com/ctrip/dcs/tms/transport/application/command/impl/DataMigrationCommandServiceImpl.java,DataRegulationQueryServiceImpl,src/main/java/com/ctrip/dcs/tms/transport/application/query/impl/DataRegulationQueryServiceImpl.java,src/main/java/com/ctrip/dcs/tms/transport/application/command/impl/DriverAccountCommandServiceImpl.java,
            src/main/java/com/ctrip/dcs/tms/transport/infrastructure/constant/ApiTypeEnum.java
        </sonar.exclusions>
        <tinypinyin.version>2.0.3</tinypinyin.version>
        <!--        <ch-dependencies-bom.version>1.1.6</ch-dependencies-bom.version>-->
<!--        <credis.version>4.3.69</credis.version>-->
        <recognitionservice.version>1.0.3</recognitionservice.version>
        <dcsdriverlevelservice.version>1.0.4</dcsdriverlevelservice.version>
        <phonenumbersplitservice.version>1.2.0</phonenumbersplitservice.version>
        <spock.version>1.3-groovy-2.5</spock.version>
        <groovy.version>2.5.4</groovy.version>
        <dispatchorder.service.version>1.0.10</dispatchorder.service.version>
        <scm.version>1.0.4</scm.version>
        <driver.domain.service.version>1.0.54</driver.domain.service.version>
        <tms.transport.mq.version>1.0.1</tms.transport.mq.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.ctrip.dcs</groupId>
                <artifactId>dcs-phonebridge-client</artifactId>
                <version>1.0.30</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.frt.framework</groupId>
                <artifactId>frt-xresource-framework-utility</artifactId>
                <version>0.3.13</version>
            </dependency>
            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>3.6</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.23343</groupId>
                <artifactId>phonenumbersplitservice</artifactId>
                <version>${phonenumbersplitservice.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.basebiz</groupId>
                <artifactId>accounts-mobile-request-filter</artifactId>
                <version>${request.filter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.igt.im</groupId>
                <artifactId>im-service-client</artifactId>
                <version>${im.service}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.arch</groupId>
                <artifactId>distlock-client</artifactId>
                <version>${distlock.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.locationqueryservice</groupId>
                <artifactId>location-query-service-client</artifactId>
                <version>${location.query.service.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.framework</groupId>
                <artifactId>canal-json</artifactId>
                <version>${canal.json.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.igt.infrastructure.service</groupId>
                <artifactId>client</artifactId>
                <version>${infrastructure.service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.tms</groupId>
                <artifactId>transport-interfaces</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.tms</groupId>
                <artifactId>transport-application</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.tms</groupId>
                <artifactId>transport-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.tms</groupId>
                <artifactId>transport-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.tms</groupId>
                <artifactId>transport-api</artifactId>
                <version>${api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.pms</groupId>
                <artifactId>product-api</artifactId>
                <version>${pms.product.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.dcs.jnt.price</groupId>
                <artifactId>sku-price-soa-service</artifactId>
                <version>${jnt.price.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.dcs.jnt.dcsscmmerchantservice.v1</groupId>
                <artifactId>dcsscmmerchantservice</artifactId>
                <version>${dcsscmmerchantservice.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.order</groupId>
                <artifactId>vbk-supplier-order-service</artifactId>
                <version>1.0.29</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.igt.open</groupId>
                <artifactId>open-pub-client</artifactId>
                <version>${open.pub.client.version}</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>org.mockito</groupId>-->
<!--                <artifactId>mockito-all</artifactId>-->
<!--                <version>${mockito.version}</version>-->
<!--                <scope>test</scope>-->
<!--            </dependency>-->
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>3.11.2</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.ctrip.ibu.platform</groupId>
                <artifactId>ibu-shark-sdk</artifactId>
                <version>5.2.0</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.tms</groupId>
                <artifactId>tmsconnectservice</artifactId>
                <version>${tmsconnectservice.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>4.1.2</version>
            </dependency>
            <dependency>
                <groupId>com.github.promeg</groupId>
                <artifactId>tinypinyin</artifactId>
                <version>${tinypinyin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs</groupId>
                <artifactId>recognitionservice</artifactId>
                <version>${recognitionservice.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.infosec</groupId>
                <artifactId>sso-client-new</artifactId>
                <version>${sso-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.21828</groupId>
                <artifactId>dcsdriverlevelservice</artifactId>
                <version>${dcsdriverlevelservice.version}</version>
            </dependency>
            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>3.22.0-GA</version>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>org.javassist</groupId>
                        <artifactId>javassist</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- spock -->
            <dependency>
                <groupId>org.spockframework</groupId>
                <artifactId>spock-core</artifactId>
                <version>${spock.version}</version>
                <scope>test</scope>
            </dependency>
            <!-- spock依赖的groovy -->
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-all</artifactId>
                <type>pom</type>
                <version>${groovy.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>groovy-test-junit5</artifactId>
                        <groupId>org.codehaus.groovy</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>groovy-testng</artifactId>
                        <groupId>org.codehaus.groovy</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.dispatchorder</groupId>
                <artifactId>self-dispatchorder-service</artifactId>
                <version>${dispatchorder.service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs</groupId>
                <artifactId>basicdatadomain</artifactId>
                <version>1.0.9</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs</groupId>
                <artifactId>data-compare-message-schema</artifactId>
                <version>1.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.geo</groupId>
                <artifactId>geo-platform-sdk</artifactId>
                <version>1.3.6</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.libphonenumber</groupId>
                <artifactId>libphonenumber</artifactId>
                <version>8.13.41</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.soa</groupId>
                <artifactId>geo-platform-service</artifactId>
                <version>1.3.1</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.scm</groupId>
                <artifactId>scm-sdk</artifactId>
                <version>${scm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.driver.domain</groupId>
                <artifactId>driver-domain-service</artifactId>
                <version>${driver.domain.service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.infosec.kms</groupId>
                <artifactId>kms-sdk</artifactId>
                <version>1.2.3</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.driver.domain</groupId>
                <artifactId>driver-domain-service-mq</artifactId>
                <version>1.0.6</version>
            </dependency>
            <!--            向导桥接-->
            <dependency>
                <groupId>com.ctrip.tour.driver</groupId>
                <artifactId>driver-platform-api-service</artifactId>
                <exclusions>
                    <exclusion>
                        <artifactId>ifsintranetssoservice</artifactId>
                        <groupId>com.ctrip.soa.22320</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>vendor-support</artifactId>
                        <groupId>com.ctrip.tour</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>driver-utility</artifactId>
                        <groupId>com.ctrip.tour</groupId>
                    </exclusion>
                </exclusions>
                <version>1.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.frt</groupId>
                <artifactId>product-basic-client</artifactId>
                <version>1.0.164</version>
            </dependency>
            <!--            向导桥接-->
            <dependency>
                <groupId>com.ctrip.corp.pub.user</groupId>
                <artifactId>soa-extend</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.3.2</version>
                <exclusions>
                    <exclusion>
                        <artifactId>jakarta.activation</artifactId>
                        <groupId>com.sun.activation</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-collections4</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.19</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.basebiz</groupId>
                <artifactId>account-service-client</artifactId>
                <version>0.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.order.hybrid.search</groupId>
                <artifactId>hybrid-search-service-client</artifactId>
                <version>1.0.20</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.car.och.orderqueryservice.v1</groupId>
                <artifactId>igtorderqueryservice-api</artifactId>
                <version>2.3.9</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.self</groupId>
                <artifactId>self-orderquery-service-api</artifactId>
                <version>1.0.38</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-math3</artifactId>
                <scope>3.6.1</scope>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.tms</groupId>
                <artifactId>transport-mq</artifactId>
                <version>${tms.transport.mq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.poi</groupId>
                <artifactId>dcs-poi-sdk</artifactId>
                <version>0.0.15</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.basebiz</groupId>
                <artifactId>service-accountsmessage-client</artifactId>
                <version>1.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.basebiz.ai.aiplatform</groupId>
                <artifactId>ai-platform-contract</artifactId>
                <version>0.1.4</version>
            </dependency>
        </dependencies>


    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <executions>
                    <execution>
                        <id>default-instrument</id>
                        <goals>
                            <goal>instrument</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-restore-instrumented-classes</id>
                        <goals>
                            <goal>restore-instrumented-classes</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <configuration>
                            <dataFile>${sonar.jacoco.reportPaths}</dataFile>
                            <outputDirectory>${sonar.jacoco.csvPath}</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.19.1</version>
                <!--离线模式必需指定， 否则到模块根目录而不是target目录了-->
                <configuration>
                    <systemPropertyVariables>
                        <jacoco-agent.destfile>target/jacoco.exec</jacoco-agent.destfile>
                    </systemPropertyVariables>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.ctrip.ibu.platform</groupId>
                <artifactId>shark-maven-plugin</artifactId>
                <version>1.1.1</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>pack-download</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <version>1.6</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>compileTests</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.21.0</version>
                <configuration>
                    <forkCount>5</forkCount>
                    <reuseForks>true</reuseForks>
                    <!-- <parallel>classes</parallel>-->
                    <!-- <threadCount>5</threadCount>-->
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
