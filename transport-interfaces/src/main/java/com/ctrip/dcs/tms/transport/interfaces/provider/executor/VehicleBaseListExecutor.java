package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryVehicleBaseSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryVehicleBaseSOAResponseType;
import com.ctrip.dcs.tms.transport.interfaces.bridge.ProductLineBridgeManagement;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class VehicleBaseListExecutor extends AbstractRpcExecutor<QueryVehicleBaseSOARequestType, QueryVehicleBaseSOAResponseType> implements Validator<QueryVehicleBaseSOARequestType> {

    private static final Logger logger = LoggerFactory.getLogger(VehicleBaseListExecutor.class);

    @Autowired
    ProductLineBridgeManagement productLineBridgeManagement;

    @Override
    public QueryVehicleBaseSOAResponseType execute(QueryVehicleBaseSOARequestType requestType) {
        return productLineBridgeManagement.queryVehicleListBySupplierId(requestType);
    }
}
