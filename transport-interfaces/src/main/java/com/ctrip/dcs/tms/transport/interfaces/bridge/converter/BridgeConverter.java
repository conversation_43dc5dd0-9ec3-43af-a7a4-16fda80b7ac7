package com.ctrip.dcs.tms.transport.interfaces.bridge.converter;

import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.DrvFreezeInfoSOADTO;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailDTOSOA;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvFreezeInfoForDspSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvFreezeInfoForDspSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryVehicleBaseSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.TransportGroup;
import com.ctrip.dcs.tms.transport.api.model.TransportGroupResponseDTO;
import com.ctrip.dcs.tms.transport.api.model.VehicleDetailDTO;
import com.ctrip.dcs.tms.transport.api.model.VehicleDetailSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.VehicleDetailSOAResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverForSaasResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverIdForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverIdForSaasResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleForSaasResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleIdForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleIdForSaasResponseType;
import com.ctrip.dcs.tms.transport.api.saas.SaasDriverSoaDTO;
import com.ctrip.dcs.tms.transport.api.saas.SaasTransportGroupSoaDTO;
import com.ctrip.dcs.tms.transport.api.saas.SaasVehicleSoaDTO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model.DriverGuideDTO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model.VehVehicleDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DriverGuidDriverRequestDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DriverGuidVehicleRequestDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.dcs.tms.transport.interfaces.provider.executor.UpdateTransportApproveStatusExecutor;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.QUERY_DRV_DETAIL;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.QUERY_DRV_DETAIL_EMPTY;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.QUERY_VEHICLE_DETAIL;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.QUERY_VEHICLE_DETAIL_EMPTY;

public class BridgeConverter {

  private static final Logger logger = LoggerFactory.getLogger(BridgeConverter.class);

  public static QueryDrvDetailDTOSOA convert2QueryDrvDetailDTOSOA(DriverGuideDTO driverGuidDto) {
    QueryDrvDetailDTOSOA queryDrvDetailDTOSOA = new QueryDrvDetailDTOSOA();
    queryDrvDetailDTOSOA.setDrvId(driverGuidDto.getDrvId());
    queryDrvDetailDTOSOA.setDrvIdcard(driverGuidDto.getDrvIdcard());
    queryDrvDetailDTOSOA.setDrvName(driverGuidDto.getDrvName());
    queryDrvDetailDTOSOA.setDrvPhone(driverGuidDto.getDrvPhone());
    queryDrvDetailDTOSOA.setVehicleLicense(driverGuidDto.getVehicleLicense());
    queryDrvDetailDTOSOA.setCityId(driverGuidDto.getCityId());
    queryDrvDetailDTOSOA.setCityName(driverGuidDto.getCityName());
    queryDrvDetailDTOSOA.setVehicleTypeId(driverGuidDto.getVehicleTypeId());
    queryDrvDetailDTOSOA.setVehicleTypeName(driverGuidDto.getVehicleTypeName()); //
    queryDrvDetailDTOSOA.setDrvFrom(driverGuidDto.getDrvFrom());
    queryDrvDetailDTOSOA.setDrvStatus(driverGuidDto.getDrvStatus());
    queryDrvDetailDTOSOA.setDrvLanguage(driverGuidDto.getDrvLanguage());
    queryDrvDetailDTOSOA.setDrvLanguageName(null);
    queryDrvDetailDTOSOA.setSupplierId(driverGuidDto.getSupplierId());
    queryDrvDetailDTOSOA.setSupplierName(driverGuidDto.getSupplierName());
    queryDrvDetailDTOSOA.setTransportGroupId(null); //
    queryDrvDetailDTOSOA.setTransportGroupName(null); //
    queryDrvDetailDTOSOA.setCreateUser(driverGuidDto.getCreateUser());
    queryDrvDetailDTOSOA.setModifyUser(driverGuidDto.getModifyUser());
    queryDrvDetailDTOSOA.setVehicleId(driverGuidDto.getVehicleId());
    queryDrvDetailDTOSOA.setCertificateNumber(driverGuidDto.getCertificateNumber());
    queryDrvDetailDTOSOA.setDrvAddr(driverGuidDto.getDrvAddr());
    queryDrvDetailDTOSOA.setIntendVehicleTypeId(driverGuidDto.getIntendVehicleTypeId());
    queryDrvDetailDTOSOA.setIntendVehicleTypeName(driverGuidDto.getVehicleTypeName()); //
    queryDrvDetailDTOSOA.setEmail(driverGuidDto.getEmail());
    queryDrvDetailDTOSOA.setWechat(driverGuidDto.getWechat());
    queryDrvDetailDTOSOA.setWorkPeriod(driverGuidDto.getWorkPeriod());
    queryDrvDetailDTOSOA.setCertiDate(getStringValue(driverGuidDto.getCertiDate()));
    queryDrvDetailDTOSOA.setQuasiDrivingType(driverGuidDto.getQuasiDrivingType());
    queryDrvDetailDTOSOA.setExpiryBeginDate(getStringValue(driverGuidDto.getExpiryBeginDate()));
    queryDrvDetailDTOSOA.setExpiryEndDate(getStringValue(driverGuidDto.getExpiryEndDate()));
    queryDrvDetailDTOSOA.setDrvcardImg(driverGuidDto.getDrvcardImg());
    queryDrvDetailDTOSOA.setIdcardImg(driverGuidDto.getIdcardImg());
    queryDrvDetailDTOSOA.setDrvHeadImg(driverGuidDto.getDrvHeadImg());
    queryDrvDetailDTOSOA.setPeopleVehicleImg(driverGuidDto.getPeopleVehicleImg());
    queryDrvDetailDTOSOA.setNetVehiclePeoImg(driverGuidDto.getNetVehiclePeoImg());
    queryDrvDetailDTOSOA.setFreezeHour(driverGuidDto.getFreezeHour());
    queryDrvDetailDTOSOA.setFreezeReason(driverGuidDto.getFreezeReason());
    queryDrvDetailDTOSOA.setFreezeTime(getStringValue(driverGuidDto.getFreezeTime()));
    queryDrvDetailDTOSOA.setOpFrom(driverGuidDto.getOpFrom());
    queryDrvDetailDTOSOA.setDatachangeCreatetime(getStringValue(driverGuidDto.getDatachangeCreatetime()));
    queryDrvDetailDTOSOA.setDatachangeLasttime(getStringValue(driverGuidDto.getDatachangeLasttime()));
    queryDrvDetailDTOSOA.setTransportGroupList(convert2TransportGroupResponseDTO(driverGuidDto.getTransportGroups()));
    queryDrvDetailDTOSOA.setDrvEnglishName(driverGuidDto.getDrvEnglishName());
    queryDrvDetailDTOSOA.setIgtCode(driverGuidDto.getIgtCode());
    queryDrvDetailDTOSOA.setLoginAccount(driverGuidDto.getLoginAccount());
    queryDrvDetailDTOSOA.setPpmAccount(driverGuidDto.getPpmAccount());
    queryDrvDetailDTOSOA.setQunarAccount(driverGuidDto.getQunarAccount());
    queryDrvDetailDTOSOA.setDrvStatusName(driverGuidDto.getDrvStatusName()); //
    queryDrvDetailDTOSOA.setDrvFromName(driverGuidDto.getDrvFromName()); //
    queryDrvDetailDTOSOA.setAreaScope(driverGuidDto.getAreaScope()); //
    queryDrvDetailDTOSOA.setCoopMode(driverGuidDto.getCoopMode());
    queryDrvDetailDTOSOA.setCoopModeName(null);
    queryDrvDetailDTOSOA.setNoCriminalProofImg(driverGuidDto.getNoCriminalProofImg());
    queryDrvDetailDTOSOA.setIdcardBackImg(driverGuidDto.getIdcardBackImg());
    queryDrvDetailDTOSOA.setOtherCertificateImg(driverGuidDto.getOtherCertificateImg());
    queryDrvDetailDTOSOA.setDrvLicenseData(null); //
    queryDrvDetailDTOSOA.setIdCardData(null); //
    queryDrvDetailDTOSOA.setNetDrvLicenseData(null);
    queryDrvDetailDTOSOA.setVehicleCertiData(null);
    queryDrvDetailDTOSOA.setNetTansCtfctData(null);
    // 报名状态（0：未报名，1：已报名，2：报名成功）
    queryDrvDetailDTOSOA.setApplyStatus(0);
    queryDrvDetailDTOSOA.setProLineList(driverGuidDto.getDrvProductionLineCodeList());
    queryDrvDetailDTOSOA.setNetDrvCheckStatus(null);
    queryDrvDetailDTOSOA.setNucleicAcidReportUrl(null);
    queryDrvDetailDTOSOA.setVaccinationReportUrl(null);
    queryDrvDetailDTOSOA.setNucleicAcidReportCertiData(null);
    queryDrvDetailDTOSOA.setVaccinationReportCtfctData(null);
    queryDrvDetailDTOSOA.setNetAppealMaterials(driverGuidDto.getNetAppealMaterials());
    queryDrvDetailDTOSOA.setNucleicAcidTestingTime(null);
    queryDrvDetailDTOSOA.setVaccinationTimeList(null);
    queryDrvDetailDTOSOA.setOcrheadPortraitResult(null);
    queryDrvDetailDTOSOA.setHeadPortraitCertiData(null);
    queryDrvDetailDTOSOA.setVersionFlag(driverGuidDto.getVersionFlag());
    queryDrvDetailDTOSOA.setDrvLicenseNumber(driverGuidDto.getDrvLicenseNumber());
    queryDrvDetailDTOSOA.setDrvLicenseName(driverGuidDto.getDrvLicenseName());
    queryDrvDetailDTOSOA.setCertificateConfigStr(null);
    queryDrvDetailDTOSOA.setOcrFieldValue(driverGuidDto.getOcrFieldValue());
    queryDrvDetailDTOSOA.setIdcardAppealMaterials(driverGuidDto.getIdcardAppealMaterials());
    queryDrvDetailDTOSOA.setDrvLicenseAppealMaterials(driverGuidDto.getDrvLicenseAppealMaterials());
    queryDrvDetailDTOSOA.setRaisingPickUp(driverGuidDto.getRaisingPickUp());
    queryDrvDetailDTOSOA.setChildSeat(driverGuidDto.getChildSeat());
    queryDrvDetailDTOSOA.setInternalScope(driverGuidDto.getInternalScope());
    queryDrvDetailDTOSOA.setDriverNetCertNo(driverGuidDto.getDriverNetCertNo());
    queryDrvDetailDTOSOA.setActive(driverGuidDto.getActive());
    queryDrvDetailDTOSOA.setOnlineTime(getStringValue(driverGuidDto.getOnlineTime()));
    queryDrvDetailDTOSOA.setCheckDataList(null);
    queryDrvDetailDTOSOA.setTemporaryDispatchMark(driverGuidDto.getTemporaryDispatchMark());
    queryDrvDetailDTOSOA.setTemporaryDispatchEndDatetime(getStringValue(driverGuidDto.getTemporaryDispatchEndDatetime()));
    queryDrvDetailDTOSOA.setPaiayAccount(driverGuidDto.getPaiayAccount());
    queryDrvDetailDTOSOA.setPaiayEmail(driverGuidDto.getPaiayEmail());
    queryDrvDetailDTOSOA.setAccountType(driverGuidDto.getAccountType());
    return queryDrvDetailDTOSOA;
  }

  public static List<TransportGroupResponseDTO> convert2TransportGroupResponseDTO(List<TransportGroup> transportGroups) {
    return Optional.ofNullable(transportGroups).orElse(Lists.newArrayList()).stream().map(transportGroup -> {
      TransportGroupResponseDTO transportGroupResponseDTO = new TransportGroupResponseDTO();
      transportGroupResponseDTO.setTransportGroupId(transportGroup.getTransportGroupId());
      transportGroupResponseDTO.setTransportGroupName(transportGroup.getTransportGroupName());
      transportGroupResponseDTO.setTransportGroupMode(transportGroup.getTransportGroupMode());
      return transportGroupResponseDTO;
    }).collect(Collectors.toList());
  }

  public static DriverGuidDriverRequestDTO buildToDriverGuidDriverRequestDTOForQueryDrvDetail(
    QueryDrvDetailSOARequestType request) {
    DriverGuidDriverRequestDTO driverGuidDriverRequestDTO = new DriverGuidDriverRequestDTO();
    driverGuidDriverRequestDTO.setDriverIdList(Lists.newArrayList(request.getDrvId()));
    driverGuidDriverRequestDTO.setSupplierId(request.getSupplierId());
    return driverGuidDriverRequestDTO;
  }

  public static QueryDrvDetailSOAResponseType convert2QueryDriverDetailResp(Long drvId, List<DriverGuideDTO> driverGuidDTOS) {
    if (CollectionUtils.isEmpty(driverGuidDTOS)) {
      logger.info(QUERY_DRV_DETAIL_EMPTY, " is empty:{}", drvId);
      Cat.logEvent(Constant.EventType.BRIDGE, QUERY_DRV_DETAIL_EMPTY);
      return new QueryDrvDetailSOAResponseType();
    }
    Cat.logEvent(Constant.EventType.BRIDGE, QUERY_DRV_DETAIL);
    QueryDrvDetailSOAResponseType responseType = new QueryDrvDetailSOAResponseType();
    responseType.setData(convert2QueryDrvDetailDTOSOA(driverGuidDTOS.get(0)));
    return responseType;
  }

  public static VehicleDetailSOAResponseType convert2DriverGuidVehicleRequestDTO4QueryVehicleDetail(Integer vehicleId, List<VehVehicleDTO> vehVehicleDTOS) {
    if (CollectionUtils.isEmpty(vehVehicleDTOS)) {
      logger.info(QUERY_VEHICLE_DETAIL_EMPTY, " is empty:{}", vehicleId);
      Cat.logEvent(Constant.EventType.BRIDGE, QUERY_VEHICLE_DETAIL_EMPTY);
      return new VehicleDetailSOAResponseType();
    }
    Cat.logEvent(Constant.EventType.BRIDGE, QUERY_VEHICLE_DETAIL);
    VehicleDetailSOAResponseType responseType = new VehicleDetailSOAResponseType();
    responseType.setData(BridgeConverter.convert2VehicleDetailDTO(vehVehicleDTOS.get(0)));
    return responseType;
  }

  public static VehicleDetailDTO convert2VehicleDetailDTO(VehVehicleDTO vehVehicleDT) {
    VehicleDetailDTO vehicleDetailDTO = new VehicleDetailDTO();
    vehicleDetailDTO.setVehicleId(vehVehicleDT.getVehicleId());
    vehicleDetailDTO.setAreaScope(null);
    vehicleDetailDTO.setCityId(vehVehicleDT.getCityId());
    vehicleDetailDTO.setCityName(null);
    vehicleDetailDTO.setSupplierId(vehVehicleDT.getSupplierId());
    vehicleDetailDTO.setSupplierName(null);
    vehicleDetailDTO.setVehicleTypeId(vehVehicleDT.getVehicleTypeId());
    vehicleDetailDTO.setVehicleTypeName(vehVehicleDT.getVehicleTypeName());
    vehicleDetailDTO.setHasDrv(Objects.equals(vehVehicleDT.getHasDrv(), true) ? 1 : 0);
    vehicleDetailDTO.setHasDrvValue(null);
    vehicleDetailDTO.setVehicleLicense(vehVehicleDT.getVehicleLicense());
    vehicleDetailDTO.setVehicleBrandId(vehVehicleDT.getVehicleBrandId());
    vehicleDetailDTO.setVehicleBrandName(vehVehicleDT.getVehicleBrandName());
    vehicleDetailDTO.setVehicleEnergyType(vehVehicleDT.getVehicleEnergyType());
    vehicleDetailDTO.setVehicleSeries(vehVehicleDT.getVehicleSeries());
    vehicleDetailDTO.setVehicleSeriesName(vehVehicleDT.getVehicleSeriesName());
    vehicleDetailDTO.setVehicleColorId(vehVehicleDT.getVehicleColorId());
    vehicleDetailDTO.setVehicleColorName(vehVehicleDT.getVehicleColorName());
    vehicleDetailDTO.setVehicleCertiImg(vehVehicleDT.getVehicleCertiImg());
    vehicleDetailDTO.setNetTansCtfctImg(vehVehicleDT.getNetTansCtfctImg());
    vehicleDetailDTO.setVehicleFullImg(vehVehicleDT.getVehicleFullImg());
    vehicleDetailDTO.setVehicleFrontImg(vehVehicleDT.getVehicleFrontImg());
    vehicleDetailDTO.setVehicleBackImg(vehVehicleDT.getVehicleBackImg());
    vehicleDetailDTO.setVehicleTrunkImg(vehVehicleDT.getVehicleTrunkImg());
    vehicleDetailDTO.setDatachangeCreatetime(getStringValue(vehVehicleDT.getDatachangeCreatetime()));
    vehicleDetailDTO.setDatachangeLasttime(getStringValue(vehVehicleDT.getDatachangeLasttime()));
    vehicleDetailDTO.setCreateUser(vehVehicleDT.getCreateUser());
    vehicleDetailDTO.setModifyUser(vehVehicleDT.getModifyUser());
    vehicleDetailDTO.setVin(vehVehicleDT.getVin());
    vehicleDetailDTO.setRegstDate(getStringValue(vehVehicleDT.getRegstDate()));
    vehicleDetailDTO.setVehiclePowerModeValue(null);
    vehicleDetailDTO.setUsingNature(vehVehicleDT.getUsingNature());
    vehicleDetailDTO.setUsingNatureValue(null);
    vehicleDetailDTO.setVehicleStatus(vehVehicleDT.getVehicleStatus());
    vehicleDetailDTO.setVehicleStatusName(null);
    vehicleDetailDTO.setVehicleCertiData(null);
    vehicleDetailDTO.setNetTansCtfctData(null);
    vehicleDetailDTO.setProLineList(null);
    vehicleDetailDTO.setNetVehicleCheckStatus(null);
    vehicleDetailDTO.setNetAppealMaterials(vehVehicleDT.getNetAppealMaterials());
    vehicleDetailDTO.setVehicleLicenseCityId(vehVehicleDT.getVehicleLicenseCityId());
    vehicleDetailDTO.setVehicleLicenseCityName(null);
    vehicleDetailDTO.setVersionFlag(vehVehicleDT.getVersionFlag());
    vehicleDetailDTO.setCertificateConfigStr(null);
    vehicleDetailDTO.setOcrFieldValue(vehVehicleDT.getOcrFieldValue());
    vehicleDetailDTO.setBindingDrvId(null);
    vehicleDetailDTO.setVehicleLicenseAppealMaterials(vehVehicleDT.getVehicleLicenseAppealMaterials());
    vehicleDetailDTO.setVehicleNetCertNo(vehVehicleDT.getVehicleNetCertNo());
    vehicleDetailDTO.setActive(vehVehicleDT.getActive());
    vehicleDetailDTO.setCheckDataList(null);
    vehicleDetailDTO.setVehicleAuditStatus(null);
    vehicleDetailDTO.setOcrPassStatusList(null);
    vehicleDetailDTO.setTemporaryDispatchMark(vehVehicleDT.getTemporaryDispatchMark());
    vehicleDetailDTO.setTemporaryDispatchEndDatetime(getStringValue(vehVehicleDT.getTemporaryDispatchEndDatetime()));
    return vehicleDetailDTO;
  }

  public static DriverGuidVehicleRequestDTO convert2DriverGuidVehicleRequestDTO4QueryVehicleDetail(
    VehicleDetailSOARequestType request) {
    DriverGuidVehicleRequestDTO driverGuidVehicleRequestDTO = new DriverGuidVehicleRequestDTO();
    driverGuidVehicleRequestDTO.setVehicleIdList(Lists.newArrayList(Long.valueOf(request.getVehicleId())));
    return driverGuidVehicleRequestDTO;
  }



  public static QueryDriverIdForSaasResponseType convert2QueryDriverIdForSaas(List<DriverGuideDTO> driverGuidDTOS) {
    QueryDriverIdForSaasResponseType queryDriverIdForSaasResponseType = new QueryDriverIdForSaasResponseType();
    queryDriverIdForSaasResponseType.setDriverIds(Optional.ofNullable(driverGuidDTOS).orElse(Lists.newArrayList()).stream().map(
      DriverGuideDTO::getDrvId).collect(Collectors.toList()));
    return queryDriverIdForSaasResponseType;
  }

  public static DriverGuidDriverRequestDTO convert2DriverGuidDriverRequestDTO(QueryDriverIdForSaasRequestType queryDriverIdForSaasRequestType) {
    DriverGuidDriverRequestDTO driverGuidDriverRequestDTO = new DriverGuidDriverRequestDTO();
    driverGuidDriverRequestDTO.setDriverPhoneList(queryDriverIdForSaasRequestType.getDriverPhone());
    driverGuidDriverRequestDTO.setSupplierId(queryDriverIdForSaasRequestType.getSupplierId());
    return driverGuidDriverRequestDTO;
  }

  public static QueryVehicleIdForSaasResponseType convert2QueryVehicleIdForSaas(List<VehVehicleDTO> vehVehicleDTOS) {
    QueryVehicleIdForSaasResponseType response = new QueryVehicleIdForSaasResponseType();
    response.setVehicleIds(Optional.ofNullable(vehVehicleDTOS).orElse(Lists.newArrayList()).stream().map(VehVehicleDTO::getVehicleId).collect(Collectors.toList()));
    return response;
  }

  public static DriverGuidVehicleRequestDTO convert2QueryVehicleIdForSaasDTO(QueryVehicleIdForSaasRequestType request) {
    DriverGuidVehicleRequestDTO driverGuidVehicleRequestDTO = new DriverGuidVehicleRequestDTO();
    driverGuidVehicleRequestDTO.setVehicleLicenseList(request.getVehicleLicense());
    driverGuidVehicleRequestDTO.setSupplierId(request.getSupplierId());
    return driverGuidVehicleRequestDTO;
  }

  public static QueryDriverForSaasResponseType convert2QueryDriverForSaasRspDTO(List<DriverGuideDTO> driverGuidDTOS) {
    QueryDriverForSaasResponseType response = new QueryDriverForSaasResponseType();
    response.setDriverList(Optional.ofNullable(driverGuidDTOS).orElse(Lists.newArrayList()).stream().map(BridgeConverter::convert2SaasDriverSoaDTO).collect(Collectors.toList()));
    return response;
  }

  public static SaasDriverSoaDTO convert2SaasDriverSoaDTO(DriverGuideDTO driverGuidDTO) {
    SaasDriverSoaDTO saasDriverSoaDTO = new SaasDriverSoaDTO();
    saasDriverSoaDTO.setDriverId(driverGuidDTO.getDrvId());
    saasDriverSoaDTO.setSupplierId(driverGuidDTO.getSupplierId());
    saasDriverSoaDTO.setDriverStatus(driverGuidDTO.getDrvStatus());
    saasDriverSoaDTO.setDriverName(driverGuidDTO.getDriverName());
    saasDriverSoaDTO.setCityId(driverGuidDTO.getCityId());
    saasDriverSoaDTO.setCityName(driverGuidDTO.getCityName());
    saasDriverSoaDTO.setDriverPhone(driverGuidDTO.getDriverPhone());
    saasDriverSoaDTO.setDriverPhoneAreaCode(driverGuidDTO.getIgtCode());
    saasDriverSoaDTO.setTransportGroupList(convert2SaasTransportGroupSoaDTO(driverGuidDTO.getTransportGroups()));  // saas不需要运力组
    saasDriverSoaDTO.setDriverCategory(driverGuidDTO.getCategorySynthesizeCode());
    saasDriverSoaDTO.setVehicleId(driverGuidDTO.getVehicleId());
    saasDriverSoaDTO.setDriverLanguage(driverGuidDTO.getDrvLanguage());
    return saasDriverSoaDTO;
  }

  private static List<SaasTransportGroupSoaDTO> convert2SaasTransportGroupSoaDTO(List<TransportGroup> transportGroups) {
    return Optional.ofNullable(transportGroups).orElse(Lists.newArrayList()).stream().map(transportGroup -> {
      SaasTransportGroupSoaDTO saasTransportGroupSoaDTO = new SaasTransportGroupSoaDTO();
      saasTransportGroupSoaDTO.setTransportGroupId(transportGroup.getTransportGroupId());
      saasTransportGroupSoaDTO.setTransportGroupStatus(TmsTransportConstant.TransportGroupStatusEnum.ONLINE.getCode());
      return saasTransportGroupSoaDTO;
    }).collect(Collectors.toList());
  }

  public static DriverGuidDriverRequestDTO convert2QueryDriverForSaasRequestDTO(
    QueryDriverForSaasRequestType requestType) {
    DriverGuidDriverRequestDTO driverGuidDriverRequestDTO = new DriverGuidDriverRequestDTO();
    driverGuidDriverRequestDTO.setDriverIdList(requestType.getDriverIds());
    return driverGuidDriverRequestDTO;
  }

  public static DriverGuidDriverRequestDTO convert2DriverGuidDriverRequestDTO(QueryDriverForSaasRequestType requestType) {
    DriverGuidDriverRequestDTO driverGuidDriverRequestDTO = new DriverGuidDriverRequestDTO();
    driverGuidDriverRequestDTO.setDriverIdList(requestType.getDriverIds());
    return driverGuidDriverRequestDTO;
  }

  public static DriverGuidVehicleRequestDTO convert2QueryVehicleForSaasRequestDTO(
    QueryVehicleForSaasRequestType requestType) {
    DriverGuidVehicleRequestDTO driverGuidVehicleRequestDTO = new DriverGuidVehicleRequestDTO();
    driverGuidVehicleRequestDTO.setVehicleIdList(requestType.getVehicleIds());
    return driverGuidVehicleRequestDTO;
  }

  public static QueryVehicleForSaasResponseType convert2QueryVehicleForSaasRsp(List<VehVehicleDTO> vehVehicleDTOS) {
    QueryVehicleForSaasResponseType responseType = new QueryVehicleForSaasResponseType();
    responseType.setVehicleList(Optional.ofNullable(vehVehicleDTOS).orElse(Lists.newArrayList()).stream().map(BridgeConverter::convert2VehicleDTO).collect(Collectors.toList()));
    return responseType;
  }

  public static SaasVehicleSoaDTO convert2VehicleDTO(VehVehicleDTO vehVehicleDTO) {
    SaasVehicleSoaDTO saasVehicleSoaDTO = new SaasVehicleSoaDTO();
    saasVehicleSoaDTO.setVehicleId(vehVehicleDTO.getVehicleId());
    saasVehicleSoaDTO.setSupplerId(vehVehicleDTO.getSupplierId());
    saasVehicleSoaDTO.setVehicleStatus(vehVehicleDTO.getVehicleStatus());
    saasVehicleSoaDTO.setVehicleLicense(vehVehicleDTO.getVehicleLicense());
    saasVehicleSoaDTO.setVehicleBrandId(vehVehicleDTO.getVehicleBrandId());
    saasVehicleSoaDTO.setVehicleBrandName(vehVehicleDTO.getVehicleBrandName());
    saasVehicleSoaDTO.setVehicleSeriesId(vehVehicleDTO.getVehicleSeries());
    saasVehicleSoaDTO.setVehicleSeriesName(vehVehicleDTO.getVehicleSeriesName());
    saasVehicleSoaDTO.setVehicleTypeId(vehVehicleDTO.getVehicleTypeId());
    saasVehicleSoaDTO.setVehicleTypeName(vehVehicleDTO.getVehicleTypeName());
    saasVehicleSoaDTO.setVehicleCategory(vehVehicleDTO.getVehicleCategory());
    saasVehicleSoaDTO.setVehicleColorId(vehVehicleDTO.getVehicleColorId());
    saasVehicleSoaDTO.setVehicleColorName(vehVehicleDTO.getVehicleColorName());
    return saasVehicleSoaDTO;
  }
  public static DriverGuidVehicleRequestDTO convert2DriverGuidVehicleRequestDTO(
    QueryVehicleBaseSOARequestType requestType) {
    DriverGuidVehicleRequestDTO driverGuidVehicleRequestDTO = new DriverGuidVehicleRequestDTO();
    driverGuidVehicleRequestDTO.setSupplierId(requestType.getSupplierId());
    driverGuidVehicleRequestDTO.setCityId(requestType.getCityId());
    driverGuidVehicleRequestDTO.setHasDrv(requestType.getHasDrv());
    driverGuidVehicleRequestDTO.setVehicleStatus(TmsTransportConstant.VehStatusEnum.ONLINE.getCode());
    return driverGuidVehicleRequestDTO;
  }

  public static List<DriverInfo> convert2DriverInfo(List<DriverGuideDTO> driverGuidDTOList) {
    return setDriverInfo(driverGuidDTOList);
  }

  public static List<DriverInfo> setDriverInfo(List<DriverGuideDTO> driverGuidDTOList) {
    return Optional.ofNullable(driverGuidDTOList).orElse(Collections.emptyList()).stream().map(driverGuidDTO -> convert2DriverInfo(driverGuidDTO)).collect(
      Collectors.toList());
  }

  public static DriverInfo convert2DriverInfo(DriverGuideDTO driverGuidDTO) {
    DriverInfo driverInfo = new DriverInfo();
    driverInfo.setDriverId(driverGuidDTO.getDrvId());
    driverInfo.setStatus(driverGuidDTO.getDrvStatus());
    driverInfo.setBroadcast(driverGuidDTO.getBroadcast());
    driverInfo.setIsSendWorkPeriod(driverGuidDTO.getIsSendWorkPeriod());
    driverInfo.setCompatibleCoopMode(driverGuidDTO.getCompatibleCoopMode());
    driverInfo.setCoopMode(driverGuidDTO.getCoopMode());
    driverInfo.setDriverName(driverGuidDTO.getDriverName());
    driverInfo.setDriverPhone(driverGuidDTO.getDrvPhone());
    driverInfo.setPhoneAreaCode(driverGuidDTO.getPhoneAreaCode());
    driverInfo.setCarId(driverGuidDTO.getCarId());
    driverInfo.setCarLicense(driverGuidDTO.getCarLicense());
    driverInfo.setCarBrandId(driverGuidDTO.getCarBrandId());
    driverInfo.setCarBrandName(driverGuidDTO.getCarBrandName());
    driverInfo.setCarTypeId(driverGuidDTO.getCarTypeId());
    driverInfo.setCarTypeName(driverGuidDTO.getCarTypeName());
    driverInfo.setCarColorId(driverGuidDTO.getCarColorId());
    driverInfo.setCarColor(driverGuidDTO.getCarColor());
    driverInfo.setIntendVehicleTypeId(driverGuidDTO.getIntendVehicleTypeId());
    driverInfo.setCityId(driverGuidDTO.getCityId());
    driverInfo.setCityName(driverGuidDTO.getCityName());
    driverInfo.setAddressLongitude(driverGuidDTO.getAddressLongitude());
    driverInfo.setAddressLatitude(driverGuidDTO.getAddressLatitude());
    driverInfo.setIsEnergy(driverGuidDTO.getIsEnergy());
    driverInfo.setWorkTimes(driverGuidDTO.getWorkTimes());
    driverInfo.setDriverLanguage(driverGuidDTO.getDrvLanguage());
    driverInfo.setCountryId(driverGuidDTO.getCountryId());
    driverInfo.setCountryName(driverGuidDTO.getCountryName());
    driverInfo.setSupplierId(driverGuidDTO.getSupplierId());
    driverInfo.setEmail(driverGuidDTO.getEmail());
    driverInfo.setWechat(driverGuidDTO.getWechat());
    driverInfo.setCreateTime(driverGuidDTO.getCreateTime());
    driverInfo.setTransportGroups(driverGuidDTO.getTransportGroups());
    driverInfo.setInternalScope(driverGuidDTO.getInternalScope());
    driverInfo.setQunarCityCode(driverGuidDTO.getQunarCityCode());
    driverInfo.setPicUrl(driverGuidDTO.getPicUrl());
    driverInfo.setCarSeriesId(driverGuidDTO.getCarSeriesId());
    driverInfo.setCarSeriesName(driverGuidDTO.getCarSeriesName());
    driverInfo.setMaxLuggages(driverGuidDTO.getMaxLuggages());
    driverInfo.setMaxPassengers(driverGuidDTO.getMaxPassengers());
    driverInfo.setSupplierName(driverGuidDTO.getSupplierName());
    driverInfo.setDrvProductionLineCodeList(driverGuidDTO.getDrvProductionLineCodeList());
    driverInfo.setVehProductionLineCodeList(driverGuidDTO.getVehProductionLineCodeList());
    driverInfo.setVehicleStatus(driverGuidDTO.getVehicleStatus());
    driverInfo.setDrvIdcard(driverGuidDTO.getDrvIdcard());
    driverInfo.setCertiDate(getStringValue(driverGuidDTO.getCertiDate()));
    driverInfo.setExpiryBeginDate(getStringValue(driverGuidDTO.getExpiryBeginDate()));
    driverInfo.setExpiryEndDate(getStringValue(driverGuidDTO.getExpiryEndDate()));
    driverInfo.setDrvLicenseNumber(driverGuidDTO.getDrvLicenseNumber());
    driverInfo.setVin(driverGuidDTO.getVin());
    driverInfo.setRideHailingVehCertValid(driverGuidDTO.getRideHailingVehCertValid());
    driverInfo.setRideHailingDrvCertValid(driverGuidDTO.getRideHailingDrvCertValid());
    driverInfo.setDrvConnectAddress(driverGuidDTO.getDrvConnectAddress());
    driverInfo.setOnlineTime(getStringValue(driverGuidDTO.getOnlineTime()));
    driverInfo.setVehRegstDate(driverGuidDTO.getVehRegstDate());
    driverInfo.setVehCreateTime(driverGuidDTO.getVehCreateTime());
    driverInfo.setVehBindTime(getStringValue(driverGuidDTO.getVehBindTime()));
    driverInfo.setDrvcardImg(driverGuidDTO.getDrvcardImg());
    driverInfo.setVehicleFullImg(driverGuidDTO.getVehicleFullImg());
    driverInfo.setNation(driverGuidDTO.getNation());
    driverInfo.setRaisingPickUp(driverGuidDTO.getRaisingPickUp());
    driverInfo.setChildSeat(driverGuidDTO.getChildSeat());
    driverInfo.setRealPicUrl(driverGuidDTO.getRealPicUrl());
    driverInfo.setDriverNetCertNo(driverGuidDTO.getDriverNetCertNo());
    driverInfo.setVehicleNetCertNo(driverGuidDTO.getVehicleNetCertNo());
    driverInfo.setDispatchSupplierIdList(driverGuidDTO.getDispatchSupplierIdList());
    driverInfo.setDrvTemporaryMark(driverGuidDTO.getTemporaryDispatchMark());
    driverInfo.setVehTemporaryMark(driverGuidDTO.getTemporaryVehicleMark());
    driverInfo.setDrvTemporaryEndTime(driverGuidDTO.getDrvTemporaryEndTime());
    driverInfo.setVehTemporaryEndTime(driverGuidDTO.getVehTemporaryEndTime());
    driverInfo.setPaiayAccount(driverGuidDTO.getPaiayAccount());
    driverInfo.setPaiayEmail(driverGuidDTO.getPaiayEmail());
    driverInfo.setAccountType(driverGuidDTO.getAccountType());
    driverInfo.setPpmAccount(driverGuidDTO.getPpmAccount());
    return driverInfo;
  }

  public static String getStringValue(Object obj) {
    if (Objects.nonNull(obj)) {
      if (obj instanceof Timestamp) {
        return DateUtil.timestampToString((Timestamp) obj,DateUtil.YYYYMMDDHHMMSS);
      }
      return String.valueOf(obj);
    }
    return null;
}

  public static DriverGuidDriverRequestDTO buildToDriverGuidDriverRequestDTO(DriverInfoSOARequestType request) {
    DriverGuidDriverRequestDTO driverGuidDriverRequestDTO = new DriverGuidDriverRequestDTO();
    driverGuidDriverRequestDTO.setDriverIdListByString(request.getDriverIds());
    driverGuidDriverRequestDTO.setDriverName(request.getDriverName());
    driverGuidDriverRequestDTO.setDriverPhoneListByString(request.getDriverPhone());
    driverGuidDriverRequestDTO.setStatus(request.getStatus());
    driverGuidDriverRequestDTO.setSupplierId(request.getSupplierId());
    return driverGuidDriverRequestDTO;
  }
}
