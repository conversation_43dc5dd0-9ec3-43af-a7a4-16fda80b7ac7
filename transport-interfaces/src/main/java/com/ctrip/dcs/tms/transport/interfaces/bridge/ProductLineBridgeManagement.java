package com.ctrip.dcs.tms.transport.interfaces.bridge;

import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvIdByTransportGroupsRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvIdByTransportGroupsResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryVehicleBaseSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryVehicleBaseSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.VehicleDetailSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.VehicleDetailSOAResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverForSaasResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverIdForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverIdForSaasResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleForSaasResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleIdForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleIdForSaasResponseType;
import com.ctrip.dcs.tms.transport.application.query.impl.TransportGroupQueryServiceImpl;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctriposs.baiji.specific.SpecificRecord;
import com.dianping.cat.Cat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 桥接处理器
 * 1 根据请求类型获取桥接服务
 * 2 埋点
 * <AUTHOR>
 */
@Component
public class ProductLineBridgeManagement {

  @Autowired
  DriverBridgeService driverBridgeService;

  @Autowired
  DriverGuideBridgeService driverGuidBridgeService;

  @Autowired
  TransportGroupQueryServiceImpl transportGroupQueryService;

  @Autowired
  TmsTransportQconfig transportQconfig;

  @Autowired
  DriverAndDriverGuideMergeProcessService driverAndDriverGuideMergeProcessService;


  public DriverInfoSOAResponseType queryDriver(DriverInfoSOARequestType request) {
    return getBridge(request).queryDriver(request);
  }

  public QueryVehicleBaseSOAResponseType queryVehicleListBySupplierId(QueryVehicleBaseSOARequestType request) {
    return getBridge(request).queryVehicleListBySupplierId(request);
  }

  public QueryDrvDetailSOAResponseType queryDrvDetail(QueryDrvDetailSOARequestType request) {
    return getBridge(request).queryDrvDetail(request);
  }

  public QueryDrvIdByTransportGroupsResponseType queryDrvIdByTransportGroups(
    QueryDrvIdByTransportGroupsRequestType request) {
    return getBridge(request).queryDrvIdByTransportGroups(request);
  }

  public VehicleDetailSOAResponseType queryVehicleDetail(VehicleDetailSOARequestType request){
    return getBridge(request).queryVehicleDetail(request);
  }

  public QueryDriverIdForSaasResponseType queryDriverIdForSaas(
    QueryDriverIdForSaasRequestType request) {
    return getBridge(request).queryDriverIdForSaas(request);
  }

  public QueryVehicleIdForSaasResponseType queryVehicleIdForSaas(
    QueryVehicleIdForSaasRequestType request){
    return getBridge(request).queryVehicleIdForSaas(request);
  }

  public QueryDriverForSaasResponseType queryDriverForSaas(QueryDriverForSaasRequestType request){
    return getBridge(request).queryDriverForSaas(request);
  }

  public QueryVehicleForSaasResponseType queryVehicleForSaas(
    QueryVehicleForSaasRequestType request){
    return getBridge(request).queryVehicleForSaas(request);
  }

  protected ProductLineBridge getBridge(SpecificRecord record) {
    Object productLineRoute = record.get("productLineRoute");
    if (Objects.equals(productLineRoute, "day") && transportQconfig.openSupplierDayProLineMigration()) {
      if (transportQconfig.dayFull()) {
        Cat.logEvent(Constant.EventType.PRODUCT_LINE_ROUTE, "day");
        return driverGuidBridgeService;
      }
      Cat.logEvent(Constant.EventType.PRODUCT_LINE_ROUTE, "day-gray");
      return driverAndDriverGuideMergeProcessService;
    }
    Cat.logEvent(Constant.EventType.PRODUCT_LINE_ROUTE, "jnt");
    return driverBridgeService;
  }

}
