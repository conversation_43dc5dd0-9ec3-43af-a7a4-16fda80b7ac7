package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.model.ShowTaxiLabelRequestType;
import com.ctrip.model.ShowTaxiLabelResponseType;

/**
 * 显示出租车标签
 */
@Component
public class ShowTaxiLabelExecutor extends AbstractRpcExecutor<ShowTaxiLabelRequestType, ShowTaxiLabelResponseType> implements Validator<ShowTaxiLabelRequestType> {

    @Autowired
    private DriverQueryService driverQueryService;

    @Override
    public ShowTaxiLabelResponseType execute(ShowTaxiLabelRequestType requestType) {
        ShowTaxiLabelResponseType responseType = new ShowTaxiLabelResponseType();
        responseType.setShow(false);
        Long cityId = requestType.getCityId();
        Long supplierId = requestType.getSupplierId();
        try {
            Result<Boolean> booleanResult = driverQueryService.checkTaxiLabel(supplierId, cityId);
            if (booleanResult.isSuccess()) {
                responseType.setShow(booleanResult.getData());
            }
            return ServiceResponseUtils.success(responseType);
        } catch (Exception e) {
            return ServiceResponseUtils.fail(responseType, "500", "显示出租车标签失败: " + e.getMessage());
        }
    }
}
