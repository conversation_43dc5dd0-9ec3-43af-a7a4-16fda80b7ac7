package com.ctrip.dcs.tms.transport.interfaces.provider.executor;


import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.dcs.tms.transport.application.query.VehicleQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.TmsTransUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.QueryDrvDO;
import com.ctrip.igt.PaginationDTO;
import com.ctrip.igt.framework.common.base.PageHolder;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 司机列表
 */
@Component
public class QueryDiscardVehicleListExecutor extends AbstractRpcExecutor<QueryDiscardVehicleListSOARequestType, QueryDiscardVehicleListSOAResponseType> implements Validator<QueryDiscardVehicleListSOARequestType> {

    @Autowired
    private VehicleQueryService vehQueryService;

    @Override
    public QueryDiscardVehicleListSOAResponseType execute(QueryDiscardVehicleListSOARequestType requestType) {
        QueryDiscardVehicleListSOAResponseType responseType = new QueryDiscardVehicleListSOAResponseType();
        Result<PageHolder<VehicleListSOADTO>> pageHolderResult = vehQueryService.queryDiscardVehList(requestType);
        PaginationDTO paginationDTO = ServiceResponseUtils.newPagination(pageHolderResult.getData().getPageIndex(),
                pageHolderResult.getData().getPageSize(), pageHolderResult.getData().getTotalSize());
        responseType.setVehicleList(pageHolderResult.getData().getData());
        responseType.setPagination(paginationDTO);
        return ServiceResponseUtils.success(responseType);
    }

    @Override
    public void validate(AbstractValidator<QueryDiscardVehicleListSOARequestType> validator) {
        validator.ruleFor("supplierId").notNull();
    }
}
