package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;


/**
 * <AUTHOR>
 * @Date 2020/4/24 11:45
 */
@Component
public class QueryDrvInfoExecutor extends AbstractRpcExecutor<QueryDrvInfoSOARequestType, QueryDrvInfoSOAResponseType> implements Validator<QueryDrvInfoSOARequestType> {

    @Autowired
    private DriverQueryService driverQueryService;
    @Override
    public QueryDrvInfoSOAResponseType execute(QueryDrvInfoSOARequestType queryDrvInfoSOARequestType) {
        QueryDrvInfoSOAResponseType soaResponseType = new QueryDrvInfoSOAResponseType();
        Result<QueryDrvInfoDTOSOA> infoDTOSOAResult = driverQueryService.queryDrvInfo(queryDrvInfoSOARequestType);
        if (infoDTOSOAResult.isSuccess()) {
            soaResponseType.setData(infoDTOSOAResult.getData());
            return ServiceResponseUtils.success(soaResponseType);
        }else {
            return ServiceResponseUtils.fail(soaResponseType, infoDTOSOAResult.getCode(),infoDTOSOAResult.getMsg());
        }
    }
}
