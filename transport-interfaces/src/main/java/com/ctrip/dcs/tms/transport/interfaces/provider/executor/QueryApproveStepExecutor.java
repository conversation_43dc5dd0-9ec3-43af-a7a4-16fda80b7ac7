package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
　* @description: 查询单项信息
　* <AUTHOR>
　* @date 2021/12/2 15:55
*/
@Component
public class QueryApproveStepExecutor extends AbstractRpcExecutor<QueryApproveStepSOARequestType, QueryApproveStepSOAResponseType> implements Validator<QueryApproveStepSOARequestType> {

    @Autowired
    private RecruitingQueryService recruitingQueryService;

    @Override
    public QueryApproveStepSOAResponseType execute(QueryApproveStepSOARequestType requestType) {
        QueryApproveStepSOAResponseType responseType = new QueryApproveStepSOAResponseType();
        Result<QueryApproveStepSOAResponseType> result = recruitingQueryService.queryApproveStep(requestType);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(result.getData());
        }
        return ServiceResponseUtils.fail(responseType);
    }

    @Override
    public void validate(AbstractValidator<QueryApproveStepSOARequestType> validator) {
        validator.ruleFor("approveSourceId").notNull();
    }
}