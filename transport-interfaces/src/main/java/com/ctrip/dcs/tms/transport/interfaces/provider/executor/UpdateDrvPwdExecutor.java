package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * <AUTHOR>
 * @since 2020/9/16 19:27
 */
@Component
public class UpdateDrvPwdExecutor extends AbstractRpcExecutor<UpdateDrvPwdRequestType, UpdateDrvPwdResponseType>
    implements Validator<UpdateDrvPwdRequestType> {

  @Autowired
  private DriverAccountCommandService accountCmdService;

  @Override
  public UpdateDrvPwdResponseType execute(UpdateDrvPwdRequestType request) {
    Result<DrvDriverPO> result =
        accountCmdService.updatePwd(request.getDrvId(), request.getOldPwd(), request.getNewPwd(), request.getConfirmPwd());

    UpdateDrvPwdResponseType response = new UpdateDrvPwdResponseType();
    if (result.isSuccess()) {
      return ServiceResponseUtils.success(response);
    }
    return ServiceResponseUtils.fail(response, result.getCode(), result.getMsg());
  }

  @Override
  public void validate(AbstractValidator<UpdateDrvPwdRequestType> validator) {
    validator.ruleFor("drvId").notNull().greaterThan(0L);
    validator.ruleFor("newPwd").notNull().notEmpty();
    validator.ruleFor("confirmPwd").notNull().notEmpty();
  }
}
