package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import com.google.common.base.*;
import org.apache.commons.collections.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class CheckDrvPenaltyStatusExecutor extends AbstractRpcExecutor<CheckDrvPenaltyStatusSOARequestType, CheckDrvPenaltyStatusSOAResponseType> implements Validator<CheckDrvPenaltyStatusSOARequestType> {

    @Autowired
    private DriverQueryService driverQueryService;

    @Autowired
    private TmsTransportQconfig tmsTransportQconfig;

    @Override
    public CheckDrvPenaltyStatusSOAResponseType execute(CheckDrvPenaltyStatusSOARequestType checkDrvPenaltyStatusSOARequestType) {
        CheckDrvPenaltyStatusSOAResponseType soaResponseType = new CheckDrvPenaltyStatusSOAResponseType();
        if (!tmsTransportQconfig.getPenaltySwitch()) {
            soaResponseType.setData(true);
            soaResponseType.setHasDrvName(false);
            return ServiceResponseUtils.success(soaResponseType);
        }
        List<Long> freezeDrvIdList = driverQueryService.queryPenaltyFreezeDrvIdList(checkDrvPenaltyStatusSOARequestType.getDrvIdList()).getData();
        if (checkDrvPenaltyStatusSOARequestType.getDrvIdList().size() == 1 && CollectionUtils.isNotEmpty(freezeDrvIdList)) {
            soaResponseType.setData(false);
            soaResponseType.setHasDrvName(false);
            soaResponseType.setPenaltyMessage(SharkUtils.getSharkValue(SharkKeyConstant.capacitygroupmgtJudgmentFreezeHint));
            return ServiceResponseUtils.success(soaResponseType);
        }
        List<Long> offlineDrvIdList = driverQueryService.queryPenaltyOfflineDrvIdList(checkDrvPenaltyStatusSOARequestType.getDrvIdList()).getData();
        if (CollectionUtils.isNotEmpty(freezeDrvIdList) && CollectionUtils.isEmpty(offlineDrvIdList)) {
            soaResponseType.setData(false);
            soaResponseType.setHasDrvName(true);
            soaResponseType.setDrvNames(Joiner.on("、").join(driverQueryService.queryDrvNameByIdList(freezeDrvIdList).getData()));
            soaResponseType.setPenaltyMessage(SharkUtils.getSharkValue(SharkKeyConstant.capacitygroupmgtJudgmentGreezebatchHint));
        } else if (CollectionUtils.isEmpty(freezeDrvIdList) && CollectionUtils.isNotEmpty(offlineDrvIdList)) {
            soaResponseType.setData(false);
            soaResponseType.setHasDrvName(true);
            soaResponseType.setDrvNames(Joiner.on("、").join(driverQueryService.queryDrvNameByIdList(offlineDrvIdList).getData()));
            soaResponseType.setPenaltyMessage(SharkUtils.getSharkValue(SharkKeyConstant.capacitygroupmgtJudgmentofflineHint));
        } else if (CollectionUtils.isNotEmpty(freezeDrvIdList) && CollectionUtils.isNotEmpty(offlineDrvIdList)) {
            soaResponseType.setData(false);
            soaResponseType.setHasDrvName(true);
            freezeDrvIdList.addAll(offlineDrvIdList);
            soaResponseType.setDrvNames(Joiner.on("、").join(driverQueryService.queryDrvNameByIdList(freezeDrvIdList).getData()));
            soaResponseType.setPenaltyMessage(SharkUtils.getSharkValue(SharkKeyConstant.capacitygroupmgtJudgmentfreezeofflineHint));
        } else {
            soaResponseType.setData(true);
            soaResponseType.setHasDrvName(false);
        }
        return ServiceResponseUtils.success(soaResponseType);
    }

    @Override
    public void validate(AbstractValidator<CheckDrvPenaltyStatusSOARequestType> validator) {
        validator.ruleFor("drvIdList").notNull().notEmpty();
    }
}
