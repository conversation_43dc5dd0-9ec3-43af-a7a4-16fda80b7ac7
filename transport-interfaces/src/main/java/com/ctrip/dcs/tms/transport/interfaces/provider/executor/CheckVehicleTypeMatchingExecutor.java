package com.ctrip.dcs.tms.transport.interfaces.provider.executor;


import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;


/**
 * <AUTHOR>
 * @Description  检查司机或车辆变更车型是否相同
 * @Date 20:40 2020/12/8
 * @Param 
 * @return 
 **/
@Component
public class CheckVehicleTypeMatchingExecutor extends AbstractRpcExecutor<CheckVehicleTypeMatchingSOARequestType, CheckVehicleTypeMatchingSOAResponseType> implements Validator<CheckVehicleTypeMatchingSOARequestType> {

    @Autowired
    private DriverQueryService queryService;

    @Override
    public CheckVehicleTypeMatchingSOAResponseType execute(CheckVehicleTypeMatchingSOARequestType requestType){
        CheckVehicleTypeMatchingSOAResponseType responseType = new CheckVehicleTypeMatchingSOAResponseType();
        Result<Boolean> result = queryService.checkVehicleTypeMatching(requestType);
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }else {
            return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<CheckVehicleTypeMatchingSOARequestType> validator,CheckVehicleTypeMatchingSOARequestType requestType) {
        validator.ruleFor("operationType").notNull();
        if(requestType.getOperationType() == 1){
            validator.ruleFor("drvId").notNull();
        }else if (requestType.getOperationType() == 2)
            validator.ruleFor("vehicleId").notNull();
            validator.ruleFor("vehicleTypeId").notNull();
    }
}
