package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.DriverLeaveInfoSOADTO;
import com.ctrip.dcs.tms.transport.api.model.QueryDriverLeaveByIdRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDriverLeaveByIdResponseType;
import com.ctrip.dcs.tms.transport.application.dto.DriverLeaveDTO;
import com.ctrip.dcs.tms.transport.application.query.DriverLeaveQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 查询司机请假信息-运力库存服务使用
 */
@Component
public class QueryDriverLeaveByIdExecutor extends AbstractRpcExecutor<QueryDriverLeaveByIdRequestType, QueryDriverLeaveByIdResponseType> implements Validator<QueryDriverLeaveByIdRequestType> {
    private static final Logger logger = LoggerFactory.getLogger(QueryDriverLeaveByIdExecutor.class);
    @Autowired
    private DriverLeaveQueryService driverLeaveQueryService;
    @Override
    public QueryDriverLeaveByIdResponseType execute(QueryDriverLeaveByIdRequestType requestType) {
        try{
            DriverLeaveDTO driverLeaveDTO = driverLeaveQueryService.queryDriverLeaveById(requestType.getDriverId(),requestType.getDriverLeaveId());
            return ServiceResponseUtils.success(getResponseType(driverLeaveDTO));
        }catch (BizException be){
            return ServiceResponseUtils.success(new QueryDriverLeaveByIdResponseType());
        }catch (Exception e){
            logger.error("QueryDriverLeaveByIdExecutor_ex",e);
            return ServiceResponseUtils.success(new QueryDriverLeaveByIdResponseType());
        }
    }
    private QueryDriverLeaveByIdResponseType getResponseType(DriverLeaveDTO driverLeaveDTO){
        if(driverLeaveDTO == null){
            return new QueryDriverLeaveByIdResponseType();
        }
        DriverLeaveInfoSOADTO driverLeaveInfoSOADTO = new DriverLeaveInfoSOADTO();
        driverLeaveInfoSOADTO.setDriverId(driverLeaveDTO.getDriverId());
        driverLeaveInfoSOADTO.setDriverLeaveId(driverLeaveDTO.getId());
        driverLeaveInfoSOADTO.setStartTime(DateUtil.dateToString(driverLeaveDTO.getStartTime(),DateUtil.YYYYMMDDHHMMSS));
        driverLeaveInfoSOADTO.setEndTime(DateUtil.dateToString(driverLeaveDTO.getEndTime(),DateUtil.YYYYMMDDHHMMSS));
        driverLeaveInfoSOADTO.setActive(driverLeaveDTO.getActive());
        driverLeaveInfoSOADTO.setLeaveStatus(driverLeaveDTO.getLeaveStatus());
        QueryDriverLeaveByIdResponseType responseType = new QueryDriverLeaveByIdResponseType();
        responseType.setData(driverLeaveInfoSOADTO);
        return responseType;
    }
}
