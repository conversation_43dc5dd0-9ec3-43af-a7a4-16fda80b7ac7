package com.ctrip.dcs.tms.transport.interfaces.schedule;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.driver.message.dto.PushMessageDTO;
import com.ctrip.dcs.order.vbk.supplier.order.service.interfaces.QueryCarInServiceCountRequest;
import com.ctrip.dcs.order.vbk.supplier.order.service.interfaces.QueryCarInServiceCountResponse;
import com.ctrip.dcs.scm.merchant.interfaces.dto.BasicSupplierDTO;
import com.ctrip.dcs.scm.merchant.interfaces.message.QueryBasicSupplierRequestType;
import com.ctrip.dcs.scm.merchant.interfaces.message.QueryBasicSupplierResponseType;
import com.ctrip.dcs.tms.transport.api.model.VehicleUpdateStatusSOARequestType;
import com.ctrip.dcs.tms.transport.application.command.CommonCommandService;
import com.ctrip.dcs.tms.transport.application.command.VehicleCommandService;
import com.ctrip.dcs.tms.transport.application.dto.VehicleAgeInfoDTO;
import com.ctrip.dcs.tms.transport.application.dto.VehicleAgeVehicleDTO;
import com.ctrip.dcs.tms.transport.application.dto.VehicleofflineDTO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DcsScmMerchantServiceClientProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DcsVbkSupplierOrderServiceClientProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.messaging.qmq.TmsQmqProducer;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.OfflineVehicleInformationPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehVehiclePO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.VehicleAgeTypeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OverageDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CommonConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OverageQConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.SharkUtils;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.OfflineVehicleInformationRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRepository;
import com.ctrip.igt.PaginatorDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Maps;

import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

@Component
public class OverageVehicleManagementSchedule {
    private static final Logger logger = LoggerFactory.getLogger(OverageVehicleManagementSchedule.class);

    @Autowired
    private DcsScmMerchantServiceClientProxy dcsScmMerchantServiceClientProxy;
    @Autowired
    private DcsVbkSupplierOrderServiceClientProxy dcsVbkSupplierOrderServiceClientProxy;
    @Autowired
    private OverageQConfig overageQConfig;
    @Autowired
    private CommonConfig config;
    @Autowired
    private VehicleRepository vehicleRepository;
    @Autowired
    private VehicleCommandService vehicleCommandService;
    @Autowired
    private EnumRepository enumRepository;
    @Autowired
    private CommonCommandService commonCommandService;
    @Autowired
    private DrvDrvierRepository drvDrvierRepository;
    @Autowired
    private TmsQmqProducer tmsQmqProducer;
    @Autowired
    private OfflineVehicleInformationRepository offlineVehicleInformationRepository;

    @QSchedule("ctrip.drv.overage.vehicle.management.job")
    public void overageVehicleManagementSchedule(Parameter parameter) throws Exception {
        String vehicleIdsStr = parameter.getString("vehicleIds");
        String supplierIdStr = parameter.getString("supplierId");
        String activeSwitch = parameter.getString("activeSwitch");
        String reset = parameter.getString("reset");
        String resetDate = parameter.getString("resetDate");
        String init = parameter.getString("init");
        boolean activeSwitchBoolean = BooleanUtils.toBoolean(activeSwitch);
        boolean initBoolean = BooleanUtils.toBoolean(init);
        if (StringUtils.isNotBlank(vehicleIdsStr)) {
            String[] vehicleIdList = vehicleIdsStr.split(",");
            List<Long> vehicleIds = Arrays.stream(vehicleIdList).map(Long::valueOf).distinct().collect(Collectors.toList());
            List<VehVehiclePO> vehVehiclePOS = vehicleRepository.queryVehVehicleByIds(vehicleIds);
            Map<Long, List<VehVehiclePO>> supplierIdMap = vehVehiclePOS.stream().collect(Collectors.groupingBy(VehVehiclePO::getSupplierId));
            if (MapUtils.isNotEmpty(supplierIdMap)) {
                supplierIdMap.forEach((supplierId, value) -> {
                    processVehicleList(supplierId, value, activeSwitchBoolean);
                });
            }
        } else if (StringUtils.isNotBlank(supplierIdStr)) {
            String[] split = supplierIdStr.split(",");
            List<Long> supplierIdList = Arrays.stream(split).map(Long::valueOf).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(supplierIdList)) {
                supplierIdList.forEach(supplierId -> {
                    doProcess(supplierId, null, activeSwitchBoolean);
                });
            }
        } else if (BooleanUtils.isTrue(initBoolean)) {
            initProcessOverageType();
        } else if (StringUtils.isNotBlank(reset) && StringUtils.isNotBlank(resetDate)) {
            boolean resetBoolean = BooleanUtils.toBoolean(reset);
            LocalDate localDate = DateUtil.convertStringToDate(resetDate, DateUtil.YYYYMMDD);
            if (BooleanUtils.isTrue(resetBoolean)) {
                resetData(localDate);
            }
        } else {
            this.overageVehicleManagement(activeSwitchBoolean);
        }
    }

    protected void processVehicleList(Long supplierId, List<VehVehiclePO> value, boolean activeSwitchBoolean) {
        List<VehicleAgeVehicleDTO> vehicleAgeVehicleDTOS = new ArrayList<>();
        List<VehicleAgeVehicleDTO> warningVehicleAgeVehicleDTOS = new ArrayList<>();
        VehicleofflineDTO process = process(supplierId, value, activeSwitchBoolean);
        processResultList(process, vehicleAgeVehicleDTOS, warningVehicleAgeVehicleDTOS);
        sendEmail(vehicleAgeVehicleDTOS, supplierId, warningVehicleAgeVehicleDTOS,activeSwitchBoolean);
    }

    protected void resetData(LocalDate localDate) {
        long id = 0L;
        List<OfflineVehicleInformationPO> offlineVehicleInformationPOS;
        do {
            offlineVehicleInformationPOS = offlineVehicleInformationRepository.queryofflineVehicleInformation(id, localDate);
            if (CollectionUtils.isEmpty(offlineVehicleInformationPOS)) {
                break;
            }
            resetOfflineData(offlineVehicleInformationPOS);
            id = offlineVehicleInformationPOS.get(offlineVehicleInformationPOS.size() - 1).getId();
        } while (CollectionUtils.isNotEmpty(offlineVehicleInformationPOS));
    }

    protected void resetOfflineData(List<OfflineVehicleInformationPO> offlineVehicleInformationPOS) {
        VehicleUpdateStatusSOARequestType vehicleUpdateStatusSOARequestType = new VehicleUpdateStatusSOARequestType();
        vehicleUpdateStatusSOARequestType.setVehicleIdList(offlineVehicleInformationPOS.stream().map(OfflineVehicleInformationPO::getVehicleId).collect(Collectors.toList()));
        vehicleUpdateStatusSOARequestType.setVehicleStatus(TmsTransportConstant.VehStatusEnum.ONLINE.getCode());
        vehicleUpdateStatusSOARequestType.setModifyUser("system");
        vehicleCommandService.updateVehicleStatus(vehicleUpdateStatusSOARequestType);
    }

    protected void initProcessOverageType() {
        long id = 0L;
        List<VehVehiclePO> vehVehiclePOS;
        do {
            vehVehiclePOS = vehicleRepository.queryVehicleById(id);
            if (CollectionUtils.isEmpty(vehVehiclePOS)) {
                break;
            }
            List<Long> vehicleIdList = vehVehiclePOS.stream().map(VehVehiclePO::getVehicleId).collect(Collectors.toList());
            vehicleRepository.initVehicleAgeType(vehicleIdList);
            id = vehVehiclePOS.get(vehVehiclePOS.size() - 1).getVehicleId();
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        } while (CollectionUtils.isNotEmpty(vehVehiclePOS));
    }

    protected void overageVehicleManagement(boolean activeSwitchBoolean) {

        QueryBasicSupplierRequestType queryBasicSupplierRequestType = new QueryBasicSupplierRequestType();
        PaginatorDTO paginatorDTO = new PaginatorDTO();
        paginatorDTO.setPageSize(4000);
        paginatorDTO.setPageNo(1);
        queryBasicSupplierRequestType.setPagingSetting(paginatorDTO);
        QueryBasicSupplierResponseType queryBasicSupplierResponseType = dcsScmMerchantServiceClientProxy.queryBasicSupplier(queryBasicSupplierRequestType);
        if (queryBasicSupplierResponseType == null || CollectionUtils.isEmpty(queryBasicSupplierResponseType.getSuppliers())) {
            logger.warn("OverageVehicleManagementSchedule", "nothing");
            return;
        }
        Set<Long> supplierIds = queryBasicSupplierResponseType.getSuppliers().stream().map(BasicSupplierDTO::getId).collect(Collectors.toSet());
        List<Long> cityIdList = config.getCityIdList();
        supplierIds.forEach(supplierId -> {
            if (BooleanUtils.isTrue(config.getOverageGraySwitch())) {
                if (CollectionUtils.isNotEmpty(cityIdList)) {
                    doProcess(supplierId, cityIdList, activeSwitchBoolean);
                }
            } else {
                doProcess(supplierId, null, activeSwitchBoolean);
            }
        });
    }

    protected void doProcess(Long supplierId, List<Long> cityIdList, boolean activeSwitchBoolean) {
        long id = 0L;
        List<VehVehiclePO> vehVehiclePOS;
        List<VehicleAgeVehicleDTO> vehicleAgeVehicleDTOS = new ArrayList<>();
        List<VehicleAgeVehicleDTO> warningVehicleAgeVehicleDTOS = new ArrayList<>();

        do {
            vehVehiclePOS = vehicleRepository.queryVehicleByPage(supplierId, cityIdList, id);
            if (CollectionUtils.isEmpty(vehVehiclePOS)) {
                break;
            }
            VehicleofflineDTO process = process(supplierId, vehVehiclePOS, activeSwitchBoolean);
            processResultList(process, vehicleAgeVehicleDTOS, warningVehicleAgeVehicleDTOS);
            logger.info("order_count", "count {}", Integer.valueOf(vehVehiclePOS.size()));
            id = vehVehiclePOS.get(vehVehiclePOS.size() - 1).getVehicleId();
            logger.info("order_id", "id {}", id);
        } while (CollectionUtils.isNotEmpty(vehVehiclePOS));

        sendEmail(vehicleAgeVehicleDTOS, supplierId, warningVehicleAgeVehicleDTOS,activeSwitchBoolean);
    }

    protected void sendEmail(List<VehicleAgeVehicleDTO> vehicleAgeVehicleDTOS, Long supplierId, List<VehicleAgeVehicleDTO> warningVehicleAgeVehicleDTOS, boolean activeSwitchBoolean) {
        if (BooleanUtils.isNotTrue(activeSwitchBoolean)) {
            logger.info("sendEmail_supplierId", "supplierId :{}", supplierId);
            logger.info("sendEmail_vehicleAgeVehicleDTOS", "vehicleAgeVehicleDTOS : {}", JsonUtil.toJson(vehicleAgeVehicleDTOS));
            logger.info("sendEmail_warningVehicleAgeVehicleDTOS", "warningVehicleAgeVehicleDTOS : {}", JsonUtil.toJson(warningVehicleAgeVehicleDTOS));
            return;
        }
        String supplierEmail = enumRepository.getSupplierEmail(supplierId);
        if (StringUtils.isBlank(supplierEmail)) {
            return;
        }
        logger.info("getSupplierEmail", "{} {}", supplierId, supplierEmail);
        logger.info("vehicleAgeVehicleDTOS", "vehicleAgeVehicleDTOS_size:{}", vehicleAgeVehicleDTOS.size());
        logger.info("warningVehicleAgeVehicleDTOS", "warningVehicleAgeVehicleDTOS_size:{}", warningVehicleAgeVehicleDTOS.size());
        if (CollectionUtils.isNotEmpty(vehicleAgeVehicleDTOS)) {
            String content = buildContent(vehicleAgeVehicleDTOS, SharkKeyConstant.VEHICLE_OVERAGE_OFFLINE_CONTENT);
            commonCommandService.sendEmail(SharkUtils.getSharkValue(SharkKeyConstant.VEHICLE_OFFLINE_MESSAGE_PUSH), supplierEmail, content);
        }
        if (CollectionUtils.isNotEmpty(warningVehicleAgeVehicleDTOS)) {
            String content1 = buildContent(warningVehicleAgeVehicleDTOS, SharkKeyConstant.AGE_LIMIT_WARNING_PUSH);
            commonCommandService.sendEmail(SharkUtils.getSharkValue(SharkKeyConstant.VEHICLE_WARNING_INFORMATION_PUSH), supplierEmail, content1);
        }
    }

    protected static void processResultList(VehicleofflineDTO process, List<VehicleAgeVehicleDTO> vehicleAgeVehicleDTOS, List<VehicleAgeVehicleDTO> warningVehicleAgeVehicleDTOS) {
        List<VehicleAgeVehicleDTO> vehicleAgeVehicleDTOS1 = process.getVehicleAgeVehicleDTOS();
        if (CollectionUtils.isNotEmpty(vehicleAgeVehicleDTOS1)) {
            vehicleAgeVehicleDTOS.addAll(vehicleAgeVehicleDTOS1);
        }
        List<VehicleAgeVehicleDTO> warningVehicleAgeVehicleDTOS1 = process.getWarningVehicleAgeVehicleDTOS();
        if (CollectionUtils.isNotEmpty(warningVehicleAgeVehicleDTOS1)) {
            warningVehicleAgeVehicleDTOS.addAll(warningVehicleAgeVehicleDTOS1);
        }
    }

    protected VehicleofflineDTO process(Long supplierId, List<VehVehiclePO> vehVehiclePOS, boolean activeSwitchBoolean) {
        VehicleofflineDTO vehicleofflineDTO = new VehicleofflineDTO();
        if (CollectionUtils.isEmpty(vehVehiclePOS)) {
            return vehicleofflineDTO;
        }
        String supplierName = enumRepository.getSupplierName(supplierId);
        List<VehicleAgeVehicleDTO> vehicleAgeVehicleDTOS = new ArrayList<>();
        List<OfflineVehicleInformationPO> offlineVehicleInformationPOS = new ArrayList<>();
        List<VehicleAgeVehicleDTO> warningVehicleAgeVehicleDTOS = new ArrayList<>();
        vehVehiclePOS.forEach(vehicle -> {
            String vehicleAgeType = vehicle.getVehicleAgeType();
            boolean notChinaMainLand = enumRepository.isNotChinaMainLand(vehicle.getCityId());
            if (notChinaMainLand ) {
                if (!StringUtils.equals(vehicleAgeType, StringUtils.EMPTY) &&BooleanUtils.isTrue(activeSwitchBoolean)) {
                    vehicleCommandService.updateVehicleAgeType(vehicle, StringUtils.EMPTY, "system");
                }
                return;
            }

            // 获取注册时间
            LocalDateTime registerDate = getRegisterDate(vehicle);
            // 获取超龄配置信息
            Double overage = getOverage(vehicle);
            // 判断超龄以及超龄信息
            VehicleAgeInfoDTO vehicleAgeInfoDTO = checkLimit(registerDate, overage);

            VehicleAgeTypeEnum newVehicleAgeTypeEnum = vehicleAgeInfoDTO.getVehicleAgeTypeEnum();
            Double vehicleAge = vehicleAgeInfoDTO.getVehicleAge();
            LocalDateTime overAgeTime = vehicleAgeInfoDTO.getOverAgeTime();
            Boolean offlineVehicle = vehicleAgeInfoDTO.getOfflineVehicle();
            String oldVehicleAgeType = vehicle.getVehicleAgeType();
            // 当前的车龄信息与数据库中的车龄信息不一致
            // 两者不一样，进行更新，同时判断是否需要发送预警信息
            switch (newVehicleAgeTypeEnum) {
                case NOT_OVERAGE:
                    notOverageProcess(vehicle, newVehicleAgeTypeEnum, oldVehicleAgeType, activeSwitchBoolean);
                    break;
                case OVERAGE:
                    overAgeProcess(supplierId, vehicle, newVehicleAgeTypeEnum, oldVehicleAgeType, offlineVehicleInformationPOS, supplierName, vehicleAge, overAgeTime, vehicleAgeVehicleDTOS, activeSwitchBoolean, offlineVehicle);
                    break;
                case ONE_WEEK:
                case THREE_MONTH:
                case SIX_MONTH:
                    warnIngOverAgeProcess(vehicle, newVehicleAgeTypeEnum, oldVehicleAgeType, warningVehicleAgeVehicleDTOS, vehicleAge, overAgeTime,activeSwitchBoolean);
                    break;
                default:
                    break;
            }
        });

        if (BooleanUtils.isTrue(activeSwitchBoolean)) {
            offlineVehicle(offlineVehicleInformationPOS);
            //存入日志表
            offlineVehicleInformationRepository.saveOfflineVehicleInformation(offlineVehicleInformationPOS);
        }
        vehicleofflineDTO.setVehicleAgeVehicleDTOS(vehicleAgeVehicleDTOS);
        vehicleofflineDTO.setWarningVehicleAgeVehicleDTOS(warningVehicleAgeVehicleDTOS);
        return vehicleofflineDTO;
    }

    protected void offlineVehicle(List<OfflineVehicleInformationPO> offlineVehicleInformationPOS) {
        if (CollectionUtils.isNotEmpty(offlineVehicleInformationPOS)) {
            List<Long> vehicleIdList = offlineVehicleInformationPOS.stream().map(OfflineVehicleInformationPO::getVehicleId).collect(Collectors.toList());
            VehicleUpdateStatusSOARequestType requestType = new VehicleUpdateStatusSOARequestType();
            requestType.setVehicleIdList(vehicleIdList);
            requestType.setVehicleStatus(TmsTransportConstant.VehStatusEnum.OFFLINE.getCode());
            requestType.setModifyUser("system");
            vehicleCommandService.updateVehicleStatus(requestType);
        }
    }

    protected void warnIngOverAgeProcess(VehVehiclePO vehicle, VehicleAgeTypeEnum newVehicleAgeTypeEnum, String oldVehicleAgeType, List<VehicleAgeVehicleDTO> warningVehicleAgeVehicleDTOS, Double vehicleAge, LocalDateTime overAgeTime, boolean activeSwitchBoolean) {
        if (!StringUtils.equals(newVehicleAgeTypeEnum.getCode(), oldVehicleAgeType)) {
            // 如果原先是上线状态，只是更新车龄类型信息
            // 更新车龄的车龄类型信息
            if (BooleanUtils.isTrue(activeSwitchBoolean)) {
                vehicleCommandService.updateVehicleAgeType(vehicle, newVehicleAgeTypeEnum.getCode(), "system");
            }
            if (Objects.equals(vehicle.getVehicleStatus(), TmsTransportConstant.VehStatusEnum.ONLINE.getCode())) {
                // 发送预警信息给供应商
                // 更新超龄信息
                warningVehicleAgeVehicleDTOS.add(getVehicleAgeVehicleDTO(vehicle, vehicleAge));
                // 发送预警信息给司机
                if (vehicle.getHasDrv() && BooleanUtils.isTrue(activeSwitchBoolean)) {
                    Map<String, String> attrs = new HashMap<>();
                    attrs.put("vehicleId", vehicle.getVehicleId().toString());
                    logger.info("send_warning_message", "4075 {}", attrs, overAgeTime);
                    tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.DCS_DRIVER_PUSH_MESSAGE, null, getdriverMessageInfo(vehicle, "4075", overAgeTime));
                }
            }
        }
    }

    protected void overAgeProcess(Long supplierId, VehVehiclePO vehicle, VehicleAgeTypeEnum newVehicleAgeTypeEnum, String oldVehicleAgeType, List<OfflineVehicleInformationPO> offlineVehicleInformationPOS, String supplierName, Double vehicleAge, LocalDateTime overAgeTime, List<VehicleAgeVehicleDTO> vehicleAgeVehicleDTOS, boolean activeSwitchBoolean, Boolean offlineVehicle) {
        // 超龄字段不一致
        if (!StringUtils.equals(newVehicleAgeTypeEnum.getCode(), oldVehicleAgeType)) {
            if (Objects.equals(vehicle.getVehicleStatus(), TmsTransportConstant.VehStatusEnum.OFFLINE.getCode())) {
                // 如果本身在未超龄时已经下线，则不进行下线且不发送消息
            }else if (queryServicingOrderNum(vehicle) == 0 && BooleanUtils.isNotFalse(offlineVehicle)) {
                offlineVehicle(supplierId, vehicle, offlineVehicleInformationPOS, supplierName, vehicleAge, overAgeTime, vehicleAgeVehicleDTOS, activeSwitchBoolean);
            }
            // 更新超龄信息
            if (BooleanUtils.isTrue(activeSwitchBoolean)) {
                vehicleCommandService.updateVehicleAgeType(vehicle, newVehicleAgeTypeEnum.getCode(), "system");
            }
        } else if (Objects.equals(vehicle.getVehicleStatus(), TmsTransportConstant.VehStatusEnum.ONLINE.getCode()) && BooleanUtils.isTrue(activeSwitchBoolean)) {
            // 数据库中超龄，但是车辆状态为上线，则进行下线，，则说明之前该超龄车辆还存在待服务订单
            if (queryServicingOrderNum(vehicle) == 0 && BooleanUtils.isNotFalse(offlineVehicle)) {
                offlineVehicle(supplierId, vehicle, offlineVehicleInformationPOS, supplierName, vehicleAge, overAgeTime, vehicleAgeVehicleDTOS, activeSwitchBoolean);
            }
        }
    }

    protected void offlineVehicle(Long supplierId, VehVehiclePO vehicle, List<OfflineVehicleInformationPO> offlineVehicleInformationPOS, String supplierName, Double vehicleAge, LocalDateTime overAgeTime, List<VehicleAgeVehicleDTO> vehicleAgeVehicleDTOS, boolean activeSwitchBoolean) {
        // 超龄时判断是否有代服务的订单
        // 操作车辆下线,下线车辆记录
        offlineVehicleInformationPOS.add(getOfflineVehicleInformationPO(supplierId, vehicle, supplierName, vehicleAge, overAgeTime));
        // 发送供应商通知
        vehicleAgeVehicleDTOS.add(getVehicleAgeVehicleDTO(vehicle, vehicleAge));
        if (vehicle.getHasDrv() && BooleanUtils.isTrue(activeSwitchBoolean)) {
            // 存在司机的情况下需要发送司机消息
            Map<String, String> attrs = new HashMap<>();
            attrs.put("vehicleId", vehicle.getVehicleId().toString());
            logger.info("send_overage_message", "4076 {}", attrs, overAgeTime);
            tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.DCS_DRIVER_PUSH_MESSAGE, null, getdriverMessageInfo(vehicle, "4076", overAgeTime));
        }
    }

    protected void notOverageProcess(VehVehiclePO vehicle, VehicleAgeTypeEnum newVehicleAgeTypeEnum, String oldVehicleAgeType, boolean activeSwitchBoolean) {
        if (!StringUtils.equals(newVehicleAgeTypeEnum.getCode(), oldVehicleAgeType) && BooleanUtils.isTrue(activeSwitchBoolean)) {
            vehicleCommandService.updateVehicleAgeType(vehicle, newVehicleAgeTypeEnum.getCode(), "system");
        }
    }

    protected Map<String, Object> getdriverMessageInfo(VehVehiclePO vehicle, String templateId, LocalDateTime overAgeTime) {
        List<DrvDriverPO> drvDriverPOS = drvDrvierRepository.queryDrvByVehicleIds(Arrays.asList(vehicle.getVehicleId()));
        PushMessageDTO pushMessageDTO = new PushMessageDTO();
        pushMessageDTO.setDriverIds(drvDriverPOS.stream().map(DrvDriverPO::getDrvId).collect(Collectors.toList()));
        pushMessageDTO.setTemplateId(templateId);

        Map<String, String> sharkValues = Maps.newHashMap();
        sharkValues.put("license", vehicle.getVehicleLicense());
        sharkValues.put("date", DateUtil.getTimeStr(overAgeTime.toLocalDate()));
        pushMessageDTO.setSharkValues(sharkValues);

        Map<String, Object> contentParams = Maps.newHashMap();
        contentParams.put("data", JsonUtil.toJson(pushMessageDTO));
        return contentParams;
    }

    protected Integer queryServicingOrderNum(VehVehiclePO vehicle) {
        QueryCarInServiceCountRequest request = new QueryCarInServiceCountRequest();
        request.setCarId(vehicle.getVehicleId());
        QueryCarInServiceCountResponse response = dcsVbkSupplierOrderServiceClientProxy.queryCarInServiceOrderCount(request);
        return response.getOrderNum();
    }

    protected VehicleAgeVehicleDTO getVehicleAgeVehicleDTO(VehVehiclePO vehicle, Double vehicleAge) {
        VehicleAgeVehicleDTO vehicleAgeVehicleDTO = new VehicleAgeVehicleDTO();
        vehicleAgeVehicleDTO.setVehicleId(vehicle.getVehicleId());
        vehicleAgeVehicleDTO.setVehicleLicense(vehicle.getVehicleLicense());
        vehicleAgeVehicleDTO.setCityName(enumRepository.getCityName(vehicle.getCityId()));
        vehicleAgeVehicleDTO.setVehicleTypeName(enumRepository.getVehicleTypeName(vehicle.getVehicleTypeId()));
        vehicleAgeVehicleDTO.setVehicleAge(String.format("%s年", vehicleAge));
        return vehicleAgeVehicleDTO;
    }

    protected OfflineVehicleInformationPO getOfflineVehicleInformationPO(Long supplierId, VehVehiclePO vehicle, String supplierName, Double vehicleAge, LocalDateTime overAgeTime) {
        OfflineVehicleInformationPO offlineVehicleInformationPO = new OfflineVehicleInformationPO();
        offlineVehicleInformationPO.setSupplierName(supplierName);
        offlineVehicleInformationPO.setSupplierId(supplierId);
        offlineVehicleInformationPO.setVehicleId(vehicle.getVehicleId());
        offlineVehicleInformationPO.setVehicleLicense(vehicle.getVehicleLicense());
        offlineVehicleInformationPO.setCityId(vehicle.getCityId());
        offlineVehicleInformationPO.setCityName(enumRepository.getCityName(vehicle.getCityId()));
        offlineVehicleInformationPO.setVehicleTypeId(vehicle.getVehicleTypeId());
        offlineVehicleInformationPO.setVehicleAge(vehicleAge);
        offlineVehicleInformationPO.setOverAgeTime(Timestamp.valueOf(overAgeTime));
        offlineVehicleInformationPO.setSendDate(Timestamp.valueOf(LocalDateTime.now()));
        return offlineVehicleInformationPO;
    }

    protected Double getOverage(VehVehiclePO vehicle) {
        if (config.getWhiteVehicleList().contains(vehicle.getVehicleId())) {
            return 8D;
        }
        Long cityId = vehicle.getCityId(); // 城市id
        Long vehicleTypeId = vehicle.getVehicleTypeId(); // 车型id
        OverageDTO overageMap = overageQConfig.getOverageMap(cityId, vehicleTypeId); // 配置的超龄信息
        logger.info("getOverage", "{} {}", vehicle.getVehicleId(), JsonUtil.toJson(overageMap));
        return overageMap.getOverage();
    }

    protected static LocalDateTime getRegisterDate(VehVehiclePO vehicle) {
        LocalDateTime regstDate;
        if (Objects.isNull(vehicle.getRegstDate()) || StringUtils.equals(Constant.DEFAULT_YEAR, vehicle.getRegstDate().toString())) {
            regstDate = vehicle.getDatachangeCreatetime().toLocalDateTime();
        } else {
            LocalDate localDate = DateUtil.convertStringToDate(vehicle.getRegstDate().toString(), DateUtil.YYYYMMDD);
            LocalTime localTime = LocalTime.of(23, 59, 59);  // 这里设置为23点59分59秒，可以按需修改时间
            regstDate = LocalDateTime.of(localDate, localTime);
        }
        logger.info("getRegisterDate", "{},{}", vehicle.getVehicleId(), regstDate);
        return regstDate;
    }

    protected String buildContent(List<VehicleAgeVehicleDTO> vehicleAgeVehicleDTOS,String title) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("<h3>").append(SharkUtils.getSharkValue(title)).append("</h3>")
                .append("<table border='1' style='border-collapse:collapse;'>");
        stringBuilder.append("    <tr>\n" +
                "        <th>日期</th>\n" +
                "        <th>车辆id</th>\n" +
                "        <th>车牌</th>\n" +
                "\t\t<th>城市</th>\n" +
                "\t\t<th>车型</th>\n" +
                "\t\t<th>车龄</th>\n" +
                "    </tr>");
        vehicleAgeVehicleDTOS.forEach(vehicleAgeVehicleDTO -> {
            stringBuilder.append("<tr>")
                    .append("<th>").append(DateUtil.getTimeStr(LocalDateTime.now())).append("</th>")
                    .append("<th>").append(vehicleAgeVehicleDTO.getVehicleId()).append("</th>")
                    .append("<th>").append(vehicleAgeVehicleDTO.getVehicleLicense()).append("</th>")
                    .append("<th>").append(vehicleAgeVehicleDTO.getCityName()).append("</th>")
                    .append("<th>").append(vehicleAgeVehicleDTO.getVehicleTypeName()).append("</th>")
                    .append("<th>").append(vehicleAgeVehicleDTO.getVehicleAge()).append("</th>")
                   .append("</tr>");
        });
        stringBuilder.append("</table>");
        return stringBuilder.toString();
    }

    protected static VehicleAgeInfoDTO checkLimit(LocalDateTime registerDateTime, Double overage) {
        VehicleAgeInfoDTO vehicleAgeInfoDTO = new VehicleAgeInfoDTO();
        LocalDateTime currentDate = LocalDateTime.now();
        LocalDateTime overAgeDateTime = currentDate.minusMonths(BigDecimal.valueOf(overage).multiply(BigDecimal.valueOf(12L)).intValue());
        LocalDateTime localDateTime = registerDateTime.plusMonths(BigDecimal.valueOf(overage).multiply(BigDecimal.valueOf(12L)).intValue());
        vehicleAgeInfoDTO.setOverAgeTime(localDateTime);
        long intervalTime = ChronoUnit.DAYS.between(registerDateTime, currentDate) + 1;
        BigDecimal vehicleAge = BigDecimal.valueOf(intervalTime).divide(BigDecimal.valueOf(365L), 1, RoundingMode.HALF_UP);
        if (registerDateTime.isBefore(overAgeDateTime)) {
            vehicleAgeInfoDTO.setVehicleAgeTypeEnum(VehicleAgeTypeEnum.OVERAGE);
            vehicleAgeInfoDTO.setVehicleAge(vehicleAge.doubleValue());
            return vehicleAgeInfoDTO;
        }
        if (Duration.between(overAgeDateTime, registerDateTime).toHours() <= 24L) {
            vehicleAgeInfoDTO.setVehicleAgeTypeEnum(VehicleAgeTypeEnum.OVERAGE);
            vehicleAgeInfoDTO.setVehicleAge(vehicleAge.doubleValue());
            vehicleAgeInfoDTO.setOfflineVehicle(false);
            return vehicleAgeInfoDTO;
        }
        long monthsBetween = ChronoUnit.MONTHS.between(overAgeDateTime, registerDateTime) + 1;
        long weeksBetween = ChronoUnit.WEEKS.between(overAgeDateTime, registerDateTime) + 1;
        if (weeksBetween == 1) {
            vehicleAgeInfoDTO.setVehicleAgeTypeEnum(VehicleAgeTypeEnum.ONE_WEEK);
            vehicleAgeInfoDTO.setVehicleAge(vehicleAge.doubleValue());
            return vehicleAgeInfoDTO;

        } else if (monthsBetween >= 1 && monthsBetween <= 3) {
            vehicleAgeInfoDTO.setVehicleAgeTypeEnum(VehicleAgeTypeEnum.THREE_MONTH);
            vehicleAgeInfoDTO.setVehicleAge(vehicleAge.doubleValue());
            return vehicleAgeInfoDTO;
        } else if (monthsBetween > 3 && monthsBetween <= 6) {
            vehicleAgeInfoDTO.setVehicleAgeTypeEnum(VehicleAgeTypeEnum.SIX_MONTH);
            vehicleAgeInfoDTO.setVehicleAge(vehicleAge.doubleValue());
            return vehicleAgeInfoDTO;
        }
        vehicleAgeInfoDTO.setVehicleAgeTypeEnum(VehicleAgeTypeEnum.NOT_OVERAGE);
        vehicleAgeInfoDTO.setVehicleAge(vehicleAge.doubleValue());

        return vehicleAgeInfoDTO;
    }
}
