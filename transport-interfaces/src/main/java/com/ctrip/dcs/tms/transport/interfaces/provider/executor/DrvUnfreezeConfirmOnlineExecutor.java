package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.DrvUnfreezeConfirmOnlineSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.DrvUnfreezeConfirmOnlineSOAResponseType;
import com.ctrip.dcs.tms.transport.application.command.TmsDrvFreezeCommandService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DrvUnfreezeConfirmOnlineExecutor extends AbstractRpcExecutor<DrvUnfreezeConfirmOnlineSOARequestType, DrvUnfreezeConfirmOnlineSOAResponseType> implements Validator<DrvUnfreezeConfirmOnlineSOARequestType> {

    @Autowired
    TmsDrvFreezeCommandService commandService;

    @Override
    public DrvUnfreezeConfirmOnlineSOAResponseType execute(DrvUnfreezeConfirmOnlineSOARequestType requestType) {
        DrvUnfreezeConfirmOnlineSOAResponseType responseType = new DrvUnfreezeConfirmOnlineSOAResponseType();
        Result<Boolean> result = commandService.drvUnfreezeConfirmOnline(requestType);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<DrvUnfreezeConfirmOnlineSOARequestType> validator) {
        validator.ruleFor("drvId").notNull();
        validator.ruleFor("supplierId").notNull();
    }
}
