package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

@Component
public class QuerySafetyDriverInfoExecutor extends AbstractRpcExecutor<QuerySafetyDriverInfoSOARequestType, QuerySafetyDriverInfoSOAResponseType> implements Validator<QuerySafetyDriverInfoSOARequestType> {

    @Autowired
    private VehicleRepository vehicleRepository;

    @Autowired
    private DrvDrvierRepository drvDrvierRepository;

    @Autowired
    private TmsCertificateCheckRepository tmsCertificateCheckRepository;

    @Autowired
    private TmsTransportQconfig tmsTransportQconfig;

    @Override
    public QuerySafetyDriverInfoSOAResponseType execute(QuerySafetyDriverInfoSOARequestType requestType) {
        QuerySafetyDriverInfoSOAResponseType responseType = new QuerySafetyDriverInfoSOAResponseType();
        assembleDriverInfo(requestType.getDrvId(), responseType);
        return ServiceResponseUtils.success(responseType);
    }

    private void assembleDriverInfo(Long drvId, QuerySafetyDriverInfoSOAResponseType responseType) {
        DriverInfoSOADTO driverInfo = new DriverInfoSOADTO();
        if (drvId == null) {
            return;
        }
        DrvDriverPO driverPO = drvDrvierRepository.queryByPk(drvId);
        if (driverPO == null) {
            return;
        }
        if (checkTag(driverPO.getDrvId(), TmsTransportConstant.CertificateCheckTypeEnum.DRV.getCode(), TmsTransportConstant.CertificateTypeEnum.HEAD_PORTRAIT_COMPLIANCE.getCode())) {
            driverInfo.setPicUrl(driverPO.getDrvHeadImg());
        } else {
            driverInfo.setPicUrl(tmsTransportQconfig.getDefaultDriverPic());
        }
        driverInfo.setDriverName(driverPO.getDrvName());
        driverInfo.setDrivingExperienceAge(DateUtil.getIntervalYears(new Date(), driverPO.getCertiDate()).intValue());
        driverInfo.setCertificateReviewCheck(false);
        if (driverPO.getVehicleId() != null) {
            VehicleDetailDTO vehicleDetail = vehicleRepository.queryVehicleDetail(driverPO.getVehicleId());
            if (vehicleDetail != null) {
                driverInfo.setCarLicense(vehicleDetail.getVehicleLicense());
                driverInfo.setCertificateReviewCheck(checkTag(driverPO.getDrvId(), TmsTransportConstant.CertificateCheckTypeEnum.DRV.getCode(), TmsTransportConstant.CertificateTypeEnum.DRIVERLICENSE.getCode()) &&
                        checkTag(vehicleDetail.getVehicleId(), TmsTransportConstant.CertificateCheckTypeEnum.VEHICLE.getCode(), TmsTransportConstant.CertificateTypeEnum.CARCERTILICENSE.getCode()));
                driverInfo.setVehicleAge(DateUtil.getIntervalYears(new Date(), DateUtil.parseDate(vehicleDetail.getRegstDate())).intValue());
            }
        }
        driverInfo.setIdentityCheck(checkTag(driverPO.getDrvId(), TmsTransportConstant.CertificateCheckTypeEnum.DRV.getCode(), TmsTransportConstant.CertificateTypeEnum.IDCARD.getCode()));
        driverInfo.setSafetyTestCheck(true);
        driverInfo.setFaceRecognitionCheck(true);
        responseType.setDriverSafetyInfo(driverInfo);
    }

    private Boolean checkTag(Long id, int checkType, int certificateType) {
        TmsCertificateCheckPO checkPO = tmsCertificateCheckRepository.queryCertificateCheckByCondition(id, checkType, certificateType);
        if (checkPO == null ||
                checkPO.getCheckStatus() == null ||
                !Objects.equals(checkPO.getCheckStatus().intValue(), TmsTransportConstant.CheckStatusEnum.THROUGH.getCode().intValue())) {
            return false;
        }
        return true;
    }

    @Override
    public void validate(AbstractValidator<QuerySafetyDriverInfoSOARequestType> validator) {
        validator.ruleFor("drvId").notNull();
    }
}