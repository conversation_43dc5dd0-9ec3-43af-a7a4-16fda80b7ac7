package com.ctrip.dcs.tms.transport.interfaces.provider.executor;


import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * 证件信息
 */
@Component
public class QueryCertificateConfigInfoExecutor extends AbstractRpcExecutor<QueryCertificateConfigInfoSOARequestType, QueryCertificateConfigInfoSOAResponseType> implements Validator<QueryCertificateConfigInfoSOARequestType> {

    private static final Logger logger = LoggerFactory.getLogger(QueryCertificateConfigInfoExecutor.class);

    @Autowired
    CertificateQueryService queryService;


    @Override
    public QueryCertificateConfigInfoSOAResponseType execute(QueryCertificateConfigInfoSOARequestType requestType) {
        logger.info("QueryCertificateConfigInfoExecutor  requestType,params:{}", requestType.toString());
        QueryCertificateConfigInfoSOAResponseType responseType = new QueryCertificateConfigInfoSOAResponseType();
        Result<List<QueryCertificateConfigListSOADTO>> result = queryService.queryCertificateConfigInfo(requestType);
        if(result.isSuccess()){
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType,"400",result.getMsg());
    }
}
