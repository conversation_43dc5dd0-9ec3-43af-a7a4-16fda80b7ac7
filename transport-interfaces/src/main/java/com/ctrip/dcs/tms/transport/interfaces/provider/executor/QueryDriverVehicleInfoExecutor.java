package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.DriverVehicleSOADTO;
import com.ctrip.dcs.tms.transport.api.model.QueryDriverVehicleInfoSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDriverVehicleInfoSOAResponseType;
import com.ctrip.dcs.tms.transport.application.dto.DriverVehicleDTO;
import com.ctrip.dcs.tms.transport.application.query.IQueryDriverVehicleInfoService;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 查询司机车辆信息-推送系统使用
 */
@Component
public class QueryDriverVehicleInfoExecutor extends AbstractRpcExecutor<QueryDriverVehicleInfoSOARequestType, QueryDriverVehicleInfoSOAResponseType> implements Validator<QueryDriverVehicleInfoSOARequestType> {
    private static final Logger logger = LoggerFactory.getLogger(QueryDriverVehicleInfoExecutor.class);
    @Autowired
    private IQueryDriverVehicleInfoService queryDriverVehicleInfoService;
    @Override
    public QueryDriverVehicleInfoSOAResponseType execute(QueryDriverVehicleInfoSOARequestType requestType) {
        try{
            if(StringUtils.isEmpty(requestType.getDrvId())){
                return ServiceResponseUtils.fail(new QueryDriverVehicleInfoSOAResponseType());
            }
            DriverVehicleDTO driverVehicleDTO = queryDriverVehicleInfoService.query(Long.valueOf(requestType.getDrvId()));
            return ServiceResponseUtils.success(getResponse(driverVehicleDTO));
        }catch (BizException be){
            return ServiceResponseUtils.fail(new QueryDriverVehicleInfoSOAResponseType());
        }catch (Exception e){
            Map<String,String> tag = new HashMap<>();
            tag.put("driverId",requestType.getDrvId().toString());
            logger.error("QueryDriverVehicleInfoExecutor_ex",requestType.getDrvId().toString(),e,tag);
            return ServiceResponseUtils.fail(new QueryDriverVehicleInfoSOAResponseType());
        }
    }

    /**
     * 封装数据
     * @param driverVehicleDTO
     * @return
     */
    private QueryDriverVehicleInfoSOAResponseType getResponse(DriverVehicleDTO driverVehicleDTO){
        QueryDriverVehicleInfoSOAResponseType responseType = new QueryDriverVehicleInfoSOAResponseType();
        DriverVehicleSOADTO driverVehicleSOADTO = new DriverVehicleSOADTO();
        driverVehicleSOADTO.setVehicleId(driverVehicleDTO.getVehicleId());
        driverVehicleSOADTO.setDrvId(driverVehicleDTO.getDrvId());
        driverVehicleSOADTO.setDrvcardImg(driverVehicleDTO.getDrvcardImg());
        driverVehicleSOADTO.setDrvHeadImg(driverVehicleDTO.getDrvHeadImg());
        driverVehicleSOADTO.setIdcardImg(driverVehicleDTO.getIdcardImg());
        driverVehicleSOADTO.setOtherCertificateImg(driverVehicleDTO.getOtherCertificateImg());
        driverVehicleSOADTO.setNetVehiclePeoImg(driverVehicleDTO.getNetVehiclePeoImg());
        driverVehicleSOADTO.setVehicleFullImg(driverVehicleDTO.getVehicleFullImg());
        driverVehicleSOADTO.setNetTansCertificateImg(driverVehicleDTO.getNetTansCertificateImg());
        driverVehicleSOADTO.setPeopleVehicleImg(driverVehicleDTO.getPeopleVehicleImg());
        driverVehicleSOADTO.setVehicleCertificateImg(driverVehicleDTO.getVehicleCertificateImg());
        responseType.setData(driverVehicleSOADTO);
        return responseType;
    }
}
