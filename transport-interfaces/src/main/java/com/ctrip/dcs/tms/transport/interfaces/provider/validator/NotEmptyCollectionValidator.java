package com.ctrip.dcs.tms.transport.interfaces.provider.validator;

import com.ctriposs.baiji.rpc.server.validation.validators.*;
import org.apache.commons.collections.*;

import java.util.*;

/**
 * Collection不为空
 * <AUTHOR>
 * @Date 2020/3/3 15:25
 */
public class NotEmptyCollectionValidator extends PropertyValidator implements IPropertyValidator {

    public NotEmptyCollectionValidator(String errorMessage, String errorCode) {
        super(errorMessage, errorCode);
    }

    @Override
    protected boolean isValid(PropertyValidatorContext context) {
        List<?> collection = (List<?>) context.getPropertyValue();
        return CollectionUtils.isNotEmpty(collection);
    }
}
