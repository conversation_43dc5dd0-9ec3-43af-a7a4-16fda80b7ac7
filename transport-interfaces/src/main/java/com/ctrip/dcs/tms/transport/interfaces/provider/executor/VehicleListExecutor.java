package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.tms.transport.api.model.QueryVehicleSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryVehicleSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.VehicleListSOADTO;
import com.ctrip.dcs.tms.transport.application.query.VehicleQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ApiTypeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.MetricsUtils;
import com.ctrip.igt.PaginationDTO;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.google.common.collect.Lists;

@Component
public class VehicleListExecutor extends AbstractRpcExecutor<QueryVehicleSOARequestType, QueryVehicleSOAResponseType> implements Validator<QueryVehicleSOARequestType> {

    @Autowired
    private VehicleQueryService vehicleQueryService;

    @Override
    public QueryVehicleSOAResponseType execute(QueryVehicleSOARequestType queryVehicleRequestType) {
        QueryVehicleSOAResponseType responseType = new QueryVehicleSOAResponseType();
        try {
            int count = vehicleQueryService.queryVehicleCount(queryVehicleRequestType).getData();
            PaginationDTO paginationDTO = getPageInfoResponseType(queryVehicleRequestType, count);
            responseType.setPagination(paginationDTO);
            if (count == 0) {
                responseType.setVehicleList(Lists.newArrayList());
                return ServiceResponseUtils.success(responseType);
            }
            List<VehicleListSOADTO> vehicleList = vehicleQueryService.queryVehicleList(queryVehicleRequestType).getData();
            if (CollectionUtils.isEmpty(vehicleList)) {
                return ServiceResponseUtils.success(responseType);
            }
            responseType.setVehicleList(vehicleList);
            return ServiceResponseUtils.success(responseType);
        } catch (Exception e) {
            if(StringUtils.isNotEmpty(queryVehicleRequestType.getVehicleLicense())){
                MetricsUtils.queryVehicleListNoResult(ApiTypeEnum.QUERY_VEHCILE_LIST,queryVehicleRequestType.getVehicleLicense());
            }
            throw new BaijiRuntimeException("VehicleListExecutor queryVehicleList error", e);
        }finally {
            MetricsUtils.queryVehicleListConnectTimes(ApiTypeEnum.QUERY_VEHCILE_LIST,"");
        }
    }

    private PaginationDTO getPageInfoResponseType(QueryVehicleSOARequestType queryDrvRequestType, int count) {
        PaginationDTO paginationDTO = ServiceResponseUtils.newPagination(queryDrvRequestType.getPaginator().getPageNo(),
                queryDrvRequestType.getPaginator().getPageSize(), count);
        return paginationDTO;
    }

}