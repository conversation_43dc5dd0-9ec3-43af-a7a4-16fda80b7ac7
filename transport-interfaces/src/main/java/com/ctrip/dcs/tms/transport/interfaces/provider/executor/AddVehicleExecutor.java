package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.constant.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

@Component
public class AddVehicleExecutor extends AbstractRpcExecutor<VehicleAddSOARequestType, VehicleAddSOAResponseType> implements Validator<VehicleAddSOARequestType> {

    @Autowired
    private VehicleCommandService vehicleCommandService;
    @Autowired
    private VehicleQueryService vehicleQueryService;

    @Override
    public VehicleAddSOAResponseType execute(VehicleAddSOARequestType vehicleAddSOARequestType) {
        if (!vehicleQueryService.isVehicleLicenseUniqueness(null, vehicleAddSOARequestType.getVehicleLicense()).getData()) {
            return ServiceResponseUtils.fail(new VehicleAddSOAResponseType(), ServiceResponseConstants.ResStatus.EXCEPTION_CODE, SharkUtils.getSharkValue(SharkKeyConstant.transportVehicleLicenseUniqueness));
        }
        VehicleAddSOAResponseType vehicleAddSOAResponseType = new VehicleAddSOAResponseType();
        Result<Integer> result = vehicleCommandService.addVehicle(vehicleAddSOARequestType,false);
        if (result.isSuccess()) {
            vehicleAddSOAResponseType.setData(Long.valueOf(result.getData()));
            return ServiceResponseUtils.success(vehicleAddSOAResponseType);
        } else {
            return ServiceResponseUtils.fail(vehicleAddSOAResponseType,result.getCode(),result.getMsg());
        }
    }

}