package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryTransportInformRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryTransportInformResponseType;
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 查询运力组通知信息
 *
 * <AUTHOR> <PERSON><PERSON>hen
 * @create 2023/7/3 15:08
 */
@Component
@ServiceLogTagPair(key = "transportGroupId")
public class QueryTransportInformExecutor extends AbstractRpcExecutor<QueryTransportInformRequestType, QueryTransportInformResponseType> implements Validator<QueryTransportInformRequestType> {

    @Resource
    private TransportGroupQueryService transportGroupQueryService;

    @Override
    public QueryTransportInformResponseType execute(QueryTransportInformRequestType req) {
        Result<QueryTransportInformResponseType> informRes = transportGroupQueryService.queryTransportInform(req.getTransportGroupId());
        if (!informRes.isSuccess()) {
            return ServiceResponseUtils.fail(new QueryTransportInformResponseType(), informRes.getCode(), informRes.getMsg());
        }
        return ServiceResponseUtils.success(informRes.getData());
    }

    @Override
    public void validate(AbstractValidator<QueryTransportInformRequestType> validator) {
        validator.ruleFor("transportGroupId").notNull().greaterThan(0L);
    }

}