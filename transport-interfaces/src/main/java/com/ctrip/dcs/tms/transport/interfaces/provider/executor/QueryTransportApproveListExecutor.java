package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.common.base.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

@Component
public class QueryTransportApproveListExecutor extends AbstractRpcExecutor<QueryTransportApproveListSOARequestType, QueryTransportApproveListSOAResponseType> implements Validator<QueryTransportApproveListSOARequestType> {

    @Autowired
    private TmsTransportApproveQueryService queryService;

    @Override
    public QueryTransportApproveListSOAResponseType execute(QueryTransportApproveListSOARequestType requestType) {
        QueryTransportApproveListSOAResponseType soaResponseType = new QueryTransportApproveListSOAResponseType();
        Result<PageHolder<QueryTransportApproveListSOADTO>> pageHolderResult;
        try {
            pageHolderResult = queryService.queryApproveList(requestType);
        } catch (Exception e) {
            return ServiceResponseUtils.fail(soaResponseType);
        }
        if (pageHolderResult == null || pageHolderResult.getData() == null) {
            return ServiceResponseUtils.fail(soaResponseType);
        }
        PaginationDTO paginationDTO = ServiceResponseUtils.newPagination(pageHolderResult.getData().getPageIndex(),
                pageHolderResult.getData().getPageSize(), pageHolderResult.getData().getTotalSize());
        soaResponseType.setPagination(paginationDTO);
        soaResponseType.setData(pageHolderResult.getData().getData());
        return ServiceResponseUtils.success(soaResponseType);
    }

    @Override
    public void validate(AbstractValidator<QueryTransportApproveListSOARequestType> validator) {
        validator.ruleFor("paginator").notNull();
    }

}