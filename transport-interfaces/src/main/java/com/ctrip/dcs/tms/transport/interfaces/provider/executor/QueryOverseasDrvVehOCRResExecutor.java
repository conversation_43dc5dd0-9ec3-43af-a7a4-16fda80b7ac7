package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryOverseasDrvVehOCRResRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryOverseasDrvVehOCRResResponseType;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.BaseUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.RedisUtils;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Component
public class QueryOverseasDrvVehOCRResExecutor extends AbstractRpcExecutor<QueryOverseasDrvVehOCRResRequestType, QueryOverseasDrvVehOCRResResponseType> implements Validator<QueryOverseasDrvVehOCRResRequestType> {


    @Override
    public QueryOverseasDrvVehOCRResResponseType execute(QueryOverseasDrvVehOCRResRequestType requestType) {
        QueryOverseasDrvVehOCRResResponseType responseType = new QueryOverseasDrvVehOCRResResponseType();
        String key = TmsTransportConstant.OVERSEAS_DRV_OCR_COMPARISON_RES;
        if(requestType.getType() == 2){
            key = TmsTransportConstant.OVERSEAS_VEH_OCR_COMPARISON_RES;
        }
        Set<Long> drvIdSet = requestType.getIds().stream().collect(Collectors.toSet());
        List<String> res = RedisUtils.mGet(BaseUtil.toCacheKeyList(key, drvIdSet));
        responseType.setData(res);
        return ServiceResponseUtils.success(responseType);
    }

    @Override
    public void validate(AbstractValidator<QueryOverseasDrvVehOCRResRequestType> validator) {
    }

}