package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.constant.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

@Component
public class QueryDrvAddrModCountExecutor extends AbstractRpcExecutor<QueryDrvAddrModCountSOARequestType, QueryDrvAddrModCountSOAResponseType> implements Validator<QueryDrvAddrModCountSOARequestType> {

    @Autowired
    private DriverQueryService driverQueryService;

    @Override
    public QueryDrvAddrModCountSOAResponseType execute(QueryDrvAddrModCountSOARequestType requestType) {
        QueryDrvAddrModCountSOAResponseType soaResponseType = new QueryDrvAddrModCountSOAResponseType();
        Result<QueryDrvAddrModCountSOAResponseType> result = driverQueryService.queryDrvAddrModCount(requestType);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(result.getData());
        }
        return ServiceResponseUtils.fail(soaResponseType, ServiceResponseConstants.ResStatus.VALIDATION_ERROR_CODE,result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<QueryDrvAddrModCountSOARequestType> validator) {
        validator.ruleFor("drvId").notNull();
    }

}