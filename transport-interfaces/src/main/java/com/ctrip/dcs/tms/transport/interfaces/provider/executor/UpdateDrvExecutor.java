package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.impl.NetCertNoCheckRuleService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctrip.platform.dal.exceptions.DalException;
import com.google.common.base.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.*;
import org.springframework.beans.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.sql.*;

/**
 * 编辑司机
 */
@Component
public class UpdateDrvExecutor extends AbstractRpcExecutor<DrvUpdateSOARequestType, DrvUpdateSOAResponseType> implements Validator<DrvUpdateSOARequestType> {

    @Autowired
    private DriverCommandService commandService;
    @Autowired
    private NetCertNoCheckRuleService netCertNoCheckRuleService;
    @Autowired
    private ProductionLineUtil productionLineUtil;

    @Override
    public DrvUpdateSOAResponseType execute(DrvUpdateSOARequestType drvUpdateRequestType) {
        try {
            DrvUpdateSOAResponseType responseType = new DrvUpdateSOAResponseType();
            //校验网约车司机驾驶证号
            if(!checkDriverNetCertNo(drvUpdateRequestType)){
                return ServiceResponseUtils.fail(responseType, "500",SharkUtils.getSharkValue(SharkKeyConstant.driverNetCertNoCheckFalse));
            }
            //更新司机数据
            Result<Long> result = commandService.updateDrv(toDrvPOBean(drvUpdateRequestType), drvUpdateRequestType.isOcrheadPortraitResult(),drvUpdateRequestType.getOcrPassStatusList());
            if (result.isSuccess()) {
                responseType.setMessage(result.getMsg());
                responseType.setHasTip(Strings.isNullOrEmpty(result.getMsg()));
                return ServiceResponseUtils.success(responseType);
            }else {
                return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
            }
        }catch (Exception e) {
        if (e instanceof DalException) {
            if (e.getCause() instanceof BizException) {
                throw (BizException) e.getCause();
            }
        }
        throw e;
        }
    }

    /**
     * 组装持久化对象
     * @param drvUpdateRequestType
     * @return
     */
    private DrvDriverPO toDrvPOBean(DrvUpdateSOARequestType drvUpdateRequestType){
        DrvDriverPO po = new DrvDriverPO();
        BeanUtils.copyProperties(drvUpdateRequestType,po);
        po.setDrvPhone(TmsTransUtil.encrypt(drvUpdateRequestType.getDrvPhone(), KeyType.Phone));
        po.setEmail(TmsTransUtil.encrypt(drvUpdateRequestType.getEmail(),KeyType.Mail));
        po.setDrvIdcard(TmsTransUtil.encrypt(drvUpdateRequestType.getDrvIdcard(),KeyType.Identity_Card));
        if(StringUtils.isNoneBlank(drvUpdateRequestType.getCertiDate())){
            po.setCertiDate(Date.valueOf(drvUpdateRequestType.getCertiDate()));
        }
        if(StringUtils.isNotEmpty(drvUpdateRequestType.getExpiryBeginDate())){
            po.setExpiryBeginDate(Date.valueOf(drvUpdateRequestType.getExpiryBeginDate()));
        }
        if(StringUtils.isNotEmpty(drvUpdateRequestType.getExpiryEndDate())){
            po.setExpiryEndDate(Date.valueOf(drvUpdateRequestType.getExpiryEndDate()));
        }
        po.setInternalScope(drvUpdateRequestType.getAreaScope());
        po.setCheckStatusList(drvUpdateRequestType.getCheckStatusList());
        po.setCertificateConfig(drvUpdateRequestType.getCertificateConfigStr());
        po.setRaisingPickUp(drvUpdateRequestType.isRaisingPickUp());
        po.setChildSeat(drvUpdateRequestType.isChildSeat());
        //加密网约车运输证号
        if(!org.springframework.util.StringUtils.isEmpty(drvUpdateRequestType.getDriverNetCertNo())){
            String result = TmsTransUtil.encrypt(drvUpdateRequestType.getDriverNetCertNo(), KeyType.OtherDocument);
            po.setDriverNetCertNo(result);
        }
        if(CollectionUtils.isNotEmpty(drvUpdateRequestType.getProLineList())){
            //新增产线可编辑
            po.setCategorySynthesizeCode(productionLineUtil.getIntegratedLine(drvUpdateRequestType.getProLineList()));
        }
        po.setPaiayEmail(TmsTransUtil.encrypt(drvUpdateRequestType.getPaiayEmail(),KeyType.Mail));
        return po;
    }

    /**
     * 校验证件号
     * @param requestType
     * @return
     */
    private boolean checkDriverNetCertNo(DrvUpdateSOARequestType requestType){
        //非必传参数
        if(org.springframework.util.StringUtils.isEmpty(requestType.getDriverNetCertNo())){
            return true;
        }
        //必传参数
        if(requestType.getCityId() == null){
            return false;
        }
        //必传参数
        if(requestType.getDrvId() == null){
            return false;
        }
        return netCertNoCheckRuleService.checkDriverNetCertNo(requestType.getCityId().toString(),requestType.getDriverNetCertNo(),requestType.getDrvId());
    }
}
