package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/4/14 10:49
 */
@Component
public class QueryDrvBySupplierIdListExecutor extends AbstractRpcExecutor<QueryDrvBySupplierIdSOARequestType, QueryDrvBySupplierIdSOAResponseType> implements Validator<QueryDrvBySupplierIdSOARequestType> {

    @Autowired
    private DriverQueryService driverQueryService;

    @Override
    public QueryDrvBySupplierIdSOAResponseType execute(QueryDrvBySupplierIdSOARequestType soaRequestType) {
        QueryDrvBySupplierIdSOAResponseType responseType = new QueryDrvBySupplierIdSOAResponseType();
        Result<List<QueryDrvBySupplierIdSOADTO>> result = driverQueryService.queryDrvBySupplierIdList(soaRequestType.getSupplierId());
        if(result.isSuccess()){
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType);
    }
}
