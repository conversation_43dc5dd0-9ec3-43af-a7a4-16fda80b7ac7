package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.sql.*;
import java.util.Date;

/**
 * 司机销假
 * <AUTHOR>
 * @Date 2020/2/25 18:29
 */
@Component
public class CloseDrvLeaveExecutor  extends AbstractRpcExecutor<DrvLeaveCloseSOARequestType, DrvLeaveCloseSOAResponseType> implements Validator<DrvLeaveCloseSOARequestType> {

    @Autowired
    private DriverLeaveCommandService driverLeaveCommandService;

    @Override
//    @DalTransactional(logicDbName = "dcstransportdb_w")
    public DrvLeaveCloseSOAResponseType execute(DrvLeaveCloseSOARequestType drvLeaveCloseSOARequestType) {
        DrvLeaveCloseSOAResponseType responseType = new DrvLeaveCloseSOAResponseType();
        DrvDriverLeavePO driverLeavePO = requestTypeToPO(drvLeaveCloseSOARequestType);
        Result<Boolean> result = driverLeaveCommandService.closeDrvLeave(driverLeavePO);
        if(result.isSuccess()){
            return ServiceResponseUtils.success(responseType);
        }else {
            return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<DrvLeaveCloseSOARequestType> validator) {
        validator.ruleFor("modifyUser").notNull().notEmpty();
    }

    private DrvDriverLeavePO requestTypeToPO(DrvLeaveCloseSOARequestType requestType){
        DrvDriverLeavePO driverLeavePO = new DrvDriverLeavePO();
        driverLeavePO.setDrvId(requestType.getDrvId());
        driverLeavePO.setActive(Boolean.FALSE);
        driverLeavePO.setId(requestType.getLeaveId());
        driverLeavePO.setModifyUser(requestType.getModifyUser());
        driverLeavePO.setDatachangeDeltime(new Timestamp(new Date().getTime()));
        return driverLeavePO;
    }
}
