package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.dcs.tms.transport.application.command.CommonCommandService;
import com.ctrip.dcs.tms.transport.application.command.TmsDrvFreezeCommandService;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TmsDrvFreezePO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehVehiclePO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.AreaScopeTypeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.ApprovalProcessAuthQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.EmailTemplateQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsDrvFreezeRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 司机解冻前24小时发送短信
 */
@Component
public class DrvUnfreezeSchedule {

    private static final Logger logger = LoggerFactory.getLogger(DrvUnfreezeSchedule.class);

    @Autowired
    TmsDrvFreezeRepository freezeRepository;

    @Autowired
    CommonCommandService commandService;

    @Autowired
    DrvDrvierRepository drvierRepository;
    @Autowired
    private ApprovalProcessAuthQconfig approvalProcessAuthQconfig;
    @Autowired
    private TmsDrvFreezeCommandService freezeCommandService;
    @Autowired
    private EnumRepository enumRepository;
    @Autowired
    private DriverQueryService driverQueryService;
    @Autowired
    private VehicleRepository vehicleRepository;
    @Autowired
    EmailTemplateQconfig emailTemplateQconfig;
    @Autowired
    TmsTransportQconfig tmsTransportQconfig;

    @QSchedule("ctrip.drv.unfreeze.auto.job")
    public void drvUnfreezeSchedule(Parameter parameter) throws Exception {
        this.drvUnfreezeScheduleMethod();
    }

    public void drvUnfreezeScheduleMethod(){
        try {
            List<TmsDrvFreezePO> drvFreezePOList = freezeRepository.queryDrvFreezeAll();
            if (CollectionUtils.isEmpty(drvFreezePOList)) {
                return;
            }

            Map<Integer,Integer> drvFreezeTimeConfig = tmsTransportQconfig.getDrvFreezeTimeConfigMap();
            Integer config1= drvFreezeTimeConfig.get(TmsTransportConstant.DrvFreezeTimeConfigEnum.drvFreezeRemindDays.getCode());
            Integer config2= drvFreezeTimeConfig.get(TmsTransportConstant.DrvFreezeTimeConfigEnum.unfreezeBeforeHour.getCode());
            //冻结期累计总时长
            Integer drvFreezeRemindDays = config1 == null ? 7 : config1;
            //解冻前的小时配置
            Integer unfreezeBeforeHour = config2 == null ? 24 : config2;

            //redis标记时间，永远比配置的时候多一小时，防止二次发送短信和邮箱
            int redisHour = unfreezeBeforeHour * RedisUtils.ONE_HOUR + RedisUtils.ONE_HOUR;
            logger.info("drvUnfreezeScheduleStart","count:{}", drvFreezePOList.size());
            for (TmsDrvFreezePO freezePO : drvFreezePOList) {
                //冻结状态为冻结时才生效
                if(!Objects.equals(TmsTransportConstant.FreezeStatusEnum.FREEZE.getValue(),freezePO.getFreezeStatus())){
                    continue;
                }
                //首次状态时间不为空,冻结小时数必须>0
                if(freezePO.getFirstFreezeTime() == null || freezePO.getFreezeHour() <= 0){
                    continue;
                }
                //解冻时间超过24小时不操作
                long unfreeze24min  = DateUtil.getRemainingDay(freezePO.getFirstFreezeTime(),freezePO.getFreezeHour());
                long  unfreeze24h = unfreeze24min/60;
                if(unfreeze24h > unfreezeBeforeHour){
                    continue;
                }
                logger.info("unfreeze24h","drv:{},result:{}",freezePO.getDrvId(), unfreeze24h);
                DrvDriverPO drvDriverPO = drvierRepository.queryByPk(freezePO.getDrvId());
                if(drvDriverPO == null || drvDriverPO.getSupplierId() == null){
                    continue;
                }
                List<Long> cityIds = Lists.newArrayList();
                cityIds.add(drvDriverPO.getCityId());
                if(Objects.equals(freezePO.getUnfreezeAction(), TmsTransportConstant.UnfreezeActionEnum.UNFREEZEONLINE.getValue())){
                    if(drvDriverPO.getVehicleId()!=null && drvDriverPO.getVehicleId() > 0){
                        VehVehiclePO vehiclePO = vehicleRepository.queryByPk(drvDriverPO.getVehicleId());
                        if(!Objects.isNull(vehiclePO)){
                            cityIds.add(vehiclePO.getCityId());
                        }
                    }
                }
                //新增禁止城市解冻,如果是解冻后上线,则判断司机所在城市是否存在于禁止解冻城市列表中
                if(Objects.equals(freezePO.getUnfreezeAction(), TmsTransportConstant.UnfreezeActionEnum.UNFREEZEONLINE.getValue()) &&
                        driverQueryService.judgeDisableDrvOnlineCity(cityIds)){
                    continue;
                }
                String supplierEmail = enumRepository.getSupplierEmail(drvDriverPO.getSupplierId());
                Date now = new Date();
                String key = TmsTransportConstant.DRV_UNFREEZE_24SENDMESSAGEKEY+freezePO.getDrvId();
                Boolean drvFlag = RedisUtils.get(key);
                //境内标识
                Boolean domesticFlag = Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(),drvDriverPO.getInternalScope());
                //只限境内发短信
                if (domesticFlag && (drvFlag == null || !drvFlag)) {
                    String smsCode =  "";
                    Map<String,String> params = Maps.newHashMap();
                    params.put("DriverName",drvDriverPO.getDrvName());
                    params.put("ResidueHour",String.valueOf(unfreeze24h));
                    //原逻辑，如果剩余冻结时间是24小时，则给司机发送短信，不区分首次冻结vbk
                    if(unfreeze24h == 24){
                         smsCode = approvalProcessAuthQconfig.getBefore24MessageCode();//默认解冻后自动上线短信code码
                        if(Objects.equals(freezePO.getUnfreezeAction(), TmsTransportConstant.UnfreezeActionEnum.UNFREEZEOFFLINE.getValue())){
                            smsCode = approvalProcessAuthQconfig.getBefore24UnfreezeOfflineMessageCode();
                        }
                    }
                    //新逻辑，判断是否是供应商冻结，并满足大于7天冻结，冻结后上线，解冻前24小时发送短信
                    if(this.verdictBool(freezePO,unfreeze24h,drvFreezeRemindDays,unfreezeBeforeHour) && tmsTransportQconfig.getDrvUnfreezeNewSendEmailSwitch()){
                        smsCode = approvalProcessAuthQconfig.getVbkUnfreezeMessageCode();
                    }
                    if(StringUtils.isNotEmpty(smsCode)){
                        commandService.sendMessageByPhone(drvDriverPO.getIgtCode(),TmsTransUtil.decrypt(drvDriverPO.getDrvPhone(), KeyType.Phone),smsCode,params);
                        RedisUtils.set(key,redisHour,Boolean.TRUE);
                    }
                }
            /*供应商发起的冻结，并且冻结期累计>=7天 （可配）
            当前冻结模式为解冻后自动上线
            解冻时间 - 当前时间 <= 24h （可配）
            确认邮件“未发送”*/
                String emailKey = TmsTransportConstant.DRV_UNFREEZE_24SENDEMAILKEY+freezePO.getDrvId();
                String randomStrKey = TmsTransportConstant.DRV_UNFREEZE_CONFIRM_KEY+freezePO.getDrvId();
                Boolean emailFlag = RedisUtils.get(emailKey);
                //只限境内发邮件
                if (domesticFlag && this.verdictBool(freezePO,unfreeze24h,drvFreezeRemindDays,unfreezeBeforeHour) && (emailFlag == null || !emailFlag) && StringUtils.isNotEmpty(supplierEmail)) {
                    logger.info("24HourUnFreezeDrv","params:{}", JsonUtil.toJson(freezePO));
                    //发送邮件
                    String subject = String.format(emailTemplateQconfig.getDrvUnfreezeEmailOldSubject(),drvDriverPO.getDrvName(),drvDriverPO.getDrvId());
                    String  content = String.format(emailTemplateQconfig.getDrvUnfreezeEmailOldContent(),drvDriverPO.getDrvName(),drvDriverPO.getDrvId());
                    String randomStr = RandomStringUtils.randomAlphabetic(20);
                    //新逻辑，发邮件供应商确认链接，上线后过一段时间打开链接
                    if(tmsTransportQconfig.getDrvUnfreezeNewSendEmailSwitch()){
                        subject = String.format(emailTemplateQconfig.getDrvUnfreezeEmailNewSubject(),drvDriverPO.getDrvName(),drvDriverPO.getDrvId());
                        content = String.format(emailTemplateQconfig.getDrvUnfreezeEmailNewContent(),drvDriverPO.getDrvName(),drvDriverPO.getDrvId(),unfreeze24h)
                                + "&drvId=" + freezePO.getDrvId() + "&randomStr=" + randomStr;
                        RedisUtils.set(randomStrKey,redisHour,randomStr);
                    }
                    commandService.sendEmail(subject,supplierEmail,content);
                    RedisUtils.set(emailKey,redisHour,Boolean.TRUE);
                }

                Date unfreezehDate  = DateUtil.getDayShift(freezePO.getFirstFreezeTime(),freezePO.getFreezeHour());
                if (!now.before(unfreezehDate)) {
                    //供应商未确认解冻后上线的按下线处理
                    if (domesticFlag && this.verdictBool(freezePO,unfreeze24h,drvFreezeRemindDays,unfreezeBeforeHour)  && tmsTransportQconfig.getDrvUnfreezeNewSendEmailSwitch()) {
                        freezePO.setUnfreezeAction(TmsTransportConstant.UnfreezeActionEnum.UNFREEZEOFFLINE.getValue());
                    }
                    logger.info("execUnfreezeDrvMethod","params:{}", JsonUtil.toJson(freezePO));
                    freezeCommandService.execUnfreezeDrv(freezePO,drvDriverPO,freezePO.getFreezeHour(),true);
                }
            }
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    //判断是否是供应商冻结，并满足大于7天冻结，冻结后上线
    public Boolean verdictBool(TmsDrvFreezePO freezePO,long unfreeze24h,Integer drvFreezeRemindDays,Integer unfreezeBeforeHour){
        int freezeDay = DateUtil.getFreezeDay(freezePO.getFreezeHour());
        if(CtripCommonUtils.judgeFreezeFromIsSupplier(freezePO.getTotalFreezeFrom()) && freezeDay >= drvFreezeRemindDays
                && Objects.equals(freezePO.getUnfreezeAction(),TmsTransportConstant.UnfreezeActionEnum.UNFREEZEONLINE.getValue())
                && unfreeze24h <= unfreezeBeforeHour.longValue() && !freezePO.getConfirmOnlineStatus()){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}