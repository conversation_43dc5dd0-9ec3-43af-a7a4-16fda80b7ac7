package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.apache.commons.lang.time.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class CheckDrvPenaltyFreezeExecutor extends AbstractRpcExecutor<CheckDrvPenaltyFreezeSOARequestType, CheckDrvPenaltyFreezeSOAResponseType> implements Validator<CheckDrvPenaltyFreezeSOARequestType> {

    @Autowired
    private TmsDrvFreezeRepository tmsDrvFreezeRepository;
    @Autowired
    private TmsTransportQconfig tmsTransportQconfig;

    @Override
    public CheckDrvPenaltyFreezeSOAResponseType execute(CheckDrvPenaltyFreezeSOARequestType soaRequestType) {
        CheckDrvPenaltyFreezeSOAResponseType soaResponseType = new CheckDrvPenaltyFreezeSOAResponseType();
        TmsDrvFreezePO freezePO = tmsDrvFreezeRepository.queryByPk(soaRequestType.getDrvId());
        Date relievePenaltyDate;
        if (!tmsTransportQconfig.getPenaltySwitch() || freezePO == null || Objects.equals(CommonEnum.FreezeStatusEnum.UNFREEZE.getValue(), freezePO.getFreezeStatus()) || System.currentTimeMillis() >= (relievePenaltyDate = DateUtils.addHours(freezePO.getFirstFreezeTime(), freezePO.getPenalizeFreezeHour())).getTime()) {
            soaResponseType.setData(true);
            return ServiceResponseUtils.success(soaResponseType);
        }
        soaResponseType.setData(false);
        soaResponseType.setPenalizeFreezeTime(DateUtil.dateToString(relievePenaltyDate, DateUtil.YYYYMMDDHHMMSS));
        soaResponseType.setPenaltyMessage(SharkUtils.getSharkValue(SharkKeyConstant.capacitygroupmgtJudgmentreduceHint, (Object) soaResponseType.getPenalizeFreezeTime()));
        return ServiceResponseUtils.success(soaResponseType);
    }

    @Override
    public void validate(AbstractValidator<CheckDrvPenaltyFreezeSOARequestType> validator) {
        validator.ruleFor("drvId").notNull();
    }
}
