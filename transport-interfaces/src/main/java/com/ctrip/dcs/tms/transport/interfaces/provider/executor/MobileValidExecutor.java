package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.infrastructure.common.util.MobileHelper;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.model.MobileValidRequestType;
import com.ctrip.model.MobileValidResponseType;

@Component
public class MobileValidExecutor  extends AbstractRpcExecutor<MobileValidRequestType, MobileValidResponseType> implements Validator<MobileValidRequestType> {

    @Autowired
    private MobileHelper mobileHelper;
    @Override
    public MobileValidResponseType execute(MobileValidRequestType mobileValidRequestType) {
        MobileValidResponseType mobileValidResponseType = new MobileValidResponseType();
        Result<Boolean> mobileValid = mobileHelper.isMobileValid(mobileValidRequestType.getCountryCode(), mobileValidRequestType.getMobilePhone(), mobileValidRequestType.getCityId());
        if (mobileValid.isSuccess()) {
            mobileValidResponseType.setValid(true);
            return ServiceResponseUtils.success(mobileValidResponseType);
        }else {
            mobileValidResponseType.setValid(false);
            return ServiceResponseUtils.fail(mobileValidResponseType, mobileValid.getCode(), mobileValid.getMsg());
        }
    }
}
