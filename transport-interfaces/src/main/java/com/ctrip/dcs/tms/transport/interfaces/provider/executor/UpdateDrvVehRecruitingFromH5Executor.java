package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
　* @description: H5司机修改招募信息
　* <AUTHOR>
　* @date 2021/12/7 16:26
*/
@Component
public class UpdateDrvVehRecruitingFromH5Executor extends AbstractRpcExecutor<DrvVehRecruitingUpdateFromH5RequestType, DrvVehRecruitingUpdateFromH5ResponseType> implements Validator<DrvVehRecruitingUpdateFromH5RequestType> {

    @Autowired
    private DrvVehRecruitingCommandService commandService;

    @Override
    public DrvVehRecruitingUpdateFromH5ResponseType execute(DrvVehRecruitingUpdateFromH5RequestType requestType) {
        DrvVehRecruitingUpdateFromH5ResponseType responseType = new DrvVehRecruitingUpdateFromH5ResponseType();
        Result<Boolean> result = commandService.updateDrvVehRecruitingFromH5(requestType);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(responseType);
        }else {
            return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<DrvVehRecruitingUpdateFromH5RequestType> validator) {
        validator.ruleFor("drvPhone").notNull();
        validator.ruleFor("drvRecruitingId").notNull();
    }
}
