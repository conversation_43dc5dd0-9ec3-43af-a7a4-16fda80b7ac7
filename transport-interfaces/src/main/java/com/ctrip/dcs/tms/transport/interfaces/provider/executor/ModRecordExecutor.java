package com.ctrip.dcs.tms.transport.interfaces.provider.executor;


import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.igt.PaginationDTO;
import com.ctrip.igt.framework.common.base.PageHolder;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * 查询历史记录
 */
@Component
public class ModRecordExecutor extends AbstractRpcExecutor<ModRecordSOARequestType, ModRecordSOAResponseType> implements Validator<ModRecordSOARequestType> {

    @Autowired
    ChangeRecordAttributeNameQconfig qconfig;

    @Autowired
    private TmsModRecordQueryService tmsModRecordQueryService;

    @Override
    public ModRecordSOAResponseType execute(ModRecordSOARequestType requestType) {
        ModRecordSOAResponseType responseType = new ModRecordSOAResponseType();
        Result<Void> checkResult = tmsModRecordQueryService.preCheck(requestType);
        //鉴权失败，返回空数据
        if (Boolean.FALSE.equals(checkResult.isSuccess())) {
            if(requestType.getPaginator()!=null) {
                PaginationDTO paginationDTO = ServiceResponseUtils.newPagination(requestType.getPaginator().getPageNo(),
                        requestType.getPaginator().getPageSize(), 0);
                responseType.setPagination(paginationDTO);
            }
            return ServiceResponseUtils.success(responseType);
        }
        int count = tmsModRecordQueryService.queryModRecordListCount(requestType);
        if(count == 0){
            if(requestType.getPaginator()!=null){
                PaginationDTO paginationDTO = ServiceResponseUtils.newPagination(requestType.getPaginator().getPageNo(),
                        requestType.getPaginator().getPageSize(), count);
                responseType.setPagination(paginationDTO);
            }
            responseType.setRecordList(Lists.newArrayList());
            return ServiceResponseUtils.success(responseType);
        }
        Result<List<ModRecordSOADTO>> listResult = tmsModRecordQueryService.queryModRecordList(requestType);
        if (listResult.isSuccess()) {
            if(requestType.getPaginator()!=null){
                PaginationDTO paginationDTO = ServiceResponseUtils.newPagination(requestType.getPaginator().getPageNo(),
                        requestType.getPaginator().getPageSize(), count);
                responseType.setPagination(paginationDTO);
            }
            responseType.setRecordList(listResult.getData());
            return ServiceResponseUtils.success(responseType);
        }else {
            return ServiceResponseUtils.fail(responseType, listResult.getCode(),listResult.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<ModRecordSOARequestType> validator) {
        validator.ruleFor("rrdId").notNull();
        validator.ruleFor("rrdType").notNull();
    }
}
