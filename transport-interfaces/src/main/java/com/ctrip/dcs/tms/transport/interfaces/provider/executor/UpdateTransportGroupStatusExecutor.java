package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.sql.*;

/**
 * <AUTHOR>
 * @Date 2020/3/3 19:36
 */
@Component
public class UpdateTransportGroupStatusExecutor extends AbstractRpcExecutor<UpdateTransportGroupStatusSOARequestType, UpdateTransportGroupStatusSOAResponseType> implements Validator<UpdateTransportGroupStatusSOARequestType> {

    @Autowired
    private TransportGroupCommandService transportGroupCommandService;

    @Override
    public UpdateTransportGroupStatusSOAResponseType execute(UpdateTransportGroupStatusSOARequestType updateTransportGroupStatusSOARequestType) {
        UpdateTransportGroupStatusSOAResponseType soaResponseType = new UpdateTransportGroupStatusSOAResponseType();
        TspTransportGroupPO transportGroupPO = requestTypeToGroupPO(updateTransportGroupStatusSOARequestType);
        Result<Boolean> result = transportGroupCommandService.updateTransportGroupStatus(transportGroupPO);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(soaResponseType);
        }else {
            return ServiceResponseUtils.fail(soaResponseType, result.getCode(),result.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<UpdateTransportGroupStatusSOARequestType> validator, UpdateTransportGroupStatusSOARequestType req) {
        validator.ruleFor("transportGroupId").notNull();
        validator.ruleFor("groupStatus").notNull();
        validator.ruleFor("modifyUser").notNull().notEmpty();
    }

    /**
     * 运力组基础信息PO
     * @param requestType
     * @return
     */
    private TspTransportGroupPO requestTypeToGroupPO(UpdateTransportGroupStatusSOARequestType requestType){
        TspTransportGroupPO transportGroupPO = new TspTransportGroupPO();
        transportGroupPO.setTransportGroupId(requestType.getTransportGroupId());
        transportGroupPO.setGroupStatus(requestType.getGroupStatus());
        transportGroupPO.setModifyUser(requestType.getModifyUser());
        transportGroupPO.setDatachangeLasttime(new Timestamp(System.currentTimeMillis()));
        return transportGroupPO;
    }
}
