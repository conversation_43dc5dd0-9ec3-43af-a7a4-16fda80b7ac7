package com.ctrip.dcs.tms.transport.interfaces.listener;

import com.ctrip.dcs.tms.transport.api.model.DriverBackgroundChecksSOADTO;
import com.ctrip.dcs.tms.transport.application.command.CertificateCheckCommandService;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.List;

/**
　* @description: 身份证背审信安返回值
　* <AUTHOR>
　* @date 2022/7/22 17:38
*/
@Component
public class DrvBackgroundPrincipalResultListener {

    private static final Logger logger = LoggerFactory.getLogger(DrvBackgroundPrincipalResultListener.class);

    public static final String KEY_NAME = "checksDTOList";

    @Autowired
    CertificateCheckCommandService commandService;


    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.SUBJECT_BACKGROUND_INFORM,consumerGroup = TmsTransportConstant.QMQ_CONSUMER_GROUP + TmsTransportConstant.QmqTag.TAG_IDCARDBACKGROUND)
    public void drvBackgroundPrincipalResultListener(Message message) {
        String value = message.getStringProperty(KEY_NAME);
        try {
            logger.info("drvBackgroundPrincipalResultListenerStart","params:{}",value);
            if (Strings.isNullOrEmpty(value)) {
                return ;
            }
            //times 记录重试次数
            if(message.times() > 2){
                logger.warn("BackgroundRetry","times:{}",message.times());
                return;
            }
            //localRetries是连续本地重试了多少次的意思(防止死循环)
            if (message.localRetries() > 5){
                //抛出远程重试异常  NeedRetryException指定下次重试的时间点
                throw new NeedRetryException(System. currentTimeMillis() + 60 * 1000 , "1min retry" );
            }

            List<DriverBackgroundChecksSOADTO> eventList = JsonUtil.fromJson(value, new TypeReference<List<DriverBackgroundChecksSOADTO>>() {
            });
            if (CollectionUtils.isEmpty(eventList)) {
                return;
            }
            commandService.insertBackGroundCheck(eventList);
        }catch (NeedRetryException e){
            logger.error("drvBackgroundPrincipalResultListenerError","value:{},e:{}",value,e);
            throw e;
        }
    }
}