package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.application.dto.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctriposs.baiji.exception.*;
import com.fasterxml.jackson.core.type.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import qunar.tc.qschedule.config.*;
import qunar.tc.schedule.*;

import javax.annotation.*;
import java.util.*;
import java.util.stream.*;

/**
 * <AUTHOR>
 * @Description  定时刷新 tms_certificate_check 表中 check_status = 4(核验中) 的数据
 * @Date 20:06 2021/3/18
 * @Param 
 * @return 
 **/
@Component
public class ApproveCertificateCheckResultSchedule {

    private static final Logger logger = LoggerFactory.getLogger(ApproveCertificateCheckResultSchedule.class);
    @Autowired
    DrvDrvierRepository repository;
    @Autowired
    CertificateCheckQueryService checkQueryService;
    @Autowired
    VehicleRepository vehicleRepository;
    @Autowired
    TmsTransportApproveRepository approveRepository;
    @Resource
    DriverQueryService driverQueryService;
    @Resource
    VehicleQueryService vehicleQueryService;

    private static final String drvName_Key = "drvName";
    private static final String drvidcard_Key = "drvIdcard";

    @QSchedule("certificate.check.approve.result.job")
    public void certificateCheckResultSchedule(Parameter parameter) throws Exception {
        String ids = parameter.getString("ids");
        Integer approveSourceType = parameter.getProperty("approveSourceType",Integer.class);
        this.certificateCheckResultScheduleMethod(ids,approveSourceType);
    }

    public void certificateCheckResultScheduleMethod(String ids,Integer approveSourceType) {
        logger.info("ApproveCertificateCheckResultSchedule","params:{}",ids);
        try {
            List<Long> checkIds = Lists.newArrayList();
            if (StringUtils.isNotEmpty(ids)) {
                checkIds = Arrays.stream(ids.split(",")).mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
            }
            List<TmsTransportApprovePO> approvePOList = approveRepository.queryApproveIngBySourceId(checkIds, approveSourceType, TmsTransportConstant.TransportApproveStatusEnum.WAITAPPROVE.getCode());
            if(CollectionUtils.isEmpty(approvePOList)){
                return;
            }
            for (TmsTransportApprovePO approvePO : approvePOList) {
                if (StringUtils.isEmpty(approvePO.getCertificateCheckResult()) || !BaseUtil.isJson(approvePO.getCertificateCheckResult())) {
                    continue;
                }
                Long sourceId = approvePO.getApproveSourceId();
                List<TmsCertificateCheckPO> checkResultList = JsonUtil.fromJson(approvePO.getCertificateCheckResult(), new TypeReference<List<TmsCertificateCheckPO>>() {
                });
                if (CollectionUtils.isEmpty(checkResultList)) {
                    continue;
                }
                //按checkId分组
                for(TmsCertificateCheckPO checkPO : checkResultList){
                    if (!Objects.equals(checkPO.getCheckStatus(),TmsTransportConstant.CheckStatusEnum.CHECKING.getCode())) {
                        continue;
                    }
                    //驾驶证
                    if (Objects.equals(checkPO.getCertificateType(), TmsTransportConstant.CertificateTypeEnum.DRIVERLICENSE.getCode())) {
                        TmsCertificateCheckPO result = drvLicenseDataCheck(sourceId);
                        if(!Objects.isNull(result)){
                            checkPO.setCheckStatus(result.getCheckStatus());
                            checkPO.setCheckContent(result.getCheckContent());
                            checkPO.setCheckResult(result.getCheckResult());
                        }
                    }
                    //行驶证
                    if (Objects.equals(checkPO.getCertificateType(), TmsTransportConstant.CertificateTypeEnum.CARCERTILICENSE.getCode())) {
                        TmsCertificateCheckPO result = vehCertificateCheck(sourceId);
                        if(!Objects.isNull(result)){
                            checkPO.setCheckStatus(result.getCheckStatus());
                            checkPO.setCheckContent(result.getCheckContent());
                            checkPO.setCheckResult(result.getCheckResult());
                        }

                    }
                    //网约车驾驶证
                    if (Objects.equals(checkPO.getCertificateType(), TmsTransportConstant.CertificateTypeEnum.NETDRVCTFCT.getCode())) {
                        TmsCertificateCheckPO result = netDrvLicenseDataCheck(checkPO.getCheckKeyword(),checkPO.getCheckId());
                        if(!Objects.isNull(result)){
                            checkPO.setCheckStatus(result.getCheckStatus());
                            checkPO.setCheckContent(result.getCheckContent());
                            checkPO.setCheckKeyword(result.getCheckKeyword());
                            checkPO.setCheckResult(result.getCheckResult());
                        }
                    }
                    //网约车行驶证
                    if (Objects.equals(checkPO.getCertificateType(), TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT.getCode())) {
                        TmsCertificateCheckPO result = netVehLicenseDataCheck(checkPO.getCheckKeyword(),checkPO.getCheckId());
                        if(!Objects.isNull(result)){
                            checkPO.setCheckStatus(result.getCheckStatus());
                            checkPO.setCheckContent(result.getCheckContent());
                            checkPO.setCheckKeyword(result.getCheckKeyword());
                            checkPO.setCheckResult(result.getCheckResult());
                        }
                    }
                }

                Boolean flag = Boolean.TRUE;
                for (TmsCertificateCheckPO tmsCertificate : checkResultList) {
                    if (Objects.equals(tmsCertificate.getCheckStatus(), TmsTransportConstant.CheckStatusEnum.CHECKING.getCode())) {
                        flag = Boolean.FALSE;
                    }
                }
                approveRepository.updateCheckResultAndFinishStatus(approvePO.getId(),JsonUtil.toJson(checkResultList),flag ? 1 : 0,approvePO.getModifyUser());
            }
            logger.info("job end");
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    //驾驶证核验
    public TmsCertificateCheckPO drvLicenseDataCheck(Long sourceId) {
        Map<String,Object> resultMap = getDrvNameAndIdCard(sourceId);
        if(MapUtils.isEmpty(resultMap)){
            return null;
        }
        if(resultMap.get(TmsTransportConstant.RESULTFLAG_KEY)!=null && !(Boolean) resultMap.get(TmsTransportConstant.RESULTFLAG_KEY)){
            return  null;
        }
        String drvName = String.valueOf(resultMap.get(drvName_Key));
        String drvIdcard = String.valueOf(resultMap.get(drvidcard_Key));
        if(StringUtils.isNotEmpty(drvName) && StringUtils.isNotEmpty(drvIdcard)){
            return checkQueryService.refreshApproveDrvLicenseCheckIng(DrvAuditDTO.refreshApprovebuildDrvDTO(drvName, drvIdcard));
        }
        return null;
    }

    //行驶证核验
    public TmsCertificateCheckPO vehCertificateCheck(Long sourceId) {
        VehVehiclePO vehiclePO = vehicleRepository.queryByPk(sourceId);
        if (Objects.isNull(vehiclePO)) {
            return null;
        }
        String vehicleLicense = vehiclePO.getVehicleLicense();
        String vinCode = vehiclePO.getVin();
        if(StringUtils.isNotEmpty(vehicleLicense)){
            VehicleAuditDTO vehicleAuditDTO = VehicleAuditDTO.rehApproveBuildVehicleDTO(vehicleLicense ,vinCode);
            return checkQueryService.refreshApproveVehLicenseCheckIng(vehicleAuditDTO);
        }
        return null;
    }

    //网约车驾驶证核验
    public TmsCertificateCheckPO netDrvLicenseDataCheck(String checkKeyword,Long checkId) {
        if(StringUtils.isEmpty(checkKeyword) || checkId == null){
            return null;
        }
        //查询司机信息
        SimpleDriverInfoDTO simpleDriverInfoDTO = driverQueryService.querySimpleDriver(checkId);
        if(simpleDriverInfoDTO == null){
            return null;
        }
        String cityId = String.valueOf(simpleDriverInfoDTO.getCityId());
        //上海 合规信息查询
        if(checkQueryService.checkFromCityPlatform(cityId)){
            return checkQueryService.refreshApproveNetDrvLicenseCheckIng(checkKeyword,simpleDriverInfoDTO.getDriverName(),cityId);
        }
        //其他城市 合规信息查询
        return checkQueryService.refreshApproveNetDrvLicenseCheckIng(checkKeyword);
    }

    //网约车行驶证核验
    public TmsCertificateCheckPO netVehLicenseDataCheck(String checkKeyword,Long checkId) {
        if(StringUtils.isEmpty(checkKeyword) || checkId == null){
            return null;
        }
        //查询车辆信息
        SimpleVehicleInfoDTO simpleVehicleInfoDTO = vehicleQueryService.querySimpleVehicleInfo(checkId);
        if(simpleVehicleInfoDTO == null){
            return null;
        }
        String cityId = String.valueOf(simpleVehicleInfoDTO.getCityId());
        //上海 合规信息查询处理
        if(checkQueryService.checkFromCityPlatform(cityId)){
            return checkQueryService.refreshApproveNetVehLicenseCheckIng(checkKeyword,cityId);
        }
        //其他城市合规信息查询处理
        return checkQueryService.refreshApproveNetVehLicenseCheckIng(checkKeyword);
    }

    public Map<String,Object> getDrvNameAndIdCard(Long checkId){
        Map<String,Object> resultMap = Maps.newHashMap();
        resultMap.put(TmsTransportConstant.RESULTFLAG_KEY,Boolean.FALSE);
        String drvName = "";
        String drvIdcard = "";
        DrvDriverPO drvDriverPO = repository.queryByPk(checkId);
        if (Objects.isNull(drvDriverPO)) {
            return resultMap;
        }
        drvName = StringUtils.isEmpty(drvDriverPO.getDrvLicenseName())?drvDriverPO.getDrvName():drvDriverPO.getDrvLicenseName();
        drvIdcard = StringUtils.isEmpty(drvDriverPO.getDrvLicenseNumber())?drvDriverPO.getDrvIdcard():drvDriverPO.getDrvLicenseNumber();
        resultMap.put(TmsTransportConstant.RESULTFLAG_KEY,Boolean.TRUE);
        resultMap.put(drvName_Key,drvName);
        resultMap.put(drvidcard_Key,TmsTransUtil.decrypt(drvIdcard,KeyType.Identity_Card));
        return resultMap;
    }
}