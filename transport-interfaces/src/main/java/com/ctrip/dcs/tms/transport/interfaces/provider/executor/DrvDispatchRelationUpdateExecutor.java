package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.DrvDispatchRelationUpdateSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.DrvDispatchRelationUpdateSOAResponseType;
import com.ctrip.dcs.tms.transport.application.command.DriverCommandService;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DrvDispatchRelationUpdateExecutor extends AbstractRpcExecutor<DrvDispatchRelationUpdateSOARequestType, DrvDispatchRelationUpdateSOAResponseType> implements Validator<DrvDispatchRelationUpdateSOARequestType> {

    @Autowired
    DriverCommandService driverCommandService;

    @Override
    public DrvDispatchRelationUpdateSOAResponseType execute(DrvDispatchRelationUpdateSOARequestType requestType) {
        DrvDispatchRelationUpdateSOAResponseType responseType = new DrvDispatchRelationUpdateSOAResponseType();
        Result<Boolean> result = driverCommandService.drvDispatchRelationUpdate(requestType);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<DrvDispatchRelationUpdateSOARequestType> validator) {
        validator.ruleFor("drvId").notNull();
        validator.ruleFor("supplierId").notNull();
        validator.ruleFor("operationType").notNull();
    }
}
