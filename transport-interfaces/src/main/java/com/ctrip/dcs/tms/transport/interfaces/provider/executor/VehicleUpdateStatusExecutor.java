package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

@Component
public class VehicleUpdateStatusExecutor extends AbstractRpcExecutor<VehicleUpdateStatusSOARequestType, VehicleUpdateStatusSOAResponseType> implements Validator<VehicleUpdateStatusSOARequestType> {

    @Autowired
    private VehicleCommandService vehicleCommandService;

    @Override
    public VehicleUpdateStatusSOAResponseType execute(VehicleUpdateStatusSOARequestType requestType) {
        VehicleUpdateStatusSOAResponseType responseType = new VehicleUpdateStatusSOAResponseType();
        Result<Boolean> result = vehicleCommandService.updateVehicleStatus(requestType);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType,"400",result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<VehicleUpdateStatusSOARequestType> validator) {
        validator.ruleFor("vehicleIdList").notNull();
        validator.ruleFor("vehicleStatus").notNull();
        validator.ruleFor("modifyUser").notNull();
    }

}