package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/***
 　* @description: 获取城市网约车证件图例和核验地址列表
 　* <AUTHOR>
 　* @date 2021/8/17 15:09
 */
@Component
public class QueryCityNetConfigListExecutor extends AbstractRpcExecutor<QueryCityNetConfigListSOARequestType, QueryCityNetConfigListSOAResponseType> implements Validator<QueryCityNetConfigListSOARequestType> {

    @Autowired
    private CommonCommandService commandService;

    @Override
    public QueryCityNetConfigListSOAResponseType execute(QueryCityNetConfigListSOARequestType requestType) {
        QueryCityNetConfigListSOAResponseType responseType = new QueryCityNetConfigListSOAResponseType();
        Result<List<QueryCityNetConfigListSOADTO>> result = commandService.queryCityNetConfigList(requestType);
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType);
    }

    @Override
    public void validate(AbstractValidator<QueryCityNetConfigListSOARequestType> validator) {
        validator.ruleFor("cityId").notNull();
    }
}