package com.ctrip.dcs.tms.transport.interfaces.provider;

import com.ctrip.dcs.tms.transport.api.TmsTransportService;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.api.regulation.*;
import com.ctrip.dcs.tms.transport.api.resource.driver.QueryDriver4BaseSOARequestType;
import com.ctrip.dcs.tms.transport.api.resource.driver.QueryDriver4BaseSOAResponseType;
import com.ctrip.dcs.tms.transport.api.saas.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ErrorCodeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.SharkUtils;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.framework.soa.server.executor.ServiceExecutors;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.model.*;
import com.ctriposs.baiji.rpc.common.types.CheckHealthRequestType;
import com.ctriposs.baiji.rpc.common.types.CheckHealthResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2020/3/16 15:49
 */
@Component
public class TmsTransportServiceImpl implements TmsTransportService {
    
    @Override
    public CheckHealthResponseType checkHealth(CheckHealthRequestType request) throws Exception {
        return ServiceResponseUtils.getDefaultCheckHealthResponse();
    }

    @Override
    public DrvAddSOAResponseType addDrv(DrvAddSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvAddSOAResponseType.class);
    }

    @Override
    public DrvLeaveAddSOAResponseType addDrvLeave(DrvLeaveAddSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvLeaveAddSOAResponseType.class);
    }

    @Override
    public DrvLeaveCloseSOAResponseType closeDrvLeave(DrvLeaveCloseSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvLeaveCloseSOAResponseType.class);
    }

    @Override
    public QueryDrvLeaveDetailSOAResponseType queryDrvLeaveDetail(QueryDrvLeaveDetailSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDrvLeaveDetailSOAResponseType.class);
    }

    @Override
    public QueryDrvLeaveDetailForDspSOAResponseType queryDrvLeaveDetailForDsp(QueryDrvLeaveDetailForDspSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDrvLeaveDetailForDspSOAResponseType.class);
    }

    @Override
    public VehicleAddSOAResponseType addVehicle(VehicleAddSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, VehicleAddSOAResponseType.class);
    }

    @Override
    public VehicleUpdateSOAResponseType updateVehicle(VehicleUpdateSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, VehicleUpdateSOAResponseType.class);
    }

    @Override
    public VehicleDetailSOAResponseType queryVehicleDetail(VehicleDetailSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, VehicleDetailSOAResponseType.class);
    }

    @Override
    public QueryVehicleSOAResponseType queryVehicleList(QueryVehicleSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryVehicleSOAResponseType.class);
    }

    @Override
    public DrvUpdateSOAResponseType updateDrv(DrvUpdateSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvUpdateSOAResponseType.class);
    }

    @Override
    public QueryDrvDetailSOAResponseType queryDrvDetail(QueryDrvDetailSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDrvDetailSOAResponseType.class);
    }

    @Override
    public QueryDrvSOAResponseType queryDrvList(QueryDrvSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDrvSOAResponseType.class);
    }

    @Override
    public DrvStatusUpdateSOAResponseType updateDrvStatus(DrvStatusUpdateSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvStatusUpdateSOAResponseType.class);
    }

    @Override
    public DrvFreezeSOAResponseType freezeDrv(DrvFreezeSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvFreezeSOAResponseType.class);
    }

    @Override
    public ModRecordSOAResponseType modRecordList(ModRecordSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, ModRecordSOAResponseType.class);
    }

    @Override
    public TransportGroupAddSOAResponseType addTransportGroup(TransportGroupAddSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, TransportGroupAddSOAResponseType.class);
    }

    @Override
    public QueryTransportGroupDetailSOAResponseType queryTransportGroupDetail(QueryTransportGroupDetailSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryTransportGroupDetailSOAResponseType.class);
    }

    @Override
    public QueryTransportGroupListSOAResponseType queryTransportGroupList(QueryTransportGroupListSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryTransportGroupListSOAResponseType.class);
    }

    @Override
    public DriverRelationListResponseSOAType queryDriverRelationList(DriverRelationListRequestSOAType request) throws Exception {
        return ServiceExecutors.execute(request, DriverRelationListResponseSOAType.class);
    }

    @Override
    public DriverRelationBindResponseSOAType updateDriverRelationBind(DriverRelationBindRequestSOAType request) throws Exception {
        return ServiceExecutors.execute(request, DriverRelationBindResponseSOAType.class);
    }

    @Override
    public UpdateTransportGroupSOAResponseType updateTransportGroup(UpdateTransportGroupSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, UpdateTransportGroupSOAResponseType.class);
    }

    @Override
    public UpdateTransportGroupStatusSOAResponseType updateTransportGroupStatus(UpdateTransportGroupStatusSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, UpdateTransportGroupStatusSOAResponseType.class);
    }

    @Override
    public SkuRelationBindResponseSOAType updateTransportGroupSkuRelation(SkuRelationBindRequestSOAType request) throws Exception {
        return ServiceExecutors.execute(request, SkuRelationBindResponseSOAType.class);
    }

    @Override
    public TransportGroupSkuListResponseSOAType queryTransportGroupSkuInfoList(TransportGroupSkuListRequestSOAType request) throws Exception {
        return ServiceExecutors.execute(request, TransportGroupSkuListResponseSOAType.class);
    }

    @Override
    public DrvUpdateAddrResponseType updateDrvAddr(DrvUpdateAddrRequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvUpdateAddrResponseType.class);
    }

    @Override
    public QueryTransportGroupsSOAResponseType queryTransportGroups(QueryTransportGroupsSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryTransportGroupsSOAResponseType.class);
    }

    @Override
    public CheckSkuBindInfoSOAResponseType checkSkuBindInfo(CheckSkuBindInfoSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, CheckSkuBindInfoSOAResponseType.class);
    }

    @Override
    public QueryBingIngSkuSOAResponseType queryBingIngSkuList(QueryBingIngSkuSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryBingIngSkuSOAResponseType.class);
    }

    @Override
    public IsDispatcherModeSOAResponseType isDispatcherMode(IsDispatcherModeSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, IsDispatcherModeSOAResponseType.class);
    }

    @Override
    public QueryDrvListByAppResponseType queryDrvListByApp(QueryDrvListByAppRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDrvListByAppResponseType.class);
    }

    @Override
    public QueryVehicleBaseSOAResponseType queryVehicleListBySupplierId(QueryVehicleBaseSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryVehicleBaseSOAResponseType.class);
    }

    @Override
    public QueryAllTransGroupSOAResponseType queryAllTransGroupList(QueryAllTransGroupSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryAllTransGroupSOAResponseType.class);
    }

    @Override
    public QueryDrvBySupplierIdSOAResponseType queryDrvBySupplierIdList(QueryDrvBySupplierIdSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDrvBySupplierIdSOAResponseType.class);
    }

    @Override
    public DrvVehRecruitingAddSOAResponseType addDrvVehRecruiting(DrvVehRecruitingAddSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvVehRecruitingAddSOAResponseType.class);
    }

    @Override
    public DrvVehRecruitingDetailSOAResponseType queryDrvVehRecruitingDetail(DrvVehRecruitingDetailSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvVehRecruitingDetailSOAResponseType.class);
    }

    @Override
    public QueryRecruitingSOAResponseType queryRecruitingList(QueryRecruitingSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryRecruitingSOAResponseType.class);
    }

    @Override
    public RecruitingApproveSOAResponseType recruitingApproveAction(RecruitingApproveSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, RecruitingApproveSOAResponseType.class);
    }

    @Override
    public QueryDrvVehRecruitingModRrdSOAResponseType queryDrvVehRecruitingModRrdList(QueryDrvVehRecruitingModRrdSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDrvVehRecruitingModRrdSOAResponseType.class);
    }

    @Override
    public CheckSupplierQualificationSOAResponseType checkSupplierQualification(CheckSupplierQualificationSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, CheckSupplierQualificationSOAResponseType.class);
    }

    @Override
    public QueryDrvInfoSOAResponseType queryDrvInfo(QueryDrvInfoSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDrvInfoSOAResponseType.class);
    }

    @Override
    public RecruitingApproveJurisdictionSOAResponseType recruitingApproveJurisdiction(RecruitingApproveJurisdictionSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, RecruitingApproveJurisdictionSOAResponseType.class);
    }

    @Override
    public CheckRecruitingPhoneSOAResponseType checkRecruitingPhone(CheckRecruitingPhoneSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, CheckRecruitingPhoneSOAResponseType.class);
    }

    @Override
    public SendVerificationPhoneSOAResponseType sendVerificationPhone(SendVerificationPhoneSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, SendVerificationPhoneSOAResponseType.class);
    }

    @Override
    public CheckVerificationPhoneSOAResponseType checkVerificationPhone(CheckVerificationPhoneSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, CheckVerificationPhoneSOAResponseType.class);
    }

    @Override
    public RecruitingApproveAddSOAResponseType recruitingApproveAdd(RecruitingApproveAddSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, RecruitingApproveAddSOAResponseType.class);
    }

    @Override
    public CheckDrvPwdSOAResponseType checkDrvPwd(CheckDrvPwdSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, CheckDrvPwdSOAResponseType.class);
    }

    @Override
    public DataMigrationCarSOAResponseType dataMigrationCar(DataMigrationCarSOARequestType dataMigrationCarSOARequestType) throws Exception {
        return ServiceExecutors.execute(dataMigrationCarSOARequestType, DataMigrationCarSOAResponseType.class);
    }

    @Override
    public DataMigrationDriverSOAResponseType dataMigrationDriver(DataMigrationDriverSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DataMigrationDriverSOAResponseType.class);
    }

    @Override
    public DataMigrationDriverLeaveSOAResponseType dataMigrationDriverLeave(DataMigrationDriverLeaveSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DataMigrationDriverLeaveSOAResponseType.class);
    }

    @Override
    public DataMigrationDeleteSOAResponseType dataMigrationDelete(DataMigrationDeleteSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DataMigrationDeleteSOAResponseType.class);
    }

    @Override
    public QueryDrvInfoListSOAResponseType queryDrvInfoList(QueryDrvInfoListSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDrvInfoListSOAResponseType.class);
    }

    @Override
    public CheckRecruitingAccountSOAResponseType checkRecruitingAccount(CheckRecruitingAccountSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, CheckRecruitingAccountSOAResponseType.class);
    }

    @Override
    public CheckDrvTransportModeSOAResponseType checkDrvTransportMode(CheckDrvTransportModeSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, CheckDrvTransportModeSOAResponseType.class);
    }

    @Override
    public TmsDrvFreezeAddSOAResponseType addTmsDrvFreeze(TmsDrvFreezeAddSOARequestType requestType) throws Exception {
        return ServiceExecutors.execute(requestType, TmsDrvFreezeAddSOAResponseType.class);
    }

    @Override
    public QueryTransportGroupListByIdListSOAResponseType queryTransportGroupListByIdList(QueryTransportGroupListByIdListSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryTransportGroupListByIdListSOAResponseType.class);
    }

    @Override
    public QueryDrvByMuSelConditionsSOAResponseType queryDrvByMuSelConditions(QueryDrvByMuSelConditionsSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDrvByMuSelConditionsSOAResponseType.class);
    }

    @Override
    public QueryCityByTransGroupSOAResponseType queryCityByTransPortGroup(QueryCityByTransGroupSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryCityByTransGroupSOAResponseType.class);
    }

    @Override
    public QueryLocationByTransGroupSOAResponseType queryLocationByTransPortGroup(QueryLocationByTransGroupSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryLocationByTransGroupSOAResponseType.class);
    }

    @Override
    public QueryTransportGroupAreaIdSOAResponseType queryTransportGroupAreaId(QueryTransportGroupAreaIdSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryTransportGroupAreaIdSOAResponseType.class);
    }

    @Override
    public CheckTransportGroupCitySOAResponseType checkTransportGroupCity(CheckTransportGroupCitySOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, CheckTransportGroupCitySOAResponseType.class);
    }

    @Override
    public CheckTransportGroupLocationCodeSOAResponseType checkTransportGroupLocationCode(CheckTransportGroupLocationCodeSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, CheckTransportGroupLocationCodeSOAResponseType.class);
    }

    @Override
    public QueryContractInfoSOAResponseType queryContractInfo(QueryContractInfoSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryContractInfoSOAResponseType.class);
    }

    @Override
    public DataMigrationMapCheckSOAResponseType dataMigrationMapCheck(DataMigrationMapCheckSOARequestType dataMigrationMapCheckSOARequestType) throws Exception {
        return ServiceExecutors.execute(dataMigrationMapCheckSOARequestType, DataMigrationMapCheckSOAResponseType.class);
    }

    @Override
    public QueryDrvFreezeDetailSOAResponseType queryDrvFreezeDetail(QueryDrvFreezeDetailSOARequestType requestType) throws Exception {
        return ServiceExecutors.execute(requestType, QueryDrvFreezeDetailSOAResponseType.class);
    }

    @Override
    public CheckSupplierFreezePermSOAResponseType checkSupplierFreezePerm(CheckSupplierFreezePermSOARequestType requestType) throws Exception {
        return ServiceExecutors.execute(requestType, CheckSupplierFreezePermSOAResponseType.class);
    }

    @Override
    public QueryDoDrvFreezeSOAResponseType queryDoDrvFreeze(QueryDoDrvFreezeSOARequestType requestType) throws Exception {
        return ServiceExecutors.execute(requestType, QueryDoDrvFreezeSOAResponseType.class);
    }

    @Override
    public DrvUnFreezeSOAResponseType drvUnFreeze(DrvUnFreezeSOARequestType requestType) throws Exception {
        return ServiceExecutors.execute(requestType, DrvUnFreezeSOAResponseType.class);
    }

    @Override
    public DrvFreezeBatchSOAResponseType batchDrvFreeze(DrvFreezeBatchSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvFreezeBatchSOAResponseType.class);
    }

    @Override
    public DataRegulationDriverAppResponseType dataRegulationDriverApp(DataRegulationDriverAppRequestType dataRegulationDriverAppRequestType) throws Exception {
        return ServiceExecutors.execute(dataRegulationDriverAppRequestType,DataRegulationDriverAppResponseType.class);
    }

    @Override
    public UpdateTransportGroupFlagResponseType updateTransportGroupFlag(
      UpdateTransportGroupFlagRequestType requestType) throws Exception {
        return ServiceExecutors.execute(requestType,UpdateTransportGroupFlagResponseType.class);
    }

    @Override
    public DataRegulationDriverCompactResponseType dataRegulationDriverCompact(DataRegulationDriverCompactRequestType dataRegulationDriverCompactRequestType) throws Exception {
        return ServiceExecutors.execute(dataRegulationDriverCompactRequestType,DataRegulationDriverCompactResponseType.class);
    }

    @Override
    public DataRegulationDriverEducateResponseType dataRegulationDriverEducate(DataRegulationDriverEducateRequestType dataRegulationDriverEducateRequestType) throws Exception {
        return ServiceExecutors.execute(dataRegulationDriverEducateRequestType,DataRegulationDriverEducateResponseType.class);
    }

    @Override
    public DataRegulationDriverInfoResponseType dataRegulationDriverInfo(DataRegulationDriverInfoRequestType dataRegulationDriverInfoRequestType) throws Exception {
        return ServiceExecutors.execute(dataRegulationDriverInfoRequestType,DataRegulationDriverInfoResponseType.class);
    }

    @Override
    public DataRegulationDriverRateResponseType dataRegulationDriverRate(DataRegulationDriverRateRequestType dataRegulationDriverRateRequestType) throws Exception {
        return ServiceExecutors.execute(dataRegulationDriverRateRequestType,DataRegulationDriverRateResponseType.class);
    }

    @Override
    public DataRegulationVehicleInfoResponseType dataRegulationVehicleInfo(DataRegulationVehicleInfoRequestType dataRegulationVehicleInfoRequestType) throws Exception {
        return ServiceExecutors.execute(dataRegulationVehicleInfoRequestType,DataRegulationVehicleInfoResponseType.class);
    }

    @Override
    public DataRegulationVehicleMileResponseType dataRegulationVehicleMile(DataRegulationVehicleMileRequestType request) throws Exception {
        return ServiceExecutors.execute(request,DataRegulationVehicleMileResponseType.class);
    }

    @Override
    public UpdateDrvIntendVehicleTypeResponseType updateDrvIntendVehicleType(UpdateDrvIntendVehicleTypeRequestType request) throws Exception {
        return ServiceExecutors.execute(request,UpdateDrvIntendVehicleTypeResponseType.class);
    }

    @Override
    public QueryOptionalIntendVehicleTypeSOAResponseType queryOptionalIntendVehicleType(QueryOptionalIntendVehicleTypeSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request,QueryOptionalIntendVehicleTypeSOAResponseType.class);
    }

    @Override
    public AuthorizationSOAResponseType checkAuthorization(AuthorizationSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, AuthorizationSOAResponseType.class);
    }

    @Override
    public QueryOptionalTransportGroupModeSOAResponseType queryOptionalTransportGroupMode(QueryOptionalTransportGroupModeSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryOptionalTransportGroupModeSOAResponseType.class);
    }

    @Override
    public QueryTransportGroupConfigSOAResponseType queryTransportGroupConfig(QueryTransportGroupConfigSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryTransportGroupConfigSOAResponseType.class);
    }

    @Override
    public UpdateTransportGroupApplyStatusSOAResponseType updateTransportGroupApplyStatus(UpdateTransportGroupApplyStatusSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, UpdateTransportGroupApplyStatusSOAResponseType.class);
    }

    @Override
    public QueryTransportGroupApplyInfoSOAResponseType queryTransportGroupApplyInfo(QueryTransportGroupApplyInfoSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryTransportGroupApplyInfoSOAResponseType.class);
    }

    @Override
    public DrvVehRecruitingUpdateSOAResponseType updateDrvVehRecruiting(DrvVehRecruitingUpdateSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvVehRecruitingUpdateSOAResponseType.class);
    }

    @Override
    public QueryAddTransportGroupCityListSOAResponseType queryAddTransportGroupCityList(QueryAddTransportGroupCityListSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryAddTransportGroupCityListSOAResponseType.class);
    }

    @Override
    public QueryDrvCodeByDrvIdSOAResponseType queryDrvCodeByDrvId(QueryDrvCodeByDrvIdSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDrvCodeByDrvIdSOAResponseType.class);
    }

    @Override
    public QueryDrvIdByCodeSOAResponseType queryDrvIdByCode(QueryDrvIdByCodeSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDrvIdByCodeSOAResponseType.class);
    }

    @Override
    public CheckDrvGroupModeSOAResponseType checkDrvGroupModeIsQzsj(CheckDrvGroupModeSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, CheckDrvGroupModeSOAResponseType.class);
    }

    @Override
    public QueryDrvForCharterSOAResponseType queryDrvForCharterList(QueryDrvForCharterSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDrvForCharterSOAResponseType.class);
    }

    @Override
    public UploadVehicleDispatchPhotoListResponseType uploadVehicleDispatchPhotoList(
      UploadVehicleDispatchPhotoListRequestType reuest) throws Exception {
        return ServiceExecutors.execute(reuest, UploadVehicleDispatchPhotoListResponseType.class);
    }

    @Override
    public IsNeedCreateTaskResponseType isNeedCreateTask(IsNeedCreateTaskRequestType reuest)
      throws Exception {
        return ServiceExecutors.execute(reuest, IsNeedCreateTaskResponseType.class);
    }

    @Override
    public SaveIsNeedCreateTaskResponseType saveIsNeedCreateTask(
      SaveIsNeedCreateTaskRequestType reuest) throws Exception {
        return ServiceExecutors.execute(reuest, SaveIsNeedCreateTaskResponseType.class);
    }

    @Override
    public QueryTaskConfigResponseType queryTaskConfig(QueryTaskConfigRequestType reuest)
      throws Exception {
        return ServiceExecutors.execute(reuest, QueryTaskConfigResponseType.class);
    }

    @Override
    public CheckPhoneNumberResponseType checkPhoneNumber(CheckPhoneNumberRequestType request)
      throws Exception {
        return ServiceExecutors.execute(request, CheckPhoneNumberResponseType.class);
    }

    @Override
    public QueryVehicleForCharterSOAResponseType queryVehicleForCharterList(QueryVehicleForCharterSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryVehicleForCharterSOAResponseType.class);
    }

    @Override
    public QueryDataMigrationDriverSOAResponseType queryDataMigrationDriver(QueryDataMigrationDriverSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDataMigrationDriverSOAResponseType.class);
    }

    @Override
    public QueryLegalSubjectInfoSOAResponseType queryLegalSubjectInfo(QueryLegalSubjectInfoSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryLegalSubjectInfoSOAResponseType.class);
    }

    @Override
    public QueryCityInfoSOAResponseType queryCityInfo(QueryCityInfoSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryCityInfoSOAResponseType.class);
    }

    @Override
    public QueryVehicleBaseInfoSOAResponseType queryVehicleBaseInfo(QueryVehicleBaseInfoSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryVehicleBaseInfoSOAResponseType.class);
    }

    @Override
    public QueryCertificateConfigListSOAResponseType queryCertificateConfigList(QueryCertificateConfigListSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request,QueryCertificateConfigListSOAResponseType.class);
    }

    @Override
    public CertificateConfigAddSOAResponseType certificateConfigAdd(CertificateConfigAddSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request,CertificateConfigAddSOAResponseType.class);
    }

    @Override
    public CertificateConfigUpdateSOAResponseType certificateConfigUpdate(CertificateConfigUpdateSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request,CertificateConfigUpdateSOAResponseType.class);
    }

    @Override
    public QueryCertificateConfigInfoSOAResponseType queryCertificateConfigInfo(QueryCertificateConfigInfoSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request,QueryCertificateConfigInfoSOAResponseType.class);
    }

    @Override
    public VehicleUpdateStatusSOAResponseType vehicleUpdateStatus(VehicleUpdateStatusSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, VehicleUpdateStatusSOAResponseType.class);
    }

    @Override
    public CheckDrvLeaveResponseType checkDrvLeave(CheckDrvLeaveRequestType request) throws Exception {
        return ServiceExecutors.execute(request, CheckDrvLeaveResponseType.class);
    }

    @Override
    public CheckDrvLoginResponseType checkDrvLogin(CheckDrvLoginRequestType request) throws Exception {
        return ServiceExecutors.execute(request, CheckDrvLoginResponseType.class);
    }

    @Override
    public ResetDrvPwdResponseType resetDrvPwd(ResetDrvPwdRequestType request) throws Exception {
        return ServiceExecutors.execute(request, ResetDrvPwdResponseType.class);
    }

    @Override
    public UpdateDrvPwdResponseType updateDrvPwd(UpdateDrvPwdRequestType request) throws Exception {
        return ServiceExecutors.execute(request, UpdateDrvPwdResponseType.class);
    }

    @Override
    public BackGroudCheckCallBackSOAResponseType backGroudCheckCallBack(BackGroudCheckCallBackSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request,BackGroudCheckCallBackSOAResponseType.class);
    }

    @Override
    public DriverInfoSOAResponseType queryDriver(DriverInfoSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DriverInfoSOAResponseType.class);
    }

    @Override
    public CheckVehicleTypeMatchingSOAResponseType checkVehicleTypeMatching(CheckVehicleTypeMatchingSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, CheckVehicleTypeMatchingSOAResponseType.class);
    }

    @Override
    public QueryCertificateConfigDetailSOAResponseType queryCertificateConfigDetail(QueryCertificateConfigDetailSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryCertificateConfigDetailSOAResponseType.class);
    }

    @Override
    public CheckDrvPenaltyStatusSOAResponseType checkDrvPenaltyStatus(CheckDrvPenaltyStatusSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, CheckDrvPenaltyStatusSOAResponseType.class);
    }

    @Override
    public TodoListCountSOAResponseType todoListCount(TodoListCountSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, TodoListCountSOAResponseType.class);
    }

    @Override
    public QueryCategorySOAResponseType queryCategoryList(QueryCategorySOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryCategorySOAResponseType.class);
    }

    @Override
    public CheckDrvPenaltyFreezeSOAResponseType checkDrvPenaltyFreeze(CheckDrvPenaltyFreezeSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, CheckDrvPenaltyFreezeSOAResponseType.class);
    }

    @Override
    public QueryTransportApproveListSOAResponseType queryTransportApproveList(QueryTransportApproveListSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryTransportApproveListSOAResponseType.class);
    }

    @Override
    public QueryTransportApproveDetailSOAResponseType queryTransportApproveDetail(QueryTransportApproveDetailSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryTransportApproveDetailSOAResponseType.class);
    }

    @Override
    public TransportApproveStatusUpdateSOAResponseType updateTransportApproveStatus(TransportApproveStatusUpdateSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, TransportApproveStatusUpdateSOAResponseType.class);
    }

    @Override
    public QueryTransportKanbanDetailSOAResponseType queryTransportKanbanDetail(QueryTransportKanbanDetailSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryTransportKanbanDetailSOAResponseType.class);
    }

    @Override
    public QueryTransportKanbanListSOAResponseType queryTransportKanbanList(QueryTransportKanbanListSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryTransportKanbanListSOAResponseType.class);
    }

    @Override
    public QueryTransportKanbanForDspSOAResponseType queryTransportKanbanForDsp(QueryTransportKanbanForDspSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryTransportKanbanForDspSOAResponseType.class);
    }

    @Override
    public SaveDriverSafetyInfoSOAResponseType saveDriverSafetyInfo(SaveDriverSafetyInfoSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, SaveDriverSafetyInfoSOAResponseType.class);
    }

    @Override
    public QueryDriverSafetyInfoSOAResponseType queryDriverSafetyInfo(QueryDriverSafetyInfoSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDriverSafetyInfoSOAResponseType.class);
    }

    @Override
    public QueryEpidemicPreventionControlLimitSOAResponseType queryEpidemicPreventionControlLimit(QueryEpidemicPreventionControlLimitSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryEpidemicPreventionControlLimitSOAResponseType.class);
    }

    @Override
    public QueryUpdateReportStatusSOAResponseType queryUpdateReportStatus(QueryUpdateReportStatusSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryUpdateReportStatusSOAResponseType.class);
    }

    @Override
    public QuerySafetyDriverInfoSOAResponseType querySafetyDriverPanelInfo(QuerySafetyDriverInfoSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QuerySafetyDriverInfoSOAResponseType.class);
    }


    @Override
    public DataMigrationCharterDriverSOAResponseType dataMigrationCharterDriver(DataMigrationCharterDriverSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DataMigrationCharterDriverSOAResponseType.class);
    }

    @Override
    public DataMigrationCharterCarSOAResponseType dataMigrationCharterCar(DataMigrationCharterCarSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DataMigrationCharterCarSOAResponseType.class);
    }


    @Override
    public DrvIsNeedVerifySOAResponseType queryDrvIsNeedVerify(DrvIsNeedVerifySOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvIsNeedVerifySOAResponseType.class);
    }

    @Override
    public DrvVerifyParamsSOAResponseType queryDrvVerifyParams(DrvVerifyParamsSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvVerifyParamsSOAResponseType.class);
    }

    @Override
    public DrvVerifyResultSOAResponseType queryDrvVerifyResult(DrvVerifyResultSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvVerifyResultSOAResponseType.class);
    }

    @Override
    public DrvVerifyTimesSOAResponseType pushDrvVerifyTimes(DrvVerifyTimesSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvVerifyTimesSOAResponseType.class);
    }

    @Override
    public DrvVerifyGraySOAResponseType queryDrvVerifyGray(DrvVerifyGraySOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvVerifyGraySOAResponseType.class);
    }

    @Override
    public QueryDrvVerifyEventListSOAResponseType queryDrvVerifyEnentList(QueryDrvVerifyEventListSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDrvVerifyEventListSOAResponseType.class);
    }

    @Override
    public VehicleVerifyResultSOAResponseType queryVehicleVerifyResult(VehicleVerifyResultSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, VehicleVerifyResultSOAResponseType.class);
    }

    @Override
    public UpdateDrvVerifyEventSOAResponseType updateDrvVerifyEvent(UpdateDrvVerifyEventSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, UpdateDrvVerifyEventSOAResponseType.class);
    }

    @Override
    public DrvFaceVerifyCountTransfiniteResponseType drvFaceVerifyCountTransfinite(DrvFaceVerifyCountTransfiniteRequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvFaceVerifyCountTransfiniteResponseType.class);
    }

    @Override
    public OcrLimitCheckSOAResponseType ocrLimitCheck(OcrLimitCheckSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, OcrLimitCheckSOAResponseType.class);
    }

    @Override
    public QueryTransportGroupModeListSOAResponseType queryTransportGroupModeList(QueryTransportGroupModeListSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryTransportGroupModeListSOAResponseType.class);
    }

    @Override
    public QueryDrvSafetyCenterSOAResponseType queryDrvSafetyCenter(QueryDrvSafetyCenterSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDrvSafetyCenterSOAResponseType.class);
    }

    @Override
    public QueryDrvAddrModCountSOAResponseType queryDrvAddrModCount(QueryDrvAddrModCountSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDrvAddrModCountSOAResponseType.class);
    }

    @Override
    public QueryDrvFreezeInfoForDspSOAResponseType queryDrvFreezeInfoForDsp(QueryDrvFreezeInfoForDspSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request,QueryDrvFreezeInfoForDspSOAResponseType.class);
    }

    @Override
    public QueryCityNetConfigListSOAResponseType queryCityNetConfig(QueryCityNetConfigListSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryCityNetConfigListSOAResponseType.class);
    }

    @Override
    public DrvHealthPunchAddResponseType drvHealthPunchAdd(DrvHealthPunchAddRequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvHealthPunchAddResponseType.class);
    }

    @Override
    public QueryDrvHealthPunchPermissionsResponseType queryDrvHealthPunchPermissions(QueryDrvHealthPunchPermissionsRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDrvHealthPunchPermissionsResponseType.class);
    }

    @Override
    public CertificateConfigIsWhiteSOAResponseType certificateConfigIsWhite(CertificateConfigIsWhiteSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, CertificateConfigIsWhiteSOAResponseType.class);
    }

    @Override
    public QueryVehicleAccessYearSOAResponseType queryVehicleAccessYear(QueryVehicleAccessYearSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryVehicleAccessYearSOAResponseType.class);
    }

    @Override
    public NewWorkbenchSupplierGraySOAResponseType newWorkbenchSupplierGray(NewWorkbenchSupplierGraySOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, NewWorkbenchSupplierGraySOAResponseType.class);
    }

    @Override
    public SaveDrvVerifyResultResponseType saveDrvVerifyResult(SaveDrvVerifyResultRequestType request) throws Exception {
        return ServiceExecutors.execute(request, SaveDrvVerifyResultResponseType.class);
    }

    @Override
    public QueryRecruitingDrvForCrawlerResponseType queryRecruitingDrvForCrawler(QueryRecruitingDrvForCrawlerRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryRecruitingDrvForCrawlerResponseType.class);
    }

    @Override
    public QueryRecruitingVehForCrawlerResponseType queryRecruitingVehForCrawler(QueryRecruitingVehForCrawlerRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryRecruitingVehForCrawlerResponseType.class);
    }

    @Override
    public QueryVehicleInfoResponseType queryVehicleInfo(QueryVehicleInfoRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryVehicleInfoResponseType.class);
    }

    @Override
    public QueryRandomVehicleResponseType queryRandomVehicle(QueryRandomVehicleRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryRandomVehicleResponseType.class);
    }

    @Override
    public QueryTransportGroupsForDspResponseType queryTransportGroupsForDsp(QueryTransportGroupsForDspRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryTransportGroupsForDspResponseType.class);
    }

    @Override
    public QueryRecruitingApproveStatusSOAResponseType queryRecruitingApproveStatus(QueryRecruitingApproveStatusSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryRecruitingApproveStatusSOAResponseType.class);
    }

    @Override
    public QueryApproveStepSOAResponseType queryApproveStep(QueryApproveStepSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryApproveStepSOAResponseType.class);
    }

    @Override
    public SingleApprovalSOAResponseType singleApproval(SingleApprovalSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, SingleApprovalSOAResponseType.class);
    }

    @Override
    public DrvVehRecruitingDetailFromH5ResponseType queryDrvVehRecruitingDetailFromH5(DrvVehRecruitingDetailFromH5RequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvVehRecruitingDetailFromH5ResponseType.class);
    }

    @Override
    public DrvVehRecruitingUpdateFromH5ResponseType updateDrvVehRecruitingFromH5(DrvVehRecruitingUpdateFromH5RequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvVehRecruitingUpdateFromH5ResponseType.class);
    }

    @Override
    public WorkbenchSupplierGraySOAResponseType workbenchSupplierGray(WorkbenchSupplierGraySOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, WorkbenchSupplierGraySOAResponseType.class);
    }

    @Override
    public SaveRecruitingOperationLogSOAResponseType saveRecruitingOperationLog(SaveRecruitingOperationLogSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, SaveRecruitingOperationLogSOAResponseType.class);
    }

    @Override
    public QueryApplyTransGroupsSkuForDspResponseType queryApplyTransGroupsSkuForDsp(QueryApplyTransGroupsSkuForDspRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryApplyTransGroupsSkuForDspResponseType.class);
    }

    @Override
    public QueryApproveStepRecordSOAResponseType queryApproveStepRecord(QueryApproveStepRecordSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryApproveStepRecordSOAResponseType.class);
    }

    @Override
    public QuerySupplierByApproveIdSOAResponseType querySupplierByApproveId(QuerySupplierByApproveIdSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QuerySupplierByApproveIdSOAResponseType.class);
    }

    @Override
    public WorkBenchFourthGraySOAResponseType workBenchFourthGray(WorkBenchFourthGraySOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, WorkBenchFourthGraySOAResponseType.class);
    }

    @Override
    public QueryVehicleRecruitingSupplierIdResponseType queryVehicleRecruitingSupplierId(QueryVehicleRecruitingSupplierIdRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryVehicleRecruitingSupplierIdResponseType.class);
    }

    @Override
    public QueryDriverRecruitingSupplierIdResponseType queryDriverRecruitingSupplierId(QueryDriverRecruitingSupplierIdRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDriverRecruitingSupplierIdResponseType.class);
    }

    @Override
    public QueryDriver4BaseSOAResponseType queryDriver4Base(QueryDriver4BaseSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDriver4BaseSOAResponseType.class);
    }

    @Override
    public QueryNetCertNoCheckRuleSOAResponseType queryNetCertNoCheckRule(QueryNetCertNoCheckRuleSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryNetCertNoCheckRuleSOAResponseType.class);
    }

    @Override
    public CheckDriverNetCertNoSOAResponseType checkDriverNetCertNo(CheckDriverNetCertNoSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, CheckDriverNetCertNoSOAResponseType.class);
    }

    @Override
    public CheckVehicleNetCertNoSOAResponseType checkVehicleNetCertNo(CheckVehicleNetCertNoSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, CheckVehicleNetCertNoSOAResponseType.class);
    }

    @Override
    public DiscardDrvSOAResponseType discardDrv(DiscardDrvSOARequestType request) throws Exception {
        ResponseResult result = new ResponseResult();
        result.setReturnCode(ErrorCodeEnum.TRANSPORT_UN_SUPPORT_DRIVER_DISCARD.getCode());
        result.setReturnMessage(SharkUtils.getSharkValue(ErrorCodeEnum.TRANSPORT_UN_SUPPORT_DRIVER_DISCARD.getMessage()));
        result.setSuccess(false);
        DiscardDrvSOAResponseType responseType = new DiscardDrvSOAResponseType();
        responseType.setResponseResult(result);
        return responseType;
//        return ServiceExecutors.execute(request, DiscardDrvSOAResponseType.class);
    }

    @Override
    public DiscardRecruitingDrvSOAResponseType discardRecruitingDrv(DiscardRecruitingDrvSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DiscardRecruitingDrvSOAResponseType.class);
    }

    @Override
    public DiscardRecruitingVehSOAResponseType discardRecruitingVeh(DiscardRecruitingVehSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DiscardRecruitingVehSOAResponseType.class);
    }

    @Override
    public DrvPreCheckResponseType drvPreCheck(DrvPreCheckRequestType request) throws Exception {
        return  ServiceExecutors.execute(request, DrvPreCheckResponseType.class);
    }

    @Override
    public DiscardVehSOAResponseType discardVeh(DiscardVehSOARequestType request) throws Exception {
        DiscardVehSOAResponseType responseType = new DiscardVehSOAResponseType();
        ResponseResult result = new ResponseResult();
        result.setReturnCode(ErrorCodeEnum.TRANSPORT_UN_SUPPORT_VEHICLE_DISCARD.getCode());
        result.setReturnMessage(SharkUtils.getSharkValue(ErrorCodeEnum.TRANSPORT_UN_SUPPORT_VEHICLE_DISCARD.getMessage()));
        result.setSuccess(false);
        responseType.setResponseResult(result);
        return responseType;
//        return ServiceExecutors.execute(request, DiscardVehSOAResponseType.class);
    }

    @Override
    public QueryHistoryDrvDataResponseType queryHistoryDrvData(QueryHistoryDrvDataRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryHistoryDrvDataResponseType.class);
    }

    @Override
    public AddHistoryDrvDataResponseType addHistoryDrvData(AddHistoryDrvDataRequestType request) throws Exception {
        return ServiceExecutors.execute(request, AddHistoryDrvDataResponseType.class);
    }

    @Override
    public DrvUnfreezeConfirmOnlineSOAResponseType drvUnfreezeConfirmOnline(DrvUnfreezeConfirmOnlineSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvUnfreezeConfirmOnlineSOAResponseType.class);
    }

    @Override
    public QueryTransportGroupTakeOrderTimeScopeResponseType queryTransportGroupTakeOrderTimeScope(QueryTransportGroupTakeOrderTimeScopeRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryTransportGroupTakeOrderTimeScopeResponseType.class);
    }

    @Override
    public QueryEffCapacityVehicleTypeSOAResponseType queryEffCapacityVehicleType(QueryEffCapacityVehicleTypeSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryEffCapacityVehicleTypeSOAResponseType.class);
    }

    @Override
    public QueryResidualInventorySOAResponseType queryResidualInventory(QueryResidualInventorySOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryResidualInventorySOAResponseType.class);
    }

    @Override
    public QueryInventoryCapacitySOAResponseType queryInventoryCapacity(QueryInventoryCapacitySOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryInventoryCapacitySOAResponseType.class);
    }

    @Override
    public QueryCapacityAnalyseSOAResponseType queryCapacityAnalyse(QueryCapacityAnalyseSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryCapacityAnalyseSOAResponseType.class);
    }

    @Override
    public QueryDriverIdCountSOAResponseType queryDriverIdCount(QueryDriverIdCountSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDriverIdCountSOAResponseType.class);
    }

    @Override
    public QueryDriverIdSOAResponseType queryDriverId(QueryDriverIdSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDriverIdSOAResponseType.class);
    }

    @Override
    public QueryDrvDispatchListSOAResponseType queryDrvDispatchList(QueryDrvDispatchListSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDrvDispatchListSOAResponseType.class);
    }

    @Override
    public DrvDispatchRelationUpdateSOAResponseType drvDispatchRelationUpdate(DrvDispatchRelationUpdateSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvDispatchRelationUpdateSOAResponseType.class);
    }

    @Override
    public VBKDrvDispatchBindingSOAResponseType vbkDrvDispatchBinding(VBKDrvDispatchBindingSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, VBKDrvDispatchBindingSOAResponseType.class);
    }

    @Override
    public DrvDispatchCityGraySOAResponseType drvDispatchCityGray(DrvDispatchCityGraySOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvDispatchCityGraySOAResponseType.class);
    }

    @Override
    public QueryTransportGroupInOrderConfigResponseType queryTransportGroupInOrderConfig(QueryTransportGroupInOrderConfigRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryTransportGroupInOrderConfigResponseType.class);
    }

    @Override
    public QueryDriverLeaveByIdResponseType queryDriverLeaveById(QueryDriverLeaveByIdRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDriverLeaveByIdResponseType.class);
    }
    @Override
    public QueryRecruitingDrvForPenaltyResponseType queryRecruitingDrvForPenalty(QueryRecruitingDrvForPenaltyRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryRecruitingDrvForPenaltyResponseType.class);
    }

    @Override
    public PropertyAddTagSOAResponseType propertyAddTag(PropertyAddTagSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, PropertyAddTagSOAResponseType.class);
    }

    @Override
    public PropertyUpdateTagSOAResponseType propertyUpdateTag(PropertyUpdateTagSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, PropertyUpdateTagSOAResponseType.class);
    }

    @Override
    public QueryDriverVehicleInfoSOAResponseType queryDriverVehicleInfo(QueryDriverVehicleInfoSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDriverVehicleInfoSOAResponseType.class);
    }

    @Override
    public QueryCommonEnumResponseType queryCommonEnum(QueryCommonEnumRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryCommonEnumResponseType.class);
    }

    @Override
    public QueryTransportInformResponseType queryTransportInform(QueryTransportInformRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryTransportInformResponseType.class);
    }

    @Override
    public OverseasOCRRecognitionSOAResponseType overseasOCRRecognition(OverseasOCRRecognitionSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, OverseasOCRRecognitionSOAResponseType.class);
    }

    @Override
    public QueryDrvVehLegendListSOAResponseType queryDrvVehLegendList(QueryDrvVehLegendListSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDrvVehLegendListSOAResponseType.class);
    }

    @Override
    public QuerySupplierConfigInfoSOAResponseType querySupplierConfigInfo(QuerySupplierConfigInfoSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QuerySupplierConfigInfoSOAResponseType.class);
    }

    @Override
    public SaveOverseasTagsSOAResponseType saveOverseasTags(SaveOverseasTagsSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, SaveOverseasTagsSOAResponseType.class);
    }

    @Override
    public QueryDrvIdByTransportGroupsResponseType queryDrvIdByTransportGroups(QueryDrvIdByTransportGroupsRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDrvIdByTransportGroupsResponseType.class);
    }

    @Override
    public QueryVehRecognitionStatusResponseType queryVehRecognitionStatus(QueryVehRecognitionStatusRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryVehRecognitionStatusResponseType.class);
    }

    @Override
    public QuerySupplierIdBySkuIdResponseType querySupplierIdBySkuId(QuerySupplierIdBySkuIdRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QuerySupplierIdBySkuIdResponseType.class);
    }

    @Override
    public QueryDiscardDrvListSOAResponseType queryDiscardDrvList(QueryDiscardDrvListSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDiscardDrvListSOAResponseType.class);
    }

    @Override
    public QueryDiscardVehicleListSOAResponseType queryDiscardVehicleList(QueryDiscardVehicleListSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryDiscardVehicleListSOAResponseType.class);
    }

    @Override
    public TemporaryDispatchDrvAddSOAResponseType temporaryDispatchDrvAdd(TemporaryDispatchDrvAddSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, TemporaryDispatchDrvAddSOAResponseType.class);
    }

    @Override
    public TemporaryDispatchVehicleAddSOAResponseType temporaryDispatchVehicleAdd(TemporaryDispatchVehicleAddSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, TemporaryDispatchVehicleAddSOAResponseType.class);
    }

    @Override
    public TemporaryDrvVehAddSOAResponseType temporaryDrvVehAdd(TemporaryDrvVehAddSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, TemporaryDrvVehAddSOAResponseType.class);
    }

    @Override
    public QueryOverseasDrvVehOCRResResponseType queryOverseasDrvVehOCRRes(QueryOverseasDrvVehOCRResRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryOverseasDrvVehOCRResResponseType.class);
    }

    @Override
    public SendVoiceCodeResponseType sendVoiceCode(SendVoiceCodeRequestType request) throws Exception {
        return ServiceExecutors.execute(request, SendVoiceCodeResponseType.class);
    }

    @Override
    public SendIVRVoiceResponseType sendIVRVoice(SendIVRVoiceRequestType request) throws Exception {
        return ServiceExecutors.execute(request, SendIVRVoiceResponseType.class);
    }

    @Override
    public GetIVRVoiceResultResponseType getIVRVoiceResult(GetIVRVoiceResultRequestType request) throws Exception {
        return ServiceExecutors.execute(request, GetIVRVoiceResultResponseType.class);
    }

    @Override
    public MobileValidResponseType mobileValid(MobileValidRequestType request) throws Exception {
        return ServiceExecutors.execute(request, MobileValidResponseType.class);
    }

    @Override
    public QueryDriverIdForSaasResponseType queryDriverIdForSaas(QueryDriverIdForSaasRequestType queryDriverIdForSaasRequestType) throws Exception {
        return ServiceExecutors.execute(queryDriverIdForSaasRequestType, QueryDriverIdForSaasResponseType.class);
    }

    @Override
    public QueryVehicleIdForSaasResponseType queryVehicleIdForSaas(QueryVehicleIdForSaasRequestType queryVehicleIdForSaasRequestType) throws Exception {
        return ServiceExecutors.execute(queryVehicleIdForSaasRequestType, QueryVehicleIdForSaasResponseType.class);
    }

    @Override
    public QueryDriverForSaasResponseType queryDriverForSaas(QueryDriverForSaasRequestType queryDriverForSaasRequestType) throws Exception {
        return ServiceExecutors.execute(queryDriverForSaasRequestType, QueryDriverForSaasResponseType.class);
    }

    @Override
    public QueryVehicleForSaasResponseType queryVehicleForSaas(QueryVehicleForSaasRequestType queryVehicleForSaasRequestType) throws Exception {
        return ServiceExecutors.execute(queryVehicleForSaasRequestType, QueryVehicleForSaasResponseType.class);
    }

    @Override
    public DrvAddPaiayAccountResponseType drvAddPaiayAccount(DrvAddPaiayAccountRequestType request) throws Exception {
        return ServiceExecutors.execute(request, DrvAddPaiayAccountResponseType.class);
    }

    @Override
    public CreateDrvChangeEquipmentEventResponseType createDrvChangeEquipmentEvent(CreateDrvChangeEquipmentEventRequestType request) throws Exception {
        return ServiceExecutors.execute(request, CreateDrvChangeEquipmentEventResponseType.class);
    }

    @Override
    public GenerateVehicleGlobalIdResponseType generateVehicleGlobalId(
      GenerateVehicleGlobalIdRequestType generateVehicleGlobalIdRequestType) throws Exception {
        return ServiceExecutors.execute(generateVehicleGlobalIdRequestType, GenerateVehicleGlobalIdResponseType.class);
    }

    @Override
    public QueryCategoryListV2ResponseType queryCategoryListV2(
      QueryCategoryListV2RequestType queryCategoryListV2RequestType) throws Exception {
        return ServiceExecutors.execute(queryCategoryListV2RequestType, QueryCategoryListV2ResponseType.class);
    }

    @Override
    public PersonRecruitingAddSOAResponseType personRecruitingAdd(PersonRecruitingAddSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, PersonRecruitingAddSOAResponseType.class);
    }

    @Override
    public CheckPersonRecruitingAccountSOAResponseType checkPersonRecruitingAccount(CheckPersonRecruitingAccountSOARequestType request) throws Exception {
        return ServiceExecutors.execute(request, CheckPersonRecruitingAccountSOAResponseType.class);
    }

    @Override
    public IsSupplierInDayMigerationGrayProcessResponseType isSupplierInDayMigerationGrayProcess(
      IsSupplierInDayMigerationGrayProcessRequestType request)
      throws Exception {
        return ServiceExecutors.execute(request, IsSupplierInDayMigerationGrayProcessResponseType.class);
    }


    @Override
    public RequiredFieldResponseType requiredField(RequiredFieldRequestType request) throws Exception {
        return ServiceExecutors.execute(request, RequiredFieldResponseType.class);
    }

    @Override
    public OcrRecognitionResponseType ocrRecognition(OcrRecognitionRequestType request) throws Exception {
        return ServiceExecutors.execute(request, OcrRecognitionResponseType.class);
    }

    @Override
    public QueryRejectionReasonResponseType queryRejectionReason(QueryRejectionReasonRequestType request) throws Exception {
        return ServiceExecutors.execute(request, QueryRejectionReasonResponseType.class);
    }

    @Override
    public ShowTaxiLabelResponseType showTaxiLabel(ShowTaxiLabelRequestType request) throws Exception {
        return ServiceExecutors.execute(request, ShowTaxiLabelResponseType.class);
    }

    @Override
    public ComplianceRuleGrayCheckResponseType complianceRuleGrayCheck(ComplianceRuleGrayCheckRequestType request) throws Exception {
        return ServiceExecutors.execute(request, ComplianceRuleGrayCheckResponseType.class);
    }

    @Override
    public EditFieldAppearsTextResponseType editFieldAppearsText(EditFieldAppearsTextRequestType request) throws Exception {
        return ServiceExecutors.execute(request, EditFieldAppearsTextResponseType.class);
    }

    @Override
    public InternatSettleOCRGrayResponseType internatSettleOCRGray(InternatSettleOCRGrayRequestType request) throws Exception {
        return ServiceExecutors.execute(request, InternatSettleOCRGrayResponseType.class);
    }

}
