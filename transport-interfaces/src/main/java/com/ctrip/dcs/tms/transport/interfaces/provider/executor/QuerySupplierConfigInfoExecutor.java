package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QuerySupplierConfigInfoSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QuerySupplierConfigInfoSOAResponseType;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
　* @description: 境外新逻辑灰度
　* <AUTHOR>
　* @date 2023/6/8 15:59
*/
@Component
public class QuerySupplierConfigInfoExecutor extends AbstractRpcExecutor<QuerySupplierConfigInfoSOARequestType, QuerySupplierConfigInfoSOAResponseType> implements Validator<QuerySupplierConfigInfoSOARequestType> {

    @Autowired
    DriverQueryService driverQueryService;

    @Override
    public QuerySupplierConfigInfoSOAResponseType execute(QuerySupplierConfigInfoSOARequestType requestType) {
        QuerySupplierConfigInfoSOAResponseType responseType = new QuerySupplierConfigInfoSOAResponseType();
        Result<QuerySupplierConfigInfoSOAResponseType> result = driverQueryService.querySupplierConfigInfo(requestType.getSupplierId(),
          requestType.getCityId(), requestType.getUseCarTime());
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(result.getData());
        }
        return ServiceResponseUtils.fail(responseType);
    }

    @Override
    public void validate(AbstractValidator<QuerySupplierConfigInfoSOARequestType> validator) {
        validator.ruleFor("supplierId").notNull();
    }
}
