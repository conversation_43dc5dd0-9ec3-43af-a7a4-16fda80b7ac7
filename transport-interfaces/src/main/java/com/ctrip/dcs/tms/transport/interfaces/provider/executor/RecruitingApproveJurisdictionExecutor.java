package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import com.google.common.collect.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

@Component
public class RecruitingApproveJurisdictionExecutor extends AbstractRpcExecutor<RecruitingApproveJurisdictionSOARequestType, RecruitingApproveJurisdictionSOAResponseType> implements Validator<RecruitingApproveJurisdictionSOARequestType> {

    @Autowired
    private CommonCommandService commonCommandService;

    @Override
    public RecruitingApproveJurisdictionSOAResponseType execute(RecruitingApproveJurisdictionSOARequestType recruitingApproveSOARequestType) {
        RecruitingApproveJurisdictionSOAResponseType soaResponseType = new RecruitingApproveJurisdictionSOAResponseType();
        soaResponseType.setButtonJurisdictionList(commonCommandService.getAuthCode(
                getJurisdictionCode(commonCommandService.getJurisdictionMap(recruitingApproveSOARequestType.getRoleCode(),
                                recruitingApproveSOARequestType.getAreaScope(), recruitingApproveSOARequestType.getDrvFrom(),
                        recruitingApproveSOARequestType.getApproveStatus()))));
        return ServiceResponseUtils.success(soaResponseType);
    }

    private List<Integer> getJurisdictionCode(Map<String, Integer> map) {
        return Lists.newArrayList(map.values());
    }

    @Override
    public void validate(AbstractValidator<RecruitingApproveJurisdictionSOARequestType> validator) {
        validator.ruleFor("roleCode").notNull().notEmpty();
        validator.ruleFor("areaScope").notNull();
        validator.ruleFor("drvFrom").notNull();
        validator.ruleFor("approveStatus").notNull();
    }

}