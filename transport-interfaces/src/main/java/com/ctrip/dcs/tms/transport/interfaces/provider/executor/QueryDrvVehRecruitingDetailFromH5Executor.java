package com.ctrip.dcs.tms.transport.interfaces.provider.executor;


import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
　* @description: H5司机查询招募详情
　* <AUTHOR>
　* @date 2021/12/7 16:06
*/
@Component
public class QueryDrvVehRecruitingDetailFromH5Executor extends AbstractRpcExecutor<DrvVehRecruitingDetailFromH5RequestType, DrvVehRecruitingDetailFromH5ResponseType> implements Validator<DrvVehRecruitingDetailFromH5RequestType> {

    @Autowired
    private DrvVehRecruitingQueryService queryService;

    @Override
    public DrvVehRecruitingDetailFromH5ResponseType execute(DrvVehRecruitingDetailFromH5RequestType requestType) {
        DrvVehRecruitingDetailFromH5ResponseType responseType = new DrvVehRecruitingDetailFromH5ResponseType();
        Result<DrvVehRecruitingDetailSOADTO> result = queryService.queryDrvVehRecruitingDetailFromH5(requestType);
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType,"400",result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<DrvVehRecruitingDetailFromH5RequestType> validator) {
        validator.ruleFor("drvPhone").notNull();
    }
}
