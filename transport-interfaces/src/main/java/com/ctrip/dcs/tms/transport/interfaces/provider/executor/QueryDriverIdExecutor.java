package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryDriverIdSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDriverIdSOAResponseType;
import com.ctrip.dcs.tms.transport.api.resource.driver.DrvBase;
import com.ctrip.dcs.tms.transport.application.convert.DrvResourceConverter;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.igt.framework.infrastructure.exception.ServiceValidationException;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 查询司机id
 *
 * <AUTHOR> ZhangZhen
 * @create 2023/3/6 18:23
 */
@Component
public class QueryDriverIdExecutor extends AbstractRpcExecutor<QueryDriverIdSOARequestType, QueryDriverIdSOAResponseType> implements Validator<QueryDriverIdSOARequestType> {

    private final Integer PROTECT_PAGE_LIMIT = 200;

    @Autowired
    private DriverQueryService driverQueryService;

    @Autowired
    private DrvResourceConverter drvResourceConverter;

    @Override
    public QueryDriverIdSOAResponseType execute(QueryDriverIdSOARequestType req) {
        QueryDriverIdSOAResponseType responseType = new QueryDriverIdSOAResponseType();
        List<DrvBase> res = driverQueryService.queryDrvBaseResource(drvResourceConverter.convertCondition(req));
        if (CollectionUtils.isEmpty(res)) {
            return ServiceResponseUtils.success(responseType);
        }
        List<Long> resIdList = res.stream().map(DrvBase::getDrvId).collect(Collectors.toList());
        responseType.setDrvIdList(resIdList);
        responseType.setBoundaryDrvId(resIdList.get(resIdList.size() - 1));
        return ServiceResponseUtils.success(responseType);
    }

    @Override
    public void onExecuting(QueryDriverIdSOARequestType req) {
        if (CollectionUtils.isEmpty(req.getCountryIdList()) || req.getBoundaryDrvId() == null || req.getBoundaryDrvId() < 0L) {
            throw new ServiceValidationException("Missing required parameters");
        }
        if (req.getPageSize() == null || (req.getPageSize() <= 0 || req.getPageSize() > PROTECT_PAGE_LIMIT)) {
            throw new ServiceValidationException(String.format("invalid page size, It has to be less than or equal to %s", PROTECT_PAGE_LIMIT));
        }
    }

}