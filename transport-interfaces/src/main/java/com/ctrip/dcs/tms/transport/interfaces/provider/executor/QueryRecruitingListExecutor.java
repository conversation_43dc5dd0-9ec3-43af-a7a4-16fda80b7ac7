package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.common.base.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

@Component
public class QueryRecruitingListExecutor extends AbstractRpcExecutor<QueryRecruitingSOARequestType, QueryRecruitingSOAResponseType> implements Validator<QueryRecruitingSOARequestType> {

    @Autowired
    private RecruitingQueryService recruitingQueryService;

    @Override
    public QueryRecruitingSOAResponseType execute(QueryRecruitingSOARequestType queryRecruitingSOARequestType) {
        QueryRecruitingSOAResponseType soaResponseType = new QueryRecruitingSOAResponseType();
        PaginatorDTO paginator = queryRecruitingSOARequestType.getData().getPaginator();
        Result<PageHolder<RecruitingSOAResponseDTO>> pageHolderResult;
        try {
            pageHolderResult = recruitingQueryService.queryRecruitingSOAList(queryRecruitingSOARequestType.getData(), paginator);
        } catch (Exception e) {
            return ServiceResponseUtils.fail(soaResponseType);
        }
        if (pageHolderResult == null || pageHolderResult.getData() == null) {
            return ServiceResponseUtils.fail(soaResponseType);
        }
        PaginationDTO paginationDTO = ServiceResponseUtils.newPagination(pageHolderResult.getData().getPageIndex(),
                pageHolderResult.getData().getPageSize(), pageHolderResult.getData().getTotalSize());
        soaResponseType.setPagination(paginationDTO);
        soaResponseType.setData(pageHolderResult.getData().getData());
        return ServiceResponseUtils.success(soaResponseType);
    }

    @Override
    public void validate(AbstractValidator<QueryRecruitingSOARequestType> validator) {
        validator.ruleFor("data").notNull();
    }

}