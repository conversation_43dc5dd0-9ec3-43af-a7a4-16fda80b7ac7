package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryDrvIdByTransportGroupsRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvIdByTransportGroupsResponseType;
import com.ctrip.dcs.tms.transport.interfaces.bridge.ProductLineBridgeManagement;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 根据上线运力组id查询关联的司机id
 *
 * <AUTHOR> ZhangZhen
 * @create 2023/8/7 16:09
 */
@Component
public class QueryDrvIdByTransportGroupsExecutor extends AbstractRpcExecutor<QueryDrvIdByTransportGroupsRequestType, QueryDrvIdByTransportGroupsResponseType> implements Validator<QueryDrvIdByTransportGroupsRequestType> {

    @Autowired
    ProductLineBridgeManagement productLineBridgeManagement;

    @Override
    public QueryDrvIdByTransportGroupsResponseType execute(QueryDrvIdByTransportGroupsRequestType req) {
        return productLineBridgeManagement.queryDrvIdByTransportGroups(req);
    }

}
