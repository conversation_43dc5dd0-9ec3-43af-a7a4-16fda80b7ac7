package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctrip.platform.dal.exceptions.DalException;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 编辑司机
 */
@Component
public class UpdateTransportApproveStatusExecutor extends AbstractRpcExecutor<TransportApproveStatusUpdateSOARequestType, TransportApproveStatusUpdateSOAResponseType> implements Validator<TransportApproveStatusUpdateSOARequestType> {

    private static final Logger logger = LoggerFactory.getLogger(UpdateTransportApproveStatusExecutor.class);


    @Autowired
    private TmsTransportApproveCommandService commandService;

    @Override
    public TransportApproveStatusUpdateSOAResponseType execute(TransportApproveStatusUpdateSOARequestType requestType) {
        try{
            TransportApproveStatusUpdateSOAResponseType responseType = new TransportApproveStatusUpdateSOAResponseType();
            Result<Boolean> result = commandService.updateApproveStatus(requestType);
            if (result.isSuccess()) {
                return ServiceResponseUtils.success(responseType);
            }else {
                return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
            }
        }catch (Exception e) {
            if (e instanceof DalException) {
                if (e.getCause() instanceof BizException) {
                    throw (BizException) e.getCause();
                }
            }
            if (e instanceof BizException) {
                throw (BizException) e;
            }
            throw e;
        }
    }
}
