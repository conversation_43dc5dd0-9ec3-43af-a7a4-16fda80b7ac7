package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import com.fasterxml.jackson.core.type.*;
import com.google.common.collect.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 *修改司机住址
 * <AUTHOR>
 * @Date 2020/03/06
 */
@Component
public class UpdateDrvAddrExecutor extends AbstractRpcExecutor<DrvUpdateAddrRequestType, DrvUpdateAddrResponseType> implements Validator<DrvUpdateAddrRequestType> {

    private static final Logger logger = LoggerFactory.getLogger(UpdateDrvAddrExecutor.class);

    @Autowired
    private TmsQmqProducerCommandService tmsQmqProducerCommandService;
    @Autowired
    private DrvDrvierRepository drvDrvierRepository;

    @Autowired
    DriverQueryService driverQueryService;

    @Override
    public DrvUpdateAddrResponseType execute(DrvUpdateAddrRequestType drvUpdateAddrRequestType) {
        DrvUpdateAddrResponseType responseType = new DrvUpdateAddrResponseType();
        try {
            DrvDriverPO drvDriverPO = drvDrvierRepository.queryByPk(drvUpdateAddrRequestType.getDrvId());
            if(drvDriverPO == null){
                return ServiceResponseUtils.fail(responseType);
            }
            Integer nowMonth = DateUtil.getMonth();
            // 修改前判断修改次数
            if(!judgeIsNeedMod(drvUpdateAddrRequestType.getDrvId(),nowMonth)){
                return ServiceResponseUtils.fail(responseType, SharkUtils.getSharkValue(SharkKeyConstant.drvAddrModCountUpperLimit));
            }
            int count = drvDrvierRepository.updateDrvAddr(drvUpdateAddrRequestType.getDrvId(),drvUpdateAddrRequestType.getDrvAddr(),this.getDrvAddrModCount(drvDriverPO.getAddrModCount(),nowMonth),drvDriverPO.getDrvName());
            if(count > 0){
                tmsQmqProducerCommandService.sendDrvChangeQmq(drvDriverPO.getDrvId(),2,1);
                return ServiceResponseUtils.success(responseType);
            }
        }catch (Exception e){
            logger.error("UpdateDrvAddrExecutor error", "drvId PARAMS:{}", drvUpdateAddrRequestType.getDrvId());
        }
        return ServiceResponseUtils.fail(responseType);
    }
    @Override
    public void validate(AbstractValidator<DrvUpdateAddrRequestType> validator){
        validator.ruleFor("drvId").notNull();
        validator.ruleFor("drvAddr").notNull();
    }

    //组装地址修改次数json
    public String getDrvAddrModCount(String nowAddrModCount,Integer nowMonth) {
        Map<Integer, Integer> monthModCountMap = Maps.newHashMap();
        Integer nowMothCount = 1;
        if (StringUtils.isNotEmpty(nowAddrModCount)) {
            monthModCountMap = JsonUtil.fromJson(nowAddrModCount, new TypeReference<Map<Integer, Integer>>() {
            });
            nowMothCount = monthModCountMap.get(nowMonth) + 1;
        }
        monthModCountMap = initModCount(nowMonth, nowMothCount);
        return JsonUtil.toJson(monthModCountMap);
    }

    //初始化地址修改次数
    public Map<Integer, Integer> initModCount(Integer nwoMonth, Integer nowMothCount) {
        Map<Integer, Integer> map = Maps.newHashMap();
        for (int i = 1; i <= 12; i++) {
            map.put(i, nwoMonth.intValue() == i ? nowMothCount : 0);
        }
        return map;
    }

    public Boolean judgeIsNeedMod(Long drvId,Integer nowMonth){
        QueryDrvAddrModCountSOARequestType soaRequestType = new QueryDrvAddrModCountSOARequestType();
        soaRequestType.setDrvId(drvId);
        soaRequestType.setMonth(nowMonth);
        Result<QueryDrvAddrModCountSOAResponseType> modCount =   driverQueryService.queryDrvAddrModCount(soaRequestType);
        if(!modCount.isSuccess()){
            return Boolean.FALSE;
        }
        QueryDrvAddrModCountSOAResponseType responseType = modCount.getData();
        if(responseType == null){
            return Boolean.FALSE;
        }
        return responseType.isIsModify();

    }
}
