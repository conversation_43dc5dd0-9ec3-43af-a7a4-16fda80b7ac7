package com.ctrip.dcs.tms.transport.interfaces.listener;

import com.ctrip.dcs.tms.transport.api.model.OverseasOCRRecognitionSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.OverseasOCRRecognitionSOAResponseType;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehVehiclePO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.AreaScopeTypeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CommonEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.RedisUtils;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;


@Component
public class OverseasDrvVehBatchOCRListener {
    private static final Logger logger = LoggerFactory.getLogger(OverseasDrvVehBatchOCRListener.class);

    @Autowired
    private DrvDrvierRepository repository;
    @Autowired
    private VehicleRepository vehicleRepository;
    @Resource
    private DriverQueryService driverQueryService;

    @Autowired
    private EnumRepository enumRepository;

    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.SUBJECT_OVERSEAS_DRVVEH_OCR, consumerGroup = TmsTransportConstant.QMQ_CONSUMER_GROUP)
    public void handle(Message message) {
        logger.info("OverseasDrvVehBatchOCRListener", JsonUtil.toJson(message));
        Long driverIdOrVehicleId = message.getLongProperty("id");
        Integer type = message.getIntProperty("type");
        if (driverIdOrVehicleId == null || driverIdOrVehicleId <= 0) {
            return;
        }
        if (type == null || type <= 0) {
            return;
        }
        switch (type) {
            case 1:
                drvOCRMethod(driverIdOrVehicleId);
                break;
            case 2:
                vehOCRMethod(driverIdOrVehicleId);
        }
    }


    //司机OCR
    public Boolean drvOCRMethod(Long drvId) {
        DrvDriverPO drvDriverPO = repository.queryByPk(drvId);
        if (Objects.isNull(drvDriverPO)) {
            return Boolean.FALSE;
        }
        //如果是境内司机或驾驶证为空，则不处理
        if (Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(), drvDriverPO.getInternalScope()) || StringUtils.isEmpty(drvDriverPO.getDrvcardImg())) {
            return Boolean.FALSE;
        }
        String drvKey = TmsTransportConstant.OVERSEAS_DRV_OCR_COMPARISON_RES + drvId;
        OverseasOCRRecognitionSOAResponseType responseType = getOCRRes(drvDriverPO.getDrvcardImg(), drvDriverPO.getCityId(), CommonEnum.OcrTypeEnum.Driver.getValue(), drvKey,drvId);
        if (responseType == null) {
            return Boolean.FALSE;
        }
        if (StringUtils.isEmpty(responseType.getDrvName()) || StringUtils.isEmpty(drvDriverPO.getDrvName())) {
            RedisUtils.set(drvKey, RedisUtils.ONE_WEEK, ocrRes(TmsTransportConstant.OverseasOCRPassStatusEnum.no_pass,drvId));
            return Boolean.FALSE;
        }
        //姓名比对
        if(Objects.equals(cleanValue(responseType.getDrvName()),cleanValue(drvDriverPO.getDrvName()))){
            RedisUtils.set(drvKey, RedisUtils.ONE_WEEK, ocrRes(TmsTransportConstant.OverseasOCRPassStatusEnum.pass,drvId));
        }else {
            RedisUtils.set(drvKey, RedisUtils.ONE_WEEK, ocrRes(TmsTransportConstant.OverseasOCRPassStatusEnum.no_pass,drvId));
        }
        return Boolean.TRUE;

    }

    //车辆OCR
    public Boolean vehOCRMethod(Long vehicleId) {
        VehVehiclePO vehVehiclePO = vehicleRepository.queryByPk(vehicleId);
        if (Objects.isNull(vehVehiclePO)) {
            return Boolean.FALSE;
        }
        String vehKey = TmsTransportConstant.OVERSEAS_VEH_OCR_COMPARISON_RES + vehicleId;
        //如果是境内车辆或车身图片为空，则不处理
        if (enumRepository.getAreaScope(vehVehiclePO.getCityId()) == AreaScopeTypeEnum.DOMESTIC.getCode().intValue() || StringUtils.isEmpty(vehVehiclePO.getVehicleFullImg())) {
            return Boolean.FALSE;
        }
        OverseasOCRRecognitionSOAResponseType responseType = getOCRRes(vehVehiclePO.getVehicleFullImg(), vehVehiclePO.getCityId(), CommonEnum.OcrTypeEnum.Car.getValue(), vehKey,vehicleId);

        if (responseType == null) {
            return Boolean.FALSE;
        }
        if (StringUtils.isEmpty(responseType.getVehicleColor()) && StringUtils.isEmpty(responseType.getVehicleLicense())) {
            RedisUtils.set(vehKey, RedisUtils.ONE_WEEK, ocrRes(TmsTransportConstant.OverseasOCRPassStatusEnum.no_pass,vehicleId));
            return Boolean.FALSE;
        }
        Map<String, Object> vehResult = ocrVehFailRes(TmsTransportConstant.OverseasOCRPassStatusEnum.no_pass,vehicleId);
        //比对车身颜色
        if(StringUtils.isNotEmpty(responseType.getVehicleColor())){
            String colorKey = enumRepository.getColorKey(vehVehiclePO.getVehicleColorId());
            if(Objects.equals(responseType.getVehicleColor(),colorKey)){
                vehResult.put("VehicleColor",TmsTransportConstant.OverseasOCRPassStatusEnum.pass.getCode());
            }
        }
        //车牌比对
        if (StringUtils.isNotEmpty(responseType.getVehicleLicense())) {
            Boolean boRes = isSubsequence(responseType.getVehicleLicense(),vehVehiclePO.getVehicleLicense());
            if(boRes){
                vehResult.put("VehicleLicense",TmsTransportConstant.OverseasOCRPassStatusEnum.pass.getCode());
            }
        }
        RedisUtils.set(vehKey, RedisUtils.ONE_WEEK, JsonUtil.toJson(vehResult));
        return Boolean.TRUE;
    }

    public OverseasOCRRecognitionSOAResponseType getOCRRes(String ocrImgUrl, Long cityId, Integer ocrType, String key,Long id) {
        OverseasOCRRecognitionSOARequestType soaRequestType = new OverseasOCRRecognitionSOARequestType();
        soaRequestType.setOcrType(ocrType);
        soaRequestType.setCityId(cityId);
        soaRequestType.setOcrImgUrl(ocrImgUrl);
        Result<OverseasOCRRecognitionSOAResponseType> soaResponse = driverQueryService.overseasOCRRecognition(soaRequestType);
        if (soaResponse == null) {
            RedisUtils.set(key, RedisUtils.ONE_WEEK, ocrRes(TmsTransportConstant.OverseasOCRPassStatusEnum.no_pass,id));
            return null;
        }
        if (!soaResponse.isSuccess()) {
            RedisUtils.set(key, RedisUtils.ONE_WEEK, ocrRes(TmsTransportConstant.OverseasOCRPassStatusEnum.no_pass,id));
            return null;
        }
        OverseasOCRRecognitionSOAResponseType soaResponseType = soaResponse.getData();
        if (soaResponseType == null) {
            RedisUtils.set(key, RedisUtils.ONE_WEEK, ocrRes(TmsTransportConstant.OverseasOCRPassStatusEnum.no_pass,id));
            return null;
        }
        return soaResponseType;

    }

    //OCR服务失败，默认比如结果不通过
    public  String ocrRes(TmsTransportConstant.OverseasOCRPassStatusEnum statusEnum,Long id) {
        Map<String, Object> failResult = Maps.newHashMap();
        failResult.put("passStatus", statusEnum.getCode());
        failResult.put("id", id);
        return JsonUtil.toJson(failResult);
    }

    public  Map<String, Object> ocrVehFailRes(TmsTransportConstant.OverseasOCRPassStatusEnum statusEnum,Long id) {
        Map<String, Object> failResult = Maps.newHashMap();
        failResult.put("VehicleColor", statusEnum.getCode());
        failResult.put("VehicleLicense", statusEnum.getCode());
        failResult.put("id", id);
        return failResult;
    }

    public static String cleanValue(String value){
        String regex = "[\\s,，.。\\(\\)][^\\(\\)\\.,，。]*\\)|\\s|\\.|\\,";
        return value.replaceAll(regex,"").toUpperCase();
    }

    public static boolean isSubsequence(String ocrVal, String dataVal) {
        int i = 0; // A 字符串的指针
        int j = 0; // B 字符串的指针

        while (i < ocrVal.length() && j < dataVal.length()) {
            if (ocrVal.charAt(i) == dataVal.charAt(j)) {
                i++; // A 字符串的指针向前移动
            }
            j++; // B 字符串的指针向前移动
        }
        // 如果 A 字符串的指针移动到末尾，说明 A 中的每个字符都按顺序出现在 B 中
        return i == ocrVal.length();
    }
}
