package com.ctrip.dcs.tms.transport.interfaces.listener;

import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.google.common.base.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import qunar.tc.qmq.*;
import qunar.tc.qmq.consumer.annotation.*;

import java.time.*;
import java.util.Objects;
import java.util.*;

/**
 * 司机自动解冻消息监听
 * <AUTHOR>
 * @Date 2020/3/16 16:04
 */
@Component
public class DrvUnFreezeListener {

    private static final Logger logger = LoggerFactory.getLogger(DrvUnFreezeListener.class);

    @Autowired
    private DrvDrvierRepository drvDrvierRepository;
    @Autowired
    private TmsTransportQconfig tmsTransportQconfig;
    @Autowired
    private TmsDrvFreezeCommandService freezeCommandService;
    @Autowired
    private TmsDrvFreezeRepository freezeRepository;

    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.SUBJECT_DRIVERSTATE_CHANGED,tagType = TagType.AND,tags = {TmsTransportConstant.QmqTag.TAG_DRIVERSTATE_UNFREEZE}, consumerGroup = TmsTransportConstant.QMQ_CONSUMER_GROUP+TmsTransportConstant.QmqTag.TAG_DRIVERSTATE_UNFREEZE)
    public void unfreezeDrv(Message message){
        logger.info("unfreezeDrv","message:{}", JsonUtil.toJson(message));
        String drvId = message.getStringProperty("drvId");
        String hours = message.getStringProperty("hours");
        if (Strings.isNullOrEmpty(drvId)) {
            return ;
        }
        //司机自动解冻开关,true:开,false:关
        if(tmsTransportQconfig.getDrvUnfreezeQmqSwitch()){
            return;
        }

        List<DrvDriverPO> drvDriverPOList = drvDrvierRepository.queryDrvList(Lists.newArrayList(Long.valueOf(drvId)));
        if (CollectionUtils.isEmpty(drvDriverPOList)) {
            return ;
        }
        for (DrvDriverPO drvDriverPO : drvDriverPOList) {
            if (!TmsTransportConstant.DrvStatusEnum.FREEZE.getCode().equals(drvDriverPO.getDrvStatus())) {
                return ;
            }
            if (Objects.isNull(drvDriverPO.getFreezeTime()) || Objects.isNull(drvDriverPO.getFreezeHour())) {
                continue;
            }
            LocalDateTime end = LocalDateTime.ofInstant(drvDriverPO.getFreezeTime().toInstant(), ZoneId.systemDefault()).plusHours(drvDriverPO.getFreezeHour());
            LocalDateTime now = LocalDateTime.now();
            Duration duration = Duration.between(end,now);
            long minutes = duration.toMinutes();
            if (minutes < 0){
                return ;
            }
            //兼容新冻结
            TmsDrvFreezePO tmsDrvFreezePO = freezeRepository.queryByPk(Long.valueOf(drvId));
            if(Objects.nonNull(tmsDrvFreezePO)){
                freezeCommandService.execUnfreezeDrv(tmsDrvFreezePO,drvDriverPO, StringUtils.isEmpty(hours)?-1:Integer.parseInt(hours),true);
            }
        }
        drvDrvierRepository.updateDrvStatus(Lists.newArrayList(Long.valueOf(drvId)),TmsTransportConstant.DrvStatusEnum.ONLINE.getCode(),"system");
    }

}