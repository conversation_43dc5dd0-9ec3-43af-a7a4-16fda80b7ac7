package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
　* @description: 查询单项审批操作记录
　* <AUTHOR>
　* @date 2022/4/20 10:52
*/
@Component
public class QueryApproveStepRecordExecutor extends AbstractRpcExecutor<QueryApproveStepRecordSOARequestType, QueryApproveStepRecordSOAResponseType> implements Validator<QueryApproveStepRecordSOARequestType> {

    @Autowired
    private RecruitingQueryService recruitingQueryService;

    @Override
    public QueryApproveStepRecordSOAResponseType execute(QueryApproveStepRecordSOARequestType requestType) {
        QueryApproveStepRecordSOAResponseType responseType = new QueryApproveStepRecordSOAResponseType();
        Result<List<QueryApproveStepRecordSOADTO>> result = recruitingQueryService.queryApproveStepRecord(requestType);
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType);
    }

    @Override
    public void validate(AbstractValidator<QueryApproveStepRecordSOARequestType> validator) {
        validator.ruleFor("approveSourceId").notNull();
        validator.ruleFor("approveType").notNull();
    }
}