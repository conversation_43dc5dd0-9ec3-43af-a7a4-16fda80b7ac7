package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.TemporaryDispatchDrvAddSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.TemporaryDispatchDrvAddSOAResponseType;
import com.ctrip.dcs.tms.transport.application.command.DriverCommandService;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.platform.dal.exceptions.DalException;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
　* @description: 新增临派司机
　* <AUTHOR>
　* @date 2023/10/9 11:12
*/
@Component
@ServiceLogTag(tagKeys = {"drvPhone","supplierId"})
public class TemporaryDispatchDrvAddExecutor extends AbstractRpcExecutor<TemporaryDispatchDrvAddSOARequestType, TemporaryDispatchDrvAddSOAResponseType> implements Validator<TemporaryDispatchDrvAddSOARequestType> {

    @Autowired
    private DriverCommandService driverCommandService;

    @Override
    public TemporaryDispatchDrvAddSOAResponseType execute(TemporaryDispatchDrvAddSOARequestType requestType) {
        try {
            TemporaryDispatchDrvAddSOAResponseType responseType = new TemporaryDispatchDrvAddSOAResponseType();
            Result<Long> result = driverCommandService.temporaryDispatchDrvAdd(requestType);
            if (result.isSuccess()) {
                responseType.setData(result.getData());
                return ServiceResponseUtils.success(responseType);
            } else {
                return ServiceResponseUtils.fail(responseType,result.getCode(),result.getMsg());
            }
        }catch (Exception e) {
            if (e instanceof DalException) {
                if (e.getCause() instanceof BizException) {
                    throw (BizException) e.getCause();
                }
            }
            throw e;
        }
    }

    @Override
    public void validate(AbstractValidator<TemporaryDispatchDrvAddSOARequestType> validator) {
        validator.ruleFor("supplierId").notNull();
        validator.ruleFor("drvName").notNull();
        validator.ruleFor("cityId").notNull();
        validator.ruleFor("drvPhone").notNull();
        validator.ruleFor("igtCode").notNull();
    }

}
