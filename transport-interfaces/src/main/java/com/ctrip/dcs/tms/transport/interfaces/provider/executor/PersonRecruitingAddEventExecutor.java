package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.PersonRecruitingAddSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.PersonRecruitingAddSOAResponseType;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.PersonRecruitingPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.PersonRecruitingRepository;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/***
　* @description: 司机加盟
　* <AUTHOR>
　* @date 2024/6/4 15:14
*/
@Component
public class PersonRecruitingAddEventExecutor extends AbstractRpcExecutor<PersonRecruitingAddSOARequestType, PersonRecruitingAddSOAResponseType> implements Validator<PersonRecruitingAddSOARequestType> {

    @Autowired
    private PersonRecruitingRepository personRecruitingRepository;

    @Override
    public PersonRecruitingAddSOAResponseType execute(PersonRecruitingAddSOARequestType requestType) {
        PersonRecruitingAddSOAResponseType responseType = new PersonRecruitingAddSOAResponseType();
        try {
            Long result = personRecruitingRepository.insert(toBean(requestType));
            if (result > 0) {
                return ServiceResponseUtils.success(responseType);
            }
            return ServiceResponseUtils.fail(responseType);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    public PersonRecruitingPO toBean(PersonRecruitingAddSOARequestType requestType){
        PersonRecruitingPO personRecruitingPO = new PersonRecruitingPO();
        BeanUtils.copyProperties(requestType,personRecruitingPO);
        if(StringUtils.isNotEmpty(requestType.getCreateTime()) && StringUtils.isNotEmpty(requestType.getUpdateTime())){
            personRecruitingPO.setCreateTime(DateUtil.string2Timestamp(requestType.getCreateTime(),DateUtil.YYYYMMDDHHMMSS));
            personRecruitingPO.setUpdateTime(DateUtil.string2Timestamp(requestType.getUpdateTime(),DateUtil.YYYYMMDDHHMMSS));
        }
        return personRecruitingPO;
    }

    @Override
    public void validate(AbstractValidator<PersonRecruitingAddSOARequestType> validator) {
    }

}