package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.regulation.*;
import com.ctrip.dcs.tms.transport.application.convert.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import com.google.common.base.*;
import org.springframework.stereotype.*;

import javax.annotation.*;
import java.util.*;

/**
 * <AUTHOR>
 * 提供查询历史司机数据接口
 *
 */
@Component
public class QueryHistoryDrvDataExecutor extends AbstractRpcExecutor<QueryHistoryDrvDataRequestType, QueryHistoryDrvDataResponseType> implements Validator<QueryHistoryDrvDataRequestType> {

    @Resource
    private DriverQueryService driverQueryService;

    /**
     * 没有流量也不能删除
     * @param req
     * @return
     */
    @Override
    public QueryHistoryDrvDataResponseType execute(QueryHistoryDrvDataRequestType req) {
        QueryHistoryDrvDataResponseType responseType = new QueryHistoryDrvDataResponseType();
        List<OldDriverInfo> res = driverQueryService.queryHistoryDrvResource(HistoryDrvResourceConverter.convertCondition(req));
        responseType.setInfoList(res);
        return ServiceResponseUtils.success(responseType);
    }

    @Override
    public void validate(AbstractValidator<QueryHistoryDrvDataRequestType> validator, QueryHistoryDrvDataRequestType req) {
        validator.ruleFor("drvId").notNull().greaterThan(0L).when(Strings.isNullOrEmpty(req.getDrvPhone()));
        validator.ruleFor("drvPhone").notNull().notEmpty().when(req.getDrvId() == null);
    }

}
