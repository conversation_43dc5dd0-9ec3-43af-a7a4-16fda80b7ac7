package com.ctrip.dcs.tms.transport.interfaces.bridge;

import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvIdByTransportGroupsRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvIdByTransportGroupsResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryVehicleBaseSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryVehicleBaseSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.ResetDrvPwdResponseType;
import com.ctrip.dcs.tms.transport.api.model.VehicleDetailSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.VehicleDetailSOAResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverForSaasResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverIdForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverIdForSaasResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleForSaasResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleIdForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleIdForSaasResponseType;
import com.ctrip.dcs.tms.transport.application.query.driverguide.DriverGuidQueryProcessService;
import com.ctrip.dcs.tms.transport.application.query.impl.DriverCacheAgentImpl;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model.DriverGuideDTO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model.VehVehicleDTO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehVehiclePO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DriverGuidDriverRequestDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.TmsTransUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportGroupRepository;
import com.ctrip.dcs.tms.transport.interfaces.bridge.executorservice.VehicleBaseListExecutorService;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EVENT_FAILED;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventType.BRIDGE;
import static com.ctrip.dcs.tms.transport.interfaces.bridge.converter.BridgeConverter.buildToDriverGuidDriverRequestDTO;
import static com.ctrip.dcs.tms.transport.interfaces.bridge.converter.BridgeConverter.buildToDriverGuidDriverRequestDTOForQueryDrvDetail;
import static com.ctrip.dcs.tms.transport.interfaces.bridge.converter.BridgeConverter.convert2DriverGuidDriverRequestDTO;
import static com.ctrip.dcs.tms.transport.interfaces.bridge.converter.BridgeConverter.convert2DriverGuidVehicleRequestDTO;
import static com.ctrip.dcs.tms.transport.interfaces.bridge.converter.BridgeConverter.convert2DriverGuidVehicleRequestDTO4QueryVehicleDetail;
import static com.ctrip.dcs.tms.transport.interfaces.bridge.converter.BridgeConverter.convert2DriverInfo;
import static com.ctrip.dcs.tms.transport.interfaces.bridge.converter.BridgeConverter.convert2QueryDriverDetailResp;
import static com.ctrip.dcs.tms.transport.interfaces.bridge.converter.BridgeConverter.convert2QueryDriverForSaasRequestDTO;
import static com.ctrip.dcs.tms.transport.interfaces.bridge.converter.BridgeConverter.convert2QueryDriverForSaasRspDTO;
import static com.ctrip.dcs.tms.transport.interfaces.bridge.converter.BridgeConverter.convert2QueryDriverIdForSaas;
import static com.ctrip.dcs.tms.transport.interfaces.bridge.converter.BridgeConverter.convert2QueryVehicleForSaasRequestDTO;
import static com.ctrip.dcs.tms.transport.interfaces.bridge.converter.BridgeConverter.convert2QueryVehicleForSaasRsp;
import static com.ctrip.dcs.tms.transport.interfaces.bridge.converter.BridgeConverter.convert2QueryVehicleIdForSaas;
import static com.ctrip.dcs.tms.transport.interfaces.bridge.converter.BridgeConverter.convert2QueryVehicleIdForSaasDTO;

/**
 * 司导域桥接服务
 * <AUTHOR>
 */
@Service
public class DriverGuideBridgeService implements ProductLineBridge {

  private static final Logger logger = LoggerFactory.getLogger(DriverGuideBridgeService.class);

  @Autowired
  DriverGuidQueryProcessService driverGuidQueryProcessService;

  @Autowired
  VehicleBaseListExecutorService vehicleBaseListExecutor;

  @Autowired
  TransportGroupRepository transportGroupRepository;

  @Autowired
  DriverCacheAgentImpl driverCacheAgent;

  public DriverInfoSOAResponseType queryDriver(DriverInfoSOARequestType request) {
    logger.info("queryDriver", "request: {}", request);
    if (Objects.nonNull(request.getSupplierId()) && !driverGuidQueryProcessService.getGrayControl(request.getSupplierId())) {
      logger.info("queryDriver-not-in-gray", "supplier: {}", request.getSupplierId());
      return ServiceResponseUtils.success(new DriverInfoSOAResponseType());
    }

    List<DriverInfo> driverInfoList =
      convert2DriverInfo(decrypt(driverGuidQueryProcessService.queryDriverInfo(buildToDriverGuidDriverRequestDTO(request))));
    DriverInfoSOAResponseType responseType = new DriverInfoSOAResponseType();
    driverCacheAgent.toDealDefault(driverInfoList);
    responseType.setDriverList(driverInfoList);
    DriverInfoSOAResponseType response = ServiceResponseUtils.success(responseType);
    logger.info("queryDriver", "response: {}", response);
    return response;
  }

  @Override
  public QueryDrvDetailSOAResponseType queryDrvDetail(QueryDrvDetailSOARequestType request) {
    logger.info("queryDrvDetail", "request:{}", request);
    //  qunarAccout 不考虑，线上没有流量
    if (StringUtils.isNotBlank(request.getQunarAccout())) {
      Map<String, String> params = new HashMap<>();
      params.put("qunarAccout", request.getQunarAccout());
      params.put("drvId", Optional.ofNullable(request.getDrvId()).orElse(0L).toString());
      Cat.logEvent(BRIDGE, "queryDrvDetail", EVENT_FAILED, params);
      QueryDrvDetailSOAResponseType response =
        ServiceResponseUtils.fail(new QueryDrvDetailSOAResponseType(), "qunarAccout query not support by driver guide");
      logger.info("queryDrvDetail", "response:{}", response);
      return response;
    }

    if (Objects.nonNull(request.getSupplierId()) && !driverGuidQueryProcessService.getGrayControl(request.getSupplierId())) {
      logger.info("queryDrvDetail-not-in-gray", "supplier: {}", request.getSupplierId());
      return ServiceResponseUtils.success(new QueryDrvDetailSOAResponseType());
    }

    QueryDrvDetailSOAResponseType response = ServiceResponseUtils.success(
      convert2QueryDriverDetailResp(request.getDrvId(),
        decrypt(driverGuidQueryProcessService.queryDriverInfo(buildToDriverGuidDriverRequestDTOForQueryDrvDetail(request)))));
    logger.info("queryDrvDetail", "response:{}", response);
    return response;
  }

  protected List<DriverGuideDTO> decrypt(List<DriverGuideDTO> driverGuideDTOS) {
    if (CollectionUtils.isEmpty(driverGuideDTOS)) {
      return driverGuideDTOS;
    }
    Map<String, String> phoneDecryptMap = TmsTransUtil.batchDecryptFailedReturnOriginData(
      driverGuideDTOS.stream().map(DriverGuideDTO::getDriverPhone).filter(Objects::nonNull).collect(Collectors.toList()), KeyType.Phone);

    Map<String, String> emailDecryptMap = TmsTransUtil.batchDecryptFailedReturnOriginData(
      driverGuideDTOS.stream().map(DriverGuideDTO::getEmail).filter(Objects::nonNull).collect(Collectors.toList()), KeyType.Mail);

    for (DriverGuideDTO driverGuideDTO : driverGuideDTOS) {
      driverGuideDTO.setDrvPhone(Optional.ofNullable(phoneDecryptMap.get(driverGuideDTO.getDriverPhone())).orElse(driverGuideDTO.getDrvPhone()));
      driverGuideDTO.setEmail(Optional.ofNullable(emailDecryptMap.get(driverGuideDTO.getEmail())).orElse(driverGuideDTO.getEmail()));
    }
    return driverGuideDTOS;
  }

  @Override
  public QueryVehicleBaseSOAResponseType queryVehicleListBySupplierId(QueryVehicleBaseSOARequestType requestType) {
    logger.info("queryVehicleListBySupplierId", "request:{}", requestType);
    QueryVehicleBaseSOAResponseType responseType = new QueryVehicleBaseSOAResponseType();
    try {
      if (!driverGuidQueryProcessService.getGrayControl(requestType.getSupplierId())) {
        logger.info("queryVehicleListBySupplierId", "not in gray:{}", requestType);
        return ServiceResponseUtils.success(new QueryVehicleBaseSOAResponseType());
      }
      List<VehVehicleDTO> vehVehicleDtoS =
        driverGuidQueryProcessService.searchVehicleInfo(convert2DriverGuidVehicleRequestDTO(requestType));
      if (CollectionUtils.isEmpty(vehVehicleDtoS)) {
        QueryVehicleBaseSOAResponseType response = ServiceResponseUtils.success(responseType);
        logger.info("queryVehicleListBySupplierId", "response:{}", response);
        return response;
      }

      List<VehVehiclePO> vehiclePoList =
        vehVehicleDtoS.stream().map(vehVehicleDTO -> (VehVehiclePO) vehVehicleDTO).collect(Collectors.toList());

      //车型筛选，采购用
      if (requestType.getVehicleTypeId() != null) {
        responseType.setData(vehicleBaseListExecutor.getBaseList(vehicleBaseListExecutor.getResultData(requestType.getVehicleTypeId(),vehiclePoList,requestType.getCityId())));
        QueryVehicleBaseSOAResponseType responnse = ServiceResponseUtils.success(responseType);
        logger.info("queryVehicleListBySupplierId", "response:{}", responnse);
        return responnse;
      }
      responseType.setData(vehicleBaseListExecutor.getBaseList(vehiclePoList));
      QueryVehicleBaseSOAResponseType response = ServiceResponseUtils.success(responseType);
      logger.info("queryVehicleListBySupplierId", "response:{}", response);
      return response;
    } catch (Exception e) {
      throw new BaijiRuntimeException("VehicleBaseListExecutor error", e);
    }
  }

  /**
   * 1 先根据运力组查到对应的供应商
   * 2 根据供应商判断是否是灰度
   * 3 如果是灰度，查询司导下供应商下所有服务运力组对应对点位城市的司机
   * 4 TOD
   */
  @Override
  public QueryDrvIdByTransportGroupsResponseType queryDrvIdByTransportGroups(
    QueryDrvIdByTransportGroupsRequestType request) {
    logger.info("queryDrvIdByTransportGroups", "request: {}", request);
    // 包车场景有且仅有一个运力组
    if (CollectionUtils.isEmpty(request.getTransportGroupIdList())) {
      logger.warn("queryDrvIdByTransportGroups", "invalid size: {}", request);
      Map<String, String> params = new HashMap<>();
      params.put("transport", Optional.ofNullable(request.getTransportGroupIdList()).orElse(Lists.newArrayList()).toString());
      Cat.logEvent(BRIDGE, "queryDrvIdByTransportGroups", EVENT_FAILED, params);
      return ServiceResponseUtils.fail(new QueryDrvIdByTransportGroupsResponseType());
    }

    List<TspTransportGroupPO> tspTransportGroupPoS =
      transportGroupRepository.querySupplierId(request.getTransportGroupIdList());

    QueryDrvIdByTransportGroupsResponseType responseType = new QueryDrvIdByTransportGroupsResponseType();
    if (CollectionUtils.isEmpty(tspTransportGroupPoS)) {
      logger.warn("queryDrvIdByTransportGroups", "transport not found or not active: {}", request);
      return ServiceResponseUtils.success(responseType);
    }

    Set<Long> supplierIds = new HashSet<>();
    for (TspTransportGroupPO tspGroup : tspTransportGroupPoS) {
      supplierIds.add(tspGroup.getSupplierId());
    }

    //如果属于多个供应商，则直接不返回
    if (supplierIds.size() > 1) {
      logger.warn("queryDrvIdByTransportGroups", "invalid size: {}", supplierIds.size());
      Map<String, String> params = new HashMap<>();
      params.put("transport", Optional.ofNullable(request.getTransportGroupIdList()).orElse(Lists.newArrayList()).toString());
      Cat.logEvent(BRIDGE, "queryDrvIdByTransportGroups", EVENT_FAILED, params);
      return ServiceResponseUtils.fail(new QueryDrvIdByTransportGroupsResponseType());
    }

    if (!driverGuidQueryProcessService.getGrayControl(tspTransportGroupPoS.get(0).getSupplierId())) {
      logger.info("queryDrvIdByTransportGroups", "not in gray:{}", request);
      return ServiceResponseUtils.success(new QueryDrvIdByTransportGroupsResponseType());
    }

    // 包车只有一个运力组
    DriverGuidDriverRequestDTO driverGuidRequest = new DriverGuidDriverRequestDTO();
    driverGuidRequest.setSupplierId(tspTransportGroupPoS.get(0).getSupplierId());
    driverGuidRequest.setCityId(tspTransportGroupPoS.get(0).getPointCityId());
    driverGuidRequest.setStatus(TmsTransportConstant.DrvStatusEnum.ONLINE.getCode());
    responseType.setDrvIdList(driverGuidQueryProcessService.queryDrvIdByTransportGroups(driverGuidRequest));
    QueryDrvIdByTransportGroupsResponseType response = ServiceResponseUtils.success(responseType);
    logger.info("queryDrvIdByTransportGroups", "response: {}", response);
    return response;
  }

  @Override
  public VehicleDetailSOAResponseType queryVehicleDetail(VehicleDetailSOARequestType request) {
    logger.info("queryVehicleDetail", "request:{}", request);
    VehicleDetailSOAResponseType response = ServiceResponseUtils.success(
      convert2DriverGuidVehicleRequestDTO4QueryVehicleDetail(request.getVehicleId(),
        driverGuidQueryProcessService.queryVehicleInfo(
          convert2DriverGuidVehicleRequestDTO4QueryVehicleDetail(request))));
    logger.info("queryVehicleDetail", "response:{}", response);
    return response;
  }

  @Override
  public QueryVehicleIdForSaasResponseType queryVehicleIdForSaas(
    QueryVehicleIdForSaasRequestType queryVehicleIdForSaasRequestType) {
    logger.info("queryVehicleIdForSaas", "request:{}", queryVehicleIdForSaasRequestType);
    if (!driverGuidQueryProcessService.getGrayControl(queryVehicleIdForSaasRequestType.getSupplierId())) {
      logger.info("queryVehicleIdForSaas", "not in gray:{}", queryVehicleIdForSaasRequestType);
      return ServiceResponseUtils.success(new QueryVehicleIdForSaasResponseType());
    }
    QueryVehicleIdForSaasResponseType response = ServiceResponseUtils.success(convert2QueryVehicleIdForSaas(
      driverGuidQueryProcessService.queryVehicleInfo(
        convert2QueryVehicleIdForSaasDTO(queryVehicleIdForSaasRequestType))));
    logger.info("queryVehicleIdForSaas", "response:{}", response);
    return response;
  }

  @Override
  public QueryDriverForSaasResponseType queryDriverForSaas(
    QueryDriverForSaasRequestType queryDriverForSaasRequestType) {
    logger.info("queryDriverForSaas", "request:{}", queryDriverForSaasRequestType);
    QueryDriverForSaasResponseType response = ServiceResponseUtils.success(convert2QueryDriverForSaasRspDTO(
      driverGuidQueryProcessService.queryDriverInfo(
        convert2QueryDriverForSaasRequestDTO(queryDriverForSaasRequestType))));
    logger.info("queryDriverForSaas", "response:{}", response);
    return response;
  }

  @Override
  public QueryVehicleForSaasResponseType queryVehicleForSaas(
    QueryVehicleForSaasRequestType requestType) {
    logger.info("queryVehicleForSaas", "request:{}", requestType);
    QueryVehicleForSaasResponseType response = ServiceResponseUtils.success(convert2QueryVehicleForSaasRsp(
      driverGuidQueryProcessService.queryVehicleInfo(convert2QueryVehicleForSaasRequestDTO(requestType))));
    logger.info("queryVehicleForSaas", "response:{}", response);
    return response;
  }

  @Override
  public QueryDriverIdForSaasResponseType queryDriverIdForSaas(
    QueryDriverIdForSaasRequestType queryDriverIdForSaasRequestType) {
    logger.info("queryDriverIdForSaas", "request:{}", queryDriverIdForSaasRequestType);
    if (!driverGuidQueryProcessService.getGrayControl(queryDriverIdForSaasRequestType.getSupplierId())) {
      logger.info("queryDriverIdForSaas", "not in gray:{}", queryDriverIdForSaasRequestType);
      return ServiceResponseUtils.success(new QueryDriverIdForSaasResponseType());
    }
    QueryDriverIdForSaasResponseType response = ServiceResponseUtils.success(convert2QueryDriverIdForSaas(
      driverGuidQueryProcessService.queryDriverInfo(
        convert2DriverGuidDriverRequestDTO(queryDriverIdForSaasRequestType))));
    logger.info("queryDriverIdForSaas", "response:{}", response);
    return response;
  }

}
