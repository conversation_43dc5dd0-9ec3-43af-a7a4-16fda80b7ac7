package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.tms.transport.api.model.QueryCategoryListV2RequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryCategoryListV2ResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryCategorySOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryCategorySOAResponseType;
import com.ctrip.dcs.tms.transport.application.query.driverguide.DriverGuidQueryProcessService;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DriverGuidDriverRequestDTO;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;

/**
 * <AUTHOR>
 */
@Component
public class QueryCategoryListV2Executor extends AbstractRpcExecutor<QueryCategoryListV2RequestType, QueryCategoryListV2ResponseType> implements Validator<QueryCategorySOARequestType> {

    private static final int DAY_MIGRATION_CATEGORY_ID = 3;

    @Autowired
    QueryCategoryExecutor queryCategoryExecutor;

    @Autowired
    DriverGuidQueryProcessService driverGuidQueryProcessService;

    @Override
    public QueryCategoryListV2ResponseType execute(QueryCategoryListV2RequestType requestType) {

        QueryCategorySOARequestType delegate = new QueryCategorySOARequestType();
        delegate.setSupplierId(requestType.getSupplierId());
        delegate.setCityIdList(requestType.getCityIdList());
        delegate.setRequestHeader(requestType.getRequestHeader());
        QueryCategorySOAResponseType queryCategorySOAResponseType = queryCategoryExecutor.execute(delegate);

        // 如果命中灰度，则过滤包车类型
        DriverGuidDriverRequestDTO request = new DriverGuidDriverRequestDTO();
        request.setSupplierId(requestType.getSupplierId());
        Boolean data = driverGuidQueryProcessService.getGrayControl(request);
        if (data) {
            queryCategorySOAResponseType.setData(queryCategorySOAResponseType.getData().stream().filter(category -> !Objects.equals(category.getId(), DAY_MIGRATION_CATEGORY_ID)).collect(
              Collectors.toList()));
        }
        QueryCategoryListV2ResponseType responseType = new QueryCategoryListV2ResponseType();
        responseType.setData(queryCategorySOAResponseType.getData());
        responseType.setResponseResult(queryCategorySOAResponseType.getResponseResult());
        responseType.setResponseStatus(queryCategorySOAResponseType.getResponseStatus());
        return responseType;

    }
}
