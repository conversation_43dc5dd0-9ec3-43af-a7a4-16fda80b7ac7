package com.ctrip.dcs.tms.transport.interfaces.listener;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.PhoneBridgeServiceProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.AreaScopeTypeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DrvMobileNumberAuthReqDTO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;

import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

@Component
public class RealNameAuthListener {
    private static final Logger logger = LoggerFactory.getLogger(RealNameAuthListener.class);

    @Autowired
    private DrvDrvierRepository drvDrvierRepository;

    @Autowired
    private PhoneBridgeServiceProxy phoneBridgeServiceProxy;

    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.SUBJECT_DRIVER_VIRTUALNUMBER_AUTH_CHANGED, consumerGroup = TmsTransportConstant.QMQ_CONSUMER_GROUP)
    public void realNameAuth(Message message) {
        String drvIdListStr = message.getStringProperty("drvIdList");
        logger.info("realNameAuth", "{}", drvIdListStr);

        if (StringUtils.isBlank(drvIdListStr)) {
            return;
        }

        List<Long> drvIdList = JsonUtil.fromJson(drvIdListStr, new TypeReference<List<Long>>() {});

        List<DrvDriverPO> drvDriverPOS = drvDrvierRepository.queryDrvList(drvIdList);
        if (CollectionUtils.isEmpty(drvDriverPOS)) {
            logger.info("filterDriver_empty", "{}", drvIdListStr);
            return;
        }
        List<DrvDriverPO> filterDriver = drvDriverPOS.stream().filter(d -> Objects.equals(d.getInternalScope(), AreaScopeTypeEnum.DOMESTIC.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterDriver)) {
            logger.info("filterDriver_empty", "{}", drvIdListStr);
            return;
        }

        filterDriver.forEach(driverPO ->{
            DrvMobileNumberAuthReqDTO dto = new DrvMobileNumberAuthReqDTO();
            dto.setMobilePhone(driverPO.getDrvPhone());
            dto.setIgtCode(driverPO.getIgtCode());
            dto.setDriverName(driverPO.getDrvName());
            dto.setDriverId(driverPO.getDrvId());
            dto.setIdCardImgUrl(driverPO.getIdcardImg());
            dto.setIdCardBackImgUrl(driverPO.getIdcardBackImg());
            dto.setScenePhotoUrl(StringUtils.isEmpty(driverPO.getScenePhoto()) ? driverPO.getDrvHeadImg() : driverPO.getScenePhoto());
            dto.setIdCard(driverPO.getDrvIdcard());
            logger.info("batchIdentification", "{}", JsonUtil.toJson(dto));
            phoneBridgeServiceProxy.batchIdentification(dto);
        });
    }

}
