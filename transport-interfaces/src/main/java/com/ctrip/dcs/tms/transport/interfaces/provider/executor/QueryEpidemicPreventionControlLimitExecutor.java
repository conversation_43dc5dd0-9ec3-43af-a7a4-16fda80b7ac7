package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.apache.commons.collections.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class QueryEpidemicPreventionControlLimitExecutor extends AbstractRpcExecutor<QueryEpidemicPreventionControlLimitSOARequestType, QueryEpidemicPreventionControlLimitSOAResponseType> implements Validator<QueryEpidemicPreventionControlLimitSOARequestType> {

    @Autowired
    private QueryDrvEpidemicPreventionControlInfoService queryDrvEpidemicPreventionControlInfoService;

    @Override
    public QueryEpidemicPreventionControlLimitSOAResponseType execute(QueryEpidemicPreventionControlLimitSOARequestType requestType) {
        QueryEpidemicPreventionControlLimitSOAResponseType responseType = new QueryEpidemicPreventionControlLimitSOAResponseType();
        if (CollectionUtils.isNotEmpty(requestType.getCityList())) {
            responseType.setReportInfoList(queryDrvEpidemicPreventionControlInfoService.queryEpidemicPreventionControlInfoByCityList(requestType.getCityList()));
            return ServiceResponseUtils.success(responseType);
        }
        if (CollectionUtils.isNotEmpty(requestType.getDriverIdList())) {
            responseType.setReportInfoList(queryDrvEpidemicPreventionControlInfoService.queryEpidemicPreventionControlInfoByDrvList(requestType.getDriverIdList()));
            return ServiceResponseUtils.success(responseType);
        }
        responseType.setReportInfoList(Collections.emptyList());
        return ServiceResponseUtils.fail(responseType);
    }

    @Override
    public void validate(AbstractValidator<QueryEpidemicPreventionControlLimitSOARequestType> validator) {

    }
}