package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * <AUTHOR>
 * @Date 2020/4/29 17:50
 */
@Component
public class CheckRecruitingPhoneExecutor extends AbstractRpcExecutor<CheckRecruitingPhoneSOARequestType, CheckRecruitingPhoneSOAResponseType> implements Validator<CheckRecruitingPhoneSOARequestType> {

    @Autowired
    private RecruitingQueryService recruitingQueryService;

    @Override
    public CheckRecruitingPhoneSOAResponseType execute(CheckRecruitingPhoneSOARequestType checkRecruitingPhoneSOARequestType) {
        CheckRecruitingPhoneSOAResponseType responseType = new CheckRecruitingPhoneSOAResponseType();
        Result<Boolean> result = recruitingQueryService.checkRecruitingPhone(checkRecruitingPhoneSOARequestType);
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }else {
            return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<CheckRecruitingPhoneSOARequestType> validator, CheckRecruitingPhoneSOARequestType req) {
        validator.ruleFor("igtCode").notNull().notEmpty();
        validator.ruleFor("drvPhone").notNull().notEmpty();
    }
}
