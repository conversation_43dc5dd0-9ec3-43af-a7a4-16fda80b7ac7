package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.UpdateTransportGroupFlagRequestType;
import com.ctrip.dcs.tms.transport.api.model.UpdateTransportGroupFlagResponseType;
import com.ctrip.dcs.tms.transport.application.command.TransportGroupCommandService;
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.ResponseResultUtil;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class UpdateTransportGroupFlagExecutor extends AbstractRpcExecutor<UpdateTransportGroupFlagRequestType, UpdateTransportGroupFlagResponseType> implements Validator<UpdateTransportGroupFlagRequestType> {

    @Autowired
    private TransportGroupCommandService transportGroupCommandService;

    @Autowired
    private TransportGroupQueryService transportGroupQueryService;

    @Override
    public UpdateTransportGroupFlagResponseType execute(UpdateTransportGroupFlagRequestType requestType) {
        Result<TspTransportGroupPO> groupResult =
          transportGroupQueryService.queryTransportGroupDetail(requestType.getTransportGroupId());
        TspTransportGroupPO data = groupResult.getData();
        if (data == null) {
            return ServiceResponseUtils.success(new UpdateTransportGroupFlagResponseType());
        }
        data.setDispatchOnly(requestType.getDispatchOnly());
        Result<Boolean> result = transportGroupCommandService.updateTransportGroup(data);
        return ResponseResultUtil.response(new UpdateTransportGroupFlagResponseType(), result);
    }
}
