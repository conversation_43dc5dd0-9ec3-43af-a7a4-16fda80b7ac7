package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.PropertyUpdateTagSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.PropertyUpdateTagSOAResponseType;
import com.ctrip.dcs.tms.transport.application.command.CertificateCheckCommandService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PropertyUpateTagExecutor extends AbstractRpcExecutor<PropertyUpdateTagSOARequestType, PropertyUpdateTagSOAResponseType> implements Validator<PropertyUpdateTagSOARequestType> {

    @Autowired
    private CertificateCheckCommandService commandService;

    @Override
    public PropertyUpdateTagSOAResponseType execute(PropertyUpdateTagSOARequestType requestType) {
        PropertyUpdateTagSOAResponseType responseType = new PropertyUpdateTagSOAResponseType();
        Result<Boolean> result = commandService.updatePropertyTag(requestType);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType,"400",result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<PropertyUpdateTagSOARequestType> validator) {

    }

}