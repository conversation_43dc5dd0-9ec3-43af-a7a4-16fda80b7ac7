package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.dto.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctriposs.baiji.exception.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import qunar.tc.qschedule.config.*;
import qunar.tc.schedule.*;

import java.util.*;
import java.util.stream.*;

/**
　* @description: 待平台审批招募司机调用第三方
　* <AUTHOR>
　* @date 2022/1/13 10:29
*/
@Component
public class DrvRecruitingCertificateCheckResultSchedule {

    private static final Logger logger = LoggerFactory.getLogger(DrvRecruitingCertificateCheckResultSchedule.class);

    @Autowired
    private TmsCertificateCheckRepository checkRepository;
    @Autowired
    TmsTransportQconfig qconfig;
    @Autowired
    DrvDrvierRepository repository;
    @Autowired
    CertificateCheckQueryService checkQueryService;
    @Autowired
    DrvRecruitingRepository recruitingRepository;
    @Autowired
    VehicleRecruitingRepository vehicleRecruitingRepository;
    @Autowired
    VehicleRepository vehicleRepository;
    @Autowired
    RecruitingCommandService recruitingCommandService;
    @Autowired
    TmsQmqProducerCommandService qmqProducerCommandService;

    private static final String drvName_Key = "drvName";
    private static final String drvidcard_Key = "drvIdcard";
    private static final String modifyUser_Key = "modifyUser";
    private static final String supplierId_Key = "supplierId";
    private static final String versionFlag_Key = "versionFlag";
    private static final String cityId_Key = "cityId";

    @QSchedule("drv.recruiting.certificate.check.result.job")
    public void drvRecruitingcertificateCheckResultSchedule(Parameter parameter) throws Exception {
        this.drvRecruitingcertificateCheckResultScheduleMethod();
    }

    public Boolean drvRecruitingcertificateCheckResultScheduleMethod() {
        try {

            Integer checkType = TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV.getCode();
            Integer checkStatus = TmsTransportConstant.CheckStatusEnum.CHECKING.getCode();
            //查询待平台审批的司机
            int count = recruitingRepository.countWaitApproveRecruitingDrv();
            if (count == 0) {
                return Boolean.FALSE;
            }
            int pageSize = 50;
            count = StringUtil.getPageCount(count,pageSize);
            for (int i = 1; i <= count; i ++) {
                int beginSize = i;
                List<DrvRecruitingPO> drvRecruitingPOList =  recruitingRepository.queryWaitApproveRecruitingDrvByPage(beginSize,pageSize);
                if (CollectionUtils.isEmpty(drvRecruitingPOList)) {
                    return Boolean.FALSE;
                }
                List<Long> orgVersionIds = Lists.newArrayList();
                List<Long> new3VersionIds = Lists.newArrayList();
                for(DrvRecruitingPO drvRecruitingPO : drvRecruitingPOList){
                    if(drvRecruitingPO.getVersionFlag() >= 3){
                        new3VersionIds.add(drvRecruitingPO.getDrvRecruitingId());
                    }else {
                        orgVersionIds.add(drvRecruitingPO.getDrvRecruitingId());
                    }
                }
                List<TmsCertificateCheckPO> checkPOList = Lists.newArrayList();
                List<TmsCertificateCheckPO> newCheckPOList = Lists.newArrayList();
                if(CollectionUtils.isNotEmpty(orgVersionIds)){
                    checkPOList = checkRepository.queryNewCheckListByCheckIds(orgVersionIds, checkType, false,checkStatus);
                }
                if(CollectionUtils.isNotEmpty(new3VersionIds)){
                    newCheckPOList = checkRepository.queryNewCheckListByCheckIds(new3VersionIds, checkType,true, checkStatus);
                }
                checkPOList = (List<TmsCertificateCheckPO>) CollectionUtils.union(checkPOList,newCheckPOList);
                if (CollectionUtils.isEmpty(checkPOList)) {
                    return Boolean.FALSE;
                }
                //按checkId分组
                Map<Long,List<TmsCertificateCheckPO>> checkMap = checkPOList.stream().collect(Collectors.groupingBy(TmsCertificateCheckPO::getCheckId));
                for(Map.Entry<Long,List<TmsCertificateCheckPO>> entry : checkMap.entrySet()){
                    //遍历核验表中，身份证对应的背调表信息
                    for (TmsCertificateCheckPO checkPO : entry.getValue()) {
                        //驾驶证
                        if (Objects.equals(checkPO.getCertificateType(), TmsTransportConstant.CertificateTypeEnum.DRIVERLICENSE.getCode())) {
                            drvLicenseDataCheck(checkPO.getId(), checkPO.getCheckId(), checkPO.getCheckType(),checkPO.getCheckStatus());
                        }
                        //网约车驾驶证
                        if (Objects.equals(checkPO.getCertificateType(), TmsTransportConstant.CertificateTypeEnum.NETDRVCTFCT.getCode())) {
                            netDrvLicenseDataCheck(checkPO.getId(),checkPO.getCheckId(),checkPO.getCheckType(),checkPO.getCheckKeyword(),checkPO.getCheckStatus());
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
        return Boolean.FALSE;
    }

    //驾驶证核验
    public Boolean drvLicenseDataCheck(Long id, Long checkId, Integer checkType,Integer checkStatus) {
        Map<String,Object> resultMap = getDrvNameAndIdCard(checkId);
        if(MapUtils.isEmpty(resultMap)){
            return  Boolean.FALSE;
        }
        Object resultFlag = resultMap.get(TmsTransportConstant.RESULTFLAG_KEY);
        if(resultFlag!=null && !(Boolean)resultFlag){
            return  Boolean.FALSE;
        }
        String drvName = String.valueOf(resultMap.get(drvName_Key));
        String drvIdcard = String.valueOf(resultMap.get(drvidcard_Key));
        Long supplierId = Long.valueOf(String.valueOf(resultMap.get(supplierId_Key)));
        Integer versionFlag = Integer.valueOf(String.valueOf(resultMap.get(versionFlag_Key)));
        checkQueryService.refreshDrvLicenseCheckIng(DrvAuditDTO.refreshbuildDrvDTO(id, drvName, drvIdcard,checkType,supplierId,versionFlag,checkStatus,checkId));
        //司机核验状态
        qmqProducerCommandService.sendRecruitingCheckStatusQMQ(checkId,TmsTransportConstant.RecruitingTypeEnum.drv.getCode(),TmsTransportConstant.TMS_DEFAULT_USERNAME);
        return Boolean.FALSE;
    }

    //网约车驾驶证核验
      public Boolean netDrvLicenseDataCheck(Long id,Long checkId,Integer checkType,String checkKeyword,Integer checkStatus) {
        if(StringUtils.isNotEmpty(checkKeyword)){
            Map<String,Object> resultMap = getDrvNameAndIdCard(checkId);
            if(MapUtils.isEmpty(resultMap)){
                return  Boolean.FALSE;
            }
            if (resultMap.get(TmsTransportConstant.RESULTFLAG_KEY) != null && !(Boolean) resultMap.get(TmsTransportConstant.RESULTFLAG_KEY)) {
                return  Boolean.FALSE;
            }
            //身份证解密
            String newCheckKeyword = "";
            if(StringUtils.isNotEmpty(checkKeyword)){
                newCheckKeyword = TmsTransUtil.decrypt(checkKeyword, KeyType.Identity_Card);
            }
            Long supplierId = null;
            Integer versionFlag = Integer.valueOf(String.valueOf(resultMap.get(versionFlag_Key)));
            String cityId = resultMap.get(cityId_Key) == null ?"":resultMap.get(cityId_Key).toString();
            //查询第三方合规校验接口更新数据库合规信息
            //上海城市 合规信息查询处理
            if(checkQueryService.checkFromCityPlatform(cityId)){
                RefreshNetDrvLicenseCheckIngParamDTO paramDTO = new RefreshNetDrvLicenseCheckIngParamDTO();
                paramDTO.setId(id);
                paramDTO.setIdCard(newCheckKeyword);
                paramDTO.setCheckType(checkType);
                paramDTO.setSupplierId(supplierId);
                paramDTO.setVersionFlag(versionFlag);
                paramDTO.setCheckStatus(checkStatus);
                paramDTO.setCheckId(checkId);
                paramDTO.setDriverName(resultMap.get(drvName_Key).toString());
                paramDTO.setCityId(Long.valueOf(cityId));
                checkQueryService.refreshNetDrvLicenseCheckIng(paramDTO);
            }else{
                //其他城市 合规信息查询处理
                checkQueryService.refreshNetDrvLicenseCheckIng(id,newCheckKeyword,checkType,supplierId,versionFlag,checkStatus,checkId);
            }
            //司机核验状态
            qmqProducerCommandService.sendRecruitingCheckStatusQMQ(checkId,TmsTransportConstant.RecruitingTypeEnum.drv.getCode(),TmsTransportConstant.TMS_DEFAULT_USERNAME);
        }
        return Boolean.FALSE;
    }

    private Map<String,Object> getDrvNameAndIdCard(Long checkId){
        Map<String,Object> resultMap = Maps.newHashMap();
        DrvRecruitingPO recruitingPO = recruitingRepository.queryByPK(checkId);
        if (Objects.isNull(recruitingPO)) {
            resultMap.put(TmsTransportConstant.RESULTFLAG_KEY,Boolean.FALSE);
            return resultMap;
        }
        resultMap.put(TmsTransportConstant.RESULTFLAG_KEY,Boolean.TRUE);
        resultMap.put(drvName_Key,recruitingPO.getDrvName());
        resultMap.put(drvidcard_Key,recruitingPO.getDrvIdcard());
        resultMap.put(modifyUser_Key,recruitingPO.getModifyUser());
        resultMap.put(supplierId_Key,recruitingPO.getSupplierId());
        resultMap.put(versionFlag_Key,recruitingPO.getVersionFlag());
        resultMap.put(cityId_Key,recruitingPO.getCityId());
        return resultMap;
    }
}