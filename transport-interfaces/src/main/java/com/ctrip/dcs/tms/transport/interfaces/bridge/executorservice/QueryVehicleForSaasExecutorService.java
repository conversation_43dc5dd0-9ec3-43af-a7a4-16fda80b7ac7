package com.ctrip.dcs.tms.transport.interfaces.bridge.executorservice;

import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleForSaasResponseType;
import com.ctrip.dcs.tms.transport.api.saas.SaasVehicleSoaDTO;
import com.ctrip.dcs.tms.transport.application.dto.SaasVehicleDTO;
import com.ctrip.dcs.tms.transport.application.query.IQueryVehicleForSaasService;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.LocalCollectionUtils;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class QueryVehicleForSaasExecutorService {
    @Autowired
    private IQueryVehicleForSaasService queryVehicleForSaasService;
    public QueryVehicleForSaasResponseType execute(QueryVehicleForSaasRequestType requestType) {
        try{
            if(requestType.getVehicleIds().size() > 150){
                return ServiceResponseUtils.fail(new QueryVehicleForSaasResponseType());
            }
            List<SaasVehicleDTO> vehicleDTOS = queryVehicleForSaasService.query(requestType.getVehicleIds());
            return ServiceResponseUtils.success(getResponse(vehicleDTOS));
        }catch (Exception e){
            return ServiceResponseUtils.fail(new QueryVehicleForSaasResponseType());
        }
    }
    /**
     * 封装结果
     * @param vehCacheDTOS
     * @return
     */
    private QueryVehicleForSaasResponseType getResponse(List<SaasVehicleDTO> vehCacheDTOS){
        if(LocalCollectionUtils.isEmpty(vehCacheDTOS)){
            return new QueryVehicleForSaasResponseType();
        }
        List<SaasVehicleSoaDTO> vehicleList = new ArrayList<>();
        for (SaasVehicleDTO vehCacheDTO : vehCacheDTOS) {
            vehicleList.add(getSaasVehicleSoaDTO(vehCacheDTO));
        }
        QueryVehicleForSaasResponseType responseType = new QueryVehicleForSaasResponseType();
        responseType.setVehicleList(vehicleList);
        return responseType;
    }

    /**
     * 封装结果
     * @param saasVehicleDTO
     * @return
     */
    private SaasVehicleSoaDTO getSaasVehicleSoaDTO(SaasVehicleDTO saasVehicleDTO){
        SaasVehicleSoaDTO saasVehicleSoaDTO  = new SaasVehicleSoaDTO();
        saasVehicleSoaDTO.setVehicleId(saasVehicleDTO.getVehicleId());
        saasVehicleSoaDTO.setVehicleStatus(saasVehicleDTO.getVehicleStatus());
        saasVehicleSoaDTO.setVehicleBrandId(saasVehicleDTO.getVehicleBrandId());
        saasVehicleSoaDTO.setVehicleBrandName(saasVehicleDTO.getVehicleBrandName());
        saasVehicleSoaDTO.setVehicleCategory(saasVehicleDTO.getVehicleCategory());
        saasVehicleSoaDTO.setSupplerId(saasVehicleDTO.getSupplerId());
        saasVehicleSoaDTO.setVehicleLicense(saasVehicleDTO.getVehicleLicense());
        saasVehicleSoaDTO.setVehicleSeriesId(saasVehicleDTO.getVehicleSeriesId());
        saasVehicleSoaDTO.setVehicleSeriesName(saasVehicleDTO.getVehicleSeriesName());
        saasVehicleSoaDTO.setVehicleTypeId(saasVehicleDTO.getVehicleTypeId());
        saasVehicleSoaDTO.setVehicleTypeName(saasVehicleDTO.getVehicleTypeName());
        saasVehicleSoaDTO.setVehicleColorId(saasVehicleDTO.getVehicleColorId());
        saasVehicleSoaDTO.setVehicleColorName(saasVehicleDTO.getVehicleColorName());
        return saasVehicleSoaDTO;
    }
}
