package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

@Component
public class DrvFaceVerifyCountTransfiniteExecutor extends AbstractRpcExecutor<DrvFaceVerifyCountTransfiniteRequestType, DrvFaceVerifyCountTransfiniteResponseType> implements Validator<DrvFaceVerifyCountTransfiniteRequestType> {

    @Autowired
    TmsVerifyEventQueryService queryService;

    @Override
    public DrvFaceVerifyCountTransfiniteResponseType execute(DrvFaceVerifyCountTransfiniteRequestType requestType) {
        DrvFaceVerifyCountTransfiniteResponseType responseType = new DrvFaceVerifyCountTransfiniteResponseType();
        Result<Boolean> result = queryService.drvFaceVerifyCountTransfinite(requestType.getDriverId());
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<DrvFaceVerifyCountTransfiniteRequestType> validator) {
        validator.ruleFor("driverId").notNull();
    }
}
