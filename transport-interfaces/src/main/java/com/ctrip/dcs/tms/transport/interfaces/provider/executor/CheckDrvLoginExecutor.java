package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2020/9/16 19:27
 */
@Component
public class CheckDrvLoginExecutor extends AbstractRpcExecutor<CheckDrvLoginRequestType, CheckDrvLoginResponseType>
    implements Validator<CheckDrvLoginRequestType> {

  private static final Logger logger = LoggerFactory.getLogger(CheckDrvLoginExecutor.class);

  @Autowired
  private DriverAccountQueryService queryService;
  @Autowired
  private DriverCommandService driverCommandService;
  @Autowired
  private TmsVerifyEventCommandService eventCommandService;

  @Override
  public CheckDrvLoginResponseType execute(CheckDrvLoginRequestType request) {
    logger.info("CheckDrvLogin","Params:{}", JsonUtil.toJson(request));
    LoginType loginType = LoginType.of(request.getLoginType());
    MetricsUtils.checkDrvLoginConnectTimes(ApiTypeEnum.CHECK_DRV_LOGIN,request.getHybridLoginAccount());
    Result<DrvDriverPO> result =
        queryService.login(request.getHybridLoginAccount(), request.getLoginPwd(), request.getLoginAreaCode(), loginType);

    CheckDrvLoginResponseType response = new CheckDrvLoginResponseType();
    if (result.isSuccess() && Objects.nonNull(result.getData())) {
      //司机变更设置,新增未验证事件
      eventCommandService.insertConventionalEvent(request,result.getData());
      //同步司机登录信息
      driverCommandService.insertDrvLoginInfo(buildLoginInfoPO(request,result.getData().getDrvId(),result.getData().getDrvName()));
      response.setDrvId(result.getData().getDrvId());
      return ServiceResponseUtils.success(response);
    }
    MetricsUtils.checkDrvLoginExceptionTimes(ApiTypeEnum.CHECK_DRV_LOGIN,request.getHybridLoginAccount());
    return ServiceResponseUtils.fail(response, result.getCode(), result.getMsg());
  }

  @Override
  public void validate(AbstractValidator<CheckDrvLoginRequestType> validator, CheckDrvLoginRequestType req) {
    validator.ruleFor("hybridLoginAccount").notNull().notEmpty();
    validator.ruleFor("loginPwd").notNull().notEmpty();
    validator.ruleFor("loginType").notNull().greaterThan(0);
    validator.ruleFor("loginAreaCode").notNull().notEmpty().when(req.getLoginType() == LoginType.PHONE_CODE.getCode());
  }

  public TmsDrvLoginInformationPO buildLoginInfoPO(CheckDrvLoginRequestType request,Long drvId,String drvName){
    TmsDrvLoginInformationPO informationPO = new TmsDrvLoginInformationPO();
    informationPO.setDrvId(drvId);
    informationPO.setDrvLoginTime(DateUtil.getNow());
    informationPO.setDrvLocLat(request.getDriverLocLat() == null?0D:request.getDriverLocLat());
    informationPO.setDrvLocLong(request.getDriverLocLong() == null?0D:request.getDriverLocLong());
    informationPO.setDrvLocCsys(request.getDriverLocCsys());
    informationPO.setCreateUser(drvName);
    informationPO.setModifyUser(drvName);
    informationPO.setDrvImei(request.getDriverImei());
    return informationPO;
  }
}
