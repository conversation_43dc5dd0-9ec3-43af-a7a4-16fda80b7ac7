package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.StringUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 刷新司机地址修改次数-初始化为0
 */
@Component
public class RefreshDrvAddrModCountSchedule {

    private static final Logger logger = LoggerFactory.getLogger(RefreshDrvAddrModCountSchedule.class);

    @Autowired
    private DrvDrvierRepository drvierRepository;

    @QSchedule("refresh.drv.address.mod.count.job")
    public void refreshDrvAddrModCountSchedule(Parameter parameter) throws Exception {
        String drvIds = parameter.getString("drvIds");
        this.refreshDrvAddrModCountMethod(drvIds);
    }

    public Boolean refreshDrvAddrModCountMethod(String drvIds) {
        logger.info("refreshDrvAddrModCountMethod", "drvIds:{}", drvIds);
        List<Long> paramsDrvList = Lists.newArrayList();
        if (StringUtils.isNotEmpty(drvIds)) {
            paramsDrvList = Arrays.stream(drvIds.split(",")).mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
        }

        //如果paramsDrvList不为空，则表示手动执行初始化司机地址修改次数值为0
        if (CollectionUtils.isNotEmpty(paramsDrvList)) {
            initDrvAddrModCount(paramsDrvList);
            return Boolean.TRUE;
        }
        //查询有效的并且地址修改次数字段不粉空的司机
        int count = drvierRepository.countDrvAddrModCountNoEmpty();
        if (count == 0) {
            return Boolean.TRUE;
        }
        int pageSize = 150;
        count = StringUtil.getPageCount(count, pageSize);
        for (int i = 1; i <= count; i++) {
            try {
                int beginSize = i;
                List<Long> drvIdList = drvierRepository.queryDrvAddrModCountNoEmptyList(beginSize, pageSize);
                if (CollectionUtils.isEmpty(drvIdList)) {
                    return Boolean.TRUE;
                }
                //批量执行初始化次数
                initDrvAddrModCount(drvIdList);

            } catch (Exception e) {
                logger.warn("refreshDrvAddrModCountMethodError", e);
            }
        }
        return Boolean.TRUE;
    }

    //批量初始化次数
    public Boolean initDrvAddrModCount(List<Long> drvIds) {
        drvierRepository.initDrvAddrModCount(drvIds, JsonUtil.toJson(initModCount()));
        return Boolean.TRUE;
    }

    //按12个月初始化次数，都置为0
    public Map<Integer, Integer> initModCount() {
        Map<Integer, Integer> map = Maps.newHashMap();
        for (int i = 1; i <= 12; i++) {
            map.put(i, 0);
        }
        return map;
    }
}