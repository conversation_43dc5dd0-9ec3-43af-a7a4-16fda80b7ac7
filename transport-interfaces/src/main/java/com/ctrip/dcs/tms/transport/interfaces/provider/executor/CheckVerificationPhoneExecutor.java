package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;

@Component
public class CheckVerificationPhoneExecutor extends AbstractRpcExecutor<CheckVerificationPhoneSOARequestType, CheckVerificationPhoneSOAResponseType> implements Validator<CheckVerificationPhoneSOARequestType> {

    @Autowired
    private CommonCommandService commonCommandService;

    @Override
    public CheckVerificationPhoneSOAResponseType execute(CheckVerificationPhoneSOARequestType checkVerificationPhoneSOARequestType) {
        Cat.logEvent(CatEventType.CHECK_MOBILE_VALID, "method_entry");
        CheckVerificationPhoneSOAResponseType responseType = new CheckVerificationPhoneSOAResponseType();
        Result<String> result = commonCommandService.checkPoneCode4China(checkVerificationPhoneSOARequestType.getCode(),checkVerificationPhoneSOARequestType.getMobilePhone(),checkVerificationPhoneSOARequestType.getCountryCode(),checkVerificationPhoneSOARequestType.getSite());
        if (result.isSuccess()) {
            Cat.logEvent(CatEventType.CHECK_MOBILE_VALID, "success", Event.SUCCESS, "phone:" + checkVerificationPhoneSOARequestType.getMobilePhone());
            RedisUtils.set(new StringBuilder(Constant.RESTRICT_PREFIX_STR).append(checkVerificationPhoneSOARequestType.getMobilePhone()).toString(), RedisUtils.ONE_DAY, 0);
            return ServiceResponseUtils.success(responseType);
        } else {
            Cat.logEvent(CatEventType.CHECK_MOBILE_VALID, "fail", "ERROR", "phone:" + checkVerificationPhoneSOARequestType.getMobilePhone() + ",code:" + result.getCode());
            responseType.setCode(result.getData());
            return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
        }
    }
}