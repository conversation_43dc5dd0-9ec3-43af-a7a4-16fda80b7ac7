package com.ctrip.dcs.tms.transport.interfaces.listener;

import com.ctrip.dcs.driver.message.dto.AccountBaseInfoUpdateMessageDTO;
import com.ctrip.dcs.driver.message.dto.AccountIdentitySourceDTO;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverDomainService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverDomainServiceProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.Result;
import com.fasterxml.jackson.core.type.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import qunar.tc.qmq.*;
import qunar.tc.qmq.consumer.annotation.*;

import java.util.*;
import java.util.stream.*;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.DRIVER_SOURCE;

@Component
public class DrvInfoListener {

    private static final Logger logger = LoggerFactory.getLogger(DrvInfoListener.class);
    @Autowired
    private CommonCommandService commonCommandService;
    @Autowired
    private DrvDrvierRepository drvierRepository;
    @Autowired
    DriverCommandService driverCommandService;
    @Autowired
    TransportGroupRepository transportGroupRepository;
    @Autowired
    PushDataCommandService pushDataCommandService;

    @Autowired
    DriverDomainServiceProxy domainServiceProxy;

    @Autowired
    TmsQmqProducerCommandService qmqProducerCommandService;

    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.SUBJECT_DRIVERINFO_CHANGED, tagType = TagType.AND, tags = {TmsTransportConstant.QmqTag.TAG_DRIVERINFO_ADD}, consumerGroup = TmsTransportConstant.QMQ_CONSUMER_GROUP + TmsTransportConstant.QmqTag.TAG_DRIVERINFO_ADD)
    public void addAccount(Message message) {
        Long drvId = message.getLongProperty("drvId");
        logger.info("add account for drvId:{}", drvId);
        DrvDriverPO driverPO = drvierRepository.queryByPk(drvId);
        if (driverPO == null) {
            logger.info("driverPO not found drvId:{}", drvId);
            return;
        }
        String[] qunarAndPPMAccount = commonCommandService.createQunarAccount(driverPO);
        String ctripAccount = commonCommandService.createCtripAccount(driverPO);
        int count = drvierRepository.updateDrvAccount(drvId, qunarAndPPMAccount[0], qunarAndPPMAccount[1], ctripAccount);
        logger.info("update driver count:{} drvId:{}", count, drvId);
    }

    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.SUBJECT_DRIVERINFO_CHANGED, tagType = TagType.AND, tags = {TmsTransportConstant.QmqTag.TAG_DRIVERINFO_MODIFY}, consumerGroup = TmsTransportConstant.QMQ_CONSUMER_GROUP + TmsTransportConstant.QmqTag.TAG_DRIVERINFO_MODIFY)
    public void updateImInfo(Message message) {
        Long drvId = message.getLongProperty("drvId");
        logger.info("update im account for drvId:{}", drvId);
        DrvDriverPO driverPO = drvierRepository.queryByPk(drvId);
        if (driverPO == null) {
            logger.info("driverPO not found drvId:{}", drvId);
            return;
        }
        commonCommandService.updateaCtripAccount(driverPO);
    }


    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.SUBJECT_DRIVERTRANSPORTGROUP_CHANGED,tags = {TmsTransportConstant.QmqTag.TAG_DRV_TRANSPORTGROUP_CHANGE}, consumerGroup = TmsTransportConstant.QMQ_CONSUMER_GROUP+TmsTransportConstant.QmqTag.TAG_DRV_TRANSPORTGROUP_CHANGE)
    public void calculateDrvCoopMode(Message message) {
        String drvIds = message.getStringProperty("drvIds");
        String modifyUser = message.getStringProperty("modifyUser");
        logger.info("calculateDrvCoopMode drvId:{}", drvIds);
        if(StringUtils.isEmpty(drvIds)){
            return;
        }
        List<Long> drvList = Arrays.asList(drvIds.split(",")).stream().map(d->Long.parseLong(d.trim())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(drvList)){
            return;
        }
        driverCommandService.calculateUpdateDrvCoopMode(drvList,modifyUser);
    }

    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.SUBJECT_DRIVER_START_SERVICE,consumerGroup = TmsTransportConstant.QMQ_CONSUMER_GROUP+TmsTransportConstant.QmqTag.TAG_DRIVER_START_SERVICE)
    public void driverStartService(Message message) {
        String drvIds = message.getStringProperty("driverId");
        String location = message.getStringProperty("location");
        Integer status = message.getIntProperty("status");
        logger.info("driverStartService","drvIds:{},location:{},status:{}", drvIds,location,status);
        //只接收自营服务中的司机
        if(StringUtils.isEmpty(drvIds) || !StringUtils.isNumeric(drvIds)|| status == null || status!=5){
            return;
        }
        //只推送杭州数据
        DrvDriverPO driverPO = drvierRepository.queryByPk(Long.parseLong(drvIds));
        if(Objects.isNull(driverPO) || driverPO.getCityId()!=PushDataCommandService.HangZhouCityId){
            return;
        }
        List<DriverEventDTO> resultEventDTO = Lists.newArrayList();
        DriverEventDTO driverEventDTO = new DriverEventDTO();
        if(StringUtils.isNotEmpty(location)){
             driverEventDTO = JsonUtil.fromJson(location, new TypeReference<DriverEventDTO>() { });
        }
        driverEventDTO.setDriverId(drvIds);
        driverEventDTO.setVehicleId(String.valueOf(driverPO.getVehicleId()));
        driverEventDTO.setEventType(7);
        driverEventDTO.setReportedAt(DateUtil.dateToString(new Date(),DateUtil.YYYYMMDDHHMMSS));
        resultEventDTO.add(driverEventDTO);
        //事件
        pushDataCommandService.pushEventsData(resultEventDTO);
    }

    /**
     * 接受司导，向导等修改司机公共信息的消息
     * @param message
     */
    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.DCS_DRIVER_ACCOUNT_BASE_INFO_UPDATE_CONSUMER,consumerGroup = TmsTransportConstant.QMQ_CONSUMER_GROUP)
    public void driverAccountBaseInfoUpdate(Message message) {
        AccountBaseInfoUpdateMessageDTO content  = JsonUtil.fromJson(message.getStringProperty("content"), new TypeReference<AccountBaseInfoUpdateMessageDTO>() { });
        if (DRIVER_SOURCE.equals(content.getUpdateSource())) { // 供应链自身更新，无需消费
            logger.info("driverAccountBaseInfoUpdate", "Self info no need process:{}", content.getUid());
            return;
        }

        Optional<AccountIdentitySourceDTO> driverAccountId =
          content.getAccountIdentitySourceList().stream().filter(source -> DRIVER_SOURCE.equals(source.getSource()))
            .findFirst();

        if (!driverAccountId.isPresent()) {
            logger.info("driverAccountBaseInfoUpdate", "Driver Id not found:{}", content);
            return;
        }

        Result<DrvDriverPO> result =
          domainServiceProxy.queryAccountBySource(Long.valueOf(driverAccountId.get().getSourceId()));
        if (result.isSuccess()) {
            int updateCount = drvierRepository.updateDrv(result.getData());
            if (updateCount > 0) {
                //发送司机更新消息
                qmqProducerCommandService.sendDrvChangeQmq(result.getData().getDrvId(), 2, 1);
            }
        }
    }
}
