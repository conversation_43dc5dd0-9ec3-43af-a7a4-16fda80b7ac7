package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.tms.transport.application.dto.InternatSettleOCRDTO;
import com.ctrip.dcs.tms.transport.application.query.InternationalEntryService;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.model.InternatSettleOCRGrayRequestType;
import com.ctrip.model.InternatSettleOCRGrayResponseType;

@Component
public class InternatSettleOCRGrayExecutor extends AbstractRpcExecutor<InternatSettleOCRGrayRequestType, InternatSettleOCRGrayResponseType> implements Validator<InternatSettleOCRGrayRequestType> {

    @Autowired
    private InternationalEntryService internationalEntryService;

    @Override
    public InternatSettleOCRGrayResponseType execute(InternatSettleOCRGrayRequestType requestType) {
        InternatSettleOCRGrayResponseType responseType = new InternatSettleOCRGrayResponseType();
        InternatSettleOCRDTO internatSettleOCRDTO = internationalEntryService.useNewOCR(requestType.getCityId(), requestType.getSupplierId());
        if (Objects.nonNull(internatSettleOCRDTO)) {
            responseType.setUseNewOCR(internatSettleOCRDTO.getUseNewOCR());
            responseType.setNewOCRFieldList(internatSettleOCRDTO.getNewOCRFieldList());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType);
    }
}
