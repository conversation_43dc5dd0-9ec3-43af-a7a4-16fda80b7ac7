package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 司机状态变更（上线/下线）
 * <AUTHOR>
 * @Date 2020/2/26 18:46
 */
@Component
public class UpdateDrvStatusExecutor extends AbstractRpcExecutor<DrvStatusUpdateSOARequestType, DrvStatusUpdateSOAResponseType> implements Validator<DrvStatusUpdateSOARequestType> {

    @Autowired
    private DriverCommandService driverCommandService;

    @Autowired
    private TmsDrvFreezeCommandService tmsDrvFreezeCommandService;

    @Override
    public DrvStatusUpdateSOAResponseType execute(DrvStatusUpdateSOARequestType drvStatusUpdateSOARequestType) {
        DrvStatusUpdateSOAResponseType soaResponseType = new DrvStatusUpdateSOAResponseType();
        Result<Boolean> result;
        // 判罚级别优先级最高
        if (drvStatusUpdateSOARequestType.getOpFrom() != null && drvStatusUpdateSOARequestType.getOpFrom().intValue() == CommonEnum.FreezeOPFromEnum.PUNISH.getValue().intValue()) {
            if (drvStatusUpdateSOARequestType.getStatus().intValue() == TmsTransportConstant.DrvStatusEnum.OFFLINE.getCode().intValue()) {
                result = tmsDrvFreezeCommandService.penaltyDrvFreeze(drvStatusUpdateSOARequestType.getDrvIds(), drvStatusUpdateSOARequestType.getModifyUser(), drvStatusUpdateSOARequestType.getReason());
            } else {
                // 不关心是否判罚的上线请求
                result = Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
            }
        } else {
            result = driverCommandService.updateDrvStatus(drvStatusUpdateSOARequestType.getDrvIds(), drvStatusUpdateSOARequestType.getStatus(),
                    drvStatusUpdateSOARequestType.getOpFrom(), drvStatusUpdateSOARequestType.getModifyUser());
        }
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(soaResponseType);
        }else {
            return ServiceResponseUtils.fail(soaResponseType, result.getCode(),result.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<DrvStatusUpdateSOARequestType> validator) {
        validator.ruleFor("drvIds").notNull();
        validator.ruleFor("status").notNull();
        validator.ruleFor("modifyUser").notNull().notEmpty();
    }


}
