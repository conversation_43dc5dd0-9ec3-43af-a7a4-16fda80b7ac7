package com.ctrip.dcs.tms.transport.interfaces.provider.executor;


import com.ctrip.dcs.tms.transport.api.model.CertificateConfigAddSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.CertificateConfigAddSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.GenerateVehicleGlobalIdRequestType;
import com.ctrip.dcs.tms.transport.api.model.GenerateVehicleGlobalIdResponseType;
import com.ctrip.dcs.tms.transport.application.command.CertificateCommandService;
import com.ctrip.dcs.tms.transport.application.command.VehicleCommandService;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.platform.dal.dao.annotation.DalTransactional;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 添加证件配置
 */
@Component
public class GenerateGlobalIdExecutor extends AbstractRpcExecutor<GenerateVehicleGlobalIdRequestType, GenerateVehicleGlobalIdResponseType> implements Validator<GenerateVehicleGlobalIdRequestType> {

    @Autowired
    VehicleCommandService vehicleCommandService;
    @Override
    public GenerateVehicleGlobalIdResponseType execute(GenerateVehicleGlobalIdRequestType requestType){
        Result<Long> generateGlobalId =
          vehicleCommandService.generateGlobalId(requestType.getVehicleLicense(), requestType.getSource());
        GenerateVehicleGlobalIdResponseType responseType = new GenerateVehicleGlobalIdResponseType();
        responseType.setGlobalId(generateGlobalId.getData());
        ResponseResult result = new ResponseResult();
        result.setSuccess(generateGlobalId.isSuccess());
        result.setReturnCode(generateGlobalId.getCode());
        result.setReturnMessage(generateGlobalId.getMsg());
        responseType.setResponseResult(result);
        return responseType;
    }

    @Override
    public void validate(AbstractValidator<GenerateVehicleGlobalIdRequestType> validator) {
        validator.ruleFor("source").notEmpty();
        validator.ruleFor("vehicleLicense").notEmpty();
    }
}
