package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

@Component
public class QueryTransportGroupAreaIdExecutor extends AbstractRpcExecutor<QueryTransportGroupAreaIdSOARequestType, QueryTransportGroupAreaIdSOAResponseType> implements Validator<QueryTransportGroupAreaIdSOARequestType> {

    private static final Logger logger = LoggerFactory.getLogger(QueryTransportGroupAreaIdExecutor.class);

    @Autowired
    private TransportGroupQueryService transportGroupQueryService;

    @Override
    public QueryTransportGroupAreaIdSOAResponseType execute(QueryTransportGroupAreaIdSOARequestType requestType) {
        QueryTransportGroupAreaIdSOAResponseType responseType = new QueryTransportGroupAreaIdSOAResponseType();
        Result<List<SkuTransportGroupDTO>> result = transportGroupQueryService.querySkuTransportGroup(requestType.getSkuIds());
        if (result != null && result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType);
    }

    @Override
    public void validate(AbstractValidator<QueryTransportGroupAreaIdSOARequestType> validator) {
        validator.ruleFor("skuIds").notNull();
    }
}
