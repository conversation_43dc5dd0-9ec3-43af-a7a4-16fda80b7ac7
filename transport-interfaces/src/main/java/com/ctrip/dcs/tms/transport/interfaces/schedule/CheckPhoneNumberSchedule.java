package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.basebiz.account.service.soa.AccountInfoResponseType;
import com.ctrip.basebiz.account.service.soa.ExpectedInfo;
import com.ctrip.basebiz.account.service.soa.GetAccountByMobilePhoneRequestType;
import com.ctrip.dcs.driver.domain.account.UpdateAccountRequestType;
import com.ctrip.dcs.driver.domain.account.UpdateAccountResponseType;
import com.ctrip.dcs.driver.message.dto.PushMessageDTO;
import com.ctrip.dcs.geo.domain.repository.LocationRepository;
import com.ctrip.dcs.geo.domain.value.Location;
import com.ctrip.dcs.geo.platform.interfaces.AreaCodeQueryType;
import com.ctrip.dcs.geo.platform.interfaces.GeoKey;
import com.ctrip.dcs.geo.platform.interfaces.TelephoneAreaCodeContract;
import com.ctrip.dcs.order.es.api.OrderCommonDto;
import com.ctrip.dcs.order.es.api.QueryOrderCommonInfoRequestType;
import com.ctrip.dcs.order.es.api.QueryOrderCommonInfoResponseType;
import com.ctrip.dcs.order.es.api.SortOrderDto;
import com.ctrip.dcs.tms.transport.application.command.CommonCommandService;
import com.ctrip.dcs.tms.transport.application.command.TmsQmqProducerCommandService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.http.ColumnWidthStyleStrategy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.http.NepheleHttpService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.messaging.qmq.TmsQmqProducer;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvRecruitingPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.TmsTransUtil;
import com.ctrip.dcs.tms.transport.infrastructure.gateway.PhoneNumberServiceGateway;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvRecruitingRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.igt.order.queryservice.client.OrderSourceDTO;
import com.ctrip.igt.order.queryservice.client.QueryTradeDetailRequestType;
import com.ctrip.igt.order.queryservice.client.QueryTradeDetailResponseType;
import com.ctrip.igt.order.queryservice.client.TradeServiceProviderDTO;
import com.ctrip.model.QueryTelephoneAreaCodeRequestType;
import com.ctrip.model.QueryTelephoneAreaCodeResponseType;
import com.dianping.cat.Cat;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.owasp.csrfguard.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QScheduleByClass;
import qunar.tc.schedule.Parameter;
import qunar.tc.schedule.TaskHolder;
import qunar.tc.schedule.TaskMonitor;

import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <p>标题: </p>
 * <p>描述: </p>
 * <p>创建时间: 2024/7/15 16:46</p>
 * <p>作者：tanwenzheng</p>
 */
@Component
public class CheckPhoneNumberSchedule {

    private static final Logger LOGGER = LoggerFactory.getLogger(CheckPhoneNumberSchedule.class);
    private static final Map<Long, Pair<String, String>> HM_RegionCodeMap = ImmutableMap.of(58L, Pair.of("HK", "852"), 59L, Pair.of("MO", "853"));
    private static final Pair<String, String> TW_REGION = Pair.of("TW", "886");
    private static final String SCENARIO = "dcs-tms-phone-check";
    private static final Integer SUBSYSTEM_ID = 90023;
    private final LocationRepository locationRepository;
    private final PhoneNumberServiceGateway phoneNumberServiceGateway;
    private final Map<DriverType, Function<PageFrom, List<DriverInfo>>> loadDriverActionMap;
    private final Map<DriverType, Supplier<Long>> totalCountActionMap;
    private final NepheleHttpService nepheleHttpService;
    private final CommonCommandService commonCommandService;
    private final DrvDrvierRepository drvDrvierRepository;
    private final AccountServiceProxy accountServiceProxy;
    private final DriverDomainService driverDomainService;
    private final TmsQmqProducerCommandService qmqProducerCommandService;
    private final DcsOrderHybridSearchServiceProxy dcsOrderHybridSearchServiceProxy;
    private final IGTOrderQueryServiceProxy igtOrderQueryServiceProxy;
    private final EnumRepository enumRepository;
    private TmsQmqProducer tmsQmqProducer;

    private final GeoPlatformServiceProxy geoPlatformServiceProxy;

    private LoadingCache<Long, String> cache = CacheBuilder.newBuilder().initialCapacity(30).maximumSize(200L).expireAfterAccess(6, TimeUnit.HOURS).build(new CacheLoader<Long, String>() {
        @Override
        public String load(Long key) {
            return getAreaCodeByCountryId(key);
        }
    });

    private final static String MOBILE_FORMAT = "+%s%s";

    public CheckPhoneNumberSchedule(DrvDrvierRepository drvDrvierRepository, DrvRecruitingRepository drvRecruitingRepository, LocationRepository locationRepository,
                                    PhoneNumberServiceGateway phoneNumberServiceGateway, GeoPlatformServiceProxy geoPlatformServiceProxy, NepheleHttpService nepheleHttpService,
                                    CommonCommandService commonCommandService, AccountServiceProxy accountServiceProxy, EnumRepository enumRepository,
                                    DriverDomainService driverDomainService, TmsQmqProducerCommandService qmqProducerCommandService, DcsOrderHybridSearchServiceProxy dcsOrderHybridSearchServiceProxy,
                                    IGTOrderQueryServiceProxy igtOrderQueryServiceProxy, TmsQmqProducer tmsQmqProducer) {
        this.drvDrvierRepository = drvDrvierRepository;
        this.locationRepository = locationRepository;
        this.phoneNumberServiceGateway = phoneNumberServiceGateway;
        this.geoPlatformServiceProxy = geoPlatformServiceProxy;
        loadDriverActionMap = ImmutableMap.of(
                DriverType.RECRUIT_DRIVER, pageFrom -> Optional.ofNullable(drvRecruitingRepository.queryFromByPage(pageFrom.lastId, pageFrom.pageSize))
                        .orElse(Collections.emptyList()).stream().map(this::convert).collect(Collectors.toList()),
                DriverType.REAL_DRIVER, pageFrom -> Optional.ofNullable(drvDrvierRepository.queryFromByPage(pageFrom.lastId, pageFrom.pageSize))
                        .orElse(Collections.emptyList()).stream().map(this::convert).collect(Collectors.toList()));
        totalCountActionMap = ImmutableMap.of(
                DriverType.RECRUIT_DRIVER, () -> drvRecruitingRepository.countAll(),
                DriverType.REAL_DRIVER, () -> drvDrvierRepository.countAll()
        );
        this.nepheleHttpService = nepheleHttpService;
        this.commonCommandService = commonCommandService;
        this.accountServiceProxy = accountServiceProxy;
        this.driverDomainService = driverDomainService;
        this.qmqProducerCommandService = qmqProducerCommandService;
        this.dcsOrderHybridSearchServiceProxy = dcsOrderHybridSearchServiceProxy;
        this.igtOrderQueryServiceProxy = igtOrderQueryServiceProxy;
        this.enumRepository = enumRepository;
        this.tmsQmqProducer = tmsQmqProducer;
    }

    @QScheduleByClass
    public int checkRecruitDriverPhone(Parameter parameter) throws Exception {
        int pageSize = Optional.ofNullable(parameter.getString("pageSize")).map(Integer::parseInt).orElse(200);
        int sleepSeconds = Optional.ofNullable(parameter.getString("sleepSeconds")).map(Integer::parseInt).orElse(1);
        boolean useCN = Optional.ofNullable(parameter.getString("useCN")).map(Boolean::parseBoolean).orElse(true);
        boolean autoUpdate = Optional.ofNullable(parameter.getString("autoUpdate")).map(Boolean::parseBoolean).orElse(false);
        DriverType driverType = Optional.ofNullable(parameter.getString("driverType")).map(DriverType::valueOf).orElse(DriverType.REAL_DRIVER);
        ExportConfig exportConfig = Optional.ofNullable(parameter.getString("exportConfig")).map(o -> JsonUtil.fromJson(o, new TypeReference<ExportConfig>() {
        })).orElse(null);
        OrderConfig orderConfig = Optional.ofNullable(parameter.getString("orderConfig")).map(o -> JsonUtil.fromJson(o, new TypeReference<OrderConfig>() {
        })).orElse(null);

        List<Long> driverIdList = Optional.ofNullable(parameter.getString("driverIds")).map(o -> o.split(",")).map(o -> Arrays.stream(o).map(Long::valueOf).collect(Collectors.toList())).orElse(Lists.newArrayList());

        if (DriverType.ORDER_DRIVER.equals(driverType)) {
            // 检查订单的司机数据
            return checkOrderDriverPhoneNumber(pageSize, orderConfig, exportConfig, useCN, sleepSeconds);
        }
        if (CollectionUtils.isNotEmpty(driverIdList)) {
            return checkPhoneNumber(driverType, driverIdList, sleepSeconds, useCN, autoUpdate, exportConfig);
        } else {
            return checkPhoneNumber(driverType, pageSize, sleepSeconds, useCN, autoUpdate, exportConfig);
        }

    }

    private int checkPhoneNumber(DriverType driverType, int pageSize, int sleepSeconds, boolean useCN, boolean autoUpdate, ExportConfig exportConfig)
            throws InterruptedException {
        int processSize = 0;
        int total = totalCountActionMap.get(driverType).get().intValue();
        TaskHolder.getKeeper().setRateCapacity(total);
        log("********************* {} begin checkRecruitDriverPhone *********************", driverType.name());
        long lastDriverId = 0;
        List<DriverInfo> driverInfos;
        List<DriverMobileCheckResult> invalidResultList = Lists.newArrayList();
        do {
            driverInfos = loadDriverActionMap.get(driverType).apply(new PageFrom(lastDriverId, pageSize));
            fillSupplierName(driverInfos);
            List<DriverMobileCheckResult> checkResultList = check(driverInfos, useCN, autoUpdate);
            checkResultList.stream().filter(o -> ObjectUtils.notEqual(o.getCtripCheckReason(), CtripFormatCheckReason.SUCCESS)).forEach(invalidResultList::add);
            TimeUnit.SECONDS.sleep(sleepSeconds);
            processSize += driverInfos.size();
            TaskHolder.getKeeper().setRate(processSize);
            lastDriverId = driverInfos.get(driverInfos.size() - 1).getDriverId();
        } while (driverInfos.size() == pageSize);

        // 导出
        exportInvalidResults(invalidResultList, exportConfig);

        log("********************* finish checkRecruitDriverPhone,total={} *********************", total);
        return total;
    }

    private int checkOrderDriverPhoneNumber(int pageSize, OrderConfig orderConfig, ExportConfig exportConfig, boolean useCN, int sleepSeconds)
            throws InterruptedException, ParseException {
        if (orderConfig == null) {
            return 0;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar queryStartTime = Calendar.getInstance();
        queryStartTime.setTime(sdf.parse(orderConfig.getQueryStartTimeStr()));
        Calendar queryEndTime = Calendar.getInstance();
        queryEndTime.setTime(sdf.parse(orderConfig.getQueryEndTimeStr()));
        int total = countOrder(queryStartTime, queryEndTime, orderConfig.getPatternTypeList()).intValue();
        int processSize = 0;
        TaskHolder.getKeeper().setRateCapacity(total);
        log("********************* {} begin checkOrderDriverPhone *********************", DriverType.ORDER_DRIVER.name() + ":" + total);
        Calendar lastQueryDataChangeCreateTime = null;
        List<DriverInfo> driverInfos;
        List<DriverMobileCheckResult> invalidResultList = Lists.newArrayList();
        do {
            driverInfos = loadOrderDriverInfo(pageSize, queryStartTime, queryEndTime, lastQueryDataChangeCreateTime, orderConfig.getPatternTypeList());
            List<DriverMobileCheckResult> checkResultList = check(driverInfos, useCN, false);
            if (exportConfig != null && exportConfig.exportExcel) {
                checkResultList.stream().filter(o -> ObjectUtils.notEqual(o.getCtripCheckReason(), CtripFormatCheckReason.SUCCESS)).forEach(invalidResultList::add);
            }
            TimeUnit.SECONDS.sleep(sleepSeconds);
            processSize += driverInfos.size();
            TaskHolder.getKeeper().setRate(processSize);
            lastQueryDataChangeCreateTime = driverInfos.get(driverInfos.size() - 1).getOrderDataChangeCreateTime();
        } while (driverInfos.size() == pageSize);

        // 导出
        exportInvalidResults(invalidResultList, exportConfig);

        log("********************* finish checkOrderDriverPhone,total={} *********************", total);
        return total;
    }


    private Long countOrder(Calendar queryStartTime, Calendar queryEndTime, List<Integer> patternTypeList) {
        QueryOrderCommonInfoRequestType request = new QueryOrderCommonInfoRequestType();
        request.setOrderStatuss(Lists.newArrayList(3));
        request.setUseTimeBJStart(queryStartTime);
        request.setUseTimeBJEnd(queryEndTime);
        request.setPatternTypes(patternTypeList);
        request.setPageSize(1);
        request.setOrderStatuss(Lists.newArrayList(3));// 已成交订单
        QueryOrderCommonInfoResponseType response = dcsOrderHybridSearchServiceProxy.queryOrderCommonInfo(request);
        return Optional.ofNullable(response).map(QueryOrderCommonInfoResponseType::getTotal).orElse(0L);
    }

    private List<DriverInfo> loadOrderDriverInfo(int pageSize, Calendar queryStartTime, Calendar queryEndTime, Calendar lastQueryDataChangeCreateTime, List<Integer> patternTypeList) {
        QueryOrderCommonInfoRequestType request = new QueryOrderCommonInfoRequestType();
        request.setOrderStatuss(Lists.newArrayList(3));
        request.setUseTimeBJStart(queryStartTime);
        request.setUseTimeBJEnd(queryEndTime);
        request.setPatternTypes(patternTypeList);
        request.setPageSize(pageSize);
        request.setSortOrderList(Lists.newArrayList(new SortOrderDto("dataChangeCreateTime", true)));
        request.setLastQueryDataChangeCreateTime(lastQueryDataChangeCreateTime);
        QueryOrderCommonInfoResponseType response = dcsOrderHybridSearchServiceProxy.queryOrderCommonInfo(request);
        return Optional.ofNullable(response).map(QueryOrderCommonInfoResponseType::getOrderCommonList).orElse(Lists.newArrayList())
                .stream().map(this::convert).collect(Collectors.toList());
    }


    private int checkPhoneNumber(DriverType driverType, List<Long> driverIdList, int sleepSeconds, boolean useCN, boolean autoUpdate, ExportConfig exportConfig)
            throws InterruptedException {
        int total = driverIdList.size();
        log("********************* {} begin checkRecruitDriverPhone *********************", driverType.name());
        List<DriverMobileCheckResult> invalidResultList = Lists.newArrayList();

        List<DriverInfo> driverInfoList = Optional.ofNullable(drvDrvierRepository.getDrvDriverPoList(driverIdList)).orElse(Lists.newArrayList()).stream().map(this::convert).collect(Collectors.toList());
        fillSupplierName(driverInfoList);
        List<DriverMobileCheckResult> checkResultList = checkWithSleep(driverInfoList, useCN, autoUpdate, sleepSeconds);
        checkResultList.stream().filter(o -> ObjectUtils.notEqual(o.getCtripCheckReason(), CtripFormatCheckReason.SUCCESS)).forEach(invalidResultList::add);

        // 导出
        exportInvalidResults(invalidResultList, exportConfig);

        log("********************* finish checkRecruitDriverPhone,total={} *********************", total);
        return total;
    }

    private void fillSupplierName(List<DriverInfo> driverInfos) {
        if (CollectionUtils.isNotEmpty(driverInfos)) {
            return;
        }
        List<Long> providerIdList = driverInfos.stream().map(o -> o.getProviderId()).distinct().collect(Collectors.toList());
        Map<Long, com.ctrip.dcs.scm.sdk.domain.supplier.Supplier> providerNameMap = enumRepository.batchGetSupplier(providerIdList);
        driverInfos.forEach(o -> o.setProviderName(Optional.ofNullable(providerNameMap.get(o.getProviderId())).map(x -> x.getName()).orElse("")));
    }

    private void exportInvalidResults(List<DriverMobileCheckResult> invalidResultList, ExportConfig exportConfig) {
        if (exportConfig != null && exportConfig.isExportExcel()) {
            exportInvalidResult(invalidResultList, exportConfig);
        }
    }

    private void log(String format, Object arg) {
        try {
            Optional.ofNullable(TaskHolder.getKeeper()).map(TaskMonitor::getLogger).ifPresent(logger -> logger.info(format, arg));
        } catch (Exception e) {
            LOGGER.warn("log fail", e);
        }
        LOGGER.info(format, arg);
    }

    private DriverInfo convert(DrvRecruitingPO po) {
        return new DriverInfo(DriverType.RECRUIT_DRIVER, po.getDrvRecruitingId(), BooleanUtils.toBoolean(po.getActive()), po.getDrvName(), po.getSupplierId(), po.getCountryId(), po.getCountryName(), po.getCityId(), po.getIgtCode(), po.getDrvPhone(), "", 0);
    }

    private DriverInfo convert(DrvDriverPO po) {
        return new DriverInfo(DriverType.REAL_DRIVER, po.getDrvId(), BooleanUtils.toBoolean(po.getActive()), po.getDrvName(), po.getSupplierId(), po.getCountryId(), po.getCountryName(), po.getCityId(), po.getIgtCode(), po.getDrvPhone(), po.getUid(), ObjectUtils.defaultIfNull(po.getDrvStatus(), 0));
    }

    private DriverInfo convert(OrderCommonDto order) {
        DriverInfo driverInfo = new DriverInfo();
        driverInfo.setDriverType(DriverType.ORDER_DRIVER);
        // 订单上的driverId不一定是纯数字
        if (StringUtils.isNumeric(order.getDriverId())) {
            driverInfo.setDriverId(Long.valueOf(order.getDriverId()));
        }
        driverInfo.setDriverIdStr(order.getDriverId());
        driverInfo.setActive(true);
        driverInfo.setDriverName(order.getDriverName());
        driverInfo.setProviderId(order.getActualVendorId());
        driverInfo.setProviderName(order.getActualVendorName());
        driverInfo.setServiceProviderId(order.getServiceProviderId());
        driverInfo.setServiceProviderName(order.getServiceProviderName());
        driverInfo.setCountryId(order.getCountryId());
        driverInfo.setCountryName(order.getCountryName());
        driverInfo.setCityId(order.getFromCityId());
        driverInfo.setHasOrderIn30Days(true);
        String useTimeBJ = Optional.ofNullable(order.getUseTimeBJ()).map(o -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(o.getTime())).orElse("");
        driverInfo.setLatestOrderTime(useTimeBJ);
        driverInfo.setOrderSys(order.getOrderSys());
        driverInfo.setOrderPatternType(order.getPatternType());
        driverInfo.setSaleMode(order.getSaleMode());
        driverInfo.setOrderDataChangeCreateTime(order.getDataChangeCreateTime());
        driverInfo.setConnectMode(order.getConnectMode());
        driverInfo.setOrderId(order.getOrderId());
        convertOrderInfoDetail(driverInfo, order.getOrderId());

        return driverInfo;
    }

    private void convertOrderInfoDetail(DriverInfo driverInfo, Long orderId) {
        // 订单详情获取信息
        QueryTradeDetailRequestType requestType = new QueryTradeDetailRequestType();
        requestType.setOrderId(orderId);
        QueryTradeDetailResponseType response = igtOrderQueryServiceProxy.queryTradeDetail(requestType);
        if (response != null && response.getMasterOrderDetail() != null && CollectionUtils.isNotEmpty(response.getMasterOrderDetail().getDetails())) {
            OrderSourceDTO sourceInfo = response.getMasterOrderDetail().getSourceInfo();
            if (sourceInfo != null) {
                driverInfo.setMarkArea(sourceInfo.getMarkArea());
            }
            TradeServiceProviderDTO serviceProviderInfo = response.getMasterOrderDetail().getDetails().get(0).getServiceProviderInfo();
            if (serviceProviderInfo != null) {
                driverInfo.setAreaCode(serviceProviderInfo.getDriverPhoneCode());
                driverInfo.setPhone(serviceProviderInfo.getDriverPhone());
            }
        }
    }

    private List<DriverMobileCheckResult> check(List<DriverInfo> drivers, boolean useCN, boolean autoUpdate) {
        List<DriverMobileCheckResult> resultList = Lists.newArrayList();
        for (DriverInfo d : drivers) {
            resultList.add(check(d, useCN, autoUpdate));
        }
        return resultList;
    }

    private List<DriverMobileCheckResult> checkWithSleep(List<DriverInfo> drivers, boolean useCN, boolean autoUpdate, int sleepSeconds) throws InterruptedException {
        List<DriverMobileCheckResult> resultList = Lists.newArrayList();
        for (DriverInfo d : drivers) {
            resultList.add(check(d, useCN, autoUpdate));
            if (autoUpdate) {
                TimeUnit.SECONDS.sleep(sleepSeconds);
            }
        }
        return resultList;
    }

    private DriverMobileCheckResult check(DriverInfo driver, boolean useCN, boolean autoUpdate) {
        DriverMobileCheckResult result = new DriverMobileCheckResult();

        // 构建基础信息
        result.setDriverType(driver.getDriverType());
        result.setDriverId(ObjectUtils.defaultIfNull(driver.getDriverId(), 0L));
        result.setDriverIdStr(driver.getDriverIdStr());
        result.setActive(driver.isActive());
        result.setDriverName(driver.getDriverName());
        result.setUid(driver.getUid());
        result.setProviderId(driver.getProviderId());
        result.setProviderName(driver.getProviderName());
        result.setServiceProviderId(driver.getServiceProviderId());
        result.setDriverCountryId(driver.getCountryId());
        result.setDriverCountryName(driver.getCountryName());
        result.setCityId(driver.getCityId());
        result.setOrderSys(driver.getOrderSys());
        result.setOrderPatternType(driver.getOrderPatternType());
        result.setOrderSaleMode(driver.getSaleMode());
        result.setOrderConnectMode(driver.getConnectMode());
        result.setIsChineseMainland(driver.getMarkArea());
        result.setOrderId(driver.getOrderId());
        result.setDriverStatus(driver.getDriverStatus());

        // 检查号码是否填写
        result.setHasAreaCode(StringUtils.isNotEmpty(driver.getAreaCode()));
        result.setHasPhone(StringUtils.isNotEmpty(driver.getPhone()));
        if (!result.hasPhone) {
            return result;
        }

        // 解密号码
        String encryptPhone = driver.getPhone();
        String decryptPhone = phoneNumberServiceGateway.decryptPhone(encryptPhone);
        result.setDecryptPhone(decryptPhone);
        result.setDecryptCheck(!StringUtils.equals(encryptPhone, decryptPhone));
        if (StringUtils.isNotBlank(driver.getAreaCode())) {
            result.setOriginAreaCode(driver.getAreaCode());
        }
        result.setOriginPhone(encryptPhone);

        // 检查是否已0开始
        checkZero(result);
        // 检查静态格式是否合法
        checkFormat(result, useCN);
        if (!DriverType.ORDER_DRIVER.equals(driver.driverType)) {
            // 自动更新手机号
            autoUpdatePhoneNumber(result, autoUpdate);
        }
        // ivr check
        // log
        Cat.logTags(SCENARIO, result.convertTagMap(), Collections.emptyMap());
        return result;
    }

    private void autoUpdatePhoneNumber(DriverMobileCheckResult checkResult, boolean autoUpdate) {
        if (!DriverType.REAL_DRIVER.equals(checkResult.getDriverType())) {
            return;
        }
        if (ObjectUtils.notEqual(checkResult.getCtripCheckReason(), CtripFormatCheckReason.START_WITH_ZERO) && ObjectUtils.notEqual(checkResult.getCtripCheckReason(), CtripFormatCheckReason.START_WITH_AREA_CODE)) {
            return;
        }
        if (!checkResult.isActive()) {
            checkResult.setAutoUpdateResult(AutoUpdateResult.NOT_ACTIVE.name());
            return;
        }
        if (!checkResult.isCtripMobileValid()) {
            checkResult.setAutoUpdateResult(AutoUpdateResult.PHONE_CTRIP_INVALID.name());
            LOGGER.warn("phone ctrip invalid, driverId : {}, ctripFmtPhone : {}", checkResult.getDriverId(), checkResult.getCtripFmtPhone());
            return;
        }
        if (checkResult.getCtripFmtPhone().equals(checkResult.getDecryptPhone())) {
            checkResult.setAutoUpdateResult(AutoUpdateResult.SAME_PHONE.name());
            return;
        }
        String correctEncryptPhone = checkResult.getEncryptCtripFmtPhone();
        if (StringUtils.isBlank(correctEncryptPhone)) {
            // 无加密手机号
            checkResult.setAutoUpdateResult(AutoUpdateResult.NO_EncryptCtripFmtPhone.name());
            LOGGER.warn("no encrypt ctrip fmt phone, driverId : {}, ctripFmtPhone : {}", checkResult.getDriverId(), checkResult.getCtripFmtPhone());
            return;
        }
        DrvDriverPO drvDriverPO = drvDrvierRepository.queryOneDrvByPhone(correctEncryptPhone);
        if (drvDriverPO != null) {
            checkResult.setAutoUpdateResult(AutoUpdateResult.ANOTHER_DRIVER_CONFLICT.name());
            LOGGER.warn("driver phone has bound another account, driverId : {}, driverUid : {} anotherDriverId : {}", checkResult.getDriverId(), checkResult.getUid(), drvDriverPO.getDrvId());
            return;
        }
        String userCenterUid = getUidByPhone(checkResult.getOriginAreaCode(), correctEncryptPhone);
        if (StringUtils.isNotBlank(userCenterUid) && ObjectUtils.notEqual(userCenterUid, checkResult.getUid())) {
            // 手机号在用户中心关联的uid与司机uid不匹配（即去0手机号已关联其他的uid），则不更新
            checkResult.setAutoUpdateResult(AutoUpdateResult.UID_CONFLICT.name());
            LOGGER.warn("driver phone has bound another uid, driverId : {}, driverUid : {} anotherUid : {}", checkResult.getDriverId(), checkResult.getUid(), userCenterUid);
            return;
        }
        checkResult.setFixedPhone(correctEncryptPhone);
        try {
            boolean updateResult = true;
            if (autoUpdate) {
                updateResult = update(checkResult, correctEncryptPhone);
            }
            if (updateResult) {
                checkResult.setAutoUpdateResult(AutoUpdateResult.SUCCESS.name());
            }
            LOGGER.info("auto update driver phone success, driverId : {}, before : {}, after : {}", checkResult.getDriverId(), checkResult.getOriginPhone(), correctEncryptPhone);
        } catch (Exception e) {
            checkResult.setAutoUpdateResult(AutoUpdateResult.ERROR.name());
            LOGGER.warn("job update driver phone error", e);
        }
    }

    private boolean update(DriverMobileCheckResult checkResult, String correctEncryptPhone) {
        // 先更新供应链db
        try {
            int updateCount = drvDrvierRepository.updateDrvPhone(checkResult.getDriverId(), correctEncryptPhone, "System");
            if (updateCount > 0) {
                // 发送司机更新消息
                qmqProducerCommandService.sendDrvChangeQmq(checkResult.getDriverId(), 2, 1);
            }
        } catch (Exception e) {
            checkResult.setAutoUpdateResult(AutoUpdateResult.FAILED.name());
            LOGGER.warn("update tms driver table failed, uid : " + checkResult.getUid(), e);
            return false;
        }

        // 再调用账号公共接口更新
        if (StringUtils.isNotBlank(checkResult.getUid())) {
            UpdateAccountRequestType requestType = new UpdateAccountRequestType();
            requestType.setUid(checkResult.getUid());
            requestType.setCountryCode(checkResult.getOriginAreaCode());
            requestType.setPhoneNumber(correctEncryptPhone);
            requestType.setSource("Driver");
            UpdateAccountResponseType resp = driverDomainService.updateAccount(requestType);
            if (ObjectUtils.notEqual(resp.getResponseResult().getReturnCode(), "200")) {
                checkResult.setAutoUpdateResult(AutoUpdateResult.FAILED.name());
                LOGGER.warn("update account failed, uid : {}, msg : {}", checkResult.getUid(), resp.getResponseResult().getReturnMessage());
            }
        } else {
            LOGGER.info("no uid not update account: " + checkResult.getDriverId());
        }

        // 给司机发通知
        if (CtripFormatCheckReason.START_WITH_AREA_CODE.equals(checkResult.getCtripCheckReason())) {
            sendMessageToDriver(checkResult.getDriverId(), checkResult.getCtripFmtAreaCode(), checkResult.getCtripFmtPhone());
        }
        return true;
    }

    private void sendMessageToDriver(Long driverId, String ctripFmtPhone, String newMobilePhone) {

        PushMessageDTO pushMessageDTO = new PushMessageDTO();
        pushMessageDTO.setDriverIds(Lists.newArrayList(driverId));
        pushMessageDTO.setTemplateId("4069");
        Map<String, String> sharkValues = Maps.newHashMap();
        sharkValues.put("newMobile", String.format("+%s-%s", ctripFmtPhone, newMobilePhone));
        pushMessageDTO.setSharkValues(sharkValues);

        String data = JsonUtil.toJson(pushMessageDTO);
        Map<String, Object> qmqContent = Maps.newHashMap();
        qmqContent.put("data", data);
        tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.DCS_DRIVER_PUSH_MESSAGE, null, qmqContent);
    }


    private String getUidByPhone(String countryCode, String phoneNumber) {
        GetAccountByMobilePhoneRequestType requestType = new GetAccountByMobilePhoneRequestType();
        requestType.setSubSystemId(SUBSYSTEM_ID);
        requestType.setCountryCode(countryCode);
        requestType.setPhoneNumber(phoneNumber);
        requestType.setExpect(Lists.newArrayList(ExpectedInfo.ACCOUNT_META_INFO));
        try {
            AccountInfoResponseType responseType = accountServiceProxy.getAccountByMobilePhone(requestType);
            if (responseType != null) {
                return responseType.getUid();
            }
        } catch (Exception ex) {
            LOGGER.warn("get uid by phone error " + countryCode + "-" + phoneNumber, ex);
        }
        return Strings.EMPTY;
    }

    private void checkFormat(DriverMobileCheckResult result, boolean useCN) {
        Pair<String, String> regionCodeAreaCodePair = parseRegionCode(result);
        // 是否一致
        result.setCountryAreaCode(regionCodeAreaCodePair.getLeft());
        result.setCountryAreaCodeMatch(StringUtils.equals(result.getCountryAreaCode(), result.getOriginAreaCode()));

        String regionCode = useCN ? "CN" : regionCodeAreaCodePair.getKey();
        String decryptPhone = result.getDecryptPhone();
        // 手机号带0拆码
        while (StringUtils.isNotBlank(decryptPhone) && decryptPhone.startsWith("0")) {
            decryptPhone = decryptPhone.substring(1);
        }
        String finalDecryptPhone = decryptPhone;
        String phoneNumber = String.format(MOBILE_FORMAT, result.getOriginAreaCode(), decryptPhone);
        phoneNumberServiceGateway.splitPhoneNumber(regionCode, phoneNumber)
                .ifPresent(numberDTO -> {
                    String ctripFmtPhone = numberDTO.getBodyNumber();
                    if (StringUtils.isNotBlank(numberDTO.getCityCode())) {
                        ctripFmtPhone = numberDTO.getCityCode() + ctripFmtPhone;
                    }
                    result.setCtripFmtPhone(ctripFmtPhone);
                    while (StringUtils.isNotBlank(ctripFmtPhone) && ctripFmtPhone.startsWith("0")) {
                        // 拆码后仍然带0，去掉
                        ctripFmtPhone = ctripFmtPhone.substring(1);
                    }
                    result.setEncryptCtripFmtPhone(TmsTransUtil.encrypt(ctripFmtPhone, KeyType.Phone));
                    if (StringUtils.isNotBlank(numberDTO.getCountryCode())) {
                        result.setCtripFmtAreaCode(numberDTO.getCountryCode());
                    }
                    result.setCtripPhoneType(numberDTO.getPhoneType().name());
                    result.setCtripMobileValid(numberDTO.isMobile());
                    result.setCtripValid(numberDTO.isValid());
                    result.setCtripCarrier(numberDTO.getCarrier());
                    result.setOriginFmtPhoneSame(StringUtils.equals(finalDecryptPhone, result.getCtripFmtPhone()));
                    result.setOriginFmtAreaCodeSame(StringUtils.equals(result.getOriginAreaCode(), result.getCtripFmtAreaCode()));
                });
        if (decryptPhone.startsWith(result.getOriginAreaCode()) || decryptPhone.startsWith(result.getCtripFmtAreaCode())) {
            result.setCtripCheckReason(CtripFormatCheckReason.START_WITH_AREA_CODE);
            return;
        }
        if (result.startWithZero) {
            result.setCtripCheckReason(CtripFormatCheckReason.START_WITH_ZERO);
            return;
        }
        if (result.ctripMobileValid && result.ctripValid) {
            result.setCtripCheckReason(CtripFormatCheckReason.SUCCESS);
            return;
        }
        result.setCtripCheckReason(CtripFormatCheckReason.PHONE_NUMBER_ILLEGAL);
    }

    private Pair<String, String> parseRegionCode(DriverMobileCheckResult result) {
        Location location = locationRepository.findOne(3, result.getCityId());
        if (Objects.nonNull(location) && Boolean.TRUE.equals(location.isChineseHMT())) {
            return HM_RegionCodeMap.getOrDefault(result.getCityId(), TW_REGION);
        }
        return Optional.ofNullable(locationRepository.findOne(1, result.getDriverCountryId())).map(geo -> Pair.of(geo.getGeoCode(), cache.getUnchecked(geo.getGeoId()))).orElse(Pair.of(StringUtils.EMPTY, StringUtils.EMPTY));
    }

    private String getAreaCodeByCountryId(Long countryId) {
        QueryTelephoneAreaCodeRequestType requestType = new QueryTelephoneAreaCodeRequestType();
        requestType.setType(AreaCodeQueryType.GEO);
        requestType.setGeoKey(new GeoKey(1, countryId));
        return Optional.ofNullable(geoPlatformServiceProxy.queryTelephoneAreaCode(requestType)).map(QueryTelephoneAreaCodeResponseType::getData).map(TelephoneAreaCodeContract::getAreaCode).map(String::valueOf).orElse(StringUtils.EMPTY);
    }

    private void checkZero(DriverMobileCheckResult result) {
        result.setStartWithZero(result.getOriginPhone().startsWith("0"));
        if (!result.startWithZero) {
            return;
        }
        String phone = result.getOriginPhone();
        int zeroCount = phone.startsWith("000") ? 3 : phone.startsWith("00") ? 2 : 1;
        result.setStartZeroCount(zeroCount);
    }

    private void exportInvalidResult(List<DriverMobileCheckResult> invalidResultList, ExportConfig exportConfig) {
        if (CollectionUtils.isEmpty(invalidResultList)) {
            log("no invalid result", "");
            return;
        }
        log("invalid result size : {}", invalidResultList.size());
        List<String> exportFieldList = ObjectUtils.defaultIfNull(exportConfig.getExportFieldList(), Arrays.stream(DriverMobileCheckResult.class.getDeclaredFields()).map(Field::getName).collect(Collectors.toList()));
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        WriteSheet writeSheet = EasyExcelFactory.writerSheet().registerWriteHandler(new ColumnWidthStyleStrategy(100)).build();
        List<List<String>> headList = new ArrayList<>();
        LOGGER.info("exportFields:{}", JsonUtil.toJson(exportFieldList));
        exportFieldList.forEach(field -> {
            List<String> head = new ArrayList<>();
            head.add(field);
            headList.add(head);
        });

        writeSheet.setHead(headList);
        try (ExcelWriter excelWriter = EasyExcelFactory.write(byteArrayOutputStream).build()) {
            buildExcel(invalidResultList, excelWriter, writeSheet, exportFieldList);
            String url = upLoadExcel(byteArrayOutputStream);
            log("export excel url: {}", url);
            commonCommandService.sendEmail(exportConfig.getSubject(), exportConfig.getReceiver(), url);
        } catch (Exception e) {
            log("export excel error : {}", e.getMessage());
            LOGGER.error("Exception error", e);
        }
    }

    private void buildExcel(List<DriverMobileCheckResult> resultList, ExcelWriter excelWriter,
                            WriteSheet writeSheet, List<String> exportFieldList) {
        List<List<String>> excelList = convertExcelDataList(resultList, exportFieldList);

        excelWriter.write(excelList, writeSheet);
        excelWriter.finish();
    }

    protected List<List<String>> convertExcelDataList(List<DriverMobileCheckResult> resultList, List<String> exportFields) {
        List<Map<String, String>> checkResultFieldMap = resultList.stream().map(o -> o.convertTagMap(true)).collect(Collectors.toList());
        return checkResultFieldMap.stream().map(map -> {
            List<String> dataList = new ArrayList<>();
            exportFields.forEach(field -> {
                dataList.add(map.get(field));
            });
            return dataList;
        }).collect(Collectors.toList());
    }


    enum DriverType {
        RECRUIT_DRIVER, REAL_DRIVER, ORDER_DRIVER
    }

    enum AutoUpdateResult {
        NOT_ACTIVE, PHONE_CTRIP_INVALID, SAME_PHONE, NO_EncryptCtripFmtPhone, ANOTHER_DRIVER_CONFLICT, UID_CONFLICT, SUCCESS, ERROR, FAILED
    }

    @Getter
    @Setter
    @NoArgsConstructor
    static class DriverInfo {
        private DriverType driverType;
        private Long driverId;
        private String driverIdStr;
        private boolean active;
        private String driverName;
        /**
         * 供应商id
         */
        private Long providerId;
        /**
         * 供应商名称
         */
        private String providerName;
        /**
         * 服务商id。数据源是订单时才有值
         */
        private Long serviceProviderId;
        /**
         * 服务商名称。数据源是订单时才有值
         */
        private String serviceProviderName;
        private Long countryId;
        private String countryName;
        /**
         * 数据源是订单时取fromCityId
         */
        private Long cityId;
        private String areaCode;
        private String phone;
        private String uid;
        private int driverStatus;

        /**
         * 30天内是否有订单
         */
        private boolean hasOrderIn30Days;

        /**
         * 上次订单时间
         */
        private String latestOrderTime;

        /**
         * 订单类型 1 C、2 Q、3 T。数据源是订单时才有值
         */
        private Integer orderSys;

        /**
         * 订单产品形态 17接机/站 18送机/站 23日租 28RTN 29RTN预约 35超级巴士 36-保险 50GPS 55点到点 60OSD打包 66线路包车 88定制包车 70ISD春节打包 77线路自驾 80驾照翻译件 81香港驾照 82韩国驾照 83当地语言公证件(1德语2意大利3法语4西班牙5英语) 84新西兰NZTA翻译件
         * 数据源是订单时才有值
         */
        private Integer orderPatternType;

        /**
         * 售卖模式 1：代理，2：零售，5：自营。数据源是订单时才有值
         */
        private Integer saleMode;

        /**
         * 对接模式 1vbk 2api。数据源是订单时才有值
         */
        private Integer connectMode;

        /**
         * 是否境内，1：境内，2：境外。数据源是订单时才有值
         */
        private Integer markArea;

        /**
         * 订单号。数据源是订单时才有值
         */
        private Long orderId;


        /**
         * 订单创建时间。数据源是订单时才有值
         */
        private Calendar orderDataChangeCreateTime;

        public DriverInfo(DriverType driverType, Long driverId, boolean active, String driverName, Long providerId, Long countryId, String countryName, Long cityId, String areaCode, String phone, String uid, int driverStatus) {
            this.driverType = driverType;
            this.driverId = driverId;
            this.driverIdStr = driverId.toString();
            this.active = active;
            this.driverName = driverName;
            this.providerId = providerId;
            this.countryId = countryId;
            this.countryName = countryName;
            this.cityId = cityId;
            this.areaCode = areaCode;
            this.phone = phone;
            this.uid = uid;
            this.driverStatus = driverStatus;
        }
    }

    @AllArgsConstructor
    private static class PageFrom {
        private long lastId;
        private int pageSize;
    }

    private enum CtripFormatCheckReason {
        START_WITH_AREA_CODE, START_WITH_ZERO, PHONE_NUMBER_ILLEGAL, SUCCESS;
    }

    @Getter
    @Setter
    static class DriverMobileCheckResult {
        private DriverType driverType;
        private Long providerId = 0L;
        private String providerName = "";
        private Long serviceProviderId = 0L;
        private Long driverCountryId = 0L;
        private String driverCountryName = "NA";
        private Long cityId = 0L;
        private Long driverId = 0L;
        private String driverIdStr = "";
        private String uid = "";
        private boolean active;
        private boolean hasAreaCode = false;
        private boolean hasPhone = false;
        private boolean decryptCheck = false;
        private String driverName = "NA";
        private String decryptPhone = "NA";
        private String originAreaCode = "NA";
        private String originPhone = "NA";
        private boolean startWithZero = false;
        private int startZeroCount;
        private boolean ctripValid;
        private boolean ctripMobileValid;
        private String ctripFmtAreaCode = "NA";
        private String ctripFmtPhone = "NA";
        private String encryptCtripFmtPhone = "";
        private String ctripPhoneType = "NA";
        private String ctripCarrier = "NA";
        private CtripFormatCheckReason ctripCheckReason = CtripFormatCheckReason.SUCCESS;
        private boolean originFmtAreaCodeSame;
        private boolean originFmtPhoneSame;
        private boolean countryAreaCodeMatch = false;
        private String countryAreaCode = "NA";
        private boolean originIvrCheck;
        private String originIvrReason = "NA";
        private boolean fmtIvrCheck;
        private String fmtIvrReason = "NA";
        private String autoUpdateResult = "";
        private String fixedPhone = "";
        private Integer orderSys;
        private Integer orderPatternType;
        private Integer orderSaleMode;
        private Integer orderConnectMode;
        private Integer isChineseMainland;
        private Long orderId;
        private int driverStatus;

        public Map<String, String> convertTagMap() {
            return convertTagMap(false);
        }

        public Map<String, String> convertTagMap(boolean decrypt) {
            Map<String, String> map = Maps.newHashMapWithExpectedSize(50);
            map.put("driverType", driverType.name());
            map.put("providerId", Optional.ofNullable(providerId).map(Object::toString).orElse(""));
            map.put("providerName", providerName);
            map.put("driverCountryId", Optional.ofNullable(driverCountryId).map(Object::toString).orElse(""));
            map.put("driverCountryName", driverCountryName);
            map.put("cityId", Optional.ofNullable(cityId).map(Object::toString).orElse(""));
            map.put("driverId", StringUtils.isNotBlank(driverIdStr) ? driverIdStr : String.valueOf(driverId));
            map.put("driverIdStr", driverIdStr);
            map.put("active", String.valueOf(active));
            map.put("hasAreaCode", String.valueOf(hasAreaCode));
            map.put("hasPhone", String.valueOf(hasPhone));
            map.put("decryptCheck", String.valueOf(decryptCheck));
            map.put("driverName", driverName);
            map.put("originAreaCode", originAreaCode);
            if (!StringUtils.isNumeric(originPhone)) {
                map.put("originPhone", originPhone);
            }
            map.put("startWithZero", String.valueOf(startWithZero));
            map.put("startZeroCount", String.valueOf(startZeroCount));
            map.put("ctripValid", String.valueOf(ctripValid));
            map.put("ctripMobileValid", String.valueOf(ctripMobileValid));
            map.put("ctripFmtAreaCode", ctripFmtAreaCode);
            map.put("ctripCheckReason", ctripCheckReason.name());
            map.put("ctripPhoneType", ctripPhoneType);
            map.put("ctripCarrier", ctripCarrier);
            map.put("originFmtAreaCodeSame", String.valueOf(originFmtAreaCodeSame));
            map.put("originFmtPhoneSame", String.valueOf(originFmtPhoneSame));
            map.put("originIvrCheck", String.valueOf(originIvrCheck));
            map.put("originIvrReason", originIvrReason);
            map.put("fmtIvrCheck", String.valueOf(fmtIvrCheck));
            map.put("fmtIvrReason", fmtIvrReason);
            map.put("countryAreaCodeMatch", String.valueOf(countryAreaCodeMatch));
            map.put("countryAreaCode", countryAreaCode);
            map.put("encryptCtripFmtPhone", encryptCtripFmtPhone);
            map.put("autoUpdateResult", autoUpdateResult);
            map.put("fixedPhone", fixedPhone);
            map.put("uid", uid);
            map.put("serviceProviderId", Optional.ofNullable(serviceProviderId).map(Object::toString).orElse(""));
            map.put("orderSys", Optional.ofNullable(orderSys).map(Object::toString).orElse(""));
            map.put("orderPatternType", Optional.ofNullable(orderPatternType).map(Object::toString).orElse(""));
            map.put("orderConnectMode", Optional.ofNullable(orderConnectMode).map(Object::toString).orElse(""));
            map.put("isChineseMainland", Optional.ofNullable(isChineseMainland).map(Object::toString).orElse(""));
            map.put("orderSaleMode", Optional.ofNullable(orderSaleMode).map(Object::toString).orElse(""));
            map.put("orderId", Optional.ofNullable(orderId).map(Object::toString).orElse(""));
            map.put("driverStatus", driverStatus + "");
            if (decrypt) {
                map.put("decryptPhone", decryptPhone);
                map.put("ctripFmtPhone", ctripFmtPhone);
            }
            return map;
        }
    }

    @Getter
    @Setter
    private static class ExportConfig {
        private boolean exportExcel;

        private List<String> exportFieldList;

        private String subject = "\u624b\u673a\u53f7\u68c0\u67e5\u7ed3\u679c";

        private String receiver;
    }

    @Getter
    @Setter
    private static class OrderConfig {
        private String queryStartTimeStr;

        private String queryEndTimeStr;

        private List<Integer> patternTypeList;
    }


    protected String upLoadExcel(ByteArrayOutputStream byteArrayOutputStream) {
        String FILE_NAME = "\u5f02\u5e38\u624b\u673a\u53f7%s.xlsx";
        String dateFormat = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        String fileName = String.format(FILE_NAME, dateFormat);

        String url = nepheleHttpService.upload(byteArrayOutputStream.toByteArray(), fileName);
        LOGGER.info("OrderCommandListener_doExport url :{}", url);
        return url;
    }
}
