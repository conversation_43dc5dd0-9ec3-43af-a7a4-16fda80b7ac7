package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryEffCapacityVehicleTypeSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryEffCapacityVehicleTypeSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.VehicleTypeSOADTO;
import com.ctrip.dcs.tms.transport.application.query.EffectiveCapacityQueryService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class QueryEffCapacityVehicleTypeExecutor extends AbstractRpcExecutor<QueryEffCapacityVehicleTypeSOARequestType, QueryEffCapacityVehicleTypeSOAResponseType> implements Validator<QueryEffCapacityVehicleTypeSOARequestType> {

    @Autowired
    EffectiveCapacityQueryService service;

    @Override
    public QueryEffCapacityVehicleTypeSOAResponseType execute(QueryEffCapacityVehicleTypeSOARequestType requestType) {
        QueryEffCapacityVehicleTypeSOAResponseType responseType = new QueryEffCapacityVehicleTypeSOAResponseType();
        Result<List<VehicleTypeSOADTO>> result = service.queryEffCapacityVehicleType();
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<QueryEffCapacityVehicleTypeSOARequestType> validator) {

    }
}
