package com.ctrip.dcs.tms.transport.interfaces.provider.executor.task;

import com.ctrip.dcs.tms.transport.api.model.QueryTaskConfigRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryTaskConfigResponseType;
import com.ctrip.dcs.tms.transport.task.application.query.impl.TaskQueryService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class QueryTaskConfigExecutor extends AbstractRpcExecutor<QueryTaskConfigRequestType, QueryTaskConfigResponseType> implements
  Validator<QueryTaskConfigRequestType> {

  @Autowired
  TaskQueryService taskQueryService;

  @Override
  public QueryTaskConfigResponseType execute(QueryTaskConfigRequestType requestType) {
    Result<String> result = taskQueryService.queryTaskConfig(requestType);
    QueryTaskConfigResponseType responseType = new QueryTaskConfigResponseType();
    responseType.setData(result.getData());
    if (result.isSuccess()) {
      return ServiceResponseUtils.success(responseType);
    }else {
      return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
    }
  }
}
