package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * <AUTHOR>
 * @Description  查询运力组点位
 * @Date 16:42 2020/8/6
 * @Param 
 * @return 
 **/
@Component
public class QueryLocationByTransPortGroupExecutor extends AbstractRpcExecutor<QueryLocationByTransGroupSOARequestType, QueryLocationByTransGroupSOAResponseType> implements Validator<QueryLocationByTransGroupSOARequestType> {

    @Autowired
    private TransportGroupQueryService queryService;

    @Override
    public QueryLocationByTransGroupSOAResponseType execute(QueryLocationByTransGroupSOARequestType requestType) {
        QueryLocationByTransGroupSOAResponseType responseType = new QueryLocationByTransGroupSOAResponseType();
        Result<List<LocationDTOSOA>> result =  queryService.queryLocationByGroup(requestType.getTransportGroupId());
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }else {
            return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<QueryLocationByTransGroupSOARequestType> validator) {
        validator.ruleFor("transportGroupId").notNull();
    }
}
