package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.handler.cache.AbstraceCacheHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.interfaces.bridge.ProductLineBridgeManagement;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;

import com.ctriposs.baiji.rpc.server.validation.*;
import com.google.common.collect.Lists;
import com.google.common.primitives.Longs;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;
import java.util.stream.Collectors;

import static com.ctrip.dcs.tms.transport.infrastructure.common.util.ResponseResultUtil.logQueryResult;
import static java.util.Optional.ofNullable;

/**
 * 2020-12-02 17:52
 * 2021-10-28 19:32 改版
 *
 * <AUTHOR>
 */
@Component
public class DriverCacheExecutor extends AbstractRpcExecutor<DriverInfoSOARequestType, DriverInfoSOAResponseType> implements Validator<DriverInfoSOARequestType> {

    private static final Logger logger = LoggerFactory.getLogger(DriverCacheExecutor.class);

    @Autowired
    ProductLineBridgeManagement productLineBridgeManagement;

    @Autowired
    private TmsTransportQconfig tmsTransportQconfig;

    @Override
    public void onExecuting(DriverInfoSOARequestType req) {
        if (Constant.NULL_STR.equalsIgnoreCase(req.getTransportGroupIds())) {
            req.setTransportGroupIds(null);
        }
        if (Constant.NULL_STR.equalsIgnoreCase(req.getDriverIds())) {
            req.setDriverIds(null);
        }
        if (Constant.NULL_STR.equalsIgnoreCase(req.getCoopModes())) {
            req.setCoopModes(null);
        }
        if (StringUtils.isNotBlank(req.getDriverPhone()) && req.getDriverPhone().startsWith("0") && BooleanUtils.toBooleanDefaultIfNull(tmsTransportQconfig.getDrvPhoneTrimSwitch(), false)) {
            String decrypt = req.getDriverPhone();
            try {
                decrypt = TmsTransUtil.decrypt(req.getDriverPhone(), KeyType.Phone);
            } catch (Exception e) {
                logger.warn("decrypt error", e);
            }
            req.setDriverPhone(ofNullable(Longs.tryParse(decrypt)).map(String::valueOf).orElse(req.getDriverPhone()));
        }
    }

    @Override
    public DriverInfoSOAResponseType execute(DriverInfoSOARequestType req) {
        DriverInfoSOAResponseType responseType = productLineBridgeManagement.queryDriver(req);
        List<DriverInfo> driverInfoList = ofNullable(responseType.getDriverList()).orElse(Lists.newArrayList());
        driverInfoList.forEach(driverInfo -> {
            if (CollectionUtils.isNotEmpty(driverInfo.getDispatchSupplierIdList())) {
                driverInfo.setDispatchSupplierIdList(driverInfo.getDispatchSupplierIdList().stream().sorted().collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(driverInfo.getTransportGroups())) {
                driverInfo.setTransportGroups(driverInfo.getTransportGroups().stream().sorted(Comparator.comparing(TransportGroup::getTransportGroupId, Comparator.nullsFirst(Long::compareTo))
                  .thenComparing(TransportGroup::getApplyStatus, Comparator.nullsFirst(Integer::compareTo))).collect(Collectors.toList()));
            }
        });
        responseType.setDriverList(driverInfoList.stream().sorted(Comparator.comparing(
          DriverInfo::getDriverId)).collect(
          Collectors.toList()));
        return responseType;
    }

    @Override
    public void validate(AbstractValidator<DriverInfoSOARequestType> validator) {

    }

    @Override
    public void onExecuted(DriverInfoSOARequestType req, DriverInfoSOAResponseType resp) {
        AbstraceCacheHandler.setExtension(resp);
        logQueryResult(resp.getDriverList());
    }
}
