package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * <AUTHOR>
 * @Date 2020/4/29 17:50
 */
@Component
public class CheckRecruitingAccountExecutor extends AbstractRpcExecutor<CheckRecruitingAccountSOARequestType, CheckRecruitingAccountSOAResponseType> implements Validator<CheckRecruitingAccountSOARequestType> {

    @Autowired
    private RecruitingQueryService recruitingQueryService;

    @Override
    public CheckRecruitingAccountSOAResponseType execute(CheckRecruitingAccountSOARequestType checkRecruitingPhoneSOARequestType) {
        CheckRecruitingAccountSOAResponseType responseType = new CheckRecruitingAccountSOAResponseType();
        Result<Boolean> result = recruitingQueryService.checkRecruitingAccount(checkRecruitingPhoneSOARequestType);
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }else {
            return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<CheckRecruitingAccountSOARequestType> validator, CheckRecruitingAccountSOARequestType req) {
        validator.ruleFor("drvAccount").notNull().notEmpty();
    }
}
