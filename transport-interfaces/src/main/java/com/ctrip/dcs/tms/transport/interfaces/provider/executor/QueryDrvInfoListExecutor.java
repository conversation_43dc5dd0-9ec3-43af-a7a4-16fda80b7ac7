package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.interfaces.provider.validator.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * 批量司机信息查询
 * <AUTHOR>
 * @Date 2020/5/15 14:31
 */
@Component
public class QueryDrvInfoListExecutor extends AbstractRpcExecutor<QueryDrvInfoListSOARequestType, QueryDrvInfoListSOAResponseType> implements Validator<QueryDrvInfoListSOARequestType> {

    @Autowired
    private DriverQueryService driverQueryService;

    @Override
    public QueryDrvInfoListSOAResponseType execute(QueryDrvInfoListSOARequestType queryDrvInfoListSOARequestType) {
        QueryDrvInfoListSOAResponseType soaResponseType = new QueryDrvInfoListSOAResponseType();
        Result<List<QueryDrvInfoDTOSOA>> infoDTOSOAResult = driverQueryService.queryDrvInfoList(queryDrvInfoListSOARequestType);
        if (infoDTOSOAResult.isSuccess()) {
            soaResponseType.setData(infoDTOSOAResult.getData());
            return ServiceResponseUtils.success(soaResponseType);
        }else {
            return ServiceResponseUtils.fail(soaResponseType, infoDTOSOAResult.getCode(),infoDTOSOAResult.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<QueryDrvInfoListSOARequestType> validator, QueryDrvInfoListSOARequestType req) {
        validator.ruleFor("drvId").setValidator(new NotEmptyCollectionValidator("drvId must be not null", ValidationErrors.NotNull));
    }
}
