package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

@Component
public class TodoListCountExecutor extends AbstractRpcExecutor<TodoListCountSOARequestType, TodoListCountSOAResponseType> implements Validator<TodoListCountSOARequestType> {

    private static final Logger logger = LoggerFactory.getLogger(TodoListCountExecutor.class);

    @Autowired
    private DrvVehRecruitingQueryService queryService;

    @Override
    public TodoListCountSOAResponseType execute(TodoListCountSOARequestType requestType) {
        TodoListCountSOAResponseType soaResponseType = new TodoListCountSOAResponseType();
        Result<List<TodoListTypeSOA>>  result = queryService.todoListCount(requestType);
        if (result.isSuccess()) {
            soaResponseType.setCategoryCode(requestType.getCategoryCode());
            soaResponseType.setTodoList(result.getData());
            return soaResponseType;
        }
        return ServiceResponseUtils.fail(soaResponseType);
    }

    @Override
    public void validate(AbstractValidator<TodoListCountSOARequestType> validator) {
    }

}