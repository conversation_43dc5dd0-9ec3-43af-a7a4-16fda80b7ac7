package com.ctrip.dcs.tms.transport.interfaces.provider.executor;


import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 司机详情
 */
@Component
public class QueryDrvFreezeDetailExecutor extends AbstractRpcExecutor<QueryDrvFreezeDetailSOARequestType, QueryDrvFreezeDetailSOAResponseType> implements Validator<QueryDrvFreezeDetailSOARequestType> {

    @Autowired
    DrvFreezeQueryService queryService;

    @Override
    public QueryDrvFreezeDetailSOAResponseType execute(QueryDrvFreezeDetailSOARequestType requestType) {
        QueryDrvFreezeDetailSOAResponseType responseType = new QueryDrvFreezeDetailSOAResponseType();
        Result<QueryDrvFreezeDetailDTOSOA> result = queryService.queryDrvFreezeDetail(requestType.getDrvId());
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType,"400",result.getMsg());
    }
}
