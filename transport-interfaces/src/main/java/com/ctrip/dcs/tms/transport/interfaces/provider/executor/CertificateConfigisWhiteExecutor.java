package com.ctrip.dcs.tms.transport.interfaces.provider.executor;


import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;


/**
　* @description: 证件配置白名单(编辑-车辆/司机)
　* <AUTHOR>
　* @date 2021/10/18 14:29
*/
@Component
public class CertificateConfigisWhiteExecutor extends AbstractRpcExecutor<CertificateConfigIsWhiteSOARequestType, CertificateConfigIsWhiteSOAResponseType> implements Validator<CertificateConfigIsWhiteSOARequestType> {

    @Autowired
    private CertificateQueryService certificateQueryService;

    @Override
    public CertificateConfigIsWhiteSOAResponseType execute(CertificateConfigIsWhiteSOARequestType requestType){
        CertificateConfigIsWhiteSOAResponseType responseType = new CertificateConfigIsWhiteSOAResponseType();
        Result<Boolean> result = certificateQueryService.certificateConfigIsWhite(requestType);
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }else {
            return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<CertificateConfigIsWhiteSOARequestType> validator) {
        validator.ruleFor("sourceId").notNull();
        validator.ruleFor("sourceType").notNull();
    }
}
