package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * <AUTHOR>
 */
@Component
public class OcrLimitCheckExecutor extends AbstractRpcExecutor<OcrLimitCheckSOARequestType, OcrLimitCheckSOAResponseType> implements Validator<OcrLimitCheckSOARequestType> {

    @Autowired
    TmsTransportQconfig qconfig;

    @Override
    public OcrLimitCheckSOAResponseType execute(OcrLimitCheckSOARequestType requestType) {
        OcrLimitCheckSOAResponseType soaResponseType = new OcrLimitCheckSOAResponseType();
        String key = new StringBuilder(Constant.RESTRICT_PREFIX_STR).append(requestType.getDrvPhone()).toString();
        Integer toDayReqCount = RedisUtils.get(key);
        if (toDayReqCount == null) {
            return ServiceResponseUtils.fail(soaResponseType);
        }
        if (toDayReqCount < qconfig.getOcrLimitCount()) {
            toDayReqCount++;
            RedisUtils.set(key, RedisUtils.ONE_DAY, toDayReqCount);
            return ServiceResponseUtils.success(soaResponseType);
        }
        return ServiceResponseUtils.fail(soaResponseType);
    }

    @Override
    public void validate(AbstractValidator<OcrLimitCheckSOARequestType> validator) {
        validator.ruleFor("drvPhone").notNull();
    }
}