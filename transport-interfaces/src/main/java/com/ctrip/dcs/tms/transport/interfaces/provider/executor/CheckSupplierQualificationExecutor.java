package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
* 校验供应商是否满足生成二维码资质
* <AUTHOR>
* @date 2020/4/22 11:47
*/
@Component
public class CheckSupplierQualificationExecutor extends AbstractRpcExecutor<CheckSupplierQualificationSOARequestType, CheckSupplierQualificationSOAResponseType> implements Validator<CheckSupplierQualificationSOARequestType> {

    @Autowired
    private DrvVehRecruitingQueryService queryService;

    @Override
    public CheckSupplierQualificationSOAResponseType execute(CheckSupplierQualificationSOARequestType requestType) {
        CheckSupplierQualificationSOAResponseType responseType = new CheckSupplierQualificationSOAResponseType();
        Result<String> result =  queryService.checkSupplierQualification(requestType.getSupplierId());
        if(result.isSuccess()){
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType);
    }

    @Override
    public void validate(AbstractValidator<CheckSupplierQualificationSOARequestType> validator) {
        validator.ruleFor("supplierId").notNull();
    }
}
