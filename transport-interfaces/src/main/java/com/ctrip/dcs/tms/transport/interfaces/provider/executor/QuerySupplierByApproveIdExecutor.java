package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.stereotype.*;

import javax.annotation.*;
import java.util.*;

@Component
public class QuerySupplierByApproveIdExecutor extends AbstractRpcExecutor<QuerySupplierByApproveIdSOARequestType, QuerySupplierByApproveIdSOAResponseType> implements Validator<QuerySupplierByApproveIdSOARequestType> {
    @Resource
    private TmsTransportApproveQueryService tmsTransportApproveQueryService;
    @Override
    public QuerySupplierByApproveIdSOAResponseType execute(QuerySupplierByApproveIdSOARequestType requestType) {
        try{
            Map<String,String> result =  tmsTransportApproveQueryService.querySupplierByApproveId(requestType.getApproveIds());
            QuerySupplierByApproveIdSOAResponseType responseType = new QuerySupplierByApproveIdSOAResponseType();
            responseType.setApproveSupplierIds(result);
            return ServiceResponseUtils.success(responseType);
        }catch (Exception e){
            return ServiceResponseUtils.fail(new QuerySupplierByApproveIdSOAResponseType());
        }
    }
    @Override
    public void validate(AbstractValidator<QuerySupplierByApproveIdSOARequestType> validator) {
        validator.ruleFor("approveIds").notNull();
        validator.ruleFor("approveIds").notEmpty();
    }
}
