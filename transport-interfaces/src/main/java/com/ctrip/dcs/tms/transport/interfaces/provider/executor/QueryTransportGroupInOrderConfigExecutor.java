package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryTransportGroupInOrderConfigRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryTransportGroupInOrderConfigResponseType;
import com.ctrip.dcs.tms.transport.api.model.TransportGroupInOrderConfigSOADTO;
import com.ctrip.dcs.tms.transport.api.model.TransportGroupInOrderCountSOADTO;
import com.ctrip.dcs.tms.transport.application.dto.TransportGroupInOrderConfigDTO;
import com.ctrip.dcs.tms.transport.application.dto.TransportGroupTimeSegmentConfigDTO;
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 根据运力组id 用车时间查询进单配置
 */
@Component
public class QueryTransportGroupInOrderConfigExecutor extends AbstractRpcExecutor<QueryTransportGroupInOrderConfigRequestType, QueryTransportGroupInOrderConfigResponseType> implements Validator<QueryTransportGroupInOrderConfigRequestType> {
    private static final Logger logger = LoggerFactory.getLogger(QueryTransportGroupInOrderConfigExecutor.class);
    @Autowired
    private TransportGroupQueryService queryService;
    @Override
    public QueryTransportGroupInOrderConfigResponseType execute(QueryTransportGroupInOrderConfigRequestType requestType) {
        try{
            Date bookTime = DateUtil.getDate(requestType.getBookTime(),DateUtil.YYYYMMDDHHMMSS);
            List<TransportGroupInOrderConfigDTO> configDTOS = queryService.queryInOrderConfig(requestType.getTransportGroupIds(),bookTime,requestType.getCityId(),requestType.getLocationCode(),
                requestType.getFilterDispatchOnly());
            return ServiceResponseUtils.success(getResponseType(configDTOS));
        }catch (BizException be){
            return ServiceResponseUtils.success(new QueryTransportGroupInOrderConfigResponseType());
        }catch (Exception e){
            logger.error("QueryTransportGroupInOrderConfigExecutor_ex",e);
            return ServiceResponseUtils.success(new QueryTransportGroupInOrderConfigResponseType());
        }
    }
    @Override
    public void validate(AbstractValidator<QueryTransportGroupInOrderConfigRequestType> validator) {
        validator.ruleFor("transportGroupIds").notNull();
        validator.ruleFor("cityId").notNull();
//        validator.ruleFor("bookTime").notNull();
        //validator.ruleFor("locationCode").notNull().notEmpty();
    }
    /**
     * 封装参数
     * @param configDTOS
     * @return
     */
    private QueryTransportGroupInOrderConfigResponseType getResponseType(List<TransportGroupInOrderConfigDTO> configDTOS){
        QueryTransportGroupInOrderConfigResponseType responseType = new QueryTransportGroupInOrderConfigResponseType();
        List<TransportGroupInOrderConfigSOADTO> data = new ArrayList<>();
        for (TransportGroupInOrderConfigDTO configDTO : configDTOS) {
            TransportGroupInOrderConfigSOADTO configSOADTO = new TransportGroupInOrderConfigSOADTO();
            configSOADTO.setCityId(configDTO.getCityId());
            configSOADTO.setTransportGroupId(configDTO.getTransportGroupId());
            configSOADTO.setLocationCode(configDTO.getLocationCode());
            configSOADTO.setProductLine(configDTO.getProductLine());
            configSOADTO.setInOrderCountList(getTransportGroupInOrderCountSOADTO(configDTO.getTimeSegmentConfigDTOList()));
            configSOADTO.setSupplierId(configDTO.getSupplierId());
            configSOADTO.setTotalOrderCount(configDTO.getTotalOrderCount());
            configSOADTO.setTakeOrderLimitTime(configDTO.getTakeOrderLimitTime());
            data.add(configSOADTO);
        }
        responseType.setData(data);
        return responseType;
    }

    /**
     * 封装参数
     * @param timeSegmentConfigDTOList
     * @return
     */
    private List<TransportGroupInOrderCountSOADTO> getTransportGroupInOrderCountSOADTO( List<TransportGroupTimeSegmentConfigDTO> timeSegmentConfigDTOList){
        List<TransportGroupInOrderCountSOADTO> countSOADTOS = new ArrayList<>();
        for (TransportGroupTimeSegmentConfigDTO configDTO : timeSegmentConfigDTOList) {
            String[] timeArray = configDTO.getTime().split("-");
            TransportGroupInOrderCountSOADTO countSOADTO = new TransportGroupInOrderCountSOADTO();
            countSOADTO.setOrderCount(configDTO.getOrderCount());
            countSOADTO.setStartTime(timeArray[0]);
            countSOADTO.setEndTime(timeArray[1]);
            countSOADTOS.add(countSOADTO);
        }
        return countSOADTOS;
    }
}
