package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.ICheckSupplierPermissionService;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 绑定商品关系
 */
@Component
public class BindSkuRelationExecutor extends AbstractRpcExecutor<SkuRelationBindRequestSOAType, SkuRelationBindResponseSOAType> implements Validator<SkuRelationBindRequestSOAType> {

    private static final Logger logger = LoggerFactory.getLogger(BindSkuRelationExecutor.class);

    @Autowired
    private TransportGroupCommandService transportGroupCommandService;
    @Autowired
    private TmsQmqProducerCommandService qmqCommandService;
    @Autowired
    private ICheckSupplierPermissionService checkSupplierPermissionService;

    @Override
    public SkuRelationBindResponseSOAType execute(SkuRelationBindRequestSOAType requestSOAType) {
        SkuRelationBindResponseSOAType responseSOAType = new SkuRelationBindResponseSOAType();
        Result<Boolean> result;
        //0:绑定 1:解绑
        try {
            MetricsUtils.transportGroupBingSkuConnectTimes(ApiTypeEnum.TRANSPORT_GROUP_BING_SKU,requestSOAType.getTransportGroupId(),requestSOAType.getSkuInfoList());
            if (requestSOAType.getBindStatus().intValue() == 1) {
                //供应商权限校验
                boolean checkResult = checkSupplierPermissionService.check(requestSOAType.getTransportGroupId());
                if(!checkResult){
                    String errorMsg = SharkUtils.getSharkValueDefault(SharkKeyConstant.SHARK_SUPPLIER_NOT_DELETE_TRANSPORT_GROUP_SKU);
                    return ServiceResponseUtils.fail(new SkuRelationBindResponseSOAType(),"500",errorMsg);
                }
                result = transportGroupCommandService.updateTransportGroupSkuRelationStatus(requestSOAType.getTransportGroupId(), requestSOAType.getSkuInfoList(), requestSOAType.getOperator());
            } else {
                result = transportGroupCommandService.insertTransportGroupSkuRelationStatus(requestSOAType.getTransportGroupId(), requestSOAType.getSkuInfoList(), requestSOAType.getOperator());
            }
            if (result.isSuccess()) {
                qmqCommandService.sendTransportGroupBoundSkuQmq(requestSOAType.getTransportGroupId(),requestSOAType.getSkuInfoList(),requestSOAType.getBindStatus());
                return ServiceResponseUtils.success(responseSOAType);
            } else {
                return ServiceResponseUtils.fail(responseSOAType,"400",result.getMsg());
            }
        } catch (Exception e) {
            MetricsUtils.transportGroupBingSkuExceptionTimes(ApiTypeEnum.TRANSPORT_GROUP_BING_SKU,requestSOAType.getTransportGroupId(),requestSOAType.getSkuInfoList());
            logger.error("BindSkuRelationExecutor error:", e);
            return ServiceResponseUtils.fail(responseSOAType);
        }
    }

}