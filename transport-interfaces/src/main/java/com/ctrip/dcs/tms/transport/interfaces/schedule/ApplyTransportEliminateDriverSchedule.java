package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.dcs.tms.transport.application.command.TmsQmqProducerCommandService;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TransportGroupDriverApplicationRecordPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupDriverRelationPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.TransportGroupDriverApplyFailedReasonDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.TransportGroupDriverApplyProcessDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.StringUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportGroupDriverApplicationRecordRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TspTransportGroupDriverRelationRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
　* @description: 报名制运力组剔除报名成功的司机
　* <AUTHOR>
　* @date 2022/12/29 17:17
*/
@Component
public class ApplyTransportEliminateDriverSchedule {

    private static final Logger logger = LoggerFactory.getLogger(ApplyTransportEliminateDriverSchedule.class);
    private static final Long NO_TRANSPORT_GROUP_ID = -1L;

    @Autowired
    private TspTransportGroupDriverRelationRepository relationRepository;

    @Autowired
    private TmsTransportQconfig qconfig;

    @Autowired
    TransportGroupQueryService queryService;
    @Autowired
    DriverQueryService driverQueryService;
    @Autowired
    private TmsQmqProducerCommandService tmsQmqProducerCommandService;

    @Autowired
    TransportGroupDriverApplicationRecordRepository transportGroupDriverApplicationRecordRepository;

    @Autowired
    TmsQmqProducerCommandService qmqProducerCommandService;

    /**
     * 报名制运力组剔除报名成功的司机
     * @param parameter
     * @throws Exception
     */
    @QSchedule("apply.transport.eliminate.applysuccess.driver.job")
    public void applyTransportEliminateDriverSchedule(Parameter parameter) throws Exception {
        String drvIds = parameter.getString("drvIds");
        this.applyTransportEliminateDriverScheduleMethod(drvIds);
    }

    public Boolean applyTransportEliminateDriverScheduleMethod(String drvIds){
        Transaction transaction = Cat.newTransaction("apply.transport.eliminate.applysuccess.driver.job",
          "applyTransportEliminateDriverScheduleStart");
        try {
            logger.info("applyTransportEliminateDriverScheduleStart","Start");
            //支持单独处理
            List<Long> drvIdList = Lists.newArrayList();
            if (StringUtils.isNotEmpty(drvIds)) {
                drvIdList = Arrays.stream(drvIds.split(",")).mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
            }
            int count = relationRepository.queryApplySuccessTransportCount(drvIdList);
            if(count == 0){
                return Boolean.TRUE;
            }
            logger.info("applyTransportEliminateDriverScheduleCount","count:{}",count);
            int pageSize = 50;
            count = StringUtil.getPageCount(count,pageSize);
            Set<Long> eliminateDriverList = new HashSet<>();
            for (int i = 1; i <= count; i ++) {
                int beginSize = i;
                //查询报名成功的司机
                List<TspTransportGroupDriverRelationPO> driverRelationPOList = relationRepository.queryApplySuccessTransportList(drvIdList,beginSize,pageSize);
                if(CollectionUtils.isEmpty(driverRelationPOList)){
                    continue;
                }
//                List<Long> calculateDrvIds = driverRelationPOList.stream().map(TspTransportGroupDriverRelationPO::getDrvId).collect(Collectors.toList());
                Set<Long> calculateDrvIds = Sets.newHashSet();
                List<TspTransportGroupDriverRelationPO> eliminateDriverRelationList = Lists.newArrayList();
                //获取报名成功时间
                Map<Long, TspTransportGroupDriverRelationPO> drvApplySuccessMap = driverRelationPOList.stream().collect(Collectors.toMap(TspTransportGroupDriverRelationPO::getDrvId,a -> a, (k1, k2) -> k1));
                List<Long> eliminateIds = Lists.newArrayList();
                Map<Long,Timestamp> drvCalculateBeginTimeMap = Maps.newHashMap();
                for(Map.Entry<Long,TspTransportGroupDriverRelationPO> entry : drvApplySuccessMap.entrySet()){
                    TspTransportGroupDriverRelationPO entryValue = entry.getValue();
                    if(!queryService.grayTransportId(entryValue.getTransportGroupId())){
                        continue;
                    }
                    drvCalculateBeginTimeMap.put(entry.getKey(),entryValue.getDatachangeLasttime());
                    calculateDrvIds.add(entry.getKey());
                }
                logger.info("checkApplyDriverLeaveDurationDrvIds","calculateDrvIds:{}",calculateDrvIds);
                //为空说明没有报名成功的司机
                if(MapUtils.isEmpty(drvCalculateBeginTimeMap)){
                    continue;
                }
                Integer applyTransportDurationCThreshold = qconfig.getApplyTransportDurationCThreshold();
                TransportGroupDriverApplyProcessDTO applyProcessDTO = TransportGroupDriverApplyProcessDTO.builder().rule(
                    TmsTransportConstant.TransportDriverApplyRuleEnum.C).applyTransPortOperationTypeEnum(
                    TmsTransportConstant.ApplyTransPortOperationTypeEnum.ELIMINATE)
                  .build();
                Map<Long,Long> calculateMap = driverQueryService.checkApplyDriverLeaveDuration(new ArrayList<>(calculateDrvIds),null, DateUtil.getNow(),drvCalculateBeginTimeMap, applyProcessDTO);
                logger.info("checkApplyDriverLeaveDurationResult","calculateMap:{}", JsonUtils.toJson(calculateMap));
                for(Map.Entry<Long,TspTransportGroupDriverRelationPO> entry : drvApplySuccessMap.entrySet()){
                    TspTransportGroupDriverRelationPO entryValue = entry.getValue();
                    //灰度运力组,配置中没有对应的运力组ID，不执行逻辑，-1代表全量
                    if(!queryService.grayTransportId(entryValue.getTransportGroupId())){
                        continue;
                    }
                    if(calculateMap.get(entry.getKey()) == null){
                        continue;
                    }
                    //计算请假时长
                    long leaveCount = calculateMap.get(entry.getKey());
                    //请假时长超过C小时数，则剔除司机
                    if(leaveCount > applyTransportDurationCThreshold.longValue()){
                        eliminateIds.add(entryValue.getId());
                        //记录失败信息
                        applyProcessDTO.setApplyResult(entryValue.getDrvId(), TmsTransportConstant.ApplyStatusEnum.APPLY_FAILED, TmsTransportConstant.ApplyFailedTypeEnum.LEAVE_AND_FREEZE_TOTAL_HOUR_OVER_LIMIT,
                          TmsTransportConstant.TransportDriverApplyRuleEnum.C.getText(), applyTransportDurationCThreshold);
                        eliminateDriverRelationList.add(entryValue);
                    }
                }
                if(CollectionUtils.isEmpty(eliminateIds)){
                    continue;
                }
                logger.info("updateEliminateIds","eliminateIds:{}", JsonUtils.toJson(eliminateIds));
                //将报名状态置为剔除
                relationRepository.eliminateApplySuccessDrv(eliminateIds, TmsTransportConstant.TMS_DEFAULT_USERNAME, TmsTransportConstant.ApplyStatusEnum.ELIMINATE.getCode());
                //刷新司机合作模式
                tmsQmqProducerCommandService.sendCalculateDrvCoopModeQmq(new ArrayList<>(calculateDrvIds),TmsTransportConstant.TMS_DEFAULT_USERNAME);

                // 剔除请假冻结时长没有触发阈值的记录
                if(qconfig.getLogTransportgroupDriverApplyLog()) {
                    applyProcessDTO.removeDriverLeaveAndFreezeDurationNotTriggerThreshold();
                    logger.info("updateEliminateIds", "{}", applyProcessDTO.log());

                    // 更新历史数据为inactive
                    for (TspTransportGroupDriverRelationPO transportGroupDriverRelationPO : eliminateDriverRelationList) {
                        transportGroupDriverApplicationRecordRepository.updateHistoryData2Inactive(transportGroupDriverRelationPO.getTransportGroupId(), Lists.newArrayList(transportGroupDriverRelationPO.getDrvId()));
                    }
                    //插入失败记录
                    transportGroupDriverApplicationRecordRepository.batchInsert(applyProcessDTO.convert2TransportGroupDriverRelationList(eliminateDriverRelationList));

                    //发消息
                    for (TransportGroupDriverApplyFailedReasonDTO driverApplicationRecordPo : applyProcessDTO.getFailedDriverList()) {
                        if (eliminateDriverList.add(driverApplicationRecordPo.getDrvId())) {
                            qmqProducerCommandService.sendDriverAppElistmateMessge(driverApplicationRecordPo);
                        }
                    }
                }
            }

        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }finally {
            transaction.complete();
        }
        return Boolean.TRUE;
    }

}
