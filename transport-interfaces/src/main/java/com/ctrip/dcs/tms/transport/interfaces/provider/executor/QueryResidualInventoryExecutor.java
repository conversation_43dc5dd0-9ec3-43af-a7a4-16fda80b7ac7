package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryResidualInventorySOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryResidualInventorySOAResponseType;
import com.ctrip.dcs.tms.transport.application.query.EffectiveCapacityQueryService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class QueryResidualInventoryExecutor extends AbstractRpcExecutor<QueryResidualInventorySOARequestType, QueryResidualInventorySOAResponseType> implements Validator<QueryResidualInventorySOARequestType> {

    @Autowired
    EffectiveCapacityQueryService service;

    @Override
    public QueryResidualInventorySOAResponseType execute(QueryResidualInventorySOARequestType requestType) {
        QueryResidualInventorySOAResponseType responseType = new QueryResidualInventorySOAResponseType();
        Result<QueryResidualInventorySOAResponseType> result = service.queryResidualInventory(requestType);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(result.getData());
        }
        return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<QueryResidualInventorySOARequestType> validator) {

    }
}
