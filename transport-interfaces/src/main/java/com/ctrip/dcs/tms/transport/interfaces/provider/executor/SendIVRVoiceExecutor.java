package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CatEventType;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CommonConfig;
import com.ctrip.dcs.tms.transport.infrastructure.service.IVRCallService;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.model.SendIVRVoiceRequestType;
import com.ctrip.model.SendIVRVoiceResponseType;
import com.dianping.cat.Cat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 发送IVR语音验证码
 */
@Component
public class SendIVRVoiceExecutor extends AbstractRpcExecutor<SendIVRVoiceRequestType, SendIVRVoiceResponseType> implements Validator<SendIVRVoiceRequestType> {
    private static final Logger logger = LoggerFactory.getLogger(SendIVRVoiceExecutor.class);
    private static final String CHANNEL = "tms_driver_register";

    @Autowired
    private IVRCallService ivrCallService;

    @Autowired
    private CommonConfig commonConfig;

    @Override
    public SendIVRVoiceResponseType execute(SendIVRVoiceRequestType sendIVRVoiceRequestType) {
        Cat.logEvent(CatEventType.IVR_VOICE_CALL, "method_entry");

        String mobilePhone = sendIVRVoiceRequestType.getMobilePhone();
        String countryCode = sendIVRVoiceRequestType.getCountryCode();

        // 调用IVRCallService发起IVR电话验证
        Long taskId = ivrCallService.callPhoneForVerify(mobilePhone, countryCode, CHANNEL);

        // 检查呼叫是否成功发起
        if (taskId != null) {
            logger.info("execute", "Successfully initiated or retrieved IVR call for phone: {} with country code: {}, taskId: {}",
                    mobilePhone, countryCode, taskId);

            SendIVRVoiceResponseType responseType = new SendIVRVoiceResponseType();
            responseType.setTaskId(taskId);
            return ServiceResponseUtils.success(responseType);
        } else {
            logger.error("execute", "Failed to initiate IVR call for phone: {} with country code: {}",
                    mobilePhone, countryCode);

            SendIVRVoiceResponseType responseType = new SendIVRVoiceResponseType();
            return ServiceResponseUtils.fail(responseType, "FAILED", "Failed to initiate IVR call");
        }
    }
}
