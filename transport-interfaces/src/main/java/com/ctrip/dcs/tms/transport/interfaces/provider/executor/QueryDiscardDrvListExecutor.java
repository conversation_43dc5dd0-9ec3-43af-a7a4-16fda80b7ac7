package com.ctrip.dcs.tms.transport.interfaces.provider.executor;


import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.dcs.tms.transport.api.model.DrvDriverSOAResponseDTO;
import com.ctrip.dcs.tms.transport.api.model.QueryDiscardDrvListSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDiscardDrvListSOAResponseType;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.TmsTransUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.QueryDrvDO;
import com.ctrip.igt.PaginationDTO;
import com.ctrip.igt.framework.common.base.PageHolder;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 司机列表
 */
@Component
public class QueryDiscardDrvListExecutor extends AbstractRpcExecutor<QueryDiscardDrvListSOARequestType, QueryDiscardDrvListSOAResponseType> implements Validator<QueryDiscardDrvListSOARequestType> {

    @Autowired
    private DriverQueryService driverQueryService;

    private QueryDrvDO buildQueryDrvDO(QueryDiscardDrvListSOARequestType requestType) {
        QueryDrvDO queryDrvDO = new QueryDrvDO();
        BeanUtils.copyProperties(requestType,queryDrvDO);
        queryDrvDO.setSupplierId(requestType.getSupplierId().intValue());
        queryDrvDO.setDrvId(requestType.getDrvId());
        if(StringUtils.isNotEmpty(requestType.getDrvPhone())) {
            queryDrvDO.setDrvPhone(TmsTransUtil.encrypt(requestType.getDrvPhone(), KeyType.Phone));
        }
        queryDrvDO.setPage(requestType.getPaginator().getPageNo());
        queryDrvDO.setSize(requestType.getPaginator().getPageSize());
        return queryDrvDO;
    }

    @Override
    public QueryDiscardDrvListSOAResponseType execute(QueryDiscardDrvListSOARequestType requestType) {
        QueryDiscardDrvListSOAResponseType responseType = new QueryDiscardDrvListSOAResponseType();
        Result<PageHolder<DrvDriverSOAResponseDTO>> pageHolderResult = driverQueryService.queryDiscardDrvList(buildQueryDrvDO(requestType));
        PaginationDTO paginationDTO = ServiceResponseUtils.newPagination(pageHolderResult.getData().getPageIndex(),
                pageHolderResult.getData().getPageSize(), pageHolderResult.getData().getTotalSize());
        responseType.setData(pageHolderResult.getData().getData());
        responseType.setPagination(paginationDTO);
        return ServiceResponseUtils.success(responseType);
    }

    @Override
    public void validate(AbstractValidator<QueryDiscardDrvListSOARequestType> validator) {
        validator.ruleFor("supplierId").notNull();
    }
}
