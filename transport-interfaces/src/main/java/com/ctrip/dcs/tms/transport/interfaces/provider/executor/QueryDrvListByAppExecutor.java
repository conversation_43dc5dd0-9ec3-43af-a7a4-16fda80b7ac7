package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 司机端获取司机信息
 */
@Component
public class QueryDrvListByAppExecutor extends AbstractRpcExecutor<QueryDrvListByAppRequestType, QueryDrvListByAppResponseType> implements Validator<QueryDrvListByAppRequestType> {

    @Autowired
    DriverQueryService driverQueryService;

    @Override
    public QueryDrvListByAppResponseType execute(QueryDrvListByAppRequestType queryDrvListByAppRequestType) {
        QueryDrvListByAppResponseType responseType = new QueryDrvListByAppResponseType();
        Result<QueryDrvListByAppResponseType> responseTypeResult =  driverQueryService.queryDrvListByApp(buildDO(queryDrvListByAppRequestType));
        if (responseTypeResult.isSuccess()) {
            return ServiceResponseUtils.success(responseTypeResult.getData());
        }
        return ServiceResponseUtils.fail(responseType);
    }

    private QueryDrvListByAppDO buildDO(QueryDrvListByAppRequestType requestType) {
        QueryDrvListByAppDO byAppDO = new QueryDrvListByAppDO();
        BeanUtils.copyProperties(requestType, byAppDO);
        return byAppDO;
    }
}
