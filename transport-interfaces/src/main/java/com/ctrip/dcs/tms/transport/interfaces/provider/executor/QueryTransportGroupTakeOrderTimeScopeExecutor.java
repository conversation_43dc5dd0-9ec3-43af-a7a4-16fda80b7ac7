package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.OrderTimeScope;
import com.ctrip.dcs.tms.transport.api.model.QueryTransportGroupTakeOrderTimeScopeRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryTransportGroupTakeOrderTimeScopeResponseType;
import com.ctrip.dcs.tms.transport.application.convert.TransportGroupConverter;
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.PlatformUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.exception.ServiceValidationException;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Estimate QPS 50
 * skuIdList.length <= 100
 */
@Component
public class QueryTransportGroupTakeOrderTimeScopeExecutor extends AbstractRpcExecutor<QueryTransportGroupTakeOrderTimeScopeRequestType, QueryTransportGroupTakeOrderTimeScopeResponseType> implements Validator<QueryTransportGroupTakeOrderTimeScopeRequestType> {

    private static final Logger logger = LoggerFactory.getLogger(QueryTransportGroupTakeOrderTimeScopeExecutor.class);

    @Resource
    private TransportGroupQueryService transportGroupQueryService;

    /**
     * 查询 sku 数量限制
     * */
    private final Integer TOLERATE_LIMIT_SKU_LENGTH = 100;

    @Override
    public void onExecuting(QueryTransportGroupTakeOrderTimeScopeRequestType req) {
        if (CollectionUtils.isEmpty(req.getSkuIdList())) {
            throw new ServiceValidationException("Missing required parameters");
        }
        if (req.getSkuIdList().size() > TOLERATE_LIMIT_SKU_LENGTH) {
            logger.warn("QueryTakeOrderTimeScopeOverLimit", "appName:{}", PlatformUtil.getAppName());
        }
    }

    @Override
    public QueryTransportGroupTakeOrderTimeScopeResponseType execute(QueryTransportGroupTakeOrderTimeScopeRequestType request) {
        QueryTransportGroupTakeOrderTimeScopeResponseType responseType = new QueryTransportGroupTakeOrderTimeScopeResponseType();
        Result<List<OrderTimeScope>> scopeRes = transportGroupQueryService.queryTransportGroupTakeOrderTimeScope(TransportGroupConverter.toQueryModel(request));
        if (!scopeRes.isSuccess()) {
            return ServiceResponseUtils.fail(responseType);
        }
        responseType.setScopeList(scopeRes.getData());
        return ServiceResponseUtils.success(responseType);
    }

}