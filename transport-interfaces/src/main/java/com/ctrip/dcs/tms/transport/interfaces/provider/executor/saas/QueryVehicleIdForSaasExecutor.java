package com.ctrip.dcs.tms.transport.interfaces.provider.executor.saas;

import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleIdForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleIdForSaasResponseType;
import com.ctrip.dcs.tms.transport.interfaces.bridge.ProductLineBridgeManagement;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class QueryVehicleIdForSaasExecutor extends AbstractRpcExecutor<QueryVehicleIdForSaasRequestType, QueryVehicleIdForSaasResponseType> implements Validator<QueryVehicleIdForSaasRequestType> {
    @Autowired
    ProductLineBridgeManagement productLineBridgeManagement;

    @Override
    public QueryVehicleIdForSaasResponseType execute(QueryVehicleIdForSaasRequestType requestType) {
        return productLineBridgeManagement.queryVehicleIdForSaas(requestType);
    }

    @Override
    public void validate(AbstractValidator<QueryVehicleIdForSaasRequestType> validator) {
        validator.ruleFor("vehicleLicense").notNull().notEmpty();
        validator.ruleFor("supplierId").notNull();
    }

}
