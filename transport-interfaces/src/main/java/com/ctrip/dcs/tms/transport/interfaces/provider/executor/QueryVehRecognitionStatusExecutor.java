package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryVehRecognitionStatusRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryVehRecognitionStatusResponseType;
import com.ctrip.dcs.tms.transport.api.model.VBKDrvDispatchBindingSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.VBKDrvDispatchBindingSOAResponseType;
import com.ctrip.dcs.tms.transport.application.command.DriverCommandService;
import com.ctrip.dcs.tms.transport.application.query.VehicleQueryService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class QueryVehRecognitionStatusExecutor extends AbstractRpcExecutor<QueryVehRecognitionStatusRequestType, QueryVehRecognitionStatusResponseType> implements Validator<QueryVehRecognitionStatusRequestType> {

    @Autowired
    VehicleQueryService vehicleQueryService;

    @Override
    public QueryVehRecognitionStatusResponseType execute(QueryVehRecognitionStatusRequestType requestType) {
        QueryVehRecognitionStatusResponseType responseType = new QueryVehRecognitionStatusResponseType();
        Result<QueryVehRecognitionStatusResponseType> result = vehicleQueryService.queryVehRecognitionStatus(requestType.getVehicleLicense());
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(result.getData());
        }
        return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<QueryVehRecognitionStatusRequestType> validator) {
        validator.ruleFor("vehicleLicense").notNull();
    }
}
