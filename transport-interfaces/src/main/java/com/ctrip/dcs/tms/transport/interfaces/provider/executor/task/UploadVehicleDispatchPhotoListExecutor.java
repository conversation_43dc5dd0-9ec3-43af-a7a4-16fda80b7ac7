package com.ctrip.dcs.tms.transport.interfaces.provider.executor.task;

import com.ctrip.dcs.tms.transport.api.model.UploadVehicleDispatchPhotoListRequestType;
import com.ctrip.dcs.tms.transport.api.model.UploadVehicleDispatchPhotoListResponseType;
import com.ctrip.dcs.tms.transport.task.application.command.VehicleDispatchPhotoCommandService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UploadVehicleDispatchPhotoListExecutor extends AbstractRpcExecutor<UploadVehicleDispatchPhotoListRequestType, UploadVehicleDispatchPhotoListResponseType>
  implements
  Validator<UploadVehicleDispatchPhotoListRequestType> {

  @Autowired
  private VehicleDispatchPhotoCommandService commandService;
  @Override
  public UploadVehicleDispatchPhotoListResponseType execute(UploadVehicleDispatchPhotoListRequestType requestType) {
    Result<Boolean> result = commandService.uploadVehicleDispatchPhotoList(requestType);
    UploadVehicleDispatchPhotoListResponseType responseType = new UploadVehicleDispatchPhotoListResponseType();
    if (result.isSuccess()) {
      return ServiceResponseUtils.success(responseType);
    }else {
      return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
    }
  }
}
