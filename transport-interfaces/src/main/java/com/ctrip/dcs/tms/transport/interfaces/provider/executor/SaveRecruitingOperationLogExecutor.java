package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

@Component
public class SaveRecruitingOperationLogExecutor extends AbstractRpcExecutor<SaveRecruitingOperationLogSOARequestType, SaveRecruitingOperationLogSOAResponseType> implements Validator<SaveRecruitingOperationLogSOARequestType> {

    @Autowired
    TmsModRecordCommandService modRecordCommandService;

    @Override
    public SaveRecruitingOperationLogSOAResponseType execute(SaveRecruitingOperationLogSOARequestType requestType) {
        SaveRecruitingOperationLogSOAResponseType responseType = new SaveRecruitingOperationLogSOAResponseType();
        Result<Boolean> result = modRecordCommandService.saveRecruitingOperationLog(requestType.getRecruitingId(),requestType.getRecruitingType(),requestType.getModifyUser(),requestType.getApproverStatus());
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<SaveRecruitingOperationLogSOARequestType> validator) {
        validator.ruleFor("recruitingId").notNull();
        validator.ruleFor("recruitingType").notNull();
    }
}
