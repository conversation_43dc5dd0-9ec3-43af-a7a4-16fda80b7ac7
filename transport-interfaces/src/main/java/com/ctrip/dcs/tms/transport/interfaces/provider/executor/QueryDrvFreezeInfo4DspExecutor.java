package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * <AUTHOR>
 */
@Component
public class QueryDrvFreezeInfo4DspExecutor extends AbstractRpcExecutor<QueryDrvFreezeInfoForDspSOARequestType, QueryDrvFreezeInfoForDspSOAResponseType> implements Validator<QueryDrvFreezeInfoForDspSOARequestType> {

    @Autowired
    private DrvFreezeQueryService drvFreezeQueryService;

    @Override
    public QueryDrvFreezeInfoForDspSOAResponseType execute(QueryDrvFreezeInfoForDspSOARequestType requestType) {
        QueryDrvFreezeInfoForDspSOAResponseType soaResponseType = new QueryDrvFreezeInfoForDspSOAResponseType();
        soaResponseType.setData(drvFreezeQueryService.queryDrvFreezeInfoList(BaseUtil.getLongSet(requestType.getDriverIds())));
        return ServiceResponseUtils.success(soaResponseType);
    }

    @Override
    public void validate(AbstractValidator<QueryDrvFreezeInfoForDspSOARequestType> validator) {
        validator.ruleFor("driverIds").notNull();
    }

}