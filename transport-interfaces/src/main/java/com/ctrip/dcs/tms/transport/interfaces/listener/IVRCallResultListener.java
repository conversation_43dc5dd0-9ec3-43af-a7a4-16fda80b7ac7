package com.ctrip.dcs.tms.transport.interfaces.listener;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverDomainService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TmsDrvInactiveReasonPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CatEventType;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.DrvInActiveEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CommonConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsDrvInactiveReasonRepository;
import com.dianping.cat.Cat;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.model.QueryCallPhoneForVerifyResultRequestType;
import com.ctrip.model.QueryCallPhoneForVerifyResultResponseType;
import com.dianping.cat.message.Event;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * IVR电话验证结果监听器
 */
@Component
public class IVRCallResultListener {

    private static final Logger logger = LoggerFactory.getLogger(IVRCallResultListener.class);

    @Autowired
    private DriverDomainService driverDomainService;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private DrvDrvierRepository drvDrvierRepository;

    @Autowired
    private TmsDrvInactiveReasonRepository tmsDrvInactiveReasonRepository;

    /**
     * 处理IVR电话验证结果
     * @param message QMQ消息
     */
    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.SUBJECT_IVR_CALL_VERIFICATION,
                 tags = {TmsTransportConstant.QmqTag.TAG_IVR_CALL_VERIFICATION},
                 consumerGroup = TmsTransportConstant.QMQ_CONSUMER_GROUP)
    public void handleIVRCallResult(Message message) {
        Cat.logEvent(CatEventType.IVR_CALL_RESULT, "method_entry");
        try {
            // 获取消息中的参数
            Long drvId = message.getLongProperty("drvId");
            String callTaskId = message.getStringProperty("callTaskId");
            String phoneNumber = message.getStringProperty("phoneNumber");

            if (drvId == null || callTaskId == null || phoneNumber == null) {
                Cat.logEvent(CatEventType.IVR_CALL_RESULT, "missing_parameters", "ERROR", "drvId:" + drvId + ",callTaskId:" + callTaskId + ",phoneNumber:" + phoneNumber);
                logger.info("handleIVRCallResult", "Missing required parameters: drvId={}, callTaskId={}, phoneNumber={}",
                           drvId, callTaskId, phoneNumber);
                return;
            }

            logger.info("handleIVRCallResult", "Processing IVR call result for drvId={}, callTaskId={}, phoneNumber={}",
                       drvId, callTaskId, phoneNumber);

            // 查询IVR电话验证结果
            QueryCallPhoneForVerifyResultRequestType requestType = new QueryCallPhoneForVerifyResultRequestType();
            requestType.setCallTaskId(Long.valueOf(callTaskId));
            QueryCallPhoneForVerifyResultResponseType result = driverDomainService.queryCallPhoneForVerifyResult(requestType);

            if (result == null) {
                Cat.logEvent(CatEventType.IVR_CALL_RESULT, "query_result_null", "ERROR", "drvId:" + drvId + ",callTaskId:" + callTaskId);
                logger.warn("handleIVRCallResult", "Failed to get IVR call result for drvId={}, callTaskId={}",
                           drvId, callTaskId);
                return;
            }

            String callResultStatus = result.getCallResultStatus();
            Cat.logEvent(CatEventType.IVR_CALL_RESULT, "call_result_status", Event.SUCCESS, "status:" + callResultStatus);
            logger.info("handleIVRCallResult", "IVR call result for drvId={}, callTaskId={}, status={}",
                       drvId, callTaskId, callResultStatus);

            // 判断电话是否接通
            boolean isConnected = commonConfig.getCallResultStatus().contains(callResultStatus);

            // 根据电话接通状态进行相应处理
            if (isConnected) {
                logger.info("handleIVRCallResult", "IVR call connected for drvId={}, callTaskId={}",
                           drvId, callTaskId);
                // 电话接通后的处理逻辑
                // 检查是否还有其他未激活原因
                try {
                    List<TmsDrvInactiveReasonPO> otherReasons = tmsDrvInactiveReasonRepository.query(Lists.newArrayList(drvId));

                    // 删除IVR_AUTHENTICATION_FAILURE未激活原因
                    Cat.logEvent(CatEventType.IVR_CALL_RESULT, "delete_not_login_reason", Event.SUCCESS, "drvId:" + drvId);
                    tmsDrvInactiveReasonRepository.deleteReasonByCode(drvId, Arrays.asList(DrvInActiveEnum.IVR_AUTHENTICATION_FAILURE.getCode()), "system");
                    // 过滤掉IVR_AUTHENTICATION_FAILURE原因后，检查是否还有其他未激活原因
                    List<TmsDrvInactiveReasonPO> remainingReasons = Optional.ofNullable(otherReasons).orElse(Lists.newArrayList()).stream()
                            .filter(reason -> !Objects.equals(reason.getReasonCode(), DrvInActiveEnum.IVR_AUTHENTICATION_FAILURE.getCode()))
                            .collect(Collectors.toList());
                    logger.info("handleIVRCallResult", "Remaining reasons after filtering: {}", JsonUtil.toJson(remainingReasons));
                    if (remainingReasons.isEmpty()) {
                        // 没有其他未激活原因，上线司机
                        Cat.logEvent(CatEventType.IVR_CALL_RESULT, "activate_driver", Event.SUCCESS, "drvId:" + drvId);
                        drvDrvierRepository.updateDrvStatus(Lists.newArrayList(drvId), null, TmsTransportConstant.DrvStatusEnum.ONLINE.getCode(), "system");
                        logger.info("handleIVRCallResult", "Driver {} activated successfully, no other inactive reasons found", drvId);
                    } else {
                        // 有其他未激活原因，不上线司机
                        String reasonCodes = remainingReasons.stream().map(reason -> String.valueOf(reason.getReasonCode())).collect(Collectors.joining(","));
                        Cat.logEvent(CatEventType.DRIVER_LOGIN, "driver_not_activated", "WARN", "drvId:" + drvId + ",reasons:" + reasonCodes);
                        logger.info("handleIVRCallResult", "Driver {} not activated, other inactive reasons found: {}", drvId,
                                remainingReasons.stream().map(reason -> reason.getReasonCode() + ": " + reason.getReasonDesc())
                                        .collect(Collectors.joining(", ")));
                    }
                } catch (Exception e) {
                    logger.error("handleIVRCallResult", "Failed to process driver activation for drvId={}: {}",
                               drvId, e.getMessage(), e);
                }
            } else {
                logger.info("handleIVRCallResult", "IVR call not connected for drvId={}, callTaskId={}",
                           drvId, callTaskId);
                // 电话未接通的处理逻辑
                // 在未激活原因表中添加一条记录，原因是该手机IVR认证失败
                try {
                    Cat.logEvent(CatEventType.IVR_CALL_RESULT, "add_ivr_failure_reason", "WARN", "drvId:" + drvId);
                    TmsDrvInactiveReasonPO reasonPO = TmsDrvInactiveReasonPO.builder()
                            .drvId(drvId)
                            .reasonCode(DrvInActiveEnum.IVR_AUTHENTICATION_FAILURE.getCode())
                            .reasonDesc(DrvInActiveEnum.getReasonDesc(DrvInActiveEnum.IVR_AUTHENTICATION_FAILURE.getCode()))
                            .active(true)
                            .build();
                    tmsDrvInactiveReasonRepository.insert(reasonPO);
                    logger.info("handleIVRCallResult", "Added IVR authentication failure record for drvId={}", drvId);
                } catch (Exception e) {
                    logger.error("handleIVRCallResult", "Failed to add IVR authentication failure record for drvId={}: {}",
                               drvId, e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            logger.error("handleIVRCallResult", "Error processing IVR call result: {}", e.getMessage(), e);
        }
    }
}
