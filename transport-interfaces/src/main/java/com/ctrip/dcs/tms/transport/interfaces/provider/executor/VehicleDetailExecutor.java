package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.VehicleDetailSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.VehicleDetailSOAResponseType;
import com.ctrip.dcs.tms.transport.interfaces.bridge.ProductLineBridgeManagement;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class VehicleDetailExecutor extends AbstractRpcExecutor<VehicleDetailSOARequestType, VehicleDetailSOAResponseType> implements Validator<VehicleDetailSOARequestType> {

    @Autowired
    ProductLineBridgeManagement productLineBridgeManagement;

    @Override
    public VehicleDetailSOAResponseType execute(VehicleDetailSOARequestType vehicleDetailSOARequestType) {
        return productLineBridgeManagement.queryVehicleDetail(vehicleDetailSOARequestType);
    }

    @Override
    public void validate(AbstractValidator<VehicleDetailSOARequestType> validator) {
        validator.ruleFor("vehicleId").notNull();
    }

}
