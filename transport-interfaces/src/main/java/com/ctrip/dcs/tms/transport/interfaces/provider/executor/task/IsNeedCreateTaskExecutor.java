package com.ctrip.dcs.tms.transport.interfaces.provider.executor.task;

import com.ctrip.dcs.tms.transport.api.model.IsNeedCreateTaskRequestType;
import com.ctrip.dcs.tms.transport.api.model.IsNeedCreateTaskResponseType;
import com.ctrip.dcs.tms.transport.task.application.query.impl.TaskQueryService;
import com.ctrip.dcs.tms.transport.task.infrastructure.common.enums.NeedCreateTaskEnum;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class IsNeedCreateTaskExecutor extends AbstractRpcExecutor<IsNeedCreateTaskRequestType, IsNeedCreateTaskResponseType> implements
  Validator<IsNeedCreateTaskRequestType> {

  @Autowired
  private TaskQueryService taskQueryService;
  @Override
  public IsNeedCreateTaskResponseType execute(IsNeedCreateTaskRequestType requestType) {
    IsNeedCreateTaskResponseType responseType = new IsNeedCreateTaskResponseType();
    try {
      Result<NeedCreateTaskEnum> result = taskQueryService.isNeedCreateTask(requestType);
      responseType.setData(Optional.ofNullable(result.getData()).orElse(NeedCreateTaskEnum.NEED).getCode());
      if (result.isSuccess()) {
        return ServiceResponseUtils.success(responseType);
      } else {
        return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
      }
    }catch (Exception e) {
      responseType.setData(NeedCreateTaskEnum.NOT_NEED.getCode());
      return ServiceResponseUtils.success(responseType);
    }
  }
}
