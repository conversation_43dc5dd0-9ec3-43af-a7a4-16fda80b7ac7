package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.helper.ShortTransportGroupHelper;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OverseasQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.dcs.tms.transport.interfaces.provider.validator.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.constant.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctrip.platform.dal.dao.annotation.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import com.google.common.collect.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.sql.*;
import java.util.*;

/**
 * 运力组编辑
 * <AUTHOR>
 * @Date 2020/3/3 19:22
 */
@Component
public class UpdateTransportGroupExecutor extends AbstractRpcExecutor<UpdateTransportGroupSOARequestType, UpdateTransportGroupSOAResponseType> implements Validator<UpdateTransportGroupSOARequestType> {

    @Autowired
    private TransportGroupCommandService transportGroupCommandService;

    @Autowired
    private InOrderConfigCommandService inOrderConfigCommandService;

    @Autowired
    private InOrderConfigQueryService inOrderConfigQueryService;

    @Autowired
    private TransportGroupQueryService transportGroupQueryService;

    @Autowired
    private WorkShiftCommandService workShiftCommandService;

    @Autowired
    private WorkShiftQueryService workShiftQueryService;

    @Autowired
    private EnumRepository enumRepository;

    @Autowired
    private OverseasQconfig overseasQconfig;
    @Autowired
    private ICheckSupplierPermissionService checkSupplierPermissionService;

    @Autowired
    MobileHelper mobileHelper;

    @Autowired
    ShortTransportGroupHelper shortTransportGroupHelper;

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public UpdateTransportGroupSOAResponseType execute(UpdateTransportGroupSOARequestType updateTransportGroupSOARequestType) {
        MetricsUtils.transportGroupUpdateConnectTimes(ApiTypeEnum.UPDATE_TRANSPORT_GROUP,updateTransportGroupSOARequestType.getTransportGroupId());
        UpdateTransportGroupSOAResponseType soaResponseType = new UpdateTransportGroupSOAResponseType();

        // check mobile
        if (StringUtils.isNotBlank(updateTransportGroupSOARequestType.getDispatcherPhone())) {
            Result<Boolean> mobileValid = mobileHelper.isMobileValid(updateTransportGroupSOARequestType.getIgtCode(), updateTransportGroupSOARequestType.getDispatcherPhone(),
              updateTransportGroupSOARequestType.getPointCityIdList().get(0));
            if(!mobileValid.isSuccess()){
                return ServiceResponseUtils.fail(soaResponseType, mobileValid.getCode(), mobileValid.getMsg());
            }
        }

        if (StringUtils.isNotBlank(updateTransportGroupSOARequestType.getStandbyPhone())) {
            Result<Boolean> mobileValid = mobileHelper.isMobileValid(updateTransportGroupSOARequestType.getStandbyIgtCode(), updateTransportGroupSOARequestType.getStandbyPhone(),
              updateTransportGroupSOARequestType.getPointCityIdList().get(0));
            if(!mobileValid.isSuccess()){
                return ServiceResponseUtils.fail(soaResponseType, mobileValid.getCode(), mobileValid.getMsg());
            }
        }
          
        TspTransportGroupPO transportGroupPO = requestTypeToGroupPO(updateTransportGroupSOARequestType);
        Result<TspTransportGroupPO>  orgTransportGroupPO = transportGroupQueryService.queryTransportGroupDetail(transportGroupPO.getTransportGroupId());
        if (!orgTransportGroupPO.isSuccess() || Objects.isNull(orgTransportGroupPO.getData())) {
            return ServiceResponseUtils.fail(soaResponseType,SharkUtils.getSharkValue(SharkKeyConstant.transportMsgTransportDataIsEmpty));
        }

        Result<String> shortTransportGroupCheck = checkShortTransportGroupDriver(updateTransportGroupSOARequestType, orgTransportGroupPO.getData());
        if (!shortTransportGroupCheck.isSuccess()) {
            return ServiceResponseUtils.fail(soaResponseType, shortTransportGroupCheck.getCode(), shortTransportGroupCheck.getMsg());
        }

        //判断进单配置上限
        Result<Boolean> resultUpperFlag =  transportGroupQueryService.inOrderUpperLimit(updateTransportGroupSOARequestType.getInOrderConfigs(),updateTransportGroupSOARequestType.getPointCityIdList().get(0));
        if(resultUpperFlag.isSuccess() && resultUpperFlag.getData()){
            return ServiceResponseUtils.fail(soaResponseType, ServiceResponseConstants.ResStatus.EXCEPTION_CODE,resultUpperFlag.getMsg());
        }
        TspTransportGroupPO orgTransport = orgTransportGroupPO.getData();

        //如果是供应商操作编辑运力组并且运力组对应的服务商id是1001000，则不允许编辑运力组
        boolean checkResult = checkSupplierPermissionService.checkByContractId(orgTransport.getContractId(), orgTransport.getCategorySynthesizeCode(), orgTransport.getPointCityId());
        if(!checkResult){
            String errorMsg = SharkUtils.getSharkValueDefault(SharkKeyConstant.SHARK_SUPPLIER_NOT_UPDATE_TRANSPORT_GROUP);
            return ServiceResponseUtils.fail(new UpdateTransportGroupSOAResponseType(),"500",errorMsg);
        }

        Result<Boolean> result = transportGroupCommandService.updateTransportGroup(transportGroupPO);
        if (!result.isSuccess()) {
            MetricsUtils.transportGroupUpdateExceptionTimes(ApiTypeEnum.UPDATE_TRANSPORT_GROUP,updateTransportGroupSOARequestType.getTransportGroupId());
            return ServiceResponseUtils.fail(soaResponseType, result.getCode(),result.getMsg());
        }
        Integer transportGroupMode = transportGroupPO.getTransportGroupMode();
        boolean flag = transportGroupQueryService.isDispatcherMode(transportGroupMode);
        if (flag) {
            //进单配置检查
            boolean checkPass = inOrderConfigQueryService.checkConfig(updateTransportGroupSOARequestType.getInOrderConfigs());
            if (!checkPass) {
                return ServiceResponseUtils.fail(soaResponseType, ServiceResponseConstants.ResStatus.EXCEPTION_CODE, SharkUtils.getSharkValue(SharkKeyConstant.transportCheckIntoOrderConfFail));
            }
            //获取最新进单配置
            List<TspIntoOrderConfigPO> requestConfigPOS = requestTypeToConfigPO(transportGroupPO.getTransportGroupId(), updateTransportGroupSOARequestType.getInOrderConfigs(), transportGroupPO.getModifyUser());
            inOrderConfigCommandService.addOrUpdateInOrderConfig(transportGroupPO.getTransportGroupId(),requestConfigPOS);
        }
        boolean applyMode = transportGroupQueryService.isApplyMode(transportGroupMode);
        if (applyMode) {
            // 报名制运力组，删除行政区域城市，同步解绑城市下的司机
            transportGroupCommandService.unBingTransportAssociated(transportGroupPO.getTransportGroupId(),transportGroupPO.getPointCityId(),orgTransport.getAreaGroupType(),transportGroupPO.getAreaGroupType(),orgTransport.getAreaGroupId(),transportGroupPO.getAreaGroupId(),transportGroupPO.getModifyUser());
            //工作班次检查
            boolean checkPass = workShiftQueryService.checkConfig(updateTransportGroupSOARequestType.getWorkShiftDetails());
            if (!checkPass) {
                return ServiceResponseUtils.fail(soaResponseType, ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportCheckWorkShiftDetailsFail));
            }
            List<TspTransportGroupWorkShiftPO> workShiftPOS = requestTypeToWorkShiftPO(transportGroupPO.getTransportGroupId(), updateTransportGroupSOARequestType.getWorkShiftDetails(), transportGroupPO.getModifyUser());
            workShiftCommandService.updateWorkShiftDetail(workShiftPOS);
        }
        return  ServiceResponseUtils.success(soaResponseType);
    }

    /**
     * 校验运力组是否是短运力组，以及是否司机绑定了长短运力组（长短互斥）
     * @param requestType 请求体
     * @param dbData db数据
     * @return 结果
     */
    protected Result<String> checkShortTransportGroupDriver(UpdateTransportGroupSOARequestType requestType, TspTransportGroupPO dbData) {

        //短公里运力组标签没有变化不校验
        if (Objects.isNull(requestType.getShortTransportGroup()) || Objects.equals(requestType.getShortTransportGroup(), dbData.getShortTransportGroup())) {
            return ResponseResultUtil.success();
        }

        // 根据运力组查询司机ID列表
        List<Long> drvIdList = transportGroupQueryService.queryRelationDrvIdListByTransportGroupId(requestType.getTransportGroupId());

        return shortTransportGroupHelper.checkShortTransportGroupDriver(drvIdList, requestType.getTransportGroupId(), requestType.getShortTransportGroup());
    }

    @Override
    public void validate(AbstractValidator<UpdateTransportGroupSOARequestType> validator, UpdateTransportGroupSOARequestType req) {
        validator.ruleFor("transportGroupId").notNull();
        validator.ruleFor("transportGroupName").notNull().notEmpty();
        validator.ruleFor("transportGroupMode").notNull();
        validator.ruleFor("modifyUser").notNull().notEmpty();
        Integer transportGroupMode = req.getTransportGroupMode();
        boolean predicate = transportGroupQueryService.isDispatcherMode(transportGroupMode);
        validator.ruleFor("dispatcher").notNull().notEmpty().when(predicate);
        validator.ruleFor("dispatcherPhone").notNull().notEmpty().when(predicate);
        validator.ruleFor("takeOrderLimitTime").notNull().when(predicate);
        validator.ruleFor("inOrderConfigs").setValidator(new NotEmptyCollectionValidator("用车时段与进单上限配置不能为空！", ValidationErrors.NotNull)).when(predicate);
        validator.ruleFor("pointCityIdList").notNull().notEmpty();
        validator.ruleFor("transportAreaType").notNull();
        validator.ruleFor("areaGroupId").notNull();
    }

    /**
     * 运力组基础信息PO
     * @param requestType
     * @return
     */
    private TspTransportGroupPO requestTypeToGroupPO(UpdateTransportGroupSOARequestType requestType){
        TspTransportGroupPO transportGroupPO = new TspTransportGroupPO();
        BeanUtils.copyProperties(requestType,transportGroupPO);
        transportGroupPO.setTransportGroupId(requestType.getTransportGroupId());
        transportGroupPO.setTransportGroupName(requestType.getTransportGroupName());
        transportGroupPO.setTransportGroupMode(requestType.getTransportGroupMode());
        transportGroupPO.setDispatcher(requestType.getDispatcher());
        transportGroupPO.setDispatcherLanguage(requestType.getDispatcherLanguage());
        transportGroupPO.setDispatcherPhone(TmsTransUtil.encrypt(requestType.getDispatcherPhone(), KeyType.Phone));
        transportGroupPO.setTakeOrderLimitTime(requestType.getTakeOrderLimitTime());
        transportGroupPO.setModifyUser(requestType.getModifyUser());
        transportGroupPO.setDatachangeLasttime(new Timestamp(System.currentTimeMillis()));
        transportGroupPO.setPointCityId(requestType.getPointCityIdList().get(0));
        transportGroupPO.setAreaGroupId(requestType.getAreaGroupId());
        transportGroupPO.setAreaGroupType(requestType.getTransportAreaType());
        transportGroupPO.setInformSwitch(requestType.getInformSwitch());
        transportGroupPO.setInformPhone(requestType.getInformPhone());
        transportGroupPO.setInformEmail(requestType.getInformEmail());
        transportGroupPO.setStandbyPhone(TmsTransUtil.encrypt(requestType.getStandbyPhone(), KeyType.Phone));
        return transportGroupPO;
    }

    /**
     * 进单配置PO
     * @param transportGroupId
     * @param configSOATypeList
     * @return
     */
    private List<TspIntoOrderConfigPO> requestTypeToConfigPO(Long transportGroupId, List<InOrderConfigSOAType> configSOATypeList, String modifyUser){
        List<TspIntoOrderConfigPO> list = Lists.newArrayList();
        for (InOrderConfigSOAType inOrderConfig : configSOATypeList) {
            TspIntoOrderConfigPO intoOrderConfigPO = new TspIntoOrderConfigPO();
            intoOrderConfigPO.setTransportGroupId(inOrderConfig.getTransportGroupId());
            intoOrderConfigPO.setCountryId(inOrderConfig.getCountryId());
            intoOrderConfigPO.setCountryName(inOrderConfig.getCountryName());
            intoOrderConfigPO.setCityId(inOrderConfig.getCityId());
            intoOrderConfigPO.setTransportGroupId(transportGroupId);
            intoOrderConfigPO.setLocationCode(inOrderConfig.getLocationCode());
            intoOrderConfigPO.setLocationType(inOrderConfig.getLocationType());
            intoOrderConfigPO.setConfig(JsonUtil.toJson(inOrderConfig.getConfigItems()));
            intoOrderConfigPO.setActive(Boolean.TRUE);
            intoOrderConfigPO.setModifyUser(modifyUser);
            intoOrderConfigPO.setDatachangeLasttime(new Timestamp(System.currentTimeMillis()));
            list.add(intoOrderConfigPO);
        }
        return list;
    }

    private List<TspTransportGroupWorkShiftPO> requestTypeToWorkShiftPO(Long transportGroupId, List<WorkShiftDetailSOAType> workShiftDetails, String modifyUser){
        List<TspTransportGroupWorkShiftPO> list = Lists.newArrayList();
        for (WorkShiftDetailSOAType workShiftDetail : workShiftDetails) {
            TspTransportGroupWorkShiftPO po = new TspTransportGroupWorkShiftPO();
            po.setId(workShiftDetail.getId());
            po.setName(workShiftDetail.getName());
            po.setModifyUser(modifyUser);
            po.setTransportGroupId(workShiftDetail.getTransportGroupId());
            po.setDriverUpperLimit(workShiftDetail.getDriverUpperLimit());
            po.setStarTime(workShiftDetail.getStartTime());
            po.setEndTime(workShiftDetail.getEndTime());
            po.setTransportGroupId(transportGroupId);
            list.add(po);
        }
        return list;
    }

}
