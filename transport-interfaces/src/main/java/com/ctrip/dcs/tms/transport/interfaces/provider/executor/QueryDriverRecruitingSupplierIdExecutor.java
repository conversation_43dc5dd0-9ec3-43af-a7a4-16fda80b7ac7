package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.dto.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.stereotype.*;
import org.springframework.util.*;

import javax.annotation.*;
import java.util.*;

@Component
public class QueryDriverRecruitingSupplierIdExecutor extends AbstractRpcExecutor< QueryDriverRecruitingSupplierIdRequestType,QueryDriverRecruitingSupplierIdResponseType> implements Validator<QueryDriverRecruitingSupplierIdRequestType> {
    @Resource
    private IPermissionService permissionService;
    @Override
    public QueryDriverRecruitingSupplierIdResponseType execute(QueryDriverRecruitingSupplierIdRequestType requestType) {
        try{
            List<Long> result = permissionService.queryDriverRecruitingSupplierId(getQueryDriverRecruitingSupplierIdParamDTO(requestType));
            QueryDriverRecruitingSupplierIdResponseType responseType = new QueryDriverRecruitingSupplierIdResponseType();
            responseType.setSupplierIds(result);
            return ServiceResponseUtils.success(responseType);
        }catch (Exception e){
            return ServiceResponseUtils.fail(new QueryDriverRecruitingSupplierIdResponseType());
        }
    }
    @Override
    public void validate(AbstractValidator<QueryDriverRecruitingSupplierIdRequestType> validator) {
    }

    /**
     * 封装参数
     * @param requestType
     * @return
     */
    private QueryDriverRecruitingSupplierIdParamDTO getQueryDriverRecruitingSupplierIdParamDTO(QueryDriverRecruitingSupplierIdRequestType requestType){
        QueryDriverRecruitingSupplierIdParamDTO paramDTO = new QueryDriverRecruitingSupplierIdParamDTO();
        paramDTO.setDrvRecruitingIds(requestType.getDriverRecruitingIds());
        //身份证加密
        if(!StringUtils.isEmpty(requestType.getDriverIdCard())){
            paramDTO.setDriverIdCard(TmsTransUtil.encrypt(requestType.getDriverIdCard(), KeyType.Identity_Card));
        }
        //手机号加密
        if(!StringUtils.isEmpty(requestType.getDriverPhone())){
            paramDTO.setDriverPhone(TmsTransUtil.encrypt(requestType.getDriverPhone(), KeyType.Phone));
        }
        paramDTO.setVehicleLicense(requestType.getVehicleLicense());
        return paramDTO;
    }
}
