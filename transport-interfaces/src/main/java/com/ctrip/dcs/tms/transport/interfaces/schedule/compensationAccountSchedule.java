package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverDomainServiceProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.Result;
import com.dianping.cat.Cat;
import com.google.common.collect.*;
import org.apache.commons.lang.time.DateUtils;
import org.apache.logging.log4j.util.*;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import org.springframework.util.*;
import qunar.tc.qschedule.config.*;
import qunar.tc.schedule.*;

import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

@Component
public class compensationAccountSchedule {

    private static final Logger logger = LoggerFactory.getLogger(compensationAccountSchedule.class);

    @Autowired
    private CommonCommandService commonCommandService;
    @Autowired
    private DrvDrvierRepository drvierRepository;
    @Autowired
    private TmsQmqProducerCommandService tmsQmqProducerCommandService;

    @Autowired
    DriverDomainServiceProxy driverDomainServiceProxy;

    final int count = 200;

    @QSchedule("ctrip.transport.compensation.account.job")
    public void fixAccountSchedule2(Parameter parameter) throws Exception {
        logger.info("start fix account missing");
        String type = Optional.ofNullable(parameter.getString("type")).orElse("");

        switch (type) {
            case "byDate":
                createRecentDateAccountWhenNeed(Integer.parseInt(parameter.getString("day")));
                break;
            case "all":
                createAccount();
                break;
            case "byDrvIds":
                createAccountByIdsWhenNeed(parameter.getString("drvIds"));
                break;
            default:
                createRecentDateAccountWhenNeed(30);
                break;
        }

        logger.info("start fix account processing is complete {}", new Date());
    }

    private void createAccountByIdsWhenNeed(String drvIds) {
        drvierRepository.queryDrvList(getDrvIdList(drvIds)).stream().filter(DrvDriverPO::getActive).forEach(drvDriverPO -> {
            createAccountWhenNeed(drvDriverPO);
        });
    }

    protected List<Long> getDrvIdList(String drvIds) {
        List<Long> idList = Lists.newArrayList();
        String[] idArray = drvIds.split(",");
        for (String id : idArray) {
            idList.add(Long.parseLong(id));
        }
        return idList;
    }

    protected void createAccount() throws SQLException {
        AtomicReference<Long> drvId = new AtomicReference<>(0L);
        int pageNo = 1;
        int pageSize = 10;
        List<DrvDriverPO> driverPOList;

        do{
            driverPOList =
                    drvierRepository.queryDrvListByDriverIdFromAndPage(drvId.get(), null, null, pageNo, pageSize);
            driverPOList.forEach(drvDriverPO -> drvId.set(Math.max(drvId.get(), drvDriverPO.getDrvId())));
            driverPOList.stream().filter(DrvDriverPO::getActive).forEach(drvDriverPO -> {
                createAccountWhenNeed(drvDriverPO);
            });
        }while (org.apache.commons.collections.CollectionUtils.isNotEmpty(driverPOList));
    }

    protected void createRecentDateAccountWhenNeed(int amount) throws SQLException {
        AtomicReference<Long> drvId = new AtomicReference<>(0L);
        int pageNo = 1;
        int pageSize = 10;
        List<DrvDriverPO> driverPOList;

        do{
            // 判断近一个月内的账号有没有生成
            driverPOList =
              drvierRepository.queryRecentDateDrvListByDriverIdFromAndPage(pageNo, pageSize, DateUtils.addDays(new Date(), -amount));
            driverPOList.stream().filter(DrvDriverPO::getActive).forEach(driverPO -> {
                createAccountWhenNeed(driverPO);
            });
            pageNo++;
        }while (org.apache.commons.collections.CollectionUtils.isNotEmpty(driverPOList));
    }

    protected void createAccountWhenNeed(DrvDriverPO driverPO) {
        String[] ppmAndQunarAccount = new String[]{"", ""};
        if (Strings.isEmpty(driverPO.getPpmAccount()) || Strings.isEmpty(driverPO.getQunarAccount())) {
            ppmAndQunarAccount = commonCommandService.createQunarAccount(driverPO);
        }
        String imAccount = "";
        if (Strings.isEmpty(driverPO.getImAccount())) {
            imAccount = commonCommandService.createCtripAccount(driverPO);
        }
        int count = drvierRepository.updateDrvAccount(driverPO.getDrvId(), ppmAndQunarAccount[0], ppmAndQunarAccount[1], imAccount);
        if (count > 0) {
            tmsQmqProducerCommandService.sendDrvChangeQmq(driverPO.getDrvId(),2,1);
        }
        logger.info("fix {}:{} account update count:{}", driverPO.getDrvId(), driverPO.getDrvName(), count);
    }

}
