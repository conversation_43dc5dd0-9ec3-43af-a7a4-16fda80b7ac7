package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 添加司机车辆审批
 */
@Component
public class DrvHealthPunchAddExecutor extends AbstractRpcExecutor<DrvHealthPunchAddRequestType, DrvHealthPunchAddResponseType> implements Validator<DrvHealthPunchAddRequestType> {

    @Autowired
    private DriverCommandService driverCommandService;

    @Override
    public DrvHealthPunchAddResponseType execute(DrvHealthPunchAddRequestType requestType) {
        DrvHealthPunchAddResponseType responseType = new DrvHealthPunchAddResponseType();
        Result<Boolean> result = driverCommandService.drvHealthPunchAdd(requestType);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
    }
}
