package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.tms.transport.api.model.DrvPreCheckRequestType;
import com.ctrip.dcs.tms.transport.api.model.DrvPreCheckResponseType;
import com.ctrip.dcs.tms.transport.application.dto.PreCheckResultDTO;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.ResponseResultUtil;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.platform.dal.dao.annotation.DalTransactional;

/**
 * 司机入驻/编辑前置校验
 * 
 * <AUTHOR>
 */
@Component
public class DrvPreCheckExecutor extends AbstractRpcExecutor<DrvPreCheckRequestType, DrvPreCheckResponseType> implements Validator<DrvPreCheckRequestType> {

    @Autowired
    private DriverQueryService driverQueryService;

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public DrvPreCheckResponseType execute(DrvPreCheckRequestType requestType) {
        Result<PreCheckResultDTO> result = driverQueryService.drvPreCheck(requestType);
        DrvPreCheckResponseType response = new DrvPreCheckResponseType();
        response.setDrvId(Objects.nonNull(result.getData()) ? result.getData().getDriverId() : null);
        response.setDriverGuidePhone(Objects.nonNull(result.getData()) ? result.getData().getPhoneNumber() : null);
        return ResponseResultUtil.response(response, result);
    }
}
