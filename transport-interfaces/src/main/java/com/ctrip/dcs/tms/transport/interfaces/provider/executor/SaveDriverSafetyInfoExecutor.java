package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.monitoring.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * <AUTHOR>
 */
@Component
public class SaveDriverSafetyInfoExecutor extends AbstractRpcExecutor<SaveDriverSafetyInfoSOARequestType, SaveDriverSafetyInfoSOAResponseType> implements Validator<SaveDriverSafetyInfoSOARequestType> {

    @Autowired
    private SaveDriverSafetyInfoCommandService saveDriverSafetyInfoCommandService;

    @Override
    public SaveDriverSafetyInfoSOAResponseType execute(SaveDriverSafetyInfoSOARequestType saveDriverSafetyInfoSOARequestType) {
        TransportMetric.epidemicControlAllCounter.inc();
        Result<String> result = saveDriverSafetyInfoCommandService.saveDriverSafetyInfo(saveDriverSafetyInfoSOARequestType);
        if (result == null) {
            TransportMetric.checkFailCounter.inc();
            TransportMetric.epidemicControlErrorCounter.inc();
            return ServiceResponseUtils.fail(new SaveDriverSafetyInfoSOAResponseType());
        }
        return ServiceResponseUtils.success(new SaveDriverSafetyInfoSOAResponseType(), result.getCode(), result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<SaveDriverSafetyInfoSOARequestType> validator) {
        validator.ruleFor("detectionType").notNull();
        validator.ruleFor("driverId").notNull();
    }
}