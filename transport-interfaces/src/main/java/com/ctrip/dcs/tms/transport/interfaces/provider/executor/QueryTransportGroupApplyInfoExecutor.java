package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * <AUTHOR>
 * @Date 2020/11/25 15:15
 */
@Component
public class QueryTransportGroupApplyInfoExecutor extends AbstractRpcExecutor<QueryTransportGroupApplyInfoSOARequestType, QueryTransportGroupApplyInfoSOAResponseType> implements Validator<QueryTransportGroupApplyInfoSOARequestType> {

    @Autowired
    private TransportGroupQueryService transportGroupQueryService;

    @Override
    public QueryTransportGroupApplyInfoSOAResponseType execute(QueryTransportGroupApplyInfoSOARequestType queryTransportGroupApplyInfoSOARequestType) {
        QueryTransportGroupApplyInfoSOAResponseType responseType = new QueryTransportGroupApplyInfoSOAResponseType();
        Result<QueryTransportGroupApplyInfoSOAResponseType> result = transportGroupQueryService.queryTransportGroupApplyInfo(queryTransportGroupApplyInfoSOARequestType);
        if (!result.isSuccess()) {
            return ServiceResponseUtils.fail(responseType,result.getCode(),result.getMsg());
        }
        return ServiceResponseUtils.success(result.getData());
    }

    @Override
    public void validate(AbstractValidator<QueryTransportGroupApplyInfoSOARequestType> validator, QueryTransportGroupApplyInfoSOARequestType req) {
        validator.ruleFor("transportGroupId").notNull();
    }
}
