package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.infrastructure.constant.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

@Component
public class AuthorizationCheckExecutor extends AbstractRpcExecutor<AuthorizationSOARequestType, AuthorizationSOAResponseType> implements Validator<AuthorizationSOARequestType> {

    @Autowired
    private AuthorizationCheckService authorizationCheckService;

    @Override
    public AuthorizationSOAResponseType execute(AuthorizationSOARequestType authorizationSOARequestType) {
        AuthorizationSOAResponseType responseType = new AuthorizationSOAResponseType();
        if (authorizationCheckService.decisionRoute(authorizationSOARequestType.materialTypeId, authorizationSOARequestType.getMaterialId(), authorizationSOARequestType.getSupplierId())) {
            return ServiceResponseUtils.success(responseType);
        } else {
            return ServiceResponseUtils.fail(responseType,ServiceResponseConstants.ResStatus.EXCEPTION_CODE);
        }
    }

    @Override
    public void validate(AbstractValidator<AuthorizationSOARequestType> validator, AuthorizationSOARequestType req) {
        validator.ruleFor("materialId").notNull();
        validator.ruleFor("supplierId").notNull();
        validator.ruleFor("materialTypeId").notNull();
    }

}