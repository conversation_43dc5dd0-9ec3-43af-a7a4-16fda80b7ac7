package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/***
　* @description: 司机是否可健康打卡
　* <AUTHOR>
　* @date 2021/9/17 11:50
*/
@Component
public class QueryDrvHealthPunchPermissionsExecutor extends AbstractRpcExecutor<QueryDrvHealthPunchPermissionsRequestType, QueryDrvHealthPunchPermissionsResponseType> implements Validator<QueryDrvHealthPunchPermissionsRequestType> {

    @Autowired
    private DriverQueryService queryService;

    @Override
    public QueryDrvHealthPunchPermissionsResponseType execute(QueryDrvHealthPunchPermissionsRequestType requestType) {
        QueryDrvHealthPunchPermissionsResponseType responseType = new QueryDrvHealthPunchPermissionsResponseType();
        Result<Boolean> result = queryService.queryDrvHealthPunchPermissions(requestType.getDrvId());
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<QueryDrvHealthPunchPermissionsRequestType> validator) {
        validator.ruleFor("drvId").notNull();
    }
}
