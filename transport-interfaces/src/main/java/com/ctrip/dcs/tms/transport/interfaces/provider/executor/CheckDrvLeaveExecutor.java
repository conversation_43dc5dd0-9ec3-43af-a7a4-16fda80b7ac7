package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.time.*;

/**
 * <AUTHOR>
 * @since 2020/9/16 19:26
 */
@Component
public class CheckDrvLeaveExecutor extends AbstractRpcExecutor<CheckDrvLeaveRequestType, CheckDrvLeaveResponseType>
    implements Validator<CheckDrvLeaveRequestType> {

  @Autowired
  private DriverLeaveQueryService queryService;

  @Override
  public CheckDrvLeaveResponseType execute(CheckDrvLeaveRequestType request) {
    Result<String> result = queryService.checkDrvLeave(request.getDrvId(), LocalDateTime.now());

    CheckDrvLeaveResponseType response = new CheckDrvLeaveResponseType();
    if (result.isSuccess()) {
      response.setLeaveReason(result.getData());
      return ServiceResponseUtils.success(response);
    }
    return ServiceResponseUtils.fail(response, "200");
  }

  @Override
  public void validate(AbstractValidator<CheckDrvLeaveRequestType> validator) {
    validator.ruleFor("drvId").notNull().greaterThan(0L);
  }
}
