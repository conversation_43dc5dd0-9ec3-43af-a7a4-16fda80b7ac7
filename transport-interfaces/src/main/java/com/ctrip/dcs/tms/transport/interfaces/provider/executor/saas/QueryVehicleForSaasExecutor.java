package com.ctrip.dcs.tms.transport.interfaces.provider.executor.saas;

import com.ctrip.dcs.tms.transport.api.saas.*;
import com.ctrip.dcs.tms.transport.application.dto.SaasVehicleDTO;
import com.ctrip.dcs.tms.transport.application.query.IQueryVehicleForSaasService;
import com.ctrip.dcs.tms.transport.application.query.VehicleQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.VehCacheDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.LocalCollectionUtils;
import com.ctrip.dcs.tms.transport.interfaces.bridge.DriverAndDriverGuideMergeProcessService;
import com.ctrip.dcs.tms.transport.interfaces.bridge.ProductLineBridgeManagement;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class QueryVehicleForSaasExecutor extends AbstractRpcExecutor<QueryVehicleForSaasRequestType, QueryVehicleForSaasResponseType> implements Validator<QueryVehicleForSaasRequestType> {
    @Autowired
    ProductLineBridgeManagement productLineBridgeManagement;
    @Override
    public QueryVehicleForSaasResponseType execute(QueryVehicleForSaasRequestType requestType) {
        return productLineBridgeManagement.queryVehicleForSaas(requestType);
    }
    @Override
    public void validate(AbstractValidator<QueryVehicleForSaasRequestType> validator) {
        validator.ruleFor("vehicleIds").notNull().notEmpty();
    }

}
