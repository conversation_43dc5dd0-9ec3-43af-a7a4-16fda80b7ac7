package com.ctrip.dcs.tms.transport.interfaces.provider.executor;


import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.interfaces.bridge.DriverAndDriverGuideMergeProcessService;
import com.ctrip.dcs.tms.transport.interfaces.bridge.ProductLineBridgeManagement;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 司机详情
 */
@Component
public class QueryDrvDetailExecutor extends AbstractRpcExecutor<QueryDrvDetailSOARequestType, QueryDrvDetailSOAResponseType> implements Validator<QueryDrvDetailSOARequestType> {

    @Autowired
    ProductLineBridgeManagement productLineBridgeManagement;

    @Override
    public QueryDrvDetailSOAResponseType execute(QueryDrvDetailSOARequestType queryDrvDetailRequestType) {
        return productLineBridgeManagement.queryDrvDetail(queryDrvDetailRequestType);
    }
}
