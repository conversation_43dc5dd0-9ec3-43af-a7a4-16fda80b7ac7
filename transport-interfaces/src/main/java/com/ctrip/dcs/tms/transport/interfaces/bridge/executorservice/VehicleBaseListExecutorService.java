package com.ctrip.dcs.tms.transport.interfaces.bridge.executorservice;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.basicdatadomain.interfaces.dto.CarDTO;
import com.ctrip.dcs.basicdatadomain.interfaces.dto.VehicleModelInfoDTO;
import com.ctrip.dcs.basicdatadomain.interfaces.message.QueryVehicleModelInfoRequestType;
import com.ctrip.dcs.basicdatadomain.interfaces.message.QueryVehicleModelInfoResponseType;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.tms.transport.api.model.QueryVehicleBaseSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryVehicleBaseSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.VehicleBaseSOADTO;
import com.ctrip.dcs.tms.transport.application.dto.VehicleAge;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.Dcsbasicdatadomain;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.VehicleCoreService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehVehiclePO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OverageDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CommonConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OverageQConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRepository;
import com.ctrip.dcs.vehicle.core.contract.QueryVehicleCategoryRelationsRequest;
import com.ctrip.dcs.vehicle.core.contract.QueryVehicleCategoryRelationsResponse;
import com.ctrip.dcs.vehicle.core.contract.VehicleCategoryRelationContract;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

@Component
public class VehicleBaseListExecutorService{

    private static final Logger logger = LoggerFactory.getLogger(VehicleBaseListExecutorService.class);

    @Autowired
    private VehicleRepository vehicleRepository;
    @Autowired
    private EnumRepository enumRepository;
    @Autowired
    private ProductionLineUtil productionLineUtil;
    @Autowired
    VehicleCoreService vehicleCoreService;
    @Autowired
    Dcsbasicdatadomain dcsbasicdatadomain;
    @Autowired
    OverageQConfig overageQConfig;

    @Autowired
    private CommonConfig config;


    public QueryVehicleBaseSOAResponseType execute(QueryVehicleBaseSOARequestType requestType) {
        QueryVehicleBaseSOAResponseType responseType = new QueryVehicleBaseSOAResponseType();
        try {
            List<VehVehiclePO> vehiclePOList = vehicleRepository.queryVehVehicleBySupplierId(requestType.getSupplierId(),requestType.getHasDrv(),requestType.getCityId(), productionLineUtil.getIncludeProductionLineList(requestType.getProLineList()));
            if (CollectionUtils.isEmpty(vehiclePOList)) {
                return ServiceResponseUtils.success(responseType);
            }

            //车型筛选，采购用
            if (requestType.getVehicleTypeId() != null) {
                responseType.setData(getBaseList(getResultData(requestType.getVehicleTypeId(),vehiclePOList,requestType.getCityId())));
                return ServiceResponseUtils.success(responseType);
            }
            responseType.setData(getBaseList(vehiclePOList));
            return ServiceResponseUtils.success(responseType);
        } catch (Exception e) {
            throw new BaijiRuntimeException("VehicleBaseListExecutor error", e);
        }
    }

    public List<VehicleBaseSOADTO> getBaseList(List<VehVehiclePO> vehiclePOList) {
        List<VehicleBaseSOADTO> result = Lists.newArrayListWithCapacity(vehiclePOList.size());
        for (VehVehiclePO vehVehiclePO : vehiclePOList) {
            VehicleBaseSOADTO vehicleBaseSOADTO = new VehicleBaseSOADTO();
            vehicleBaseSOADTO.setVehicleId(vehVehiclePO.getVehicleId());
            vehicleBaseSOADTO.setVehicleLicense(vehVehiclePO.getVehicleLicense());
            vehicleBaseSOADTO.setVehicleBrandId(vehVehiclePO.getVehicleBrandId());
            vehicleBaseSOADTO.setVehicleBrandName(enumRepository.getBandName(vehVehiclePO.getVehicleBrandId()));
            vehicleBaseSOADTO.setVehicleColorId(vehVehiclePO.getVehicleColorId());
            vehicleBaseSOADTO.setVehicleColorName(enumRepository.getColorName(vehVehiclePO.getVehicleColorId()));
            vehicleBaseSOADTO.setVehicleSeries(vehVehiclePO.getVehicleSeries());
            vehicleBaseSOADTO.setVehicleSeriesName(enumRepository.getVehicleSeriesName(vehVehiclePO.getVehicleSeries()));
            vehicleBaseSOADTO.setVehicleTypeId(vehVehiclePO.getVehicleTypeId());
            vehicleBaseSOADTO.setVehicleTypeName(enumRepository.getVehicleTypeName(vehVehiclePO.getVehicleTypeId()));
            vehicleBaseSOADTO.setTemporaryDispatchMark(vehVehiclePO.getTemporaryDispatchMark());
            result.add(vehicleBaseSOADTO);
        }
        processOverAgeTime(vehiclePOList,result);
        return result;
    }

    private void processOverAgeTime(List<VehVehiclePO> vehiclePOList, List<VehicleBaseSOADTO> result) {
        try {
            List<Long> cityIdList = vehiclePOList.stream().filter(Objects::nonNull).filter(item -> Objects.nonNull(item.getCityId())).map(VehVehiclePO::getCityId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(cityIdList)) {
                return;
            }
            if (BooleanUtils.isTrue(config.getOverageGraySwitch())) {
                cityIdList = cityIdList.stream().filter(item -> CollectionUtils.isNotEmpty(config.getCityIdList()) && config.getCityIdList().contains(item)).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(cityIdList)) {
                return;
            }
            List<VehicleAge> vehicleAges = getVehicleAges(vehiclePOList, cityIdList);
            if (vehicleAges == null) {
                return;
            }

            Map<Long, VehicleAge> collect = vehicleAges.stream().collect(Collectors.toMap(VehicleAge::getVehicleId, vehicleAge -> vehicleAge, (k1, k2) -> k1));
            result.forEach(item -> {
                if (collect.containsKey(item.getVehicleId())) {
                    item.setOverAgeTime(collect.get(item.getVehicleId()).getOverAgeTime());
                }
            });
        } catch (Exception e) {
            logger.error("processOverAgeTime error", e);
        }
    }


    private List<VehicleAge> getVehicleAges(List<VehVehiclePO> vehiclePOList, List<Long> cityIdList) {

        List<VehicleAge> vehicleAges = getVehicleAges(vehiclePOList);
        extracted(cityIdList, vehicleAges);
        return vehicleAges;
    }

    private static List<VehicleAge> getVehicleAges(List<VehVehiclePO> vehiclePOList) {
        List<VehicleAge> vehicleAges = vehiclePOList.stream().map(vehCacheDTO -> {
            VehicleAge vehicleAge = new VehicleAge();
            vehicleAge.setVehicleId(vehCacheDTO.getVehicleId());
            vehicleAge.setCityId(vehCacheDTO.getCityId());
            vehicleAge.setCarTypeId(Math.toIntExact(vehCacheDTO.getVehicleTypeId()));
            vehicleAge.setVehCreateTime(DateUtil.timestampToString(vehCacheDTO.getDatachangeCreatetime(),DateUtil.YYYYMMDDHHMMSS));
            vehicleAge.setVehRegstDate(Objects.nonNull(vehCacheDTO.getRegstDate()) ? vehCacheDTO.getRegstDate().toString() : null);
            return vehicleAge;
        }).collect(Collectors.toList());
        return vehicleAges;
    }


    private void extracted(List<Long> cityIdList, List<VehicleAge> vehicleAges) {
        List<City> cities = enumRepository.queryByCityIds(cityIdList);
        if (CollectionUtils.isEmpty(cities)) {
            return;
        }
        Map<Long, City> cityMap = cities.stream().collect(Collectors.toMap(City::getId, a -> a, (k1, k2) -> k1));
        for (VehicleAge vehCacheDTO : vehicleAges) {
            if (cityMap.containsKey(vehCacheDTO.getCityId())) {
                City city = cityMap.get(vehCacheDTO.getCityId());
                if (city.isChineseMainland()) {
                    OverageDTO overageMap = overageQConfig.getOverageMap(city.getId(), Long.valueOf(vehCacheDTO.getCarTypeId()));
                    String vehRegstDate = vehCacheDTO.getVehRegstDate();
                    LocalDateTime date;
                    if (StringUtils.equals(Constant.DEFAULT_YEAR, vehRegstDate)) {
                        String vehCreateTime = vehCacheDTO.getVehCreateTime();
                        date = DateUtil.convertStringToDateTime(vehCreateTime, DateUtil.YYYYMMDDHHMMSS);
                    } else {
                        LocalDate localDate = DateUtil.convertStringToDate(vehRegstDate, DateUtil.YYYYMMDD);
                        LocalTime localTime = LocalTime.of(23, 59, 59);
                        date = LocalDateTime.of(localDate, localTime);
                    }
                    BigDecimal multiply = BigDecimal.valueOf(overageMap.getOverage()).multiply(BigDecimal.valueOf(12L));
                    LocalDateTime localDateTime = date.plusMonths(multiply.intValue());
                    vehCacheDTO.setOverAgeTime(DateUtil.convertDateToString(localDateTime, DateUtil.YYYYMMDDHHMMSS));
                }
            }
        }
    }


    public List<VehVehiclePO> getResultData(Long vehicleTypeId,List<VehVehiclePO> vehiclePOList,Long cityId){
        //查询车型的同级+升级 车型
        try {
            QueryVehicleCategoryRelationsRequest relationsRequest = new QueryVehicleCategoryRelationsRequest();
            relationsRequest.setSourceVehicleCategoryCode(String.valueOf(vehicleTypeId));
            QueryVehicleCategoryRelationsResponse relationsResponse =  vehicleCoreService.queryVehicleCategoryRelations(relationsRequest);
            Set<Long> setVehicleTypeIds = Sets.newHashSet();
            if(relationsResponse != null && ServiceResponseConstants.ResStatus.SUCCESS_CODE.equals(relationsResponse.getCode())){
                List<VehicleCategoryRelationContract> categoryRelationDTOS = relationsResponse.getData();
                if(CollectionUtils.isNotEmpty(categoryRelationDTOS)){
                    for(VehicleCategoryRelationContract relationDTO : categoryRelationDTOS){
                        //筛选出同级+升级的车型ID
                        if("0".equals(relationDTO.getRelationCode()) || "1".equals(relationDTO.getRelationCode())){
                            setVehicleTypeIds.add(Long.valueOf(relationDTO.getTargetVehicleCategoryCode()));
                        }
                    }
                }
            }
            logger.info("queryVehicleCategoryRelations","setVehicleTypeIds:{}", JsonUtil.toJson(setVehicleTypeIds));
            List<VehVehiclePO> resVehiclePOList = Lists.newArrayList();
            //如果筛选出的车型不为空，则直接返回同级+升级的车型信息
            if(CollectionUtils.isNotEmpty(setVehicleTypeIds)){
                for(VehVehiclePO vehVehiclePO : vehiclePOList){
                    if(setVehicleTypeIds.contains(vehVehiclePO.getVehicleTypeId())){
                        resVehiclePOList.add(vehVehiclePO);
                    }
                }
                return resVehiclePOList;
            }
            //通过车系过滤底翅车辆数据
            return filtrationVehicleSeries(vehicleTypeId,cityId,vehiclePOList);
        }catch (Exception e){
            logger.error("VehicleBaseListExecutor_getResultData_ERROR","vehicleTypeId:{},cityId:{}",vehicleTypeId,cityId,e);
            return vehiclePOList;
        }
    }

    //车型+城市 查询对应的车系，通过车系筛选对应的车辆数据
    public List<VehVehiclePO> filtrationVehicleSeries(Long vehicleTypeId,Long cityId,List<VehVehiclePO> vehiclePOList){
        logger.info("filtrationVehicleSeriesStart","vehicleTypeId:{},cityId:{}", vehicleTypeId,cityId);
        try {
            QueryVehicleModelInfoRequestType infoRequestType = new QueryVehicleModelInfoRequestType();
            infoRequestType.setVehicleModelIds(Arrays.asList(vehicleTypeId));
            infoRequestType.setCityId(cityId);
            QueryVehicleModelInfoResponseType modelInfoResponseType =  dcsbasicdatadomain.queryVehicleModelInfo(infoRequestType);
            //调用服务异常，则返回原数据
            if(modelInfoResponseType == null){
                return vehiclePOList;
            }
            if(modelInfoResponseType.getResponseResult() == null){
                return vehiclePOList;
            }
            if(!ServiceResponseConstants.ResStatus.SUCCESS_CODE.equals(modelInfoResponseType.getResponseResult().getReturnCode())){
                return vehiclePOList;
            }
            if(CollectionUtils.isEmpty(modelInfoResponseType.getResults())){
                return vehiclePOList;
            }
            VehicleModelInfoDTO vehicleModelInfoDTO =  modelInfoResponseType.getResults().get(0);
            if(Objects.isNull(vehicleModelInfoDTO)){
                return vehiclePOList;
            }
            if(CollectionUtils.isEmpty(vehicleModelInfoDTO.getCarList())){
                return vehiclePOList;
            }
            List<CarDTO> carDTOList =  vehicleModelInfoDTO.getCarList();
            Set<Long> vehicleSerieSet = Sets.newHashSet();
            for(CarDTO carDTO : carDTOList){
                vehicleSerieSet.add(carDTO.getId());
            }
            logger.info("queryVehicleModelInfo","vehicleSerieSet:{}", JsonUtil.toJson(vehicleSerieSet));
            List<VehVehiclePO> resVehiclePOList = Lists.newArrayList();
            //筛选出同车系的车辆
            for(VehVehiclePO vehVehiclePO : vehiclePOList){
                if(vehicleSerieSet.contains(vehVehiclePO.getVehicleSeries())){
                    resVehiclePOList.add(vehVehiclePO);
                }
            }
            return resVehiclePOList;
        }catch (Exception e){
            logger.error("VehicleBaseListExecutor_filtrationVehicleSeries_ERROR","vehicleTypeId:{},cityId:{}",vehicleTypeId,cityId,e);
            return vehiclePOList;
        }
    }
}
