package com.ctrip.dcs.tms.transport.interfaces.provider.executor;


import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.common.base.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

@Component
public class QueryRecruitingVehForCrawlerExecutor extends AbstractRpcExecutor<QueryRecruitingVehForCrawlerRequestType, QueryRecruitingVehForCrawlerResponseType> implements Validator<QueryRecruitingVehForCrawlerRequestType> {

    @Autowired
    DrvVehRecruitingQueryService driverQueryService;

    @Override
    public QueryRecruitingVehForCrawlerResponseType execute(QueryRecruitingVehForCrawlerRequestType requestType) {
        QueryRecruitingVehForCrawlerResponseType responseType = new QueryRecruitingVehForCrawlerResponseType();
        Result<PageHolder<QueryRecruitingVehForCrawlerDTO>> result = driverQueryService.queryRecruitingVehForCrawler(requestType);
        if (result.isSuccess()) {
            PaginationDTO paginationDTO = ServiceResponseUtils.newPagination(result.getData().getPageIndex(),
                    result.getData().getPageSize(), result.getData().getTotalSize());
            responseType.setData(result.getData().getData());
            responseType.setPagination(paginationDTO);
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType,"400",result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<QueryRecruitingVehForCrawlerRequestType> validator) {
    }
}
