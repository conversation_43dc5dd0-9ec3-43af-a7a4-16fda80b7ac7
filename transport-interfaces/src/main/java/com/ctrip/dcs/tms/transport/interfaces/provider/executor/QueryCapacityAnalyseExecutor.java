package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryCapacityAnalyseSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryCapacityAnalyseSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryInventoryCapacitySOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryInventoryCapacitySOAResponseType;
import com.ctrip.dcs.tms.transport.application.query.EffectiveCapacityQueryService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class QueryCapacityAnalyseExecutor extends AbstractRpcExecutor<QueryCapacityAnalyseSOARequestType, QueryCapacityAnalyseSOAResponseType> implements Validator<QueryCapacityAnalyseSOARequestType> {

    @Autowired
    EffectiveCapacityQueryService service;

    @Override
    public QueryCapacityAnalyseSOAResponseType execute(QueryCapacityAnalyseSOARequestType requestType) {
        QueryCapacityAnalyseSOAResponseType responseType = new QueryCapacityAnalyseSOAResponseType();
        Result<QueryCapacityAnalyseSOAResponseType> result = service.queryCapacityAnalyse(requestType);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(result.getData());
        }
        return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<QueryCapacityAnalyseSOARequestType> validator) {

    }
}
