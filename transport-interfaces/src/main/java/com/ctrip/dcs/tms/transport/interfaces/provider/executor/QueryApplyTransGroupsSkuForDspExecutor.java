package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
　* @description: 查询报名制运力组绑定的有效SKU
　* <AUTHOR>
　* @date 2022/4/18 13:59
*/
@Component
public class QueryApplyTransGroupsSkuForDspExecutor extends AbstractRpcExecutor<QueryApplyTransGroupsSkuForDspRequestType, QueryApplyTransGroupsSkuForDspResponseType> implements Validator<QueryApplyTransGroupsSkuForDspRequestType> {

    @Autowired
    private TransportGroupQueryService transportGroupQueryService;

    @Override
    public QueryApplyTransGroupsSkuForDspResponseType execute(QueryApplyTransGroupsSkuForDspRequestType requestType) {
        QueryApplyTransGroupsSkuForDspResponseType soaResponseType = new QueryApplyTransGroupsSkuForDspResponseType();
        Result<List<QueryApplyTransGroupsSkuForDspInfo>> listResult = transportGroupQueryService.queryApplyTransGroupsSkuForDsp(requestType);
        if (listResult.isSuccess()) {
            soaResponseType.setData(listResult.getData());
            return ServiceResponseUtils.success(soaResponseType);
        }
        return ServiceResponseUtils.fail(soaResponseType, listResult.getCode(),listResult.getMsg());
    }

    @Override
    public void validate(AbstractValidator<QueryApplyTransGroupsSkuForDspRequestType> validator) {
        validator.ruleFor("cityIds").notNull();
        validator.ruleFor("carTypeIds").notNull();

    }
}
