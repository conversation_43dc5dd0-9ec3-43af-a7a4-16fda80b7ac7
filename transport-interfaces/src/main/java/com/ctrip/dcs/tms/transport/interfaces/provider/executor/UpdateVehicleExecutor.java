package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.application.query.impl.NetCertNoCheckRuleService;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.constant.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

@Component
public class UpdateVehicleExecutor extends AbstractRpcExecutor<VehicleUpdateSOARequestType, VehicleUpdateSOAResponseType> implements Validator<VehicleUpdateSOARequestType> {

    @Autowired
    private VehicleCommandService vehicleCommandService;
    @Autowired
    private VehicleQueryService vehicleQueryService;
    @Autowired
    private NetCertNoCheckRuleService netCertNoCheckRuleService;
    @Override
    public VehicleUpdateSOAResponseType execute(VehicleUpdateSOARequestType vehicleUpdateSOARequestType) {
        if (!vehicleQueryService.isVehicleLicenseUniqueness(vehicleUpdateSOARequestType.getVehicleId(), vehicleUpdateSOARequestType.getVehicleLicense()).getData()) {
            return ServiceResponseUtils.fail(new VehicleUpdateSOAResponseType(), ServiceResponseConstants.ResStatus.EXCEPTION_CODE, SharkUtils.getSharkValue(SharkKeyConstant.transportVehicleLicenseUniqueness));
        }
        //校验网约车运输证号
        if(!checkVehicleNetCertNo(vehicleUpdateSOARequestType)){
            return ServiceResponseUtils.fail(new VehicleUpdateSOAResponseType(),"400",SharkUtils.getSharkValue(SharkKeyConstant.vehicleNetCertNoCheckFalse));
        }
        //加密网约车运输证号
        encryptVehicleNetCertNo(vehicleUpdateSOARequestType);
        //更新车辆信息
        Result<Boolean> result = vehicleCommandService.updateVehicle(vehicleUpdateSOARequestType);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(new VehicleUpdateSOAResponseType());
        } else {
            return ServiceResponseUtils.fail(new VehicleUpdateSOAResponseType(),"400",result.getMsg());
        }
    }

    /**
     * 加密网约车运输证号
     * @param requestType
     */
    private void encryptVehicleNetCertNo(VehicleUpdateSOARequestType requestType){
        if(org.springframework.util.StringUtils.isEmpty(requestType.getVehicleNetCertNo())){
            return;
        }
        String result = TmsTransUtil.encrypt(requestType.getVehicleNetCertNo(), KeyType.OtherDocument);
        requestType.setVehicleNetCertNo(result);
    }
    /**
     * 校验网约车运输证号
     * @param requestType
     * @return
     */
    private boolean checkVehicleNetCertNo(VehicleUpdateSOARequestType requestType){
        //非必传参数
        if(org.springframework.util.StringUtils.isEmpty(requestType.getVehicleNetCertNo())){
            return true;
        }
        //必传参数
        if(requestType.getCityId() == null){
            return false;
        }
        return netCertNoCheckRuleService.checkVehicleNetCertNo(requestType.getCityId().toString(),requestType.getVehicleNetCertNo());
    }
}