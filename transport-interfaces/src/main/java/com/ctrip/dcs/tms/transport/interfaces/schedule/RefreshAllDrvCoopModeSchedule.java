package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.dianping.cat.Cat;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import qunar.tc.qschedule.config.*;
import qunar.tc.schedule.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.*;

/**
 * <AUTHOR>
 * @Description  刷新司机合作模式
 * @Date 10:44 2020/12/18
 * @Param 
 * @return 
 **/
@Component
public class RefreshAllDrvCoopModeSchedule {
    private static final Logger logger = LoggerFactory.getLogger(RefreshAllDrvCoopModeSchedule.class);

    @Autowired
    private DrvDrvierRepository drvierRepository;
    @Autowired
    private DriverQueryService driverQueryService;
    @Resource
    private TransportGroupQueryService transportGroupQueryService;
    
    @QSchedule("refresh.drv.coopmode.job")
    public void refreshAllDrvCoopModeSchedule(Parameter parameter) throws Exception {
        String traceId = UUID.randomUUID().toString();
        Map<String,String> logTags = LogTagUtils.getTag("globalTraceId",traceId);
        logger.info("refreshAllDrvCoopModeSchedule_start","",logTags);
        String drvIds = parameter.getString("drvIds");
        logger.info("refreshAllDrvCoopModeSchedule_drvIds",drvIds,logTags);
        //参数中的司机id为空则更新数据库中所有司机的合作模式
        if(org.springframework.util.StringUtils.isEmpty(drvIds)){
            refreshAllDriverCoopmode(traceId);
            return;
        }
        //根据参数中的司机id更新司机合作模式
        refreshDriverCoopmode(drvIds,traceId);
        logger.info("refreshAllDrvCoopModeSchedule_end","",logTags);
    }
    /**
     * 更新数据库中所有司机的合作模式
     */
    private void refreshAllDriverCoopmode(String traceId){
        try{
            int pageSize = 1000;
            int totalPage = getTotalPage(pageSize);
            if(totalPage == 0){
                return;
            }
            for(int i=0;i<totalPage;i++){
                int pageNo = i+1;
                updateCoopmode(pageNo,pageSize,traceId);
            }
        }catch (Exception e){
            Map<String,String> logTags = LogTagUtils.getTag("globalTraceId",traceId);
            logger.error("RefreshAllDrvCoopModeSchedule_updateCoopmode_excep",e,logTags);
        }
    }

    /**
     * 司机合作模式更新
     * @param pageNo
     * @param pageSize
     */
    public void updateCoopmode(int pageNo,int pageSize,String traceId){
        try{
            //复用司机缓存查询方法如果该方法有变更需要新建一个查询方法
            List<Long> driverIds = drvierRepository.queryDrvId4Cache(pageNo,pageSize);
            if(CollectionUtils.isEmpty(driverIds)){
                String logStr = StringUtil.concat("pageNo:",String.valueOf(pageNo),",pageSize:",String.valueOf(pageSize));
                Map<String,String> logTags = LogTagUtils.getTag("globalTraceId",traceId);
                logger.info("RefreshAllDrvCoopModeSchedule_updateCoopmode_driverIdsEmpty",logStr,logTags);
                return;
            }
            //从数据库查询司机信息
            List<DrvDriverPO> drvDriverPOS = queryDriver(driverIds);
            if(CollectionUtils.isEmpty(drvDriverPOS)){
                String logStr = StringUtil.concat("pageNo:",String.valueOf(pageNo),",pageSize:",String.valueOf(pageSize));
                Map<String,String> logTags = LogTagUtils.getTag("globalTraceId",traceId);
                logger.info("RefreshAllDrvCoopModeSchedule_updateCoopmode_drvDriverPOSEmpty",logStr,logTags);
                return;
            }
            //更新司机合作模式
            refreshDrvCoopMode(drvDriverPOS,traceId);
        }catch (Exception e){
            String logStr = StringUtil.concat("pageNo:",String.valueOf(pageNo),",pageSize:",String.valueOf(pageSize));
            Map<String,String> logTags = LogTagUtils.getTag("globalTraceId",traceId);
            logger.info("RefreshAllDrvCoopModeSchedule_updateCoopmode_excep",logStr,logTags);
            logger.error("RefreshAllDrvCoopModeSchedule_updateCoopmode_excep",e,logTags);
        }
    }
    /**
     * 查询数据库司机总页数
     * @param pageSize
     * @return
     */
    private int getTotalPage(int pageSize){
        //查询司机总数
        int totalCount = drvierRepository.countDrvByMuSelConditions(new QueryDrvByMuSelDO());
        if(totalCount == 0){
            return 0 ;
        }
        int totalPage = totalCount/pageSize;
        if(totalCount%pageSize > 0){
            return totalPage + 1;
        }
        return totalPage;
    }

    /**
     * 根据参数中的司机id更新司机合作模式
     * @param driverIds
     */
    private void refreshDriverCoopmode(String driverIds,String traceId){
        try{
            //解析参数中的司机id
            List<Long> driverIdList = Arrays.stream(driverIds.split(",")).mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
            //从数据库查询司机信息
            List<DrvDriverPO> drvDriverPOS = queryDriver(driverIdList);
            if(CollectionUtils.isEmpty(drvDriverPOS)){
                Map<String,String> logTags = LogTagUtils.getTag("globalTraceId",traceId);
                logger.info("RefreshAllDrvCoopModeSchedule_refreshDriverCoopmode_drvDriverPOSEmpty", JsonUtil.toJson(driverIds),logTags);
                return;
            }
            //更新司机合作模式
            refreshDrvCoopMode(drvDriverPOS,traceId);
        }catch (Exception e){
            Map<String,String> logTags = LogTagUtils.getTag("globalTraceId",traceId);
            logger.info("RefreshAllDrvCoopModeSchedule_refreshDriverCoopmode_excep", JsonUtil.toJson(driverIds),logTags);
            logger.error("RefreshAllDrvCoopModeSchedule_refreshDriverCoopmode_excep",e,logTags);
        }
    }
    /**
     * 根据id查询司机信息
     * @param driverIds
     * @return
     */
    private List<DrvDriverPO> queryDriver(List<Long> driverIds){
        return drvierRepository.queryDrvList(driverIds);
    }
    /**
     * 更新司机合作模式
     * @param drvList
     */
    private void refreshDrvCoopMode(List<DrvDriverPO> drvList,String traceId){
        //查询司机id
        List<Long> drvIdList = drvList.stream().map(DrvDriverPO::getDrvId).collect(Collectors.toList());
        //查询司机关联运力组
        Map<Long,List<TransportGroupBasePO>> driverRelationMap = queryDriverRelationMap(drvIdList);
        Map<String,String> logTags = LogTagUtils.getTag("globalTraceId",traceId);
        logger.info("RefreshAllDrvCoopModeSchedule_refreshDrvCoopMode_queryDriverRelationMap", JsonUtil.toJson(driverRelationMap),logTags);
        //封装更新司机信息
        List<DrvDriverPO> batchUpdateDrv = Lists.newArrayList();
        for(DrvDriverPO scmDriver : drvList){
            DrvDriverPO updateDriver = getUpdateDriver(scmDriver.getDrvId(), driverRelationMap);
            if (Objects.equals(updateDriver.getCoopMode(), scmDriver.getCoopMode())) { //合作模式没有变化则不进行更新
                continue;
            }
            Cat.logEvent(Constant.EventType.DRIVER, Constant.EventName.REFRESH_DRV_COOP_MODE);
            batchUpdateDrv.add(updateDriver);
        }
        if(CollectionUtils.isEmpty(batchUpdateDrv)){
            logger.info("RefreshAllDrvCoopModeSchedule_refreshDrvCoopMode_batchUpdateDrvEmpty", JsonUtil.toJson(drvList),LogTagUtils.getTag("globalTraceId",traceId));
            return;
        }
        //更新司机信息
        drvierRepository.batchUpdateDrv(batchUpdateDrv);
    }

    /**
     * 封装更新司机信息
     * @param driverId
     * @param driverRelationMap
     * @return
     */
    private DrvDriverPO getUpdateDriver(Long driverId,Map<Long,List<TransportGroupBasePO>> driverRelationMap){
        List<TransportGroupBasePO> driverRelationV = driverRelationMap.get(driverId);
        DrvDriverPO updateDrvPO = new DrvDriverPO();
        updateDrvPO.setDrvId(driverId);
        updateDrvPO.setModifyUser(TmsTransportConstant.TMS_DEFAULT_USERNAME);
        //没有关联
        if(CollectionUtils.isEmpty(driverRelationV)){
            updateDrvPO.setCoopMode(TmsTransportConstant.DrvCoopModeEnum.NO.getCode());
            return updateDrvPO;
        }
        //计算新的合作模式
        DrvInfoCacheDto cacheDto =  driverQueryService.calculateDrvCoopMode(driverId,driverRelationV);
        updateDrvPO.setCoopMode(cacheDto.getCoopMode());
        return updateDrvPO;
    }
    /**
     * 查询运力组信息
     * @param drvIdList
     * @return
     */
    private Map<Long,List<TransportGroupBasePO>> queryDriverRelationMap(List<Long> drvIdList){
        //查询司机关联运力组
        List<TransportGroupBasePO> driverRelationPOList = transportGroupQueryService.queryDriverGroupRelationPO(drvIdList, Collections.emptyList());
        if(CollectionUtils.isEmpty(driverRelationPOList)){
            return new HashMap<>();
        }
        return driverRelationPOList.stream().collect(Collectors.groupingBy(TransportGroupBasePO::getDrvId));
    }
}
