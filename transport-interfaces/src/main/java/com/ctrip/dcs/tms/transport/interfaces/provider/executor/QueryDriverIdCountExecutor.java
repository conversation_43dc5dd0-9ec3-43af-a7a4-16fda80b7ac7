package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryDriverIdCountSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDriverIdCountSOAResponseType;
import com.ctrip.dcs.tms.transport.application.convert.DrvResourceConverter;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.igt.framework.infrastructure.exception.ServiceValidationException;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 查询司机id数量
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @create 2023/3/6 18:26
 */
@Component
public class QueryDriverIdCountExecutor extends AbstractRpcExecutor<QueryDriverIdCountSOARequestType, QueryDriverIdCountSOAResponseType> implements Validator<QueryDriverIdCountSOARequestType> {

    @Autowired
    private DriverQueryService driverQueryService;

    @Autowired
    private DrvResourceConverter drvResourceConverter;

    @Override
    public QueryDriverIdCountSOAResponseType execute(QueryDriverIdCountSOARequestType req) {
        QueryDriverIdCountSOAResponseType responseType = new QueryDriverIdCountSOAResponseType();
        responseType.setTotalCount(driverQueryService.queryDrvBaseResourceCount(drvResourceConverter.convertCondition(req)));
        return ServiceResponseUtils.success(responseType);
    }

    @Override
    public void onExecuting(QueryDriverIdCountSOARequestType req) {
        if (CollectionUtils.isEmpty(req.getCountryIdList())) {
            throw new ServiceValidationException("Missing required parameters");
        }
    }

}