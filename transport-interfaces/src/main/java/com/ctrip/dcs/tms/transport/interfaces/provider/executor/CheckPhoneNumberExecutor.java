package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.CheckPhoneNumberRequestType;
import com.ctrip.dcs.tms.transport.api.model.CheckPhoneNumberResponseType;
import com.ctrip.dcs.tms.transport.application.dto.PreCheckResultDTO;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.dianping.cat.Cat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 添加证件
 * <AUTHOR>
 */
@Component
public class CheckPhoneNumberExecutor extends AbstractRpcExecutor<CheckPhoneNumberRequestType, CheckPhoneNumberResponseType>
  implements
  Validator<CheckPhoneNumberRequestType> {

    @Autowired
    DriverQueryService driverQueryService;

    @Override
    public CheckPhoneNumberResponseType execute(CheckPhoneNumberRequestType request) {
        CheckPhoneNumberResponseType responseType = new CheckPhoneNumberResponseType();
        Result<PreCheckResultDTO> result = driverQueryService.checkPhoneNumber(request);
        responseType.setDrvId(result.getData().getDriverId());
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(responseType);
        }else {
            return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
        }
    }

}
