package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * 查询司机车辆审批列表
 */
@Component
public class QueryDrvVehRecruitingModRrdExecutor extends AbstractRpcExecutor<QueryDrvVehRecruitingModRrdSOARequestType, QueryDrvVehRecruitingModRrdSOAResponseType> implements Validator<QueryDrvVehRecruitingModRrdSOARequestType> {

    private static final Logger logger = LoggerFactory.getLogger(QueryDrvVehRecruitingModRrdExecutor.class);

    @Autowired
    DrvVehRecruitingQueryService recruitingQueryService;

    @Override
    public QueryDrvVehRecruitingModRrdSOAResponseType execute(QueryDrvVehRecruitingModRrdSOARequestType requestType) {
        QueryDrvVehRecruitingModRrdSOAResponseType responseType = new QueryDrvVehRecruitingModRrdSOAResponseType();
        Result<List<QueryDrvVehRecruitingModRrdSOADTO>> result =  recruitingQueryService.queryDrvVehRecruitingModRrd(requestType.getDrvRecruitingId(),requestType.getRecruitingType());
        if(result.isSuccess()){
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType);
    }
}
