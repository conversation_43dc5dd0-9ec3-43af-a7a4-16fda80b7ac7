package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.api.resource.driver.*;
import com.ctrip.dcs.tms.transport.application.convert.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.infrastructure.exception.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.apache.commons.collections.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * <AUTHOR>
 * 司机资源 轻量级接口
 */
@Component
public class QueryDriverBaseExecutor extends AbstractRpcExecutor<QueryDriver4BaseSOARequestType, QueryDriver4BaseSOAResponseType> implements Validator<QueryDriver4BaseSOARequestType> {

    private static final Logger logger = LoggerFactory.getLogger(QueryDriverBaseExecutor.class);

    @Autowired
    private ProductionCategoryLineConfig productionCategoryLineConfig;

    @Autowired
    private DriverQueryService driverQueryService;

    @Autowired
    private DrvResourceConverter drvResourceConverter;

    private final int DEFAULT_PAGE_NO = 1;
    private final int MAX_PAGE_SIZE = 1000;
    private final int DEFAULT_PAGE_SIZE = 10;

    @Override
    public void onExecuting(QueryDriver4BaseSOARequestType req) {
        // 会有多组资源接口，对请参的校验大同小异，如何抽离共用呢？
        boolean reqKeyMiss = CollectionUtils.isEmpty(req.getDrvIdList()) && CollectionUtils.isEmpty(req.getCityIdList())
                && CollectionUtils.isEmpty(req.getDriverPhoneList()) && CollectionUtils.isEmpty(req.getDrvNameList())
                && CollectionUtils.isEmpty(req.getSupplierIdList());
        // 必要参数检查
        if (reqKeyMiss) {
            logger.error("QueryDriverBaseMissReqParameters", "Params:{}", JsonUtil.toJson(req));
            throw new ServiceValidationException("Missing required parameters");
        }
        // 枚举值检查
        if (CollectionUtils.isNotEmpty(req.getProLineList())) {
            for (Integer proLineId : req.getProLineList()) {
                if (productionCategoryLineConfig.getShowToUseLineCodeMap().get(proLineId) == null) {
                    logger.error("QueryDriverBaseParameterError", "Params:{}", JsonUtil.toJson(req));
                    throw new ServiceValidationException("parameter error proLineId:" + JsonUtil.toJson(req.getProLineList()));
                }
            }
        }
        // 最大页数检查
        if (req.getPaginator() != null) {
            if (req.getPaginator().getPageNo() == null) {
                req.getPaginator().setPageNo(DEFAULT_PAGE_NO);
            }
            if (req.getPaginator().getPageNo() <= 0) {
                req.getPaginator().setPageNo(DEFAULT_PAGE_NO);
            }
            if (req.getPaginator().getPageSize() == null) {
                req.getPaginator().setPageSize(DEFAULT_PAGE_SIZE);
            }
            if (req.getPaginator().getPageSize() <= 0) {
                req.getPaginator().setPageSize(DEFAULT_PAGE_SIZE);
            }
            if (req.getPaginator().getPageSize() > MAX_PAGE_SIZE) {
                req.getPaginator().setPageSize(DEFAULT_PAGE_SIZE);
            }
        }
        // 手机号预置处理
        if (CollectionUtils.isEmpty(req.getDriverPhoneList())) {
            return;
        }
        for (int i = req.getDriverPhoneList().size() - 1; i >= 0; i--) {
            req.getDriverPhoneList().set(i, TmsTransUtil.encrypt(req.getDriverPhoneList().get(i), KeyType.Phone));
        }
    }

    @Override
    public QueryDriver4BaseSOAResponseType execute(QueryDriver4BaseSOARequestType req) {
        QueryDriver4BaseSOAResponseType responseType = new QueryDriver4BaseSOAResponseType();
        List<DrvBase> res = driverQueryService.queryDrvBaseResource(drvResourceConverter.convertCondition(req));
        responseType.setData(res);
        return ServiceResponseUtils.success(responseType);
    }

    @Override
    public void validate(AbstractValidator<QueryDriver4BaseSOARequestType> validator) {

        // to do 枚举参数校验 中断流程

    }

}