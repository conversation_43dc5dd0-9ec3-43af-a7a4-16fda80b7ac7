package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryRecruitingDrvForPenaltyRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryRecruitingDrvForPenaltyResponseType;
import com.ctrip.dcs.tms.transport.application.query.RecruitingQueryService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class QueryRecruitingDrvForPenaltyExecutor extends AbstractRpcExecutor<QueryRecruitingDrvForPenaltyRequestType, QueryRecruitingDrvForPenaltyResponseType> implements Validator<QueryRecruitingDrvForPenaltyRequestType> {

    @Autowired
    RecruitingQueryService recruitingQueryService;

    @Override
    public QueryRecruitingDrvForPenaltyResponseType execute(QueryRecruitingDrvForPenaltyRequestType requestType) {
        QueryRecruitingDrvForPenaltyResponseType responseType = new QueryRecruitingDrvForPenaltyResponseType();
        Result<QueryRecruitingDrvForPenaltyResponseType> result = recruitingQueryService.queryRecruitingDrvForPenalty(requestType);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(result.getData());
        }
        return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<QueryRecruitingDrvForPenaltyRequestType> validator) {
//        validator.ruleFor("drvPhone").notNull();
    }
}
