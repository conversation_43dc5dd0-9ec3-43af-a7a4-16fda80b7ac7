package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

@Component
public class QueryRecruitingApproveStatusExecutor extends AbstractRpcExecutor<QueryRecruitingApproveStatusSOARequestType, QueryRecruitingApproveStatusSOAResponseType> implements Validator<QueryRecruitingApproveStatusSOARequestType> {

    @Autowired
    RecruitingQueryService recruitingQueryService;

    @Override
    public QueryRecruitingApproveStatusSOAResponseType execute(QueryRecruitingApproveStatusSOARequestType requestType) {
        QueryRecruitingApproveStatusSOAResponseType responseType = new QueryRecruitingApproveStatusSOAResponseType();
        Result<Integer> result = recruitingQueryService.queryRecruitingApproveStatus(requestType);
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<QueryRecruitingApproveStatusSOARequestType> validator) {
        validator.ruleFor("drvPhone").notNull();
    }
}
