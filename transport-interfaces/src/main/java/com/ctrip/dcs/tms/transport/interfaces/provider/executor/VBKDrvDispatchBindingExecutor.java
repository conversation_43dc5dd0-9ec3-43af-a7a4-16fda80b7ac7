package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.VBKDrvDispatchBindingSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.VBKDrvDispatchBindingSOAResponseType;
import com.ctrip.dcs.tms.transport.application.command.DriverCommandService;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class VBKDrvDispatchBindingExecutor extends AbstractRpcExecutor<VBKDrvDispatchBindingSOARequestType, VBKDrvDispatchBindingSOAResponseType> implements Validator<VBKDrvDispatchBindingSOARequestType> {

    @Autowired
    DriverCommandService driverCommandService;

    @Override
    public VBKDrvDispatchBindingSOAResponseType execute(VBKDrvDispatchBindingSOARequestType requestType) {
        VBKDrvDispatchBindingSOAResponseType responseType = new VBKDrvDispatchBindingSOAResponseType();
        Result<Boolean> result = driverCommandService.vbkDrvDispatchBinding(requestType.getDrvId(),requestType.getSupplierId());
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<VBKDrvDispatchBindingSOARequestType> validator) {
        validator.ruleFor("drvId").notNull();
        validator.ruleFor("supplierId").notNull();
    }
}
