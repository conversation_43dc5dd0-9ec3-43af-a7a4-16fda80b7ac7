package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.interfaces.provider.validator.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import com.google.common.collect.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2020/3/12 16:15
 */
@Component
public class CheckSkuBindInfoExecutor extends AbstractRpcExecutor<CheckSkuBindInfoSOARequestType, CheckSkuBindInfoSOAResponseType> implements Validator<CheckSkuBindInfoSOARequestType> {

    @Autowired
    private TspTransportGroupSkuArearRelationRepository tspTransportGroupSkuArearRelationRepository;

    @Override
    public CheckSkuBindInfoSOAResponseType execute(CheckSkuBindInfoSOARequestType checkSkuBindInfoSOARequestType) {
        CheckSkuBindInfoSOAResponseType soaResponseType = new CheckSkuBindInfoSOAResponseType();
        List<Long> list = tspTransportGroupSkuArearRelationRepository.querySkuBindInfo(checkSkuBindInfoSOARequestType.getSkuIds(), checkSkuBindInfoSOARequestType.getGroupStatus());
        Map<String,Boolean> checkResult = Maps.newHashMap();
        for (Long skuId : checkSkuBindInfoSOARequestType.getSkuIds()) {
            checkResult.put(String.valueOf(skuId),list.contains(skuId));
        }
        soaResponseType.setData(checkResult);
        return  ServiceResponseUtils.success(soaResponseType);
    }

    @Override
    public void validate(AbstractValidator<CheckSkuBindInfoSOARequestType> validator, CheckSkuBindInfoSOARequestType req) {
        validator.ruleFor("skuIds").setValidator(new NotEmptyCollectionValidator("skuIds must br not null", ValidationErrors.NotNull));
    }
}
