package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctriposs.baiji.exception.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import qunar.tc.qschedule.config.*;
import qunar.tc.schedule.*;

import java.util.*;
import java.util.stream.*;

/**
 * <AUTHOR>
 * @Description 证件核验
 * @Date 14:54 2020/11/11
 * @Param
 * @return
 **/
@Component
public class CertificateCheckSchedule {

    private static final Logger logger = LoggerFactory.getLogger(CertificateCheckSchedule.class);

    @Autowired
    private TmsCertificateCheckRepository checkRepository;
    @Autowired
    private TmsBackgroundChecksRepository backgroundChecksRepository;
    @Autowired
    TmsTransportQconfig qconfig;
    @Autowired
    DrvDrvierRepository repository;
    @Autowired
    CertificateCheckQueryService checkQueryService;
    @Autowired
    DrvRecruitingRepository recruitingRepository;
    @Autowired
    VehicleRecruitingRepository vehicleRecruitingRepository;
    @Autowired
    VehicleRepository vehicleRepository;

    private static final String drvName_Key = "drvName";
    private static final String drvidcard_Key = "drvIdcard";
    private static final String supplierId_Key = "supplierId";
    private static final String versionFlag_Key = "versionFlag";

    @QSchedule("refresh.certificate.check.job")
    public void idCardBackGroundSchedule(Parameter parameter) throws Exception {
        logger.info("start idcard back ground ..");
        String drvIds = parameter.getString("drvIds") == null ? "" : parameter.getString("drvIds");
        Integer checkType = parameter.getProperty("checkType", Integer.class);
        this.idCardBackGroundMethodSchedule(drvIds, checkType);
    }

    public void idCardBackGroundMethodSchedule(String drvIds, Integer checkType) {
        try {
            List<Long> checkIds = Lists.newArrayList();
            if (StringUtils.isNotEmpty(drvIds)) {
                checkIds = Arrays.stream(drvIds.split(",")).mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
            }
            List<Integer> certificateTypeList = Lists.newArrayList();
            Integer checkStatus = TmsTransportConstant.CheckStatusEnum.CHECKING.getCode();
            //查询审核中的身份证核验总数
            int count = checkRepository.countIdCardCheckByCheckIng(checkIds, checkType, certificateTypeList, checkStatus);
            if (count == 0) {
                return;
            }
            int pageSize = 50;
            if((count % pageSize) == 0){
                count = (count / pageSize);
            }else{
                count = (count / pageSize) + 1;
            }

            for (int i = 1; i <= count; i ++) {
                int beginNo = i;
                List<TmsCertificateCheckPO> checkPOList = checkRepository.queryIdCardCheckByCheckIng(checkIds, checkType, certificateTypeList, checkStatus, beginNo, pageSize);
                if (CollectionUtils.isEmpty(checkPOList)) {
                    return;
                }
                //按checkId分组
                Map<Long,List<TmsCertificateCheckPO>> checkMap = checkPOList.stream().collect(Collectors.groupingBy(TmsCertificateCheckPO::getCheckId));
                for(Map.Entry<Long,List<TmsCertificateCheckPO>> entry : checkMap.entrySet()){
                    //遍历核验表中，身份证对应的背调表信息
                    Map<String, TmsBackgroundChecksPO> backgroundChecksPOMap = idcardCheck(entry.getValue());
                    int checkCount = 0;
                    int listSize = entry.getValue().size();
                    int checkSourceType = TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV.getCode();
                    for (TmsCertificateCheckPO checkPO : entry.getValue()) {
                         checkSourceType = checkPO.getCheckType();
                        //身份证
                        if (Objects.equals(checkPO.getCertificateType(), TmsTransportConstant.CertificateTypeEnum.IDCARD.getCode())) {
                            Boolean flag = checkQueryService.refreshIdCardCheckIng(checkPO, backgroundChecksPOMap);
                            if(flag){
                                ++checkCount;
                            }
                        }
                        //驾驶证
                        if (Objects.equals(checkPO.getCertificateType(), TmsTransportConstant.CertificateTypeEnum.DRIVERLICENSE.getCode())) {
                            Boolean flag = drvLicenseDataCheck(checkPO.getId(), checkPO.getCheckId(), checkPO.getCheckType(),checkPO.getCheckStatus());
                            if(flag){
                                ++checkCount;
                            }

                        }
                        //行驶证
                        if (Objects.equals(checkPO.getCertificateType(), TmsTransportConstant.CertificateTypeEnum.CARCERTILICENSE.getCode())) {
                            Boolean flag = vehCertificateCheck(checkPO.getId(), checkPO.getCheckId(), checkPO.getCheckType(),checkPO.getCheckStatus());
                            if(flag){
                                ++checkCount;
                            }
                        }
                        //网约车驾驶证
                        if (Objects.equals(checkPO.getCertificateType(), TmsTransportConstant.CertificateTypeEnum.NETDRVCTFCT.getCode())) {
                            Boolean flag = netDrvLicenseDataCheck(checkPO.getId(), checkPO.getCheckId(), checkPO.getCheckType(),checkPO.getCheckStatus());
                            if(flag){
                                ++checkCount;
                            }
                        }
                        //网约车行驶证
                        if (Objects.equals(checkPO.getCertificateType(), TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT.getCode())) {
                            Boolean flag  = netVehLicenseDataCheck(checkPO.getId(), checkPO.getCheckId(), checkPO.getCheckType(),checkPO.getCheckStatus());
                            if(flag){
                                ++checkCount;
                            }
                        }
                    }
                    //TODO 核验都通过，将招募司机表/招募车辆表 核验状态制为已完成
                    if( listSize > 0 && checkCount == listSize){
                        //按核验类型更新对应的表信息-更新招募司机核验状态
                        if(Objects.equals(checkSourceType,TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV.getCode())){
                            recruitingRepository.updateCheckStatus(Arrays.asList(entry.getKey()),1);
                        }
                        //更新招募车辆核验状态
                        if(Objects.equals(checkSourceType,TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode())){
                            vehicleRecruitingRepository.updateCheckStatus(Arrays.asList(entry.getKey()),1);
                        }
                    }
                }
            }
            logger.info("job end");
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    private Map<String, TmsBackgroundChecksPO> idcardCheck(List<TmsCertificateCheckPO> checkPOList){
        List<String> idcard = Lists.newArrayList();
        for (TmsCertificateCheckPO checkPO : checkPOList) {
            if (!StringUtils.isEmpty(checkPO.getCheckKeyword())) {
                idcard.add(checkPO.getCheckKeyword());
            }
        }
        Map<String, TmsBackgroundChecksPO> checksPOMap = Maps.newConcurrentMap();
        if (CollectionUtils.isEmpty(idcard)) {
            return checksPOMap;
        }
        List<TmsBackgroundChecksPO> backgroundChecksPOS = backgroundChecksRepository.queryBackgroundByPersons(idcard);
        if (CollectionUtils.isNotEmpty(backgroundChecksPOS)) {
            checksPOMap = backgroundChecksPOS.stream().collect(Collectors.toMap(TmsBackgroundChecksPO::getPersonId, a -> a, (k1, k2) -> k1));
        }
        return checksPOMap;
    }

    //驾驶证核验
    public Boolean drvLicenseDataCheck(Long id, Long checkId, Integer checkType,Integer checkStatus) {
        Map<String,Object> resultMap = getDrvNameAndIdCard(checkType,checkId);
        if(!(Boolean) resultMap.get(TmsTransportConstant.RESULTFLAG_KEY)){
            return  Boolean.FALSE;
        }
        String drvName = String.valueOf(resultMap.get(drvName_Key));
        String drvIdcard = String.valueOf(resultMap.get(drvidcard_Key));
        Long supplierId = Long.valueOf(String.valueOf(resultMap.get(supplierId_Key)));
        Integer versionFlag = Integer.valueOf(String.valueOf(resultMap.get(versionFlag_Key)));
        if(StringUtils.isNotEmpty(drvName) && StringUtils.isNotEmpty(drvIdcard)){
            return checkQueryService.refreshDrvLicenseCheckIng(DrvAuditDTO.refreshbuildDrvDTO(id, drvName, drvIdcard,checkType,supplierId,versionFlag,checkStatus,checkId));
        }
        return Boolean.FALSE;
    }

    //行驶证核验
    private Boolean vehCertificateCheck(Long id, Long checkId, Integer checkType,Integer checkStatus) {
        String vehicleLicense = "";
        String vinCode = "";
        Integer  vehicleEnergyType = null;
        Long supplierId = 0L;
        Integer versionFlag = 0;
        if (Objects.equals(checkType, TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode())) {
            VehicleRecruitingPO vehicleRecruitingPO = vehicleRecruitingRepository.queryByPK(checkId);
            if (Objects.isNull(vehicleRecruitingPO)) {
                return Boolean.FALSE;
            }
            vehicleLicense = vehicleRecruitingPO.getVehicleLicense();
            vinCode = vehicleRecruitingPO.getVin();
            vehicleEnergyType = vehicleRecruitingPO.getVehicleEnergyType();
            supplierId = vehicleRecruitingPO.getSupplierId();
            versionFlag = vehicleRecruitingPO.getVersionFlag();
        }else if(Objects.equals(checkType, TmsTransportConstant.CertificateCheckTypeEnum.VEHICLE.getCode())){
            VehVehiclePO vehiclePO = vehicleRepository.queryByPk(checkId);
            if (Objects.isNull(vehiclePO)) {
                return Boolean.FALSE;
            }
            vehicleLicense = vehiclePO.getVehicleLicense();
            vinCode = vehiclePO.getVin();
            vehicleEnergyType = vehiclePO.getVehicleEnergyType();
        }
        String vehicleLicenseType = TmsTransportConstant.VEHICLELICENSETYPE_FUEL;
        if(Objects.equals(vehicleEnergyType,2)){
            vehicleLicenseType = TmsTransportConstant.VEHICLELICENSETYPE_ENERGY;
        }
        if(StringUtils.isNotEmpty(vehicleLicense) && StringUtils.isNotEmpty(vehicleLicenseType)){
            VehicleAuditDTO vehicleAuditDTO = VehicleAuditDTO.rehBuildVehicleDTO(vehicleLicense, vehicleLicenseType, vinCode, id, "system",checkType,supplierId,versionFlag,checkStatus,null,null);
            return checkQueryService.refreshVehLicenseCheckIng(vehicleAuditDTO);
        }
        return Boolean.FALSE;
    }

    //网约车驾驶证核验
    private Boolean netDrvLicenseDataCheck(Long id, Long checkId, Integer checkType,Integer checkStatus) {
        Map<String,Object> resultMap = getDrvNameAndIdCard(checkType,checkId);
        if(!(Boolean) resultMap.get(TmsTransportConstant.RESULTFLAG_KEY)){
            return  Boolean.FALSE;
        }
        String drvIdcard = String.valueOf(resultMap.get(drvidcard_Key));
        Long supplierId = Long.valueOf(String.valueOf(resultMap.get(supplierId_Key)));
        Integer versionFlag = Integer.valueOf(String.valueOf(resultMap.get(versionFlag_Key)));
        if(StringUtils.isNotEmpty(drvIdcard)){
            return checkQueryService.refreshNetDrvLicenseCheckIng(id, drvIdcard,checkType,supplierId,versionFlag,checkStatus,null);
        }
        return Boolean.FALSE;
    }

    //网约车行驶证核验
    private Boolean netVehLicenseDataCheck(Long id, Long checkId, Integer checkType,Integer checkStatus) {
        String vehicleLicense = "";
        Long supplierId = 0L;
        Integer versionFlag = 0;
        if (Objects.equals(checkType, TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode())) {
            VehicleRecruitingPO vehicleRecruitingPO = vehicleRecruitingRepository.queryByPK(checkId);
            if (Objects.isNull(vehicleRecruitingPO)) {
                return Boolean.FALSE;
            }
            vehicleLicense = vehicleRecruitingPO.getVehicleLicense();
            supplierId = vehicleRecruitingPO.getSupplierId();
            versionFlag = vehicleRecruitingPO.getVersionFlag();
        }else if(Objects.equals(checkType, TmsTransportConstant.CertificateCheckTypeEnum.VEHICLE.getCode())){
            VehVehiclePO vehiclePO = vehicleRepository.queryByPk(checkId);
            if (Objects.isNull(vehiclePO)) {
                return Boolean.FALSE;
            }
            vehicleLicense = vehiclePO.getVehicleLicense();
        }
        if(StringUtils.isNotEmpty(vehicleLicense)){
            return checkQueryService.refreshNetVehLicenseCheckIng(id, vehicleLicense,checkType,supplierId,versionFlag,checkStatus,null,null,null);
        }
        return Boolean.FALSE;
    }

    private Map<String,Object> getDrvNameAndIdCard(Integer checkType,Long checkId){
        Map<String,Object> resultMap = Maps.newHashMap();
        resultMap.put(TmsTransportConstant.RESULTFLAG_KEY,Boolean.FALSE);
        String drvName = "";
        String drvIdcard = "";
        Long supplierId = 0L;
        Integer versionFlag = 0;
        if (Objects.equals(checkType, TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV.getCode())) {
            DrvRecruitingPO recruitingPO = recruitingRepository.queryByPK(checkId);
            if (Objects.isNull(recruitingPO)) {
                return resultMap;
            }
            drvName = recruitingPO.getDrvName();
            drvIdcard = recruitingPO.getDrvIdcard();
            supplierId = recruitingPO.getSupplierId();
            versionFlag = recruitingPO.getVersionFlag();
        }else if(Objects.equals(checkType, TmsTransportConstant.CertificateCheckTypeEnum.DRV.getCode())){
            DrvDriverPO drvDriverPO = repository.queryByPk(checkId);
            if (Objects.isNull(drvDriverPO)) {
                return resultMap;
            }
            drvName = drvDriverPO.getDrvName();
            drvIdcard = drvDriverPO.getDrvIdcard();
        }
        resultMap.put(TmsTransportConstant.RESULTFLAG_KEY,Boolean.TRUE);
        resultMap.put(drvName_Key,drvName);
        resultMap.put(drvidcard_Key,TmsTransUtil.decrypt(drvIdcard,KeyType.Identity_Card));
        resultMap.put(supplierId_Key,supplierId);
        resultMap.put(versionFlag_Key,versionFlag);
        return resultMap;
    }
}