package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 司机冻结
 * <AUTHOR>
 * @Date 2020/2/26 18:48
 */
@Component
public class FreezeDrvExecutor extends AbstractRpcExecutor<DrvFreezeSOARequestType, DrvFreezeSOAResponseType> implements Validator<DrvFreezeSOARequestType> {

    @Autowired
    private DriverCommandService driverCommandService;

    @Override
    public DrvFreezeSOAResponseType execute(DrvFreezeSOARequestType drvFreezeSOARequestType) {
        DrvFreezeSOAResponseType soaResponseType = new DrvFreezeSOAResponseType();
        Result<Boolean> result = driverCommandService.freezeDrv(drvFreezeSOARequestType.getDrvIds(),
                drvFreezeSOARequestType.getFreezeHour(),
                drvFreezeSOARequestType.getFreezeReason(),
                drvFreezeSOARequestType.getModifyUser());
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(soaResponseType);
        }else {
            return ServiceResponseUtils.fail(soaResponseType, result.getCode(),result.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<DrvFreezeSOARequestType> validator) {
        validator.ruleFor("drvIds").notNull();
        validator.ruleFor("modifyUser").notNull().notEmpty();
    }
}
