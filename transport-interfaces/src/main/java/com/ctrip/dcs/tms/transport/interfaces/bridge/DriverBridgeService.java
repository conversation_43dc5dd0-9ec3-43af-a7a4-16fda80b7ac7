package com.ctrip.dcs.tms.transport.interfaces.bridge;

import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvIdByTransportGroupsRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvIdByTransportGroupsResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryVehicleBaseSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryVehicleBaseSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.VehicleDetailSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.VehicleDetailSOAResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverForSaasResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverIdForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverIdForSaasResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleForSaasResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleIdForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleIdForSaasResponseType;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.dcs.tms.transport.interfaces.bridge.executorservice.DriverCacheExecutorService;
import com.ctrip.dcs.tms.transport.interfaces.bridge.executorservice.QueryDriverForSaasExecutorService;
import com.ctrip.dcs.tms.transport.interfaces.bridge.executorservice.QueryDriverIdForSaasExecutorService;
import com.ctrip.dcs.tms.transport.interfaces.bridge.executorservice.QueryDrvDetailExecutorService;
import com.ctrip.dcs.tms.transport.interfaces.bridge.executorservice.QueryDrvIdByTransportGroupsExecutorService;
import com.ctrip.dcs.tms.transport.interfaces.bridge.executorservice.QueryVehicleForSaasExecutorService;
import com.ctrip.dcs.tms.transport.interfaces.bridge.executorservice.QueryVehicleIdForSaasExecutorService;
import com.ctrip.dcs.tms.transport.interfaces.bridge.executorservice.VehicleBaseListExecutorService;
import com.ctrip.dcs.tms.transport.interfaces.bridge.executorservice.VehicleDetailExecutorService;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.soa.server.executor.ServiceExecutors;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 供应链域接口查询
 * <AUTHOR>
 */
@Component
public class DriverBridgeService implements ProductLineBridge {

  @Autowired
  DriverQueryService driverQueryService;

  @Autowired
  QueryDriverForSaasExecutorService driverForSaasExecutorService;

  @Autowired
  QueryDriverIdForSaasExecutorService queryDriverIdForSaasExecutorService;

  @Autowired
  QueryVehicleForSaasExecutorService queryVehicleForSaasExecutorService;

  @Autowired
  QueryVehicleIdForSaasExecutorService queryVehicleIdForSaasExecutorService;

  @Autowired
  QueryDrvDetailExecutorService queryDrvDetailExecutorService;

  @Autowired
  QueryDrvIdByTransportGroupsExecutorService queryDrvIdByTransportGroupsExecutorService;

  @Autowired
  DriverCacheExecutorService driverCacheExecutorService;

  @Autowired
  VehicleBaseListExecutorService vehicleBaseListExecutorService;

  @Autowired
  VehicleDetailExecutorService vehicleDetailExecutorService;


  public DriverInfoSOAResponseType queryDriver(DriverInfoSOARequestType request) {
    return driverCacheExecutorService.execute(request);
  }

  @Override
  public QueryDrvDetailSOAResponseType queryDrvDetail(QueryDrvDetailSOARequestType request) {
    return queryDrvDetailExecutorService.execute(request);
  }

  @Override
  public QueryVehicleBaseSOAResponseType queryVehicleListBySupplierId(QueryVehicleBaseSOARequestType request) {
    return vehicleBaseListExecutorService.execute(request);
  }

  @Override
  public QueryDrvIdByTransportGroupsResponseType queryDrvIdByTransportGroups(
    QueryDrvIdByTransportGroupsRequestType request) {
    return queryDrvIdByTransportGroupsExecutorService.execute(request);
  }

  @Override
  public VehicleDetailSOAResponseType queryVehicleDetail(VehicleDetailSOARequestType request) {
    return vehicleDetailExecutorService.execute(request);
  }

  @Override
  public QueryDriverIdForSaasResponseType queryDriverIdForSaas(
    QueryDriverIdForSaasRequestType request) {
    return queryDriverIdForSaasExecutorService.execute(request);
  }

  @Override
  public QueryVehicleIdForSaasResponseType queryVehicleIdForSaas(
    QueryVehicleIdForSaasRequestType request) {
    return queryVehicleIdForSaasExecutorService.execute(request);
  }

  @Override
  public QueryDriverForSaasResponseType queryDriverForSaas(
    QueryDriverForSaasRequestType request) {
    return driverForSaasExecutorService.execute(request);
  }

  @Override
  public QueryVehicleForSaasResponseType queryVehicleForSaas(
    QueryVehicleForSaasRequestType request) {
    return queryVehicleForSaasExecutorService.execute(request);
  }

}
