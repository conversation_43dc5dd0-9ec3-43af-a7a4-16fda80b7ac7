package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

@Component
public class RecruitingApproveActionExecutor extends AbstractRpcExecutor<RecruitingApproveSOARequestType, RecruitingApproveSOAResponseType> implements Validator<RecruitingApproveSOARequestType> {

    @Autowired
    private RecruitingCommandService recruitingCommandService;

    @Override
    public RecruitingApproveSOAResponseType execute(RecruitingApproveSOARequestType requestType) {
        RecruitingApproveSOAResponseType soaResponseType = new RecruitingApproveSOAResponseType();
        Integer recruitingType = requestType.getData().getRecruitingType();
        //招募类型必传,并且只能为1or2
        if(recruitingType == null){
            recruitingType = TmsTransportConstant.RecruitingTypeEnum.drv.getCode();
        }
        Result<Boolean> result = recruitingCommandService.approveRoute(requestType, recruitingType);
        if (result != null && result.isSuccess() && result.getData()) {
            return ServiceResponseUtils.success(soaResponseType);
        } else {
            if(result != null){
                return ServiceResponseUtils.fail(soaResponseType,result.getCode(),result.getMsg());
            }
            return ServiceResponseUtils.fail(soaResponseType);
        }
    }

    @Override
    public void validate(AbstractValidator<RecruitingApproveSOARequestType> validator) {
        validator.ruleFor("data").notNull();
    }

}