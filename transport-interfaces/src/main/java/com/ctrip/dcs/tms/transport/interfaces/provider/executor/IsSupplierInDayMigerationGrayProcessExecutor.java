package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.IsSupplierInDayMigerationGrayProcessRequestType;
import com.ctrip.dcs.tms.transport.api.model.IsSupplierInDayMigerationGrayProcessResponseType;
import com.ctrip.dcs.tms.transport.application.query.driverguide.DriverGuidQueryProcessService;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DriverGuidDriverRequestDTO;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class IsSupplierInDayMigerationGrayProcessExecutor  extends AbstractRpcExecutor<IsSupplierInDayMigerationGrayProcessRequestType, IsSupplierInDayMigerationGrayProcessResponseType>
  implements Validator<IsSupplierInDayMigerationGrayProcessRequestType> {

  @Autowired
  DriverGuidQueryProcessService driverGuidQueryProcessService;

  @Override
  public IsSupplierInDayMigerationGrayProcessResponseType execute(
    IsSupplierInDayMigerationGrayProcessRequestType requestType) {
    DriverGuidDriverRequestDTO request = new DriverGuidDriverRequestDTO();
    request.setSupplierId(requestType.getSupplierId());
    IsSupplierInDayMigerationGrayProcessResponseType responseType = new IsSupplierInDayMigerationGrayProcessResponseType();
    Boolean data = driverGuidQueryProcessService.getGrayControl(request);
    responseType.setData(data);
    return ServiceResponseUtils.success(responseType);
  }
}
