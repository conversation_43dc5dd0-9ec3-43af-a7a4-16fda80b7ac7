package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.constant.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

@Component
public class QueryTransportApproveDetailExecutor extends AbstractRpcExecutor<QueryTransportApproveDetailSOARequestType, QueryTransportApproveDetailSOAResponseType> implements Validator<QueryTransportApproveDetailSOARequestType> {

    @Autowired
    private TmsTransportApproveQueryService queryService;

    @Override
    public QueryTransportApproveDetailSOAResponseType execute(QueryTransportApproveDetailSOARequestType requestType) {
        QueryTransportApproveDetailSOAResponseType soaResponseType = new QueryTransportApproveDetailSOAResponseType();
        Result<QueryTransportApproveDetailSOADTO> result = queryService.queryDrvDetail(requestType);
        if (result.isSuccess()) {
            soaResponseType.setData(result.getData());
            return ServiceResponseUtils.success(soaResponseType);
        }
        return ServiceResponseUtils.fail(soaResponseType, ServiceResponseConstants.ResStatus.VALIDATION_ERROR_CODE,result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<QueryTransportApproveDetailSOARequestType> validator) {
        validator.ruleFor("id").notNull();
    }

}