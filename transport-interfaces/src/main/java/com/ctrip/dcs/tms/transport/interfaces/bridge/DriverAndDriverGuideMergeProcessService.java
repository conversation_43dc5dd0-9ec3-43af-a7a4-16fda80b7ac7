package com.ctrip.dcs.tms.transport.interfaces.bridge;

import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvIdByTransportGroupsRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvIdByTransportGroupsResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryVehicleBaseSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryVehicleBaseSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.VehicleDetailSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.VehicleDetailSOAResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverForSaasResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverIdForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverIdForSaasResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleForSaasResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleIdForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleIdForSaasResponseType;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverGuideProxy;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.EVENT_SPLIT;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.QUERY_DRIVER;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.QUERY_DRIVER_FOR_SAAS;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.QUERY_DRIVER_ID_FOR_SAAS;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.QUERY_DRV_DETAIL;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.QUERY_DRV_ID_BY_TRANSPORT_GROUPS;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.QUERY_VEHICLE_DETAIL;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.QUERY_VEHICLE_FOR_SAAS;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.QUERY_VEHICLE_ID_FOR_SAAS;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.QUERY_VEHICLE_LIST_BY_SUPPLIER_ID;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.RETURN_DRIVER_GUIDE_RESULT;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.RETURN_DRIVER_RESULT;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.RETURN_EMPTY_RESULT;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventType.BRIDGE;

/**
 * 并行查询供应链，司导类，后续全量了，则直接可以去掉这个调用类
 */
@Service
public class DriverAndDriverGuideMergeProcessService implements ProductLineBridge{

  private static final Logger logger = LoggerFactory.getLogger(DriverAndDriverGuideMergeProcessService.class);

  @Autowired
  @Qualifier("driverGuideQueryPool")
  ExecutorService driverGuideQueryPool;


  @Autowired
  @Qualifier("driverGuideSearchPool")
  ExecutorService driverGuideSearchPool;

  @Autowired
  DriverBridgeService driverDelegate;

  @Autowired
  DriverGuideBridgeService driverGuidDelegate;

  @Autowired
  DriverGuideProxy driverGuideProxy;

  @SneakyThrows
  @Override
  public DriverInfoSOAResponseType queryDriver(DriverInfoSOARequestType request){
    List<CompletableFuture<DriverInfoSOAResponseType>> futures = Lists.newArrayList();
    futures.add(CompletableFuture.supplyAsync(() -> driverDelegate.queryDriver(request), driverGuideQueryPool));
    futures.add(CompletableFuture.supplyAsync(() -> driverGuidDelegate.queryDriver(request), driverGuideQueryPool));
    CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
    allFutures.join();
    return mergeQueryDriver(futures.get(0).get(), futures.get(1).get(), request);
  }

  private DriverInfoSOAResponseType mergeQueryDriver(DriverInfoSOAResponseType driverResult, DriverInfoSOAResponseType driverGuideResult, DriverInfoSOARequestType request) {

    if(CollectionUtils.isEmpty(driverGuideResult.getDriverList())) {
      logger.info("mergeProcess-queryDriver", "return driver result: {}", request);
      Cat.logEvent(BRIDGE,QUERY_DRIVER + EVENT_SPLIT + RETURN_DRIVER_RESULT);
      if (CollectionUtils.isEmpty(driverResult.getDriverList())) {
        Cat.logEvent(BRIDGE,QUERY_DRIVER + EVENT_SPLIT + RETURN_EMPTY_RESULT);
      }
      return driverResult;
    }

    Cat.logEvent(BRIDGE,QUERY_DRIVER + EVENT_SPLIT + RETURN_DRIVER_GUIDE_RESULT);
    logger.info("mergeProcess-queryDriver", "return driver guide result: {}", driverGuideResult.getDriverList());
    return driverGuideResult;
  }

  @SneakyThrows
  @Override
  public QueryDrvDetailSOAResponseType queryDrvDetail(QueryDrvDetailSOARequestType request) {
    List<CompletableFuture<QueryDrvDetailSOAResponseType>> futures = Lists.newArrayList();
    futures.add(CompletableFuture.supplyAsync(() -> driverDelegate.queryDrvDetail(request), driverGuideQueryPool));
    futures.add(CompletableFuture.supplyAsync(() -> driverGuidDelegate.queryDrvDetail(request), driverGuideQueryPool));
    CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
    allFutures.join();
    return mergeQueryDrvDetail(futures.get(0).get(), futures.get(1).get(), request);
  }

  private QueryDrvDetailSOAResponseType mergeQueryDrvDetail(QueryDrvDetailSOAResponseType driverResult, QueryDrvDetailSOAResponseType driverGuideResult, QueryDrvDetailSOARequestType request) {
    if (driverGuideResult.getData() == null) {
      logger.info("mergeProcess-queryDrvDetail", "return driver result : {}, {}", request.getDrvId());
      Cat.logEvent(BRIDGE,QUERY_DRV_DETAIL + EVENT_SPLIT + RETURN_DRIVER_RESULT);
      if ( driverResult.getData() == null) {
        Cat.logEvent(BRIDGE,QUERY_DRV_DETAIL + EVENT_SPLIT + RETURN_EMPTY_RESULT);
      }
      return driverResult;
    }
    Cat.logEvent(BRIDGE,QUERY_DRV_DETAIL + EVENT_SPLIT + RETURN_DRIVER_GUIDE_RESULT);
    logger.info("mergeProcess-queryDrvDetail", "return driver guide result : {}", driverGuideResult.getData());
    return driverGuideResult;
  }

  @SneakyThrows
  @Override
  public QueryVehicleBaseSOAResponseType queryVehicleListBySupplierId(QueryVehicleBaseSOARequestType request) {
    if (driverGuideProxy.getGrayControl(request.getSupplierId()) && driverGuideProxy.isSupplierInGrayControl(request.getSupplierId())) {
      logger.info("mergeProcess-queryVehicleListBySupplierId", "return driver guide result:{}", request.getSupplierId());
      Cat.logEvent(BRIDGE,QUERY_VEHICLE_LIST_BY_SUPPLIER_ID + EVENT_SPLIT + RETURN_DRIVER_GUIDE_RESULT);
      return driverGuidDelegate.queryVehicleListBySupplierId(request);
    }
    logger.info("mergeProcess-queryVehicleListBySupplierId", "return driver result:{}", request.getSupplierId());
    Cat.logEvent(BRIDGE,QUERY_VEHICLE_LIST_BY_SUPPLIER_ID + EVENT_SPLIT + RETURN_DRIVER_RESULT);
    return driverDelegate.queryVehicleListBySupplierId(request);
  }

  @SneakyThrows
  @Override
  public QueryDrvIdByTransportGroupsResponseType queryDrvIdByTransportGroups(
    QueryDrvIdByTransportGroupsRequestType request) {
      List<CompletableFuture<QueryDrvIdByTransportGroupsResponseType>> futures = Lists.newArrayList();
      futures.add(CompletableFuture.supplyAsync(() -> driverDelegate.queryDrvIdByTransportGroups(request), driverGuideSearchPool));
      futures.add(CompletableFuture.supplyAsync(() -> driverGuidDelegate.queryDrvIdByTransportGroups(request), driverGuideSearchPool));
      CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
      allFutures.join();
      return mergeQueryDrvIdByTransportGroups(futures.get(0).get(), futures.get(1).get(), request);
  }

  protected QueryDrvIdByTransportGroupsResponseType mergeQueryDrvIdByTransportGroups(
    QueryDrvIdByTransportGroupsResponseType driverResult, QueryDrvIdByTransportGroupsResponseType driverGuideResult, QueryDrvIdByTransportGroupsRequestType request) {
    if (CollectionUtils.isEmpty(driverGuideResult.getDrvIdList())) {
      logger.info("mergeProcess-queryDrvIdByTransportGroups", "return driver result:{}", driverResult.getDrvIdList());
      Cat.logEvent(BRIDGE,QUERY_DRV_ID_BY_TRANSPORT_GROUPS + EVENT_SPLIT + RETURN_DRIVER_RESULT);
      if (CollectionUtils.isEmpty(driverResult.getDrvIdList())) {
        Cat.logEvent(BRIDGE,QUERY_DRV_ID_BY_TRANSPORT_GROUPS + EVENT_SPLIT + RETURN_EMPTY_RESULT);
      }
      return driverResult;
    }
    Cat.logEvent(BRIDGE,QUERY_DRV_ID_BY_TRANSPORT_GROUPS + EVENT_SPLIT + RETURN_DRIVER_GUIDE_RESULT);
    logger.info("mergeProcess-queryDrvIdByTransportGroups", "return driver guide result:{}", driverGuideResult.getDrvIdList());
    return driverGuideResult;
  }

  @SneakyThrows
  @Override
  public VehicleDetailSOAResponseType queryVehicleDetail(VehicleDetailSOARequestType request) {
    List<CompletableFuture<VehicleDetailSOAResponseType>> futures = Lists.newArrayList();
    futures.add(CompletableFuture.supplyAsync(() -> driverDelegate.queryVehicleDetail(request), driverGuideQueryPool));
    futures.add(CompletableFuture.supplyAsync(() -> driverGuidDelegate.queryVehicleDetail(request), driverGuideQueryPool));
    CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
    allFutures.join();
    return mergeQueryVehicleDetail(futures.get(0).get(), futures.get(1).get(), request);
  }

  protected VehicleDetailSOAResponseType mergeQueryVehicleDetail(VehicleDetailSOAResponseType driverResult, VehicleDetailSOAResponseType driverGuideResult, VehicleDetailSOARequestType request) {
    if (driverGuideResult.getData() == null) {
      logger.info("mergeProcess-queryVehicleDetail", "return driver result:{}", request.getVehicleId());
      Cat.logEvent(BRIDGE,QUERY_VEHICLE_DETAIL + EVENT_SPLIT + RETURN_DRIVER_RESULT);
      if (driverResult.getData() == null) {
        Cat.logEvent(BRIDGE,QUERY_VEHICLE_DETAIL + EVENT_SPLIT + RETURN_EMPTY_RESULT);
      }
      return driverResult;
    }

    //  如果供应链数据不为空，且供应链数据不在灰度控制列表中，则返回供应链的数据
    if (driverResult.getData() != null && !driverGuideProxy.getGrayControl(driverResult.getData().getSupplierId())) {
      logger.info("mergeProcess-queryVehicleDetail", "return driver result:{}", request.getVehicleId());
      Cat.logEvent(BRIDGE,QUERY_VEHICLE_DETAIL + EVENT_SPLIT + RETURN_DRIVER_RESULT);
      return driverResult;
    }

    Cat.logEvent(BRIDGE,QUERY_VEHICLE_DETAIL + EVENT_SPLIT + RETURN_DRIVER_GUIDE_RESULT);
    logger.info("mergeProcess-queryVehicleDetail", "return driver guide result:{}", request.getVehicleId());
    return driverGuideResult;
  }

  @SneakyThrows
  @Override
  public QueryDriverIdForSaasResponseType queryDriverIdForSaas(
    QueryDriverIdForSaasRequestType request) {
    List<CompletableFuture<QueryDriverIdForSaasResponseType>> futures = Lists.newArrayList();
    futures.add(CompletableFuture.supplyAsync(() -> driverDelegate.queryDriverIdForSaas(request), driverGuideQueryPool));
    futures.add(CompletableFuture.supplyAsync(() -> driverGuidDelegate.queryDriverIdForSaas(request), driverGuideQueryPool));
    CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
    allFutures.join();
    return mergeQueryDriverIdForSaas(futures.get(0).get(), futures.get(1).get(), request);
  }

  private QueryDriverIdForSaasResponseType mergeQueryDriverIdForSaas(QueryDriverIdForSaasResponseType driverResult, QueryDriverIdForSaasResponseType driverGuideResult, QueryDriverIdForSaasRequestType request) {
    if (CollectionUtils.isEmpty(driverGuideResult.getDriverIds())) {
      logger.info("mergeProcess-queryDriverIdForSaas", "return driver result:{}", request.getDriverPhone());
      Cat.logEvent(BRIDGE,QUERY_DRIVER_ID_FOR_SAAS + EVENT_SPLIT + RETURN_DRIVER_RESULT);
      if (CollectionUtils.isEmpty(driverResult.getDriverIds())) {
        Cat.logEvent(BRIDGE,QUERY_DRIVER_ID_FOR_SAAS + EVENT_SPLIT + RETURN_EMPTY_RESULT);
      }
      return driverResult;
    }
    Cat.logEvent(BRIDGE,QUERY_DRIVER_ID_FOR_SAAS + EVENT_SPLIT + RETURN_DRIVER_GUIDE_RESULT);
    logger.info("mergeProcess-queryDriverIdForSaas", "return driver guide result:{}", request.getDriverPhone());
    return driverGuideResult;
  }

  @SneakyThrows
  @Override
  public QueryVehicleIdForSaasResponseType queryVehicleIdForSaas(
    QueryVehicleIdForSaasRequestType request) {
    List<CompletableFuture<QueryVehicleIdForSaasResponseType>> futures = Lists.newArrayList();
    futures.add(CompletableFuture.supplyAsync(() -> driverDelegate.queryVehicleIdForSaas(request), driverGuideQueryPool));
    futures.add(CompletableFuture.supplyAsync(() -> driverGuidDelegate.queryVehicleIdForSaas(request), driverGuideQueryPool));
    CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
    allFutures.join();
    return mergeQueryVehicleIdForSaas(futures.get(0).get(), futures.get(1).get(), request);
  }

  private QueryVehicleIdForSaasResponseType mergeQueryVehicleIdForSaas(QueryVehicleIdForSaasResponseType driverResult, QueryVehicleIdForSaasResponseType driverGuideResult, QueryVehicleIdForSaasRequestType request) {
    if (CollectionUtils.isEmpty(driverGuideResult.getVehicleIds())) {
      logger.info("mergeProcess-queryVehicleIdForSaas", "return driver result:{}", request.getVehicleLicense());
      Cat.logEvent(BRIDGE,QUERY_VEHICLE_ID_FOR_SAAS + EVENT_SPLIT + RETURN_DRIVER_RESULT);
      if (CollectionUtils.isEmpty(driverResult.getVehicleIds())) {
        Cat.logEvent(BRIDGE,QUERY_VEHICLE_ID_FOR_SAAS + EVENT_SPLIT + RETURN_EMPTY_RESULT);
      }
      return driverResult;
    }
    Cat.logEvent(BRIDGE,QUERY_VEHICLE_ID_FOR_SAAS + EVENT_SPLIT + RETURN_DRIVER_GUIDE_RESULT);
    logger.info("mergeProcess-queryVehicleIdForSaas", "return driver guide result:{}", request.getVehicleLicense());
    return driverGuideResult;
  }

  @SneakyThrows
  @Override
  public QueryDriverForSaasResponseType queryDriverForSaas(
    QueryDriverForSaasRequestType request) {
    List<CompletableFuture<QueryDriverForSaasResponseType>> futures = Lists.newArrayList();
    futures.add(CompletableFuture.supplyAsync(() -> driverDelegate.queryDriverForSaas(request), driverGuideQueryPool));
    futures.add(CompletableFuture.supplyAsync(() -> driverGuidDelegate.queryDriverForSaas(request), driverGuideQueryPool));
    CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
    allFutures.join();
    return mergeQueryDriverForSaas(futures.get(0).get(), futures.get(1).get(), request);
  }

  private QueryDriverForSaasResponseType mergeQueryDriverForSaas(QueryDriverForSaasResponseType driverResult, QueryDriverForSaasResponseType driverGuideResult, QueryDriverForSaasRequestType request) {
    if (CollectionUtils.isEmpty(driverGuideResult.getDriverList())) {
      Cat.logEvent(BRIDGE, QUERY_DRIVER_FOR_SAAS + EVENT_SPLIT + RETURN_DRIVER_RESULT);
      logger.info("mergeProcess-queryDriverForSaas", "return driver result:{}", request.getDriverIds());
      if (CollectionUtils.isEmpty(driverResult.getDriverList())) {
        Cat.logEvent(BRIDGE,QUERY_DRIVER_FOR_SAAS + EVENT_SPLIT + RETURN_EMPTY_RESULT);
      }
      return driverResult;
    }
    Cat.logEvent(BRIDGE,QUERY_DRIVER_FOR_SAAS + EVENT_SPLIT + RETURN_DRIVER_GUIDE_RESULT);
    logger.info("mergeProcess-queryDriverForSaas", "return driver guide result:{}", request.getDriverIds());
    return driverGuideResult;
  }

  @SneakyThrows
  @Override
  public QueryVehicleForSaasResponseType queryVehicleForSaas(
    QueryVehicleForSaasRequestType request) {
    List<CompletableFuture<QueryVehicleForSaasResponseType>> futures = Lists.newArrayList();
    futures.add(CompletableFuture.supplyAsync(() -> driverDelegate.queryVehicleForSaas(request), driverGuideQueryPool));
    futures.add(CompletableFuture.supplyAsync(() -> driverGuidDelegate.queryVehicleForSaas(request), driverGuideQueryPool));
    CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
    allFutures.join();
    return mergeQueryVehicleForSaas(futures.get(0).get(), futures.get(1).get(), request);
  }

  private QueryVehicleForSaasResponseType mergeQueryVehicleForSaas(QueryVehicleForSaasResponseType driverResult, QueryVehicleForSaasResponseType driverGuideResult, QueryVehicleForSaasRequestType request) {
    if (CollectionUtils.isEmpty(driverGuideResult.getVehicleList())) {
      Cat.logEvent(BRIDGE,QUERY_VEHICLE_FOR_SAAS + EVENT_SPLIT + RETURN_DRIVER_RESULT);
      logger.info("mergeProcess-queryVehicleForSaas", "return driver result:{}", request.getVehicleIds());
      if (CollectionUtils.isEmpty(driverResult.getVehicleList())) {
        Cat.logEvent(BRIDGE,QUERY_VEHICLE_FOR_SAAS + EVENT_SPLIT + RETURN_EMPTY_RESULT);
      }
      return driverResult;
    }
    Cat.logEvent(BRIDGE,QUERY_VEHICLE_FOR_SAAS + EVENT_SPLIT + RETURN_DRIVER_GUIDE_RESULT);
    logger.info("mergeProcess-queryVehicleForSaas", "return driver guide result:{}", request.getVehicleIds());
    return driverGuideResult;
  }
}
