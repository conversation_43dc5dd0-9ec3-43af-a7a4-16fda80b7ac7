package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.ResponseResultUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2020/3/10 17:03
 */
@Component
@ServiceLogTagPair(key = "qTraceId")
public class QueryTransportGroupsExecutor extends AbstractRpcExecutor<QueryTransportGroupsSOARequestType, QueryTransportGroupsSOAResponseType> implements Validator<QueryTransportGroupsSOARequestType> {

    @Autowired
    private TransportGroupQueryService transportGroupQueryService;

    @Override
    public QueryTransportGroupsSOAResponseType execute(QueryTransportGroupsSOARequestType queryTransportGroupsSOARequestType) {
        QueryTransportGroupsSOAResponseType soaResponseType = new QueryTransportGroupsSOAResponseType();
        QueryTransportGroupModel queryModel = requestToQueryModel(queryTransportGroupsSOARequestType);
        Result<List<TransportGroupDetailSOAType>> listResult = transportGroupQueryService.queryTransportGroups(queryModel);
        if (listResult.isSuccess()) {
            soaResponseType.setData(listResult.getData());
            return ServiceResponseUtils.success(soaResponseType);
        }else {
            return ServiceResponseUtils.fail(soaResponseType, listResult.getCode(),listResult.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<QueryTransportGroupsSOARequestType> validator, QueryTransportGroupsSOARequestType req) {
        Boolean predicate = req.getLatitude() != null || req.getLongitude() != null;
        validator.ruleFor("longitude").notNull().when(predicate);
        validator.ruleFor("latitude").notNull().when(predicate);
        validator.ruleFor("poiType").notNull().notEmpty().when(predicate);
    }

    private QueryTransportGroupModel requestToQueryModel(QueryTransportGroupsSOARequestType requestType){
        QueryTransportGroupModel queryModel = new QueryTransportGroupModel();
        queryModel.setSupplierId(requestType.getSupplierId());
        queryModel.setTransportGroupId(requestType.getTransportGroupId());
        queryModel.setGroupStatus(requestType.getGroupStatus());
        queryModel.setTransportGroupMode(requestType.getTransportGroupMode());
        queryModel.setSkuId(requestType.getSkuId());
        queryModel.setPoiType(requestType.getPoiType());
        queryModel.setLatitude(requestType.getLatitude());
        queryModel.setLongitude(requestType.getLongitude());
        queryModel.setPoiRef(requestType.getPoiRef());
        queryModel.setCarPlaceId(requestType.getCarPlaceId());
        return queryModel;
    }

    @Override
    public void onExecuted(QueryTransportGroupsSOARequestType req, QueryTransportGroupsSOAResponseType resp) {
        ResponseResultUtil.logQueryResult(resp.getData());
    }
}
