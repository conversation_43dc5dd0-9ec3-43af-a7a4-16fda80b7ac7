package com.ctrip.dcs.tms.transport.interfaces.provider.executor;


import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 司机车辆审批
 */
@Component
public class QueryDrvVehRecruitingDetailExecutor extends AbstractRpcExecutor<DrvVehRecruitingDetailSOARequestType, DrvVehRecruitingDetailSOAResponseType> implements Validator<DrvVehRecruitingDetailSOARequestType> {

    @Autowired
    private DrvVehRecruitingQueryService queryService;

    @Override
    public DrvVehRecruitingDetailSOAResponseType execute(DrvVehRecruitingDetailSOARequestType requestType) {
        DrvVehRecruitingDetailSOAResponseType responseType = new DrvVehRecruitingDetailSOAResponseType();
        Result<DrvVehRecruitingDetailSOADTO> result = queryService.queryDrvVehRecruitingDetail(requestType.getDrvRecruitingId(),requestType.getRecruitingType());
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType,"400",result.getMsg());
    }
}
