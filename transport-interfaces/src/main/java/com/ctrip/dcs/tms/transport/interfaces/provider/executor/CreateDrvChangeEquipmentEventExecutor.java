package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.CreateDrvChangeEquipmentEventRequestType;
import com.ctrip.dcs.tms.transport.api.model.CreateDrvChangeEquipmentEventResponseType;
import com.ctrip.dcs.tms.transport.application.command.DriverCommandService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
　* @description: 新增临派车辆
　* <AUTHOR>
　* @date 2023/10/9 11:12
*/
@Component
@ServiceLogTag(tagKeys = {"drvId"})
public class CreateDrvChangeEquipmentEventExecutor extends AbstractRpcExecutor<CreateDrvChangeEquipmentEventRequestType, CreateDrvChangeEquipmentEventResponseType> implements Validator<CreateDrvChangeEquipmentEventRequestType> {

    @Autowired
    private DriverCommandService driverCommandService;

    @Override
    public CreateDrvChangeEquipmentEventResponseType execute(CreateDrvChangeEquipmentEventRequestType requestType) {
        CreateDrvChangeEquipmentEventResponseType responseType = new CreateDrvChangeEquipmentEventResponseType();
        Result<Boolean> result = driverCommandService.createDrvChangeEquipmentEvent(requestType);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType,result.getCode(),result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<CreateDrvChangeEquipmentEventRequestType> validator) {
        validator.ruleFor("drvId").notNull();
    }

}