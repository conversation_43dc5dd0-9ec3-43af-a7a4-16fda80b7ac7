package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.PropertyAddTagSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.PropertyAddTagSOAResponseType;
import com.ctrip.dcs.tms.transport.application.command.CertificateCheckCommandService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PropertyAddTagExecutor extends AbstractRpcExecutor<PropertyAddTagSOARequestType, PropertyAddTagSOAResponseType> implements Validator<PropertyAddTagSOARequestType> {

    @Autowired
    private CertificateCheckCommandService commandService;

    @Override
    public PropertyAddTagSOAResponseType execute(PropertyAddTagSOARequestType requestType) {
        PropertyAddTagSOAResponseType responseType = new PropertyAddTagSOAResponseType();
        Result<Boolean> result = commandService.insertPropertyAddTag(requestType);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType,"400",result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<PropertyAddTagSOARequestType> validator) {

    }

}