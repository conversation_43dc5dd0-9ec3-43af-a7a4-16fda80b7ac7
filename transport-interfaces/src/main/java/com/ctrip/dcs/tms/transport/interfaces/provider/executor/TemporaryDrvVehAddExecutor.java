package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.TemporaryDrvVehAddSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.TemporaryDrvVehAddSOAResponseType;
import com.ctrip.dcs.tms.transport.application.command.DriverCommandService;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
　* @description: 新增临派司机和车辆
　* <AUTHOR>
　* @date 2023/10/9 11:12
*/
@Component
@ServiceLogTag(tagKeys = {"vehicleLicense"})
public class TemporaryDrvVehAddExecutor extends AbstractRpcExecutor<TemporaryDrvVehAddSOARequestType, TemporaryDrvVehAddSOAResponseType> implements Validator<TemporaryDrvVehAddSOARequestType> {

    @Autowired
    private DriverCommandService driverCommandService;

    @Override
    public TemporaryDrvVehAddSOAResponseType execute(TemporaryDrvVehAddSOARequestType requestType) {
        try {
            TemporaryDrvVehAddSOAResponseType responseType = new TemporaryDrvVehAddSOAResponseType();
            Result<TemporaryDrvVehAddSOAResponseType> result = driverCommandService.temporaryDrvVehAdd(requestType);
            if (result.isSuccess()) {
                return ServiceResponseUtils.success(result.getData());
            } else {
                return ServiceResponseUtils.fail(responseType,result.getCode(),result.getMsg());
            }
        }catch (Exception e) {
            if (e.getCause() instanceof BizException) {
                throw (BizException) e.getCause();
            }
            throw e;
        }
    }

    @Override
    public void validate(AbstractValidator<TemporaryDrvVehAddSOARequestType> validator) {
        validator.ruleFor("supplierId").notNull();
        validator.ruleFor("cityId").notNull();
        validator.ruleFor("vehicleLicense").notNull();
        validator.ruleFor("drvPhone").notNull();
    }

}
