package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.tms.transport.api.model.QueryVehicleAccessYearSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryVehicleAccessYearSOAResponseType;
import com.ctrip.dcs.tms.transport.application.query.VehicleQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OverageDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CommonConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OverageQConfig;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;

@Component
public class QueryVehicleAccessYearExecutor extends AbstractRpcExecutor<QueryVehicleAccessYearSOARequestType, QueryVehicleAccessYearSOAResponseType> implements Validator<QueryVehicleAccessYearSOARequestType> {

    @Autowired
    private VehicleQueryService vehicleQueryService;
    @Autowired
    private OverageQConfig overageQConfig;
    @Autowired
    private CommonConfig commonConfig;

    @Override
    public QueryVehicleAccessYearSOAResponseType execute(QueryVehicleAccessYearSOARequestType requestType) {
        QueryVehicleAccessYearSOAResponseType soaResponseType = new QueryVehicleAccessYearSOAResponseType();
        if (BooleanUtils.isFalse(commonConfig.getOverageGraySwitch())
            || (BooleanUtils.isTrue(commonConfig.getOverageGraySwitch()) && CollectionUtils.isNotEmpty(commonConfig.getCityIdList()) && commonConfig.getCityIdList().contains(requestType.getCityId()))) {
            OverageDTO overageMap = overageQConfig.getOverageMap(requestType.getCityId(), requestType.getVehicleTypeId());
            soaResponseType.setAccessPeriod(overageMap.getAccessLimit());
            soaResponseType.setData(overageMap.getOverage().intValue());
        } else {
            Result<Integer> result = vehicleQueryService.queryVehicleAccessYear(requestType.getCityId(), requestType.getVehicleTypeId());
            if (result.isSuccess()) {
                soaResponseType.setData(result.getData());
                soaResponseType.setAccessPeriod(result.getData().doubleValue());
                return ServiceResponseUtils.success(soaResponseType);
            }
        }
        return ServiceResponseUtils.success(soaResponseType);
    }

    @Override
    public void validate(AbstractValidator<QueryVehicleAccessYearSOARequestType> validator) {}

}