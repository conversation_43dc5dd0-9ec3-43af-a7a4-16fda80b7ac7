package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.dto.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctriposs.baiji.exception.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import qunar.tc.qschedule.config.*;
import qunar.tc.schedule.*;

import java.util.*;
import java.util.stream.*;

/**
　* @description: 待平台审批招募司机调用第三方
　* <AUTHOR>
　* @date 2022/1/13 10:29
*/
@Component
public class VehRecruitingCertificateCheckResultSchedule {

    private static final Logger logger = LoggerFactory.getLogger(VehRecruitingCertificateCheckResultSchedule.class);

    @Autowired
    private TmsCertificateCheckRepository checkRepository;
    @Autowired
    TmsTransportQconfig qconfig;
    @Autowired
    DrvDrvierRepository repository;
    @Autowired
    CertificateCheckQueryService checkQueryService;
    @Autowired
    DrvRecruitingRepository recruitingRepository;
    @Autowired
    VehicleRecruitingRepository vehicleRecruitingRepository;
    @Autowired
    VehicleRepository vehicleRepository;
    @Autowired
    RecruitingCommandService recruitingCommandService;
    @Autowired
    TmsQmqProducerCommandService qmqProducerCommandService;

    private static final String approveStatus_Key = "approveStatus";
    private static final String vehicleFrom_Key = "vehicleFrom";
    private static final String drvRecruitingId_Key = "drvRecruitingId";

    @QSchedule("veh.recruiting.certificate.check.result.job")
    public void VehRecruitingcertificateCheckResultSchedule(Parameter parameter) throws Exception {
        this.vehRecruitingcertificateCheckResultScheduleMethod();
    }

    public Boolean vehRecruitingcertificateCheckResultScheduleMethod() {
        try {
            Integer checkStatus = TmsTransportConstant.CheckStatusEnum.CHECKING.getCode();
            Integer checkType = TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode();
            //查询待平台审批的车辆
            int count = vehicleRecruitingRepository.queryvCountWaitApproveRecruiting();
            if (count == 0) {
                return Boolean.FALSE;
            }
            int pageSize = 50;
            count = StringUtil.getPageCount(count,pageSize);
            for (int i = 1; i <= count; i ++) {
                int beginSize = i;
                List<VehicleRecruitingPO> vehicleRecruitingPOList =  vehicleRecruitingRepository.queryvWaitApproveRecruitingByPage(beginSize,pageSize);
                if (CollectionUtils.isEmpty(vehicleRecruitingPOList)) {
                    return Boolean.FALSE;
                }
                List<Long> orgVersionIds = Lists.newArrayList();
                List<Long> new3VersionIds = Lists.newArrayList();
                for(VehicleRecruitingPO drvRecruitingPO : vehicleRecruitingPOList){
                    if(drvRecruitingPO.getVersionFlag() >= 3){
                        new3VersionIds.add(drvRecruitingPO.getVehicleId());
                    }else {
                        orgVersionIds.add(drvRecruitingPO.getVehicleId());
                    }
                }
                List<TmsCertificateCheckPO> checkPOList = Lists.newArrayList();
                List<TmsCertificateCheckPO> newCheckPOList = Lists.newArrayList();
                if(CollectionUtils.isNotEmpty(orgVersionIds)){
                    checkPOList = checkRepository.queryNewCheckListByCheckIds(orgVersionIds, checkType, false,checkStatus);
                }
                if(CollectionUtils.isNotEmpty(new3VersionIds)){
                    newCheckPOList = checkRepository.queryNewCheckListByCheckIds(new3VersionIds, checkType,true, checkStatus);
                }
                checkPOList = (List<TmsCertificateCheckPO>) CollectionUtils.union(checkPOList,newCheckPOList);

                if (CollectionUtils.isEmpty(checkPOList)) {
                    return Boolean.FALSE;
                }
                //按checkId分组
                Map<Long,List<TmsCertificateCheckPO>> checkMap = checkPOList.stream().collect(Collectors.groupingBy(TmsCertificateCheckPO::getCheckId));
                for(Map.Entry<Long,List<TmsCertificateCheckPO>> entry : checkMap.entrySet()){
                    //遍历核验表中，身份证对应的背调表信息
                    for (TmsCertificateCheckPO checkPO : entry.getValue()) {
                        //行驶证
                        if (Objects.equals(checkPO.getCertificateType(), TmsTransportConstant.CertificateTypeEnum.CARCERTILICENSE.getCode())) {
                            vehCertificateCheck(checkPO.getId(), checkPO.getCheckId(), checkPO.getCheckType(),checkPO.getCheckStatus());
                        }
                        //网约车行驶证
                        if (Objects.equals(checkPO.getCertificateType(), TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT.getCode())) {
                            netVehLicenseDataCheck(checkPO.getId(),checkPO.getCheckId(),checkPO.getCheckType(),checkPO.getCheckKeyword(),checkPO.getCheckStatus());
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
        return Boolean.FALSE;
    }

    //行驶证核验
    private Boolean vehCertificateCheck(Long id, Long checkId, Integer checkType,Integer checkStatus) {
        VehicleRecruitingPO vehicleRecruitingPO = vehicleRecruitingRepository.queryByPK(checkId);
        if (Objects.isNull(vehicleRecruitingPO)) {
            return Boolean.FALSE;
        }
        String vehicleLicense = vehicleRecruitingPO.getVehicleLicense();
        String vinCode = vehicleRecruitingPO.getVin();
        Long supplierId = vehicleRecruitingPO.getSupplierId();
        Integer versionFlag = vehicleRecruitingPO.getVersionFlag();
        if(StringUtils.isNotEmpty(vehicleLicense)){
            VehicleAuditDTO vehicleAuditDTO = VehicleAuditDTO.rehBuildVehicleDTO(vehicleLicense, "", vinCode, id, "system",checkType,supplierId,versionFlag,checkStatus,getRecruitingId(checkId,checkType),getRecruitingType(vehicleRecruitingPO.getVehicleFrom()));
            checkQueryService.refreshVehLicenseCheckIng(vehicleAuditDTO);
            vehSystemRejected(checkId,checkType);
        }

        return Boolean.FALSE;
    }

    //网约车行驶证核验
    public Boolean netVehLicenseDataCheck(Long id,Long checkId,Integer checkType, String checkKeyword,Integer checkStatus) {
        if(StringUtils.isNotEmpty(checkKeyword)){
            VehicleRecruitingPO vehicleRecruitingPO = vehicleRecruitingRepository.queryByPK(checkId);
            if (Objects.isNull(vehicleRecruitingPO)) {
                return Boolean.FALSE;
            }
            //上海 合规信息查询处理
            if(checkQueryService.checkFromCityPlatform(vehicleRecruitingPO.getCityId().toString())){
                RefreshNetVehLicenseCheckIngParamDTO paramDTO = new RefreshNetVehLicenseCheckIngParamDTO();
                paramDTO.setId(id);
                paramDTO.setVehicleLicense(checkKeyword);
                paramDTO.setCheckType(checkType);
                paramDTO.setSupplierId(vehicleRecruitingPO.getSupplierId());
                paramDTO.setVersionFlag(vehicleRecruitingPO.getVersionFlag());
                paramDTO.setCheckStatus(checkStatus);
                paramDTO.setCheckId(checkId);
                paramDTO.setRecruitingId(getRecruitingId(checkId,checkType));
                paramDTO.setRecruitingType(getRecruitingType(vehicleRecruitingPO.getVehicleFrom()));
                paramDTO.setCityId(vehicleRecruitingPO.getCityId());
                checkQueryService.refreshNetVehLicenseCheckIng(paramDTO);
            }else{
                //其他城市 合规信息查询处理
                Long supplierId = vehicleRecruitingPO.getSupplierId();
                Integer versionFlag = vehicleRecruitingPO.getVersionFlag();
                checkQueryService.refreshNetVehLicenseCheckIng(id, checkKeyword,checkType,supplierId,versionFlag,checkStatus,checkId,getRecruitingId(checkId,checkType),getRecruitingType(vehicleRecruitingPO.getVehicleFrom()));
            }
            vehSystemRejected(checkId,checkType);
        }
        return Boolean.FALSE;
    }

    /**
     　* @description: 车辆审批不通过，自动驳回
     　* <AUTHOR>
     　* @date 2021/10/11 11:50
     */
    public Boolean vehSystemRejected(Long checkId,Integer checkType){
        TmsTransportConstant.RecruitingTypeEnum recruitingType =  TmsTransportConstant.RecruitingTypeEnum.vehicle;
        Map<String,Object> vehicleResultMap = getVehRecruitingApproveStatus(checkId,checkType);
        if(Objects.equals(vehicleResultMap.get(vehicleFrom_Key),TmsTransportConstant.VehicleFromEnum.Veh_AUTO.getCode())){
            checkId = (Long) vehicleResultMap.get(drvRecruitingId_Key);
            recruitingType = TmsTransportConstant.RecruitingTypeEnum.drv;
        }
        //司机核验状态
        qmqProducerCommandService.sendRecruitingCheckStatusQMQ(checkId,recruitingType.getCode(),TmsTransportConstant.TMS_DEFAULT_USERNAME);
        return Boolean.TRUE;
    }

    public Map<String,Object> getVehRecruitingApproveStatus(Long recruitingId,Integer checkType){
        Long drvRecruitingId = null;
        Integer vehicleFrom = null;
        Map<String,Object> resultMap = Maps.newHashMap();
        if (Objects.equals(checkType, TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode())) {
            VehicleRecruitingPO vehicleRecruitingPO = vehicleRecruitingRepository.queryByPK(recruitingId);
            if (Objects.isNull(vehicleRecruitingPO)) {
                return resultMap;
            }
            vehicleFrom = vehicleRecruitingPO.getVehicleFrom();
            if(Objects.equals(vehicleFrom, TmsTransportConstant.VehicleFromEnum.Veh_AUTO.getCode())){
                List<DrvRecruitingPO> recruitingPO = recruitingRepository.queryvRecruitingByVehicleId(vehicleRecruitingPO.getVehicleId());
                if(CollectionUtils.isEmpty(recruitingPO)){
                    return resultMap;
                }
                drvRecruitingId = recruitingPO.get(0).getDrvRecruitingId();
            }
        }
        resultMap.put(vehicleFrom_Key,vehicleFrom);
        resultMap.put(drvRecruitingId_Key,drvRecruitingId);
        return resultMap;
    }

    public Long getRecruitingId(Long recruitingId,Integer checkType){
        Map<String,Object> vehRecruitingMap =  getVehRecruitingApproveStatus(recruitingId,checkType);
        if(vehRecruitingMap.get(drvRecruitingId_Key) == null){
            return recruitingId;
        }
        return (Long) vehRecruitingMap.get(drvRecruitingId_Key);
    }

    public Integer getRecruitingType(Integer vehicleFrom){
        Integer recruitingType = TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode();
        if(Objects.equals(vehicleFrom, TmsTransportConstant.VehicleFromEnum.Veh_AUTO.getCode())){
            recruitingType = TmsTransportConstant.RecruitingTypeEnum.drv.getCode();
        }
        return recruitingType;
    }
}