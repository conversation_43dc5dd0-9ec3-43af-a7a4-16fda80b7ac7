package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

@Component
public class QueryDrvVerifyGrayExecutor extends AbstractRpcExecutor<DrvVerifyGraySOARequestType, DrvVerifyGraySOAResponseType> implements Validator<DrvVerifyGraySOARequestType> {

    @Autowired
    DriverQueryService driverQueryService;

    @Override
    public DrvVerifyGraySOAResponseType execute(DrvVerifyGraySOARequestType requestType) {
        DrvVerifyGraySOAResponseType responseType = new DrvVerifyGraySOAResponseType();
        Result<DrvVerifyGraySOADTO> result = driverQueryService.queryDrvVerifyGray(requestType);
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<DrvVerifyGraySOARequestType> validator) {
        validator.ruleFor("driverId").notNull();
    }
}
