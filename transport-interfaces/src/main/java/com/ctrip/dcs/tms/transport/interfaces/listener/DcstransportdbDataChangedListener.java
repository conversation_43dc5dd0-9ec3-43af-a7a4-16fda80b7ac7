package com.ctrip.dcs.tms.transport.interfaces.listener;

import com.ctrip.arch.canal.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.config.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.handler.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.handler.cache.CacheHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.handler.cache.impl.DefaultCacheHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.monitoring.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.jackson.*;
import com.google.common.collect.*;
import lombok.SneakyThrows;
import org.apache.commons.collections.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import org.springframework.util.StringUtils;
import qunar.tc.qmq.*;
import qunar.tc.qmq.consumer.annotation.*;

import java.util.*;

/**
 * Dcstransportdb库数据变更监听
 *
 * <AUTHOR>
 * @Date 2020/10/28 15:31
 */
@Component
public class DcstransportdbDataChangedListener {

    private static final Logger logger = LoggerFactory.getLogger(DcstransportdbDataChangedListener.class);

    @Autowired
    private ModRecordRespository<TmsModRecordPO> modRecordRespository;
    @Autowired
    private TmsTransportQconfig tmsTransportQconfig;

    @Autowired
    private ContextConfig contextConfig;

    @Autowired /**FIXME 逻辑摘除*/
    private PushDataCommandService pushDataCommandService;

    @Autowired
    private TmsQmqProducerCommandService tmsQmqProducerCommandService;

    @Autowired
    private DriverCacheServiceV2 driverCacheServiceV2;

    @Autowired
    private List<CacheHandler> cacheHandlerList;

    @Autowired
    private DefaultCacheHandler defaultCacheHandler;

    private final String fieldDrvId = "drv_id";
    private final String fieldCityId = "city_id";
    private final String fieldDrvName = "drv_name";
    private final String fieldDrvPhone = "drv_phone";
    private final String fieldVehicleId = "vehicle_id";
    private final String fieldCheckType = "check_type";
    private final String fieldCheckId = "check_id";
    private final String FIELD_DRIVER_LEAVE_ID = "id";

    /**
     * dcstransportdb变更消息监听
     * 详情见：http://otterportal.fx.ctripcorp.com/
     * 申请单号：prod：OT2788201110104808  fat：OT2780201015185036
     *
     * @param message
     */
    @SneakyThrows
    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.SUBJECT_DCSTRANSPORTDB_CHANGED, consumerGroup = TmsTransportConstant.QMQ_CONSUMER_GROUP)
    public void dcstransportdbDataChanged(Message message) {
        String data = null;
        try {
            data = message.getStringProperty("dataChange");
            if (tmsTransportQconfig.isListenerDataChangedFilterSwitch()) {
                Map<String, Object> jsonObject = JacksonSerializer.INSTANCE().deserialize(data, Map.class);
                long otterSendTime = jsonObject.containsKey("otterSendTime") ? Long.valueOf(jsonObject.get("otterSendTime").toString()) : Integer.MAX_VALUE;
                if (tmsTransportQconfig.getListenerDataChangedTime() > otterSendTime) {
                    logger.info("dcstransportdbDataChanged", "filter otterSendTime：" + otterSendTime + "，MessageId：" + message.getMessageId());
                    return;
                }
            }

            DataChange dataChange = JacksonSerializer.INSTANCE().deserialize(data, DataChange.class);
            //司机车辆证件审核表变更处理
            tmsCertificateCheckHandle(dataChange);

            if (!ModRecordConstant.modRecordRrdTypeMap.containsKey(dataChange.getTableName())) {
                logger.info("DriverCacheError","Params:{}",dataChange.getTableName());
                return;
            }

            //根据不同的表,处理缓存
            getCacheHandler(dataChange.getTableName()).refreshCache(dataChange);

            TmsModRecordHandler tmsModRecordHandler = contextConfig.getTmsModRecordHandler(dataChange.getTableName());

            tmsModRecordHandler.sendTmsDataChangeMsg(dataChange);
            if (dataChange.getTableName().equals(ModRecordConstant.TableName.drvDriver)) {
                Long drvId = null;
                Long cityId = null;
                for (ColumnData columnData : dataChange.getAfterColumnList()) {
                    if (fieldDrvId.equals(columnData.getName())) {
                        drvId = Long.valueOf(columnData.getValue());
                    }
                    if (fieldCityId.equals(columnData.getName())) {
                        cityId = Long.valueOf(columnData.getValue());
                    }
                }
                if (cityId != null && cityId.longValue() == PushDataCommandService.HangZhouCityId.longValue()) {
                    pushDataCommandService.prepare4pushDrvUpdateData(drvId, CommonEnum.RecordTypeEnum.DRIVER);
                    Integer modeType = tmsModRecordHandler.initModType(dataChange);
                    if (Objects.equals(modeType,0)) {
                        pushDataCommandService.prepare4DrvIncrease(drvId);
                    }
                }
                if(drvId != null && drvId > 0) {
                    tmsQmqProducerCommandService.sendDrvDspStatusChangeQmq(drvId, TmsTransportConstant.DrvDspStatusEnum.DRV);
                }
                driverCacheServiceV2.clearDrvCache(drvId);
            }

            if (dataChange.getTableName().equals(ModRecordConstant.TableName.vehVehicle)) {
                Long vehicleId = null;
                Long cityId = null;
                for (ColumnData columnData : dataChange.getAfterColumnList()) {
                    if (fieldCityId.equals(columnData.getName())) {
                        cityId = Long.valueOf(columnData.getValue());
                    }
                    if (fieldVehicleId.equals(columnData.getName())) {
                        vehicleId = Long.valueOf(columnData.getValue());
                        break;
                    }
                }
                if (cityId != null && cityId.longValue() == PushDataCommandService.HangZhouCityId.longValue()) {
                    pushDataCommandService.prepare4pushDrvUpdateData(vehicleId,CommonEnum.RecordTypeEnum.VEHICLE);
                }
                driverCacheServiceV2.clearVehCache(vehicleId);
            }

            if (dataChange.getTableName().equals(ModRecordConstant.TableName.drvDriverLeave)) {
                //请假 或  销假
                String operateType = getOperateTypeForLeave(dataChange);
                String driverLeaveId = getColumnValue(FIELD_DRIVER_LEAVE_ID,dataChange.getAfterColumnList());
                Long leaveId = StringUtils.isEmpty(driverLeaveId)?0:Long.valueOf(driverLeaveId);
                tmsQmqProducerCommandService.sendDrvDspStatusChangeQmq(operateType,leaveId,getDrvId(dataChange.getAfterColumnList()), TmsTransportConstant.DrvDspStatusEnum.DRV_LEAVE);
            }

            if (dataChange.getTableName().equals(ModRecordConstant.TableName.tmsDrvFreeze)) {
                //冻结 或  取消冻结  修改冻结时间
                String operateType = getOperateTypeForFreeze(dataChange);
                tmsQmqProducerCommandService.sendDrvDspStatusChangeQmq(operateType,null,getDrvId(dataChange.getAfterColumnList()), TmsTransportConstant.DrvDspStatusEnum.DRV_FREEZE);
            }

            TmsModRecordPO tmsModRecordPO = tmsModRecordHandler.handle(dataChange);

            if (Objects.isNull(tmsModRecordPO)) {
                return;
            }

            modRecordRespository.getTmsModRecordRepo().insert(tmsModRecordPO);
        } catch (Exception e) {
            TransportMetric.consistencyCounter.inc();
            logger.warn("ConsistencyError", "params:{} error:{}", JsonUtil.toJson(data), e);
            logger.error("ConsistencyCanalError","params:{} error:{}", ImmutableMap.of("consistency","error"), data, e);
            throw e;
        }
    }

    private CacheHandler getCacheHandler(String tableName) {
        return cacheHandlerList.stream().filter(cacheHandler -> cacheHandler.support(tableName)).findFirst().orElse(defaultCacheHandler);
    }

    /**
     * 冻结操作标识
     * @param dataChange
     * @return
     */
    private String getOperateTypeForFreeze(DataChange dataChange ){
        if(dataChange.isInsert()){
            return "freeze";
        }
        //2冻结1解冻
        String status = getColumnValue("freeze_status",dataChange.getAfterColumnList());
        if(dataChange.isUpate() && "1".equals(status)){
            return "unfreeze";
        }
        if(dataChange.isUpate() && "2".equals(status)){
            return "freeze";
        }
        return "none";
    }

    /**
     * 请假操作标识
     * @param dataChange
     * @return
     */
    private String getOperateTypeForLeave(DataChange dataChange ){
        if(dataChange.isInsert()){
            return "leave";
        }
        //0无效1有效
        String status = getColumnValue("active",dataChange.getAfterColumnList());
        if(dataChange.isUpate() && "0".equals(status)){
            return "unLeave";
        }
        return "none";
    }

    /**
     * 查询司机id
     * @param dataList
     * @return
     */
    private Long getDrvId(List<ColumnData> dataList) {
        Long drvId = 0L;
        if (CollectionUtils.isEmpty(dataList)) {
            return drvId;
        }
        for (ColumnData columnData : dataList) {
            if (fieldDrvId.equals(columnData.getName())) {
                drvId = Long.valueOf(columnData.getValue());
                break;
            }
        }
        return drvId;
    }

    /**
     * 获取字段值
     * @param columnName
     * @param dataList
     * @return
     */
    private String getColumnValue(String columnName,List<ColumnData> dataList){
        if (CollectionUtils.isEmpty(dataList)) {
            return null;
        }
        for (ColumnData columnData : dataList) {
            if (columnName.equals(columnData.getName())) {
                return columnData.getValue();
            }
        }
        return null;
    }
    /**
     * 司机车辆证件审核表变更处理
     * @param dataChange
     */
    private void tmsCertificateCheckHandle(DataChange dataChange){
        try{
            logger.info("tmsCertificateCheckHandle_start ",JsonUtil.toJson(dataChange));
            if (dataChange.getTableName().equals(ModRecordConstant.TableName.tmsCertificateCheck)) {
                Integer checkType = null;
                Long checkId = null;
                for (ColumnData columnData : dataChange.getAfterColumnList()) {
                    if (fieldCheckType.equals(columnData.getName())) {
                        checkType = Integer.valueOf(columnData.getValue());
                    }if (fieldCheckId.equals(columnData.getName())) {
                        checkId = Long.valueOf(columnData.getValue());
                    }
                }
                if (checkType != null && TmsTransportConstant.CertificateCheckTypeEnum.DRV.getCode().intValue() == checkType.intValue()) {
                    driverCacheServiceV2.clearDrvCache(checkId);
                    logger.info("tmsCertificateCheckHandle_driverUpdate",checkId.toString());
                } else if (checkType != null && TmsTransportConstant.CertificateCheckTypeEnum.VEHICLE.getCode().intValue() == checkType.intValue()) {
                    driverCacheServiceV2.clearVehCache(checkId);
                    logger.info("tmsCertificateCheckHandle_vehicleUpdate",checkId.toString());
                }
            }
        }catch (Exception e){
            logger.info("tmsCertificateCheckHandle_excep",JsonUtil.toJson(dataChange));
            logger.error("tmsCertificateCheckHandle_excep",e);
        }
    }
}
