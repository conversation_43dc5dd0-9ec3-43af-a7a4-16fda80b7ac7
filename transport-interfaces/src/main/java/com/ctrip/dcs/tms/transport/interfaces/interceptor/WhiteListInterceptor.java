package com.ctrip.dcs.tms.transport.interfaces.interceptor;

import com.ctrip.dcs.tms.transport.infrastructure.common.monitoring.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.udl.UDLHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.udl.dto.DrvUdlMethodRuleDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.udl.qconfig.DrvUdlQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.framework.foundation.Foundation;
import com.ctrip.framework.ucs.client.ShardingKey;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.infrastructure.executor.*;
import com.ctrip.igt.framework.soa.server.interceptor.*;
import com.dianping.cat.Cat;
import com.google.common.base.*;
import com.google.common.collect.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.*;
import org.springframework.core.annotation.*;
import org.springframework.stereotype.*;

import java.lang.reflect.Field;
import java.util.*;
import java.util.Objects;

/**
 * 资源统计
 * <AUTHOR>
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE + 3)
public class WhiteListInterceptor<T, R> implements ServiceInterceptor<T, R> {

    private static final Logger logger = LoggerFactory.getLogger(WhiteListInterceptor.class);

    private static final List<String> whiteList = Lists.newArrayList("serialVersionUID", "SCHEMA", "requestHeader", "$jacocoData");

    @Autowired
    DrvUdlQconfig drvUdlQconfig;

    @Autowired
    UDLHandler udlHandler;

    @Autowired
    TrafficDiverter diverter;

    private final List<String> drvUdlMethodList = Lists.newArrayList();

    @Override
    public R handle(Executor<T, R> executor, T req) {
        String clientAppId = PlatformUtil.getAppName();
        String operationName = PlatformUtil.getMethodName();

        try {
            /**
             * 放行checkHealth请求
             */
            if (!(req instanceof HasIGTRequestHeader)) {
                return executor.execute(req);
            }
            /**
             * 放行无效请求服务的 app.id or 无效服务名称
             */
            if (Strings.isNullOrEmpty(clientAppId) || Strings.isNullOrEmpty(operationName)) {
                return executor.execute(req);
            }
            /**
             * trace key
             */
            Map<String, String> attrs = Maps.newHashMap();
            attrs.put("clientAppId", clientAppId);
            attrs.put("methodName", operationName);

            try {
                Cat.logEvent("params", operationName + ":" + getFieldValues(req));
                logUdl(req, PlatformUtil.getOperation());
            }catch (Exception e) {
                //DO NOTHING
            }
            /**
             * 统计常规观察接口调用分布
             */
            TransportMetric.operationRecordInc(operationName, attrs);
            /**
             * 测试观察 FIXME 观察后摘除 重复好用的轮子~
             */
            logger.info("InterfaceRecordReq", "Params: appId:{} methodName:{} reqParams:{}", attrs, clientAppId, operationName, JsonUtil.toJson(req));
        } catch (Exception e) {
            logger.error("WhiteListInterceptorError", "Params: appId:{} methodName:{} reqParams:{} error:{}", clientAppId, operationName, JsonUtil.toJson(req), e);
        }
        return executor.execute(req);
    }

    public String getFieldValues(Object object) throws IllegalAccessException {
        StringBuilder sb = new StringBuilder();
        Field[] fields = object.getClass().getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            if (whiteList.contains(field.getName())) {
                continue;
            }
            Object value = field.get(object);

            if (value != null) {
                sb.append(field.getName()).append("|");
            }
        }

        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1); // 移除最后一个分隔符
        }

        return sb.toString();
    }

    public void logUdl(Object req, String operationName) {
        try {
            if (!drvUdlQconfig.getRule().getOpen()) {
                return;
            }

            // udl为空，且是需要上云的方法列表，则进行埋点提示
            String udlVal = Cat.getTraceContext(true).getContext().get(ShardingKey.UDL.getTraceContextKey());
            if (udlVal == null && getDrvUldMethodList().contains(operationName)) {
                logUdlErrorInfo(operationName, PlatformUtil.getClientAppId());
            }

            DrvUdlMethodRuleDTO ruleDTO = drvUdlQconfig.getRule().getRule().get(operationName);
            if (ruleDTO == null) {
                return;
            }

            // 如果有流量控制，且流量大于百分比，则不进行埋点
            if (ruleDTO.getPercentage() != null) {
                if (diverter.greatThan(ruleDTO.getPercentage())) {
                    return;
                }
            }

            //找到对应的key，并查询真实的udl，然后做比对
            Field[] fields = req.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                if (Objects.equals(ruleDTO.getUdlKey(), field.getName())) {
                    Object val = field.get(req);
                    if (Objects.isNull(val)) {
                        return;
                    }

                    Long keyValue = null;
                    switch (ruleDTO.getKeyType()) {
                        case "String":
                            String[] values = String.valueOf(val).split(",");
                            if (values.length > 0) {
                                String value = values[0];
                                keyValue = Long.valueOf(value);
                            }
                            break;
                        case "Long":
                            keyValue = Long.valueOf(String.valueOf(val));
                            break;
                        default:
                            break;
                    }

                    //如果上下文的udl值为空，则设置udl值为境内
                    if (udlVal == null) {
                        udlVal = UDLHandler.UdlEnum.CN_CSPD.getUdl();
                    }

                    // 分页查询，不进行udl的判断
                    if (keyValue == null && Objects.equals("page", ruleDTO.getKeyValueType())) {
                        return;
                    }

                    String realUdl = null;
                    if (Objects.equals("drvId", ruleDTO.getKeyValueType())) {
                        realUdl = udlHandler.getDrvUdl(keyValue);
                    }
                    if (Objects.equals("cityId", ruleDTO.getKeyValueType())) {
                        realUdl = udlHandler.getDrvUdlByCityId(keyValue);
                    }

                    if (!Objects.equals(realUdl, udlVal)) {
                        logUdlErrorInfo(operationName, PlatformUtil.getClientAppId());
                    }
                    return;
                }
            }
        } catch (Exception e) {

        }
    }

    protected void logUdlErrorInfo(String operationName, String clientAppId) {
        Cat.logEvent("udl_error", operationName + ":" + clientAppId);
    }

    protected List<String> getDrvUldMethodList() {
        if (drvUdlMethodList.isEmpty()) {
            drvUdlMethodList.addAll(new ArrayList<>(drvUdlQconfig.getRule().getRule().keySet()));
        }
        return drvUdlMethodList;
    }

}
