package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryDrvVehLegendListSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvVehLegendListSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvVehLegendListSOAVO;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class QueryDrvVehLegendListExecutor extends AbstractRpcExecutor<QueryDrvVehLegendListSOARequestType, QueryDrvVehLegendListSOAResponseType> implements Validator<QueryDrvVehLegendListSOARequestType> {

    @Autowired
    DriverQueryService driverQueryService;

    @Override
    public QueryDrvVehLegendListSOAResponseType execute(QueryDrvVehLegendListSOARequestType requestType) {
        QueryDrvVehLegendListSOAResponseType responseType = new QueryDrvVehLegendListSOAResponseType();
        Result<List<QueryDrvVehLegendListSOAVO>> result = driverQueryService.queryDrvVehLegendList();
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType);
    }

    @Override
    public void validate(AbstractValidator<QueryDrvVehLegendListSOARequestType> validator) {
//        validator.ruleFor("supplierId").notNull();
    }
}
