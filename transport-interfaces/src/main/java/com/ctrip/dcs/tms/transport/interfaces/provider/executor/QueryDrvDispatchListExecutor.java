package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryDrvDispatchListSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDispatchListSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDispatchListSOAVO;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class QueryDrvDispatchListExecutor extends AbstractRpcExecutor<QueryDrvDispatchListSOARequestType, QueryDrvDispatchListSOAResponseType> implements Validator<QueryDrvDispatchListSOARequestType> {

    @Autowired
    DriverQueryService driverQueryService;

    @Override
    public QueryDrvDispatchListSOAResponseType execute(QueryDrvDispatchListSOARequestType requestType) {
        QueryDrvDispatchListSOAResponseType responseType = new QueryDrvDispatchListSOAResponseType();
        Result<List<QueryDrvDispatchListSOAVO>> result = driverQueryService.queryDrvDispatchList(requestType);
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<QueryDrvDispatchListSOARequestType> validator) {
        validator.ruleFor("drvIds").notNull();
    }
}
