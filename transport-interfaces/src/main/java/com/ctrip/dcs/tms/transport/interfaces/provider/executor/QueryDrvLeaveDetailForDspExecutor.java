package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.handler.cache.AbstraceCacheHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.google.common.base.*;
import com.google.common.collect.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import org.springframework.util.*;

import java.util.*;
import java.util.stream.Collectors;

import static com.ctrip.dcs.tms.transport.infrastructure.common.util.ResponseResultUtil.logQueryResult;

/**
 * 请假详情查询（给派发使用）
 * <AUTHOR>
 * @Date 2020/2/25 20:45
 */
@Component
public class QueryDrvLeaveDetailForDspExecutor extends AbstractRpcExecutor<QueryDrvLeaveDetailForDspSOARequestType, QueryDrvLeaveDetailForDspSOAResponseType> {

    @Autowired
    private DriverLeaveQueryService driverLeaveQueryService;

    @Override
    public QueryDrvLeaveDetailForDspSOAResponseType execute(QueryDrvLeaveDetailForDspSOARequestType queryDrvLeaveDetailForDspSOARequestType) {
        QueryDrvLeaveDetailForDspSOAResponseType responseType = new QueryDrvLeaveDetailForDspSOAResponseType();
        List<DrvLeaveDetailSOAType> list = Lists.newArrayList();
        List<Long> drvIds = Lists.newArrayList();
        if (!Strings.isNullOrEmpty(queryDrvLeaveDetailForDspSOARequestType.getDrvIds())) {
            Set<String> set = StringUtils.commaDelimitedListToSet(queryDrvLeaveDetailForDspSOARequestType.getDrvIds());
            for (String id : set) {
                drvIds.add(Long.parseLong(id));
            }
        }
        Result<List<DrvLeaveDetailPO>> result = driverLeaveQueryService.queryDrvLeaveDetailForDsp(drvIds, queryDrvLeaveDetailForDspSOARequestType.isUseCache());
        for (DrvLeaveDetailPO drvLeaveDetailPO : result.getData()) {
            list.add(poToDetailType(drvLeaveDetailPO));
        }
        responseType.setData(list.stream().sorted(Comparator.comparing(DrvLeaveDetailSOAType::getDrvId)).collect(
          Collectors.toList()));
        return ServiceResponseUtils.success(responseType);
    }

    private DrvLeaveDetailSOAType poToDetailType(DrvLeaveDetailPO po){
        DrvLeaveDetailSOAType drvLeaveDetailType = new DrvLeaveDetailSOAType();
        drvLeaveDetailType.setId(po.getId());
        drvLeaveDetailType.setDrvId(po.getDrvId());
        drvLeaveDetailType.setLeaveBeginTime(DateUtil.timestampToString(po.getLeaveBeginTime(), DateUtil.YYYYMMDDHHMMSS));
        drvLeaveDetailType.setLeaveEndTime(DateUtil.timestampToString(po.getLeaveEndTime(), DateUtil.YYYYMMDDHHMMSS));
        drvLeaveDetailType.setLeaveReason(po.getLeaveReason());
        drvLeaveDetailType.setCreateUser(po.getCreateUser());
        drvLeaveDetailType.setModifyUser(po.getModifyUser());
        drvLeaveDetailType.setOperateType(po.getOperateType());
        drvLeaveDetailType.setStatus(po.getLeaveStatus());
        drvLeaveDetailType.setIsActive(po.getActive() ? 1:0);
        drvLeaveDetailType.setDatachangeCreatetime(DateUtil.timestampToString(po.getDatachangeCreatetime(), DateUtil.YYYYMMDDHHMMSS));
        drvLeaveDetailType.setDatachangeLasttime(DateUtil.timestampToString(po.getDatachangeLasttime(), DateUtil.YYYYMMDDHHMMSS));
        drvLeaveDetailType.setDatachangeDeltime(DateUtil.timestampToString(po.getDatachangeDeltime(), DateUtil.YYYYMMDDHHMMSS));
        return drvLeaveDetailType;
    }

    @Override
    public void onExecuted(QueryDrvLeaveDetailForDspSOARequestType req, QueryDrvLeaveDetailForDspSOAResponseType resp) {
        AbstraceCacheHandler.setExtension(resp);
        logQueryResult(resp.getData());
    }
}
