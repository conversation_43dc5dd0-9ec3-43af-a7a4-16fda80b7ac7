package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QuerySupplierIdBySkuIdRequestType;
import com.ctrip.dcs.tms.transport.api.model.QuerySupplierIdBySkuIdResponseType;
import com.ctrip.dcs.tms.transport.api.model.SkuSupplierIdSOADTO;
import com.ctrip.dcs.tms.transport.application.dto.SkuTransportGroupDTO;
import com.ctrip.dcs.tms.transport.application.query.IQuerySupplierIdBySkuIdService;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 通过SKUid来返回关联的已上线运力组的所有供应商id
 */
@Component
public class QuerySupplierIdBySkuIdExecutor extends AbstractRpcExecutor<QuerySupplierIdBySkuIdRequestType, QuerySupplierIdBySkuIdResponseType> implements Validator<QuerySupplierIdBySkuIdRequestType> {
    private static final Logger logger = LoggerFactory.getLogger(QuerySupplierIdBySkuIdExecutor.class);
    @Autowired
    private IQuerySupplierIdBySkuIdService querySupplierIdBySkuIdService;
    @Override
    public QuerySupplierIdBySkuIdResponseType execute(QuerySupplierIdBySkuIdRequestType requestType) {
        try{
            if(requestType.getSkuIds().size() > 10){
                logger.error("QuerySupplierIdBySkuIdExecutor_skuId_max10", JsonUtil.toJson(requestType),new HashMap<>());
                return ServiceResponseUtils.fail(new QuerySupplierIdBySkuIdResponseType());
            }
            Map<Long, SkuTransportGroupDTO> skuSupplierMap = querySupplierIdBySkuIdService.querySupplierIdBySkuId(requestType.getSkuIds());
            return ServiceResponseUtils.success(getResult(skuSupplierMap));
        }catch (BizException be){
            return ServiceResponseUtils.fail(new QuerySupplierIdBySkuIdResponseType());
        }catch (Exception e){
            logger.error("QuerySupplierIdBySkuIdExecutor_ex", JsonUtil.toJson(requestType),e,new HashMap<>());
            return ServiceResponseUtils.fail(new QuerySupplierIdBySkuIdResponseType());
        }
    }
    @Override
    public void validate(AbstractValidator<QuerySupplierIdBySkuIdRequestType> validator) {
        validator.ruleFor("skuIds").notNull().notEmpty();
    }

    /**
     * 封装返回结果
     * @param skuSupplierMap
     * @return
     */
    private QuerySupplierIdBySkuIdResponseType getResult(Map<Long, SkuTransportGroupDTO> skuSupplierMap){
        QuerySupplierIdBySkuIdResponseType responseType = new QuerySupplierIdBySkuIdResponseType();
        if(CollectionUtils.isEmpty(skuSupplierMap)){
            responseType.setSupplierIdList(new ArrayList<>());
            return responseType;
        }
        List<SkuSupplierIdSOADTO> result = new ArrayList<>();
        for (Map.Entry<Long, SkuTransportGroupDTO> en : skuSupplierMap.entrySet()) {
            SkuSupplierIdSOADTO skuSupplierIdSOADTO = new SkuSupplierIdSOADTO();
            skuSupplierIdSOADTO.setSkuId(en.getKey());
            skuSupplierIdSOADTO.setSupplierIdList(new ArrayList<>(en.getValue().getSupplierIdList()));
            skuSupplierIdSOADTO.setEmailList(new ArrayList<>(en.getValue().getEmailList()));
            result.add(skuSupplierIdSOADTO);
        }
        responseType.setSupplierIdList(result);
        return responseType;
    }
}
