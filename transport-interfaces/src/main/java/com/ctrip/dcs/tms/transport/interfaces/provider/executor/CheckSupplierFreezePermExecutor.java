package com.ctrip.dcs.tms.transport.interfaces.provider.executor;


import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 校验供应商是否有操作司机冻结权限
 */
@Component
public class CheckSupplierFreezePermExecutor extends AbstractRpcExecutor<CheckSupplierFreezePermSOARequestType, CheckSupplierFreezePermSOAResponseType> implements Validator<CheckSupplierFreezePermSOARequestType> {

    @Autowired
    DrvFreezeQueryService queryService;

    @Override
    public CheckSupplierFreezePermSOAResponseType execute(CheckSupplierFreezePermSOARequestType requestType) {
        CheckSupplierFreezePermSOAResponseType responseType = new CheckSupplierFreezePermSOAResponseType();
        Result<Boolean> result = queryService.checkSupplierFreezePerm(requestType.getDrvId(),requestType.getAccountType());
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType,"400",result.getMsg());
    }
}
