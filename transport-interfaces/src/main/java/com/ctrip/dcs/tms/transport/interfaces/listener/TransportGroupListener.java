package com.ctrip.dcs.tms.transport.interfaces.listener;

import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import qunar.tc.qmq.*;
import qunar.tc.qmq.consumer.annotation.*;

import java.sql.*;
import java.util.*;

/**
 * <AUTHOR>
 * @Description  运力组消费消息
 * @Date 13:59 2020/8/7
 * @Param 
 * @return 
 **/
@Component
public class TransportGroupListener {
    private static final Logger logger = LoggerFactory.getLogger(TransportGroupListener.class);

    @Autowired
    private TransportGroupRepository transportGroupRepository;
    @Autowired
    private TransportGroupCommandService transportGroupCommandService;
    @Autowired
    private EnumRepository enumRepository;

    /**
     * <AUTHOR>
     * @Description 品牌资质模块-合同维度删除服务区域的“城市”，检查关联逻辑
     *              检查该供应商该主合同下，配置该点位城市的运力组，将涉及的运力组全部置为“下线”（下线后的通知及资源、派发等相关逻辑不变）
     *              所有模式主合同：删除的城市，涉及运力组被下线后，【上线】按钮置灰不可点（目前仅在运营工作台可上下线运力组）
     * @Date 14:00 2020/8/7
     * @Param [message]
     * @return void
     **/
    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.SUBJECT_SERVEDSCOPE_CHANGE, consumerGroup = TmsTransportConstant.QMQ_CONSUMER_GROUP)
    public void offLineTransportGroup(Message message){
        try{
            Long contractId = message.getLongProperty("contractId");
            logger.info("offLineTransportGroup start---,contractId:{}",contractId);
            if (contractId == null) {
                logger.info("params is null,contractId:{}",contractId);
                return ;
            }

            List<TspTransportGroupPO> transportGroupPOList = transportGroupRepository.queryTransGByContractAndCity(contractId);
            if (CollectionUtils.isEmpty(transportGroupPOList)) {
                return ;
            }

            //查询合同对应的有效城市ID
            Map<Integer, Set<Long>> map = enumRepository.getServedScopeMapByContractId(contractId);
            List<Long> transportGroupIds = Lists.newArrayList();
            List<Long> disableTransportGroupIds = Lists.newArrayList();
            for (TspTransportGroupPO transportGroupPO : transportGroupPOList) {
                Set<Long> cityList = map.getOrDefault(transportGroupPO.getCategorySynthesizeCode(), Sets.newHashSet());
                //已删除城市操作,如果运力组对应的城市不在有效城市cityList中,并且是已上线状态，说明需要下线，并将运力组状态墨灰,禁止操作上线
                if(transportGroupPO.getPointCityId() > 0 && !cityList.contains(transportGroupPO.getPointCityId()) && Objects.equals(transportGroupPO.getGroupStatus(),
                        TmsTransportConstant.TransportGroupStatusEnum.ONLINE.getCode())){
                    transportGroupCommandService.updateTransportGroupStatus(buildTypeToGroupPO(transportGroupPO.getTransportGroupId()));
                }
                //新增城市操作,如果运力组对应的城市在有效城市cityList中，并且是已下线状态置灰标识,说明将置灰标识解除
                if(cityList.contains(transportGroupPO.getPointCityId()) && Objects.equals(transportGroupPO.getGroupStatus(),
                        TmsTransportConstant.TransportGroupStatusEnum.OFFLINE.getCode()) &&
                        Objects.equals(TmsTransportConstant.StatusDisableEnum.Disable.getCode(),transportGroupPO.getStatusDisable())){
                    transportGroupIds.add(transportGroupPO.getTransportGroupId());
                }
                //对已下线运力组不在服务区域的城市一并置为不可上线
                if (!cityList.contains(transportGroupPO.getPointCityId()) && Objects.equals(transportGroupPO.getGroupStatus(),
                        TmsTransportConstant.TransportGroupStatusEnum.OFFLINE.getCode()) &&
                        Objects.equals(TmsTransportConstant.StatusDisableEnum.Available.getCode(), transportGroupPO.getStatusDisable())) {
                    disableTransportGroupIds.add(transportGroupPO.getTransportGroupId());
                }
            }
            if (CollectionUtils.isNotEmpty(transportGroupIds)) {
                //解除运力组置灰状态
                transportGroupRepository.updateStatusDisable(transportGroupIds, TmsTransportConstant.StatusDisableEnum.Available.getCode());
            }
            if (CollectionUtils.isNotEmpty(disableTransportGroupIds)) {
                transportGroupRepository.updateStatusDisable(disableTransportGroupIds, TmsTransportConstant.StatusDisableEnum.Disable.getCode());
            }
        }catch (Exception e){
            logger.error("offLineTransportGroup error:{}",e.getLocalizedMessage());
        }
    }

    /**
     * 运力组基础信息PO
     * @param transportGroupId
     * @return
     */
    private TspTransportGroupPO buildTypeToGroupPO(Long transportGroupId){
        TspTransportGroupPO transportGroupPO = new TspTransportGroupPO();
        transportGroupPO.setTransportGroupId(transportGroupId);
        transportGroupPO.setGroupStatus(TmsTransportConstant.TransportGroupStatusEnum.OFFLINE.getCode());
        transportGroupPO.setModifyUser(TmsTransportConstant.TMS_DEFAULT_USERNAME);
        transportGroupPO.setDatachangeLasttime(new Timestamp(System.currentTimeMillis()));
        transportGroupPO.setStatusDisable(TmsTransportConstant.StatusDisableEnum.Disable.getCode());
        return transportGroupPO;
    }
}
