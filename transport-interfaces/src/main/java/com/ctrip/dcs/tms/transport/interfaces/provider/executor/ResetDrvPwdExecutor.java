package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2020/9/16 19:27
 */
@Component
public class ResetDrvPwdExecutor extends AbstractRpcExecutor<ResetDrvPwdRequestType, ResetDrvPwdResponseType>
    implements Validator<ResetDrvPwdRequestType> {

  @Autowired
  private DriverAccountCommandService accountCmdService;

  @Override
  public ResetDrvPwdResponseType execute(ResetDrvPwdRequestType request) {
    Result<DrvDriverPO> result = accountCmdService.resetPwd(request.getHybridLoginAccount());

    ResetDrvPwdResponseType response = new ResetDrvPwdResponseType();
    if (result.isSuccess() && Objects.nonNull(result.getData())) {
      DrvDriverPO driverPO = result.getData();
      if (Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(), driverPO.getInternalScope())){
        response.setNotifyType(ResetPwdNotifyType.MOBILE_PHONE);
        response.setNotifyAddress(TmsTransUtil.decrypt(driverPO.getDrvPhone(), KeyType.Phone));
      }else{
        response.setNotifyType(ResetPwdNotifyType.EMAIL);
        response.setNotifyAddress(TmsTransUtil.decrypt(driverPO.getEmail(), KeyType.Mail));
      }
      return ServiceResponseUtils.success(response);
    }
    return ServiceResponseUtils.fail(response, result.getCode(), result.getMsg());
  }

  @Override
  public void validate(AbstractValidator<ResetDrvPwdRequestType> validator) {
    validator.ruleFor("hybridLoginAccount").notNull().notEmpty();
  }
}
