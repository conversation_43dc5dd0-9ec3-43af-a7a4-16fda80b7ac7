package com.ctrip.dcs.tms.transport.interfaces.provider.executor;


import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 司机列表
 */
@Component
public class QueryDrvByMuSelConditionsExecutor extends AbstractRpcExecutor<QueryDrvByMuSelConditionsSOARequestType, QueryDrvByMuSelConditionsSOAResponseType> implements Validator<QueryDrvByMuSelConditionsSOARequestType> {

    private static final Logger logger = LoggerFactory.getLogger(QueryDrvByMuSelConditionsExecutor.class);

    private static final String SPLIT = ",";

    @Autowired
    DrvDrvierRepository repository;
    @Autowired
    DriverQueryService driverQueryService;

    @Override
    public QueryDrvByMuSelConditionsSOAResponseType execute(QueryDrvByMuSelConditionsSOARequestType requestType) {
        QueryDrvByMuSelConditionsSOAResponseType responseType = new QueryDrvByMuSelConditionsSOAResponseType();
        Result<QueryDrvByMuSelConditionsSOAResponseType> result = driverQueryService.queryDrvByMuSelConditions(requestType);
        if(result.isSuccess()){
            return ServiceResponseUtils.success(result.getData());
        }
        return ServiceResponseUtils.fail(responseType);
    }

    @Override
    public void validate(AbstractValidator<QueryDrvByMuSelConditionsSOARequestType> validator) {
//        validator.ruleFor("drvLanguage").notNull();
    }
}
