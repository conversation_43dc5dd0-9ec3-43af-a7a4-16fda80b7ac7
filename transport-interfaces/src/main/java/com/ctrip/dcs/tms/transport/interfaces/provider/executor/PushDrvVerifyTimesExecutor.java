package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

@Component
public class PushDrvVerifyTimesExecutor extends AbstractRpcExecutor<DrvVerifyTimesSOARequestType, DrvVerifyTimesSOAResponseType> implements Validator<DrvVerifyTimesSOARequestType> {

    @Autowired
    TmsVerifyEventCommandService eventCommandService;

    @Override
    public DrvVerifyTimesSOAResponseType execute(DrvVerifyTimesSOARequestType requestType) {
        DrvVerifyTimesSOAResponseType responseType = new DrvVerifyTimesSOAResponseType();
        Result<Boolean> result = eventCommandService.pushDrvVerifyTimes(requestType);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<DrvVerifyTimesSOARequestType> validator) {
        validator.ruleFor("verifyEventId").notNull();
    }
}
