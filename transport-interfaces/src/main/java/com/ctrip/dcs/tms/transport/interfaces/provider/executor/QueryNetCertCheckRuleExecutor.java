package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.NetCertCheckRuleSOADTO;
import com.ctrip.dcs.tms.transport.api.model.NetCertNoCheckRuleDetailSOADTO;
import com.ctrip.dcs.tms.transport.api.model.QueryNetCertNoCheckRuleSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryNetCertNoCheckRuleSOAResponseType;
import com.ctrip.dcs.tms.transport.application.query.INetCertNoCheckRuleService;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.NetCertCheckRuleDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.NetCertNoCheckRuleDetailDTO;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class QueryNetCertCheckRuleExecutor extends AbstractRpcExecutor<QueryNetCertNoCheckRuleSOARequestType, QueryNetCertNoCheckRuleSOAResponseType> implements Validator<QueryNetCertNoCheckRuleSOARequestType> {
    @Resource
    private INetCertNoCheckRuleService netCertNoCheckRuleService;
    @Override
    public QueryNetCertNoCheckRuleSOAResponseType execute(QueryNetCertNoCheckRuleSOARequestType requestType) {
        NetCertCheckRuleDTO netCertCheckRuleDTO = netCertNoCheckRuleService.queryNetCertNoCheckRule(requestType.getCityId().toString());
        return ServiceResponseUtils.success(getResponseType(netCertCheckRuleDTO));
    }
    @Override
    public void validate(AbstractValidator<QueryNetCertNoCheckRuleSOARequestType> validator, QueryNetCertNoCheckRuleSOARequestType req) {
        validator.ruleFor("cityId").notNull();
    }

    /**
     * 组装返回结果
     * @param netCertCheckRuleDTO
     * @return
     */
    private QueryNetCertNoCheckRuleSOAResponseType getResponseType(NetCertCheckRuleDTO netCertCheckRuleDTO){
        QueryNetCertNoCheckRuleSOAResponseType responseType = new QueryNetCertNoCheckRuleSOAResponseType();
        if(netCertCheckRuleDTO == null){
            return responseType;
        }
        NetCertCheckRuleSOADTO data = new NetCertCheckRuleSOADTO();
        if(!CollectionUtils.isEmpty(netCertCheckRuleDTO.getDriverNetCertNoCheckRule())){
            List<NetCertNoCheckRuleDetailSOADTO> netCertNoCheckRuleDetailSOADTOList = new ArrayList<>();
            for (NetCertNoCheckRuleDetailDTO netCertNoCheckRuleDetailDTO : netCertCheckRuleDTO.getDriverNetCertNoCheckRule()) {
                NetCertNoCheckRuleDetailSOADTO netCertNoCheckRuleDetailSOADTO = new NetCertNoCheckRuleDetailSOADTO();
                BeanUtils.copyProperties(netCertNoCheckRuleDetailDTO,netCertNoCheckRuleDetailSOADTO);
                netCertNoCheckRuleDetailSOADTOList.add(netCertNoCheckRuleDetailSOADTO);
            }
            data.setDriverNetCertNoCheckRule(netCertNoCheckRuleDetailSOADTOList);
        }
        if(!CollectionUtils.isEmpty(netCertCheckRuleDTO.getVehicleNetCertNoCheckRule())){
            List<NetCertNoCheckRuleDetailSOADTO> netCertNoCheckRuleDetailSOADTOList = new ArrayList<>();
            for (NetCertNoCheckRuleDetailDTO netCertNoCheckRuleDetailDTO : netCertCheckRuleDTO.getVehicleNetCertNoCheckRule()) {
                NetCertNoCheckRuleDetailSOADTO netCertNoCheckRuleDetailSOADTO = new NetCertNoCheckRuleDetailSOADTO();
                BeanUtils.copyProperties(netCertNoCheckRuleDetailDTO,netCertNoCheckRuleDetailSOADTO);
                netCertNoCheckRuleDetailSOADTOList.add(netCertNoCheckRuleDetailSOADTO);
            }
            data.setVehicleNetCertNoCheckRule(netCertNoCheckRuleDetailSOADTOList);
        }
        data.setVehicleNetCertNoExample(netCertCheckRuleDTO.getVehicleNetCertNoExample());
        data.setDriverNetCertNoExample(netCertCheckRuleDTO.getDriverNetCertNoExample());
        responseType.setData(data);
        return responseType;
    }
}
