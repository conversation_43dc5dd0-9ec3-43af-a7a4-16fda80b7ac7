package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.dianping.cat.Cat;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

@Component
public class SendVerificationPhoneExecutor extends AbstractRpcExecutor<SendVerificationPhoneSOARequestType, SendVerificationPhoneSOAResponseType> {

    @Autowired
    private CommonCommandService commonCommandService;

    @Override
    public SendVerificationPhoneSOAResponseType execute(SendVerificationPhoneSOARequestType sendVerificationPhoneSOARequestType) {
        SendVerificationPhoneSOAResponseType responseType = new SendVerificationPhoneSOAResponseType();
        Result<String> result = commonCommandService.sendMessageByPhone4China(sendVerificationPhoneSOARequestType);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(responseType);
        } else {
            responseType.setCode(result.getData());
            Cat.logEvent("sendMessageByPhone4China", "-1");
            return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
        }
    }

}
