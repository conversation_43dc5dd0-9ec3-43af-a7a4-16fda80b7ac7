package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctrip.platform.dal.dao.annotation.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 添加司机
 * <AUTHOR>
 */
@Component
public class AddTmsDrvFreezeExecutor extends AbstractRpcExecutor<TmsDrvFreezeAddSOARequestType, TmsDrvFreezeAddSOAResponseType> implements Validator<TmsDrvFreezeAddSOARequestType> {

    @Autowired
    private TmsDrvFreezeCommandService commandService;

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public TmsDrvFreezeAddSOAResponseType execute(TmsDrvFreezeAddSOARequestType requestType) {
        TmsDrvFreezeAddSOAResponseType responseType = new TmsDrvFreezeAddSOAResponseType();
        Result<Boolean> result = commandService.addDrvFreeze(requestType, false);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(responseType);
        } else {
            return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<TmsDrvFreezeAddSOARequestType> validator) {
        validator.ruleFor("totalHours").notNull();
    }

}