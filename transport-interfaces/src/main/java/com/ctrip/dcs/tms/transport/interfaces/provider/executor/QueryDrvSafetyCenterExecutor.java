package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.constant.*;
import com.ctrip.igt.framework.infrastructure.context.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import com.fasterxml.jackson.core.type.*;
import com.google.common.base.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang.time.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.Objects;
import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class QueryDrvSafetyCenterExecutor extends AbstractRpcExecutor<QueryDrvSafetyCenterSOARequestType, QueryDrvSafetyCenterSOAResponseType> implements Validator<QueryDrvSafetyCenterSOARequestType> {

    private static final Logger logger = LoggerFactory.getLogger(QueryDrvSafetyCenterExecutor.class);

    @Autowired
    private DrvDrvierRepository repository;
    @Autowired
    private TmsDrvFreezeRepository freezeRepository;
    @Autowired
    private DrvSafetyService drvSafetyService;
    @Autowired
    private DriverLeaveQueryService driverLeaveQueryService;

    @Autowired
    private TmsVerifyEventRepository eventRepository;

    @Override
    public QueryDrvSafetyCenterSOAResponseType execute(QueryDrvSafetyCenterSOARequestType requestType) {
        QueryDrvSafetyCenterSOAResponseType responseType = new QueryDrvSafetyCenterSOAResponseType();
        DrvDriverPO driverPO = repository.queryByPk(requestType.getDrvId());
        if (driverPO == null || driverPO.getDrvStatus() == null) {
            return ServiceResponseUtils.fail(responseType, ServiceResponseConstants.ResStatus.EXCEPTION_CODE, SharkUtils.getSharkValue(SharkKeyConstant.transportDrvisEmpty));
        }
        if (Objects.equals(driverPO.getDrvStatus().intValue(), TmsTransportConstant.DrvStatusEnum.OFFLINE.getCode().intValue())) {
            return ServiceResponseUtils.fail(responseType, ServiceResponseConstants.ResStatus.EXCEPTION_CODE, SharkUtils.getSharkValue(SharkKeyConstant.transportOperatingFail));
        }
        if (Objects.equals(driverPO.getDrvStatus().intValue(), TmsTransportConstant.DrvStatusEnum.FREEZE.getCode().intValue())) {
            TmsDrvFreezePO freezePO = freezeRepository.queryByPk(driverPO.getDrvId());
            if (freezePO == null || freezePO.getFreezeStatus() == null || !Objects.equals(freezePO.getFreezeStatus().intValue(), TmsTransportConstant.FreezeStatusEnum.FREEZE.getValue().intValue())) {
                return ServiceResponseUtils.fail(responseType, ServiceResponseConstants.ResStatus.EXCEPTION_CODE, SharkUtils.getSharkValue(SharkKeyConstant.drvFreezeStatusError));
            }
            Date unFreezeTimeDate = DateUtils.addHours(freezePO.getFirstFreezeTime(), freezePO.getFreezeHour());
            String unFreezeTimeStr = DateUtil.dateToString(unFreezeTimeDate, DateUtil.CH_MMDDHHMM);
            if (Objects.equals(freezePO.getUnfreezeAction().intValue(), TmsTransportConstant.UnfreezeActionEnum.UNFREEZEOFFLINE.getValue().intValue())) {
                responseType.setDrvStatus(TmsTransportConstant.DrvCenterStatusEnum.FREEZE_OFFLINE.getCode());
                responseType.setDrvStatusDescription(String.format(SharkUtils.getSharkValue(SharkKeyConstant.drv_freeze_offline_status_describe), unFreezeTimeStr));
            } else {
                responseType.setDrvStatus(TmsTransportConstant.DrvCenterStatusEnum.FREEZE_ONLINE.getCode());
                responseType.setDrvStatusDescription(String.format(SharkUtils.getSharkValue(SharkKeyConstant.drv_freeze_online_status_describe), unFreezeTimeStr));
            }
            responseType.setUnfreezeDate(DateUtil.dateToString(unFreezeTimeDate, DateUtil.YYYYMMDDHHMMSS));
            responseType.setUnfreezeReason(getFreezeReason(freezePO));
        } else if (Objects.equals(driverPO.getDrvStatus().intValue(), TmsTransportConstant.DrvStatusEnum.ONLINE.getCode().intValue())) {
            DrvLeaveDetailPO leaveDetailPO = driverLeaveQueryService.getDrvLeaveLately(driverPO.getDrvId());
            if (leaveDetailPO != null) {
                responseType.setLeaveDate(DateUtil.dateToString(leaveDetailPO.getLeaveBeginTime(), DateUtil.YYYYMMDDHHMMSS));
                responseType.setDrvStatusDescription(String.format(SharkUtils.getSharkValue(SharkKeyConstant.drv_leave_status_describe), DateUtil.dateToString(leaveDetailPO.getLeaveEndTime(), DateUtil.CH_MMDDHHMM)));
                responseType.setDrvStatus(TmsTransportConstant.DrvCenterStatusEnum.LEAVE.getCode());
            } else {
                responseType.setDrvStatus(TmsTransportConstant.DrvCenterStatusEnum.NORMAL.getCode());
                responseType.setDrvStatusDescription("");
            }
        } else if (Objects.equals(driverPO.getDrvStatus().intValue(), TmsTransportConstant.DrvStatusEnum.UNACT.getCode().intValue())) {
            responseType.setDrvStatus(TmsTransportConstant.DrvCenterStatusEnum.UNACT.getCode());
            responseType.setDrvStatusDescription("");
        }
        deployVerifyStatus(responseType, driverPO);
        deployReportUploadStatus(responseType, driverPO.getDrvId());
        return ServiceResponseUtils.success(responseType);
    }

    private void deployVerifyStatus(QueryDrvSafetyCenterSOAResponseType responseType, DrvDriverPO driverPO) {
        List<TmsVerifyEventPO> faceEventList = eventRepository.queryWaitVerifyEvent(Arrays.asList(driverPO.getDrvId()), TmsTransportConstant.VerifyTypeEnum.FACE.getCode(), TmsTransportConstant.VerifyStatusEnum.NO_VERIFY.getCode(), null, false);
        responseType.setFaceVerify(judgeIsIndependent(faceEventList).getCode());
        if (driverPO.getVehicleId() == null) {
            responseType.setVehicleVerify(TmsTransportConstant.DrvVerifyStatusEnum.UNVERIFIED.getCode());
            return;
        }
        List<TmsVerifyEventPO> vehiclePOList = eventRepository.queryWaitVerifyEvent(Arrays.asList(driverPO.getVehicleId()), TmsTransportConstant.VerifyTypeEnum.VEHICLE.getCode(), TmsTransportConstant.VerifyStatusEnum.NO_VERIFY.getCode(), TmsTransportConstant.VerifyEventTypeEnum.SPOT_CHECK_EVENT.getCode(), false);
        responseType.setVehicleVerify(judgeIsIndependent(vehiclePOList).getCode());
    }

    public TmsTransportConstant.DrvVerifyStatusEnum judgeIsIndependent(List<TmsVerifyEventPO> eventPOList) {
        if (CollectionUtils.isEmpty(eventPOList)) {
            return TmsTransportConstant.DrvVerifyStatusEnum.VERIFIED;
        }
        Date now = new Date();
        for (TmsVerifyEventPO tmsVerifyEventPO : eventPOList) {
            if (Objects.equals(tmsVerifyEventPO.getVerifyReasonStatus(), TmsTransportConstant.VerifyEventTypeEnum.CONVENTIONAL_EVENT.getCode())) {
                return TmsTransportConstant.DrvVerifyStatusEnum.UNVERIFIED;
            }
            if (tmsVerifyEventPO.getNoticeTimes() != null &&
                    tmsVerifyEventPO.getNoticeTimes() > 0 &&
                    Objects.equals(tmsVerifyEventPO.getVerifyReasonStatus(), TmsTransportConstant.VerifyEventTypeEnum.SPOT_CHECK_EVENT.getCode())) {
                if (now.after(tmsVerifyEventPO.getVerifyEndTime())) {
                    return TmsTransportConstant.DrvVerifyStatusEnum.TIMEOUT;
                } else {
                    return TmsTransportConstant.DrvVerifyStatusEnum.UNVERIFIED;
                }
            }
        }
        return TmsTransportConstant.DrvVerifyStatusEnum.VERIFIED;
    }

    private void deployReportUploadStatus(QueryDrvSafetyCenterSOAResponseType responseType, Long drvId) {
        Result<Boolean> reportUploadResult = drvSafetyService.queryUploadReportStatus(drvId);
        if (reportUploadResult.isSuccess()) {
            if (reportUploadResult.getData()) {
                responseType.setEpidemicReportVerify(TmsTransportConstant.DrvEpidemicReportStatusEnum.UPLOADED.getCode());
            } else {
                responseType.setEpidemicReportVerify(TmsTransportConstant.DrvEpidemicReportStatusEnum.NOT_NEED.getCode());
            }
        } else {
            responseType.setEpidemicReportVerify(TmsTransportConstant.DrvEpidemicReportStatusEnum.HAVE_NOT_UPLOADED.getCode());
        }
    }

    private String getFreezeReason(TmsDrvFreezePO freezePO) {
        if (freezePO == null || Strings.isNullOrEmpty(freezePO.getFreezeReason())) {
            return "";
        }
        try {
            List<DrvFreezeReasonDTO> reasonDTOS = JsonUtil.fromJson(freezePO.getFreezeReason(), new TypeReference<List<DrvFreezeReasonDTO>>() {
            });
            if (CollectionUtils.isEmpty(reasonDTOS)) {
                return "";
            }
            StringBuilder stringBuilder = new StringBuilder(reasonDTOS.get(0).getFreezeReason());
            for (int i = 1; i < reasonDTOS.size(); i++) {
                stringBuilder.append(",");
                stringBuilder.append(reasonDTOS.get(i).getFreezeReason());
            }
            return stringBuilder.toString();
        } catch (Exception e) {
            logger.error("QueryDrvSafetyCenterAnalysisError", "DRV_ID:{} PARAMS:{} ERROR:{}", freezePO.getDrvId(), freezePO.getFreezeReason(), e);
            return "";
        }
    }

    @Override
    public void validate(AbstractValidator<QueryDrvSafetyCenterSOARequestType> validator) {
        validator.ruleFor("drvId").notNull();
    }
}