package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctriposs.baiji.exception.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import qunar.tc.qschedule.config.*;
import qunar.tc.schedule.*;

import java.util.*;
import java.util.stream.*;


/**
 * <AUTHOR>
 * @Description  司机招募定时刷新核验状态字段 check_status
 * @Date 20:04 2021/3/18
 * @Param 
 * @return 
 **/
@Component
public class DriverRecruitingCheckStatusSchedule {

    private static final Logger logger = LoggerFactory.getLogger(DriverRecruitingCheckStatusSchedule.class);

    @Autowired
    DrvRecruitingRepository recruitingRepository;
    @Autowired
    TmsCertificateCheckRepository checkRepository;
    @Autowired
    VehicleRecruitingRepository vehicleRecruitingRepository;
    @Autowired
    TmsQmqProducerCommandService qmqProducerCommandService;

    @QSchedule("driver.recruiting.check.status.job")
    public void driverRecruitingCheckStatusSchedule(Parameter parameter) throws Exception {
        String vehicleIds = parameter.getString("vehicleIds");
        this.driverRecruitingCheckStatusScheduleMethod(vehicleIds);
    }

    public void driverRecruitingCheckStatusScheduleMethod(String vehicleIds) {
        try {
            List<Long> vehicleIdList = Lists.newArrayList();
            if (StringUtils.isNotEmpty(vehicleIds)) {
                vehicleIdList = Arrays.stream(vehicleIds.split(",")).mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
            }
            int count = recruitingRepository.countApproveIngDrvRecruiting(vehicleIdList, TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode(), 2);
            if (count == 0) {
                return;
            }
            List<DrvRecruitingPO> drvRecruitingPOList = recruitingRepository.queryApproveIngDrvRecruiting(vehicleIdList, TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode(), 2);
            for (DrvRecruitingPO drvRecruitingPO : drvRecruitingPOList) {
                List<Long> drvRecruitingIds = Lists.newArrayList();
                drvRecruitingIds.add(drvRecruitingPO.getDrvRecruitingId());
                if (Objects.equals(drvRecruitingPO.getDrvFrom(), TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode())) {
                    drvRecruitingIds.add(drvRecruitingPO.getVehicleId());
                }
                List<TmsCertificateCheckPO> checkPOList = checkRepository.queryCerCheckListByCheckIds(drvRecruitingIds, Arrays.asList(TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV.getCode(),
                        TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode()));
                if (CollectionUtils.isEmpty(checkPOList)) {
                    continue;
                }
                Boolean flag = Boolean.TRUE;
                for (TmsCertificateCheckPO tmsCertificate : checkPOList) {
                    //兼容三期以后的版本,如果是三期之前的版本则判断原逻辑
                    Integer checkStatus = tmsCertificate.getCheckStatus();
                    if(drvRecruitingPO.getVersionFlag() >= 3){
                        checkStatus = tmsCertificate.getThirdCheckStatus();
                    }
                    if (Objects.equals(checkStatus, TmsTransportConstant.CheckStatusEnum.CHECKING.getCode())) {
                        flag = Boolean.FALSE;
                    }
                }
                if (flag) {
                    recruitingRepository.updateCheckStatus(Arrays.asList(drvRecruitingPO.getDrvRecruitingId()), 1);
                    if (Objects.equals(drvRecruitingPO.getDrvFrom(), TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode())) {
                        vehicleRecruitingRepository.updateCheckStatus(Arrays.asList(drvRecruitingPO.getVehicleId()), 1);
                    }
                    recruitingRepository.updateApproveAging(drvRecruitingPO.getDrvRecruitingId());
                    //发送审批时效qmq
                    qmqProducerCommandService.sendRecruitingApproveAgingQMQ(drvRecruitingPO.getDrvRecruitingId(), TmsTransportConstant.RecruitingTypeEnum.drv.getCode(), TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode());
                }
            }
            logger.info("job end");
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }
}