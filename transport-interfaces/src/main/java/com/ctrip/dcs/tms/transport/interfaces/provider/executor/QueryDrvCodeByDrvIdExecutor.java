package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * <AUTHOR>
 * @Description 生成一个随机code码,与司机ID对应，保证一对一
 * @Date 9:49 2020/8/13
 * @Param
 * @return
 **/
@Component
public class QueryDrvCodeByDrvIdExecutor extends AbstractRpcExecutor<QueryDrvCodeByDrvIdSOARequestType, QueryDrvCodeByDrvIdSOAResponseType> implements Validator<QueryDrvCodeByDrvIdSOARequestType> {

    @Autowired
    private DriverQueryService driverQueryService;

    @Override
    public QueryDrvCodeByDrvIdSOAResponseType execute(QueryDrvCodeByDrvIdSOARequestType requestType) {
        QueryDrvCodeByDrvIdSOAResponseType responseType = new QueryDrvCodeByDrvIdSOAResponseType();
        Result<String> result =  driverQueryService.queryDrvCodeByDrvId(requestType.getDrvId());
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType);
    }

    @Override
    public void validate(AbstractValidator<QueryDrvCodeByDrvIdSOARequestType> validator) {
        validator.ruleFor("drvId").notNull();
    }
}
