package com.ctrip.dcs.tms.transport.interfaces.schedule;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.ctrip.dcs.tms.transport.infrastructure.common.util.MobileHelper;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.RedisUtils;
import com.ctrip.igt.framework.common.result.Result;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.dcs.geo.platform.interfaces.AreaCodeQueryType;
import com.ctrip.dcs.geo.platform.interfaces.GeoKey;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverDomainService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.GeoPlatformServiceProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.TmsTransUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CommonConfig;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportGroupRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl.EnumRepositoryHelper;
import com.ctrip.dcs.tms.transport.infrastructure.service.IVRCallService;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.model.CallPhoneForVerifyRequestType;
import com.ctrip.model.CallPhoneForVerifyResponseType;
import com.ctrip.model.QueryTelephoneAreaCodeRequestType;
import com.ctrip.model.QueryTelephoneAreaCodeResponseType;
import com.google.common.collect.Lists;

import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

/**
 * 运力组调度员IVR校验任务 从tsp_transport_group表中获取所有dispatcher_phone不为空的数据，然后发起IVR校验
 */
@Component
public class TransportGroupDispatcherIVRVerificationSchedule {

    private static final Logger logger = LoggerFactory.getLogger(TransportGroupDispatcherIVRVerificationSchedule.class);

    private static final int BATCH_SIZE = 50;
    private static final String CHANNEL = "tms_dispatcher_verification";
    private static final String IVR_VERIFICATION_KEY_PREFIX = "tms_ivr_verification_";
    private static final int IVR_VERIFICATION_EXPIRATION = RedisUtils.ONE_DAY; // 默认过期时间为1天

    @Autowired
    private DriverDomainService driverDomainService;

    @Autowired
    private EnumRepositoryHelper enumRepositoryHelper;

    @Autowired
    private TransportGroupRepository transportGroupRepository;

    @Autowired
    private GeoPlatformServiceProxy geoPlatformServiceProxy;

    @Autowired
    private MobileHelper mobileHelper;

    @Autowired
    private IVRCallService ivrCallService;

    @Autowired
    private CommonConfig commonConfig;

    /**
     * 运力组调度员IVR校验任务
     *
     * @param parameter 参数，可以指定运力组ID列表
     * @throws Exception 异常
     */
    @QSchedule("transport.group.dispatcher.ivr.verification.job")
    public void verifyTransportGroupDispatcherPhones(Parameter parameter) throws Exception {
        String transportGroupIds = parameter.getString("transportGroupIds");
        this.verifyTransportGroupDispatcherPhonesMethod(transportGroupIds);
    }

    /**
     * 运力组调度员IVR校验方法
     *
     * @param transportGroupIds 运力组ID列表，逗号分隔
     */
    public void verifyTransportGroupDispatcherPhonesMethod(String transportGroupIds) {
        try {
            // 解析运力组ID列表
            List<Long> transportGroupIdList = parseTransportGroupIds(transportGroupIds);

            // 查询运力组信息并处理
            queryAndProcessTransportGroups(transportGroupIdList);

        } catch (Exception e) {
            logger.error("verifyTransportGroupDispatcherPhones", "Error verifying transport group dispatcher phones: {}", e.getMessage(), e);
        }
    }

    /**
     * 解析运力组ID列表
     *
     * @param transportGroupIds 运力组ID列表，逗号分隔
     * @return 运力组ID列表
     */
    private List<Long> parseTransportGroupIds(String transportGroupIds) {
        if (StringUtils.isNotEmpty(transportGroupIds)) {
            return Arrays.stream(transportGroupIds.split(",")).map(String::trim).filter(StringUtils::isNotEmpty).map(Long::parseLong).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    /**
     * 查询运力组信息并处理
     *
     * @param transportGroupIdList 运力组ID列表
     */
    private void queryAndProcessTransportGroups(List<Long> transportGroupIdList) throws SQLException {
        if (CollectionUtils.isEmpty(transportGroupIdList)) {
            // 如果没有指定运力组ID，则分页查询并处理所有运力组
            logger.info("queryAndProcessTransportGroups", "No transport group IDs specified, querying and processing all transport groups with pagination");
            queryAndProcessAllTransportGroupsWithPagination();
        } else {
            // 查询指定运力组ID的运力组信息
            List<TspTransportGroupPO> transportGroups = transportGroupRepository.queryTspTransportByIds(transportGroupIdList);
            if (CollectionUtils.isNotEmpty(transportGroups)) {
                // 过滤出有调度员电话的运力组
                List<TspTransportGroupPO> transportGroupsWithPhone = filterTransportGroupsWithPhone(transportGroups);
                if (CollectionUtils.isNotEmpty(transportGroupsWithPhone)) {
                    // 分批处理运力组调度员电话 IVR 校验
                    processBatchIVRVerification(transportGroupsWithPhone);
                } else {
                    logger.info("queryAndProcessTransportGroups", "No transport groups with dispatcher phone found");
                }
            } else {
                logger.info("queryAndProcessTransportGroups", "No transport groups found for the specified IDs");
            }
        }
    }

    /**
     * 分页查询并处理所有运力组
     */
    private void queryAndProcessAllTransportGroupsWithPagination() {
        int pageSize = 100; // 每页查询数量
        int pageNo = 1; // 起始页码
        Long lastId = 0L; // 上一页最后一条记录的ID
        int totalProcessed = 0; // 总处理数量
        int totalWithPhone = 0; // 有电话的总数

        List<TspTransportGroupPO> pageResult;
        do {
            try {
                // 分页查询运力组
                pageResult = queryTransportGroupPage(lastId, pageNo, pageSize);

                if (CollectionUtils.isEmpty(pageResult)) {
                    break;
                }

                totalProcessed += pageResult.size();

                // 过滤出有调度员电话的运力组
                List<TspTransportGroupPO> transportGroupsWithPhone = filterTransportGroupsWithPhone(pageResult);
                totalWithPhone += transportGroupsWithPhone.size();

                // 如果有电话的运力组不为空，则处理这一批
                if (CollectionUtils.isNotEmpty(transportGroupsWithPhone)) {
                    logger.info("queryAndProcessAllTransportGroupsWithPagination", "Processing page {} with {} transport groups with phone", pageNo, transportGroupsWithPhone.size());

                    // 分批处理运力组调度员电话 IVR 校验
                    processBatchIVRVerification(transportGroupsWithPhone);
                }

                // 更新lastId为当前页最后一条记录的ID
                lastId = pageResult.get(pageResult.size() - 1).getTransportGroupId();

                logger.info("queryAndProcessAllTransportGroupsWithPagination", "Retrieved page {} with {} transport groups, {} with phone, last ID: {}", pageNo, pageResult.size(), transportGroupsWithPhone.size(),
                    lastId);
            } catch (Exception e) {
                logger.error("queryAndProcessAllTransportGroupsWithPagination", "Error querying transport groups at page {}: {}", pageNo, e.getMessage(), e);
                break;
            }
        } while (CollectionUtils.isNotEmpty(pageResult));

        logger.info("queryAndProcessAllTransportGroupsWithPagination", "Processed a total of {} transport groups, {} with phone", totalProcessed, totalWithPhone);
    }

    /**
     * 查询单页运力组数据
     *
     * @param lastId 上一页最后一条记录的ID
     * @param pageNo 页码
     * @param pageSize 每页大小
     * @return 单页运力组数据
     */
    private List<TspTransportGroupPO> queryTransportGroupPage(Long lastId, int pageNo, int pageSize) {
        try {
            // 使用Repository的分页查询方法
            // 如果lastId大于0，则使用lastId作为起始点查询
            return transportGroupRepository.queryTransportGroupByIdFromAndPage(lastId, null, null, pageNo, pageSize);
        } catch (Exception e) {
            logger.error("queryTransportGroupPage", "Error querying transport group page {}: {}", pageNo, e.getMessage(), e);
            return Lists.newArrayList();
        }
    }

    /**
     * 过滤出有调度员电话的运力组
     *
     * @param transportGroups 运力组信息列表
     * @return 有调度员电话的运力组信息列表
     */
    private List<TspTransportGroupPO> filterTransportGroupsWithPhone(List<TspTransportGroupPO> transportGroups) {
        return transportGroups.stream()
            // .filter(group -> StringUtils.isNotEmpty(group.getDispatcherPhone()))
            .collect(Collectors.toList());
    }

    /**
     * 分批处理运力组调度员电话IVR校验
     *
     * @param transportGroups 运力组信息列表
     */
    private void processBatchIVRVerification(List<TspTransportGroupPO> transportGroups) {
        try {
            // 直接分批处理，不使用线程池
            List<List<TspTransportGroupPO>> batches = Lists.partition(transportGroups, BATCH_SIZE);
            logger.info("processBatchIVRVerification", "Processing {} batches with batch size {}", batches.size(), BATCH_SIZE);

            for (List<TspTransportGroupPO> batch : batches) {
                processIVRVerificationBatch(batch);
            }
        } catch (Exception e) {
            logger.error("processBatchIVRVerification", "Error processing batch IVR verification: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理一批运力组调度员电话IVR校验
     *
     * @param groups 运力组信息列表
     * @return 处理结果列表
     */
    private List<Boolean> processIVRVerificationBatch(List<TspTransportGroupPO> groups) {
        List<Boolean> results = Lists.newArrayList();
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);

        for (TspTransportGroupPO group : groups) {
            try {

                String igtCode = group.getIgtCode();
                String dispatcherPhone = group.getDispatcherPhone();
                String standbyIgtCode = group.getStandbyIgtCode();
                String standbyPhone = group.getStandbyPhone();
                boolean result = initiateIVRVerification(group.getTransportGroupId(), igtCode, dispatcherPhone, group.getCountryId(), group.getPointCityId());
                results.add(result);
                if (result) {
                    successCount.incrementAndGet();
                } else {
                    failCount.incrementAndGet();
                }

                if (!StringUtils.equals(dispatcherPhone, standbyPhone)) {
                    boolean result1 = initiateIVRVerification(group.getTransportGroupId(), standbyIgtCode, standbyPhone, group.getCountryId(), group.getPointCityId());
                    results.add(result1);
                    if (result1) {
                        successCount.incrementAndGet();
                    } else {
                        failCount.incrementAndGet();
                    }
                }
            } catch (Exception e) {
                logger.error("processIVRVerificationBatch", "Error initiating IVR verification for transport group {}: {}", group.getTransportGroupId(), e.getMessage(), e);
                results.add(false);
                failCount.incrementAndGet();
            }
        }

        logger.info("processIVRVerificationBatch", "Processed {} transport groups, success: {}, fail: {}", groups.size(), successCount.get(), failCount.get());

        return results;
    }

    /**
     * 为单个运力组调度员电话发起IVR校验
     *
     * @param phone       运力组信息
     * @param pointCityId
     * @return 是否成功发起IVR校验
     */
    private boolean initiateIVRVerification(Long transportGroupId, String igtCode, String phone, String countryId, Long pointCityId) {
        try {
            if (StringUtils.isBlank(phone)) {
                return false;
            }
            return doProcess(transportGroupId, igtCode, phone, countryId, pointCityId);
        } catch (Exception e) {
            logger.error("initiateIVRVerification", "Error initiating IVR verification for transport group {}: {}", transportGroupId, e.getMessage(), e);
            return false;
        }
    }

    private boolean doProcess(Long transportGroupId, String igtCode, String dispatcherPhone, String countryId, Long pointCityId) {
        // 如果国际区号为空，尝试从国家ID获取
        if (StringUtils.isEmpty(igtCode) && StringUtils.isNotEmpty(countryId)) {
            try {
                // 使用GeoPlatformServiceProxy获取国际区号
                QueryTelephoneAreaCodeRequestType requestType = new QueryTelephoneAreaCodeRequestType();
                requestType.setType(AreaCodeQueryType.GEO);
                requestType.setGeoKey(new GeoKey(1, Long.parseLong(countryId)));
                QueryTelephoneAreaCodeResponseType response = geoPlatformServiceProxy.queryTelephoneAreaCode(requestType);
                if (response != null && response.getData() != null && Objects.nonNull(response.getData().getAreaCode())) {
                    igtCode = String.valueOf(response.getData().getAreaCode());
                }
            } catch (Exception e) {
                logger.warn("initiateIVRVerification", "Failed to get international calling code for country {}: {}", countryId, e.getMessage());
            }
        }

        // 如果仍然为空，使用默认值+86
        if (StringUtils.isEmpty(igtCode)) {
            igtCode = "+86";
        }

        // 从RedisUtils中获取以igtcode和phone组成的key，判断是否拨打过电话
        String redisKey = IVR_VERIFICATION_KEY_PREFIX + igtCode + "_" + dispatcherPhone;
        Boolean hasVerified = RedisUtils.get(redisKey);

        // 如果已经拨打过电话，直接跳过
        if (hasVerified != null && hasVerified) {
            logger.info("initiateIVRVerification", "Skipping IVR verification for transport group {}, dispatcher phone: {} with igtCode: {}, already verified",
                    transportGroupId, dispatcherPhone, igtCode);
            return true;
        }

        // 在进行IVR拨打前调用mobileHelper的isMobileValid方法判断手机号格式是否正确
        Result<Boolean> mobileValidResult = mobileHelper.isMobileValid(igtCode, dispatcherPhone, pointCityId != null ? pointCityId : 0L);
        if (!mobileValidResult.isSuccess()) {
            logger.warn("initiateIVRVerification", "Skipping IVR verification for transport group {}, invalid phone format: {} with igtCode: {}, error: {}",
                    transportGroupId, dispatcherPhone, igtCode, mobileValidResult.getMsg());
            return false;
        }

        // 使用IVRCallService发起IVR电话验证
        Long taskId = ivrCallService.callPhoneForVerify(dispatcherPhone, igtCode, CHANNEL);

        // 记录IVR校验结果
        if (taskId != null) {
            logger.info("initiateIVRVerification", "Successfully initiated IVR verification for transport group {}, dispatcher phone: {}, callTaskId: {}", transportGroupId, dispatcherPhone, taskId);

            // 设置Redis标记，表示该号码已经拨打过电话
            RedisUtils.set(redisKey, IVR_VERIFICATION_EXPIRATION, true);

            try {
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                logger.warn("initiateIVRVerification", "Interrupted while sleeping");
            }
            return true;
        } else {
            logger.warn("initiateIVRVerification", "Failed to initiate IVR verification for transport group {}, dispatcher phone: {}", transportGroupId, dispatcherPhone);
            return false;
        }
    }

}
