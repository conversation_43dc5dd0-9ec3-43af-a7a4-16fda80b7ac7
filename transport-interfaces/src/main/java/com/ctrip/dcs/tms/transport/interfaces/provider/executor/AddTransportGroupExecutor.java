package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.interfaces.provider.validator.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.constant.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctrip.platform.dal.dao.annotation.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import com.google.common.collect.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * 运力组新增
 * <AUTHOR>
 * @Date 2020/3/3 14:55
 */
@Component
public class AddTransportGroupExecutor extends AbstractRpcExecutor<TransportGroupAddSOARequestType, TransportGroupAddSOAResponseType> implements Validator<TransportGroupAddSOARequestType> {

    private static final String DOMESTIC_DEF_DISPATCHERLANGUAGE = "cn";//境内默认调度语言-普通话
    private static final String OVERSEAS_DEF_DISPATCHERLANGUAGE = "en";//境外默认调度语言-英语

    @Autowired
    private InOrderConfigQueryService inOrderConfigQueryService;

    @Autowired
    private TransportGroupQueryService transportGroupQueryService;

    @Autowired
    private TransportGroupCommandService transportGroupCommandService;

    @Autowired
    private InOrderConfigCommandService inOrderConfigCommandService;

    @Autowired
    private EnumRepository enumRepository;

    @Autowired
    private WorkShiftQueryService workShiftQueryService;

    @Autowired
    private WorkShiftCommandService workShiftCommandService;
    @Autowired
    private ICheckSupplierPermissionService checkSupplierPermissionService;

    @Autowired
    MobileHelper mobileHelper;

    @Autowired
    private ProductionLineUtil productionLineUtil;

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public TransportGroupAddSOAResponseType execute(TransportGroupAddSOARequestType transportGroupAddSOARequestType) {
        MetricsUtils.transportGroupAddConnectTimes(ApiTypeEnum.ADD_TRANSPORT_GROUP);
        TransportGroupAddSOAResponseType soaResponseType = new TransportGroupAddSOAResponseType();

        // check mobile
        if (StringUtils.isNotBlank(transportGroupAddSOARequestType.getDispatcherPhone())) {
            Result<Boolean> mobileValid = mobileHelper.isMobileValid(transportGroupAddSOARequestType.getIgtCode(), transportGroupAddSOARequestType.getDispatcherPhone(),
              transportGroupAddSOARequestType.getPointCityIdList().get(0));
            if(!mobileValid.isSuccess()){
                return ServiceResponseUtils.fail(soaResponseType, mobileValid.getCode(), mobileValid.getMsg());
            }
        }

        if (StringUtils.isNotBlank(transportGroupAddSOARequestType.getStandbyPhone())) {
            Result<Boolean> mobileValid = mobileHelper.isMobileValid(transportGroupAddSOARequestType.getStandbyIgtCode(), transportGroupAddSOARequestType.getStandbyPhone(),
              transportGroupAddSOARequestType.getPointCityIdList().get(0));
            if(!mobileValid.isSuccess()){
                return ServiceResponseUtils.fail(soaResponseType, mobileValid.getCode(), mobileValid.getMsg());
            }
        }

        //如果是供应商操作编辑运力组并且运力组对应的服务商id是1001000，则不允许编辑运力组
        boolean checkResult = checkSupplierPermissionService.checkByContractId(transportGroupAddSOARequestType.getContractId(), productionLineUtil.getIntegratedLine(Lists.newArrayList(transportGroupAddSOARequestType.getProLineId())), transportGroupAddSOARequestType.getPointCityIdList().get(0));
        if(!checkResult){
            String errorMsg = SharkUtils.getSharkValueDefault(SharkKeyConstant.SHARK_SUPPLIER_NOT_ADD_TRANSPORT_GROUP);
            return ServiceResponseUtils.fail(new TransportGroupAddSOAResponseType(),"500",errorMsg);
        }

        TspTransportGroupPO transportGroupPO = requestTypeToGroupPO(transportGroupAddSOARequestType);
        //存储国家ID
        transportGroupPO.setCountryId(String.valueOf(enumRepository.getCountryId(transportGroupPO.getPointCityId())));
        Integer transportGroupMode = transportGroupPO.getTransportGroupMode();
        boolean flag = transportGroupQueryService.isDispatcherMode(transportGroupMode);
        if (flag) {
            //进单配置检查
            boolean checkPass = inOrderConfigQueryService.checkConfig(transportGroupAddSOARequestType.getInOrderConfigs());
            if (!checkPass) {
                return ServiceResponseUtils.fail(soaResponseType, ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportCheckIntoOrderConfFail));
            }
        }
        boolean applyMode = transportGroupQueryService.isApplyMode(transportGroupMode);
        if (applyMode) {
            //工作班次检查
            boolean checkPass = workShiftQueryService.checkConfig(transportGroupAddSOARequestType.getWorkShiftDetails());
            if (!checkPass) {
                return ServiceResponseUtils.fail(soaResponseType, ServiceResponseConstants.ResStatus.EXCEPTION_CODE,SharkUtils.getSharkValue(SharkKeyConstant.transportCheckWorkShiftDetailsFail));
            }
        }
        //判断进单配置上限
        Result<Boolean> resultUpperFlag =  transportGroupQueryService.inOrderUpperLimit(transportGroupAddSOARequestType.getInOrderConfigs(),transportGroupAddSOARequestType.getPointCityIdList().get(0));
        if(resultUpperFlag.isSuccess() && resultUpperFlag.getData()){
            return ServiceResponseUtils.fail(soaResponseType, ServiceResponseConstants.ResStatus.EXCEPTION_CODE,resultUpperFlag.getMsg());
        }

        Result<Long> result = transportGroupCommandService.addTransportGroup(transportGroupPO);

        if (result.isSuccess() && result.getData() != null) {
            if (flag){
                List<TspIntoOrderConfigPO> intoOrderConfigPOS = requestTypeToConfigPO(result.getData(), transportGroupAddSOARequestType.getInOrderConfigs(), transportGroupPO.getCreateUser());
                inOrderConfigCommandService.addInOrderConfig(intoOrderConfigPOS);
            }
            if (applyMode) {
                List<TspTransportGroupWorkShiftPO> workShiftPOS = requestTypeToWorkShiftPO(result.getData(), transportGroupAddSOARequestType.getWorkShiftDetails(), transportGroupPO.getCreateUser());
                workShiftCommandService.addWorkShiftDetail(workShiftPOS);
            }
            soaResponseType.setData(result.getData());
            return  ServiceResponseUtils.success(soaResponseType);
        }else {
            MetricsUtils.transportGroupAddExceptionTimes(ApiTypeEnum.ADD_TRANSPORT_GROUP);
            return ServiceResponseUtils.fail(soaResponseType, result.getCode(),result.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<TransportGroupAddSOARequestType> validator,TransportGroupAddSOARequestType req) {
        validator.ruleFor("transportGroupName").notNull().notEmpty();
        validator.ruleFor("transportGroupMode").notNull();
        validator.ruleFor("createUser").notNull().notEmpty();
        Integer transportGroupMode = req.getTransportGroupMode();
        boolean predicate = transportGroupQueryService.isDispatcherMode(transportGroupMode);
        validator.ruleFor("dispatcher").notNull().notEmpty().when(predicate);
        validator.ruleFor("dispatcherPhone").notNull().notEmpty().when(predicate);
        validator.ruleFor("takeOrderLimitTime").notNull().when(predicate);
        validator.ruleFor("inOrderConfigs").setValidator(new NotEmptyCollectionValidator(SharkUtils.getSharkValue(SharkKeyConstant.transportUseTimeAndInOrderConfNotNull),ValidationErrors.NotNull)).when(predicate);
        validator.ruleFor("pointCityIdList").notNull().notEmpty();
        validator.ruleFor("transportAreaType").notNull();
        validator.ruleFor("areaGroupId").notNull();
        validator.ruleFor("contractId").notNull();
        boolean applyMode = transportGroupQueryService.isApplyMode(transportGroupMode);
        validator.ruleFor("vehicleTypeId").notNull().when(applyMode);
        validator.ruleFor("workShift").notNull().when(applyMode);
        validator.ruleFor("workShiftDetails").setValidator(new NotEmptyCollectionValidator(SharkUtils.getSharkValue(SharkKeyConstant.transportWorkShiftDetailsNotNull),ValidationErrors.NotNull)).when(applyMode);
    }

    /**
     * 运力组基础信息PO
     * @param requestType
     * @return
     */
    private TspTransportGroupPO requestTypeToGroupPO(TransportGroupAddSOARequestType requestType){
        TspTransportGroupPO transportGroupPO = new TspTransportGroupPO();
        BeanUtils.copyProperties(requestType,transportGroupPO);
        transportGroupPO.setSupplierId(requestType.getSupplierId());
        transportGroupPO.setTransportGroupName(requestType.getTransportGroupName());
        transportGroupPO.setTransportGroupMode(requestType.getTransportGroupMode());
        transportGroupPO.setDispatcher(requestType.getDispatcher());
        transportGroupPO.setDispatcherLanguage(initDispatcherLanguage(requestType.getDispatcherLanguage(),requestType.getPointCityIdList().get(0)));
        transportGroupPO.setDispatcherPhone(TmsTransUtil.encrypt(requestType.getDispatcherPhone(), KeyType.Phone));
        transportGroupPO.setTakeOrderLimitTime(requestType.getTakeOrderLimitTime());
        transportGroupPO.setGroupStatus(TmsTransportConstant.TransportGroupStatusEnum.OFFLINE.getCode());
        transportGroupPO.setCreateUser(requestType.getCreateUser());
        transportGroupPO.setModifyUser(transportGroupPO.getCreateUser());
        transportGroupPO.setPointCityId(requestType.getPointCityIdList().get(0));
        transportGroupPO.setContractId(requestType.getContractId());
        transportGroupPO.setAreaGroupType(requestType.getTransportAreaType());
        transportGroupPO.setSalesModel(requestType.getSalesMode());
        transportGroupPO.setAreaGroupType(requestType.getTransportAreaType());
        transportGroupPO.setInformSwitch(requestType.getInformSwitch());
        transportGroupPO.setInformPhone(requestType.getInformPhone());
        transportGroupPO.setInformEmail(requestType.getInformEmail());
        Map<String,Object> map = enumRepository.queryServiceProvider(transportGroupPO.getContractId());
        if(!Objects.isNull(map.get("saleModeId"))){
            transportGroupPO.setSalesModel(Integer.valueOf(map.get("saleModeId").toString()));
        }
        transportGroupPO.setVehicleTypeId(requestType.getVehicleTypeId());
        transportGroupPO.setWorkShift(requestType.getWorkShift());
        transportGroupPO.setCategorySynthesizeCode(requestType.getProLineId());
        transportGroupPO.setStandbyPhone(TmsTransUtil.encrypt(requestType.getStandbyPhone(), KeyType.Phone));
        return transportGroupPO;
    }

    /**
     * 进单配置PO
     * @param transportGroupId
     * @param configSOATypeList
     * @return
     */
    private List<TspIntoOrderConfigPO> requestTypeToConfigPO(Long transportGroupId,List<InOrderConfigSOAType> configSOATypeList,String createUser){
        List<TspIntoOrderConfigPO> list = Lists.newArrayList();
        for (InOrderConfigSOAType inOrderConfig : configSOATypeList) {
            TspIntoOrderConfigPO intoOrderConfigPO = new TspIntoOrderConfigPO();
            intoOrderConfigPO.setCountryId(enumRepository.getCountryId(inOrderConfig.getCityId()));
            intoOrderConfigPO.setCountryName(inOrderConfig.getCountryName());
            intoOrderConfigPO.setCityId(inOrderConfig.getCityId());
            intoOrderConfigPO.setTransportGroupId(transportGroupId);
            intoOrderConfigPO.setLocationCode(inOrderConfig.getLocationCode());
            intoOrderConfigPO.setLocationType(inOrderConfig.getLocationType());
            intoOrderConfigPO.setConfig(JsonUtil.toJson(inOrderConfig.getConfigItems()));
            intoOrderConfigPO.setActive(Boolean.TRUE);
            intoOrderConfigPO.setCreateUser(createUser);
            intoOrderConfigPO.setModifyUser(intoOrderConfigPO.getCreateUser());
            list.add(intoOrderConfigPO);
        }
        return list;
    }

    /**
     * 班次配置PO
     * @param transportGroupId
     * @param configSOATypeList
     * @param createUser
     * @return
     */
    private List<TspTransportGroupWorkShiftPO> requestTypeToWorkShiftPO(Long transportGroupId, List<WorkShiftDetailSOAType> configSOATypeList, String createUser){
        List<TspTransportGroupWorkShiftPO> list =  Lists.newArrayList();
        for (WorkShiftDetailSOAType workShiftDetailSOAType : configSOATypeList) {
            TspTransportGroupWorkShiftPO tspTransportGroupWorkShiftPO = new TspTransportGroupWorkShiftPO();
            tspTransportGroupWorkShiftPO.setName(workShiftDetailSOAType.getName());
            tspTransportGroupWorkShiftPO.setStarTime(workShiftDetailSOAType.getStartTime());
            tspTransportGroupWorkShiftPO.setEndTime(workShiftDetailSOAType.getEndTime());
            tspTransportGroupWorkShiftPO.setDriverUpperLimit(workShiftDetailSOAType.getDriverUpperLimit());
            tspTransportGroupWorkShiftPO.setTransportGroupId(transportGroupId);
            tspTransportGroupWorkShiftPO.setActive(1);
            tspTransportGroupWorkShiftPO.setCreateUser(createUser);
            tspTransportGroupWorkShiftPO.setModifyUser(tspTransportGroupWorkShiftPO.getCreateUser());
            tspTransportGroupWorkShiftPO.setOrder(workShiftDetailSOAType.getOrder());
            list.add(tspTransportGroupWorkShiftPO);
        }
        return list;
    }

    //调度语言设置默认值
    public String initDispatcherLanguage(String dispatcherLanguage,Long cityId){
        if(StringUtils.isNotEmpty(dispatcherLanguage)){
            return dispatcherLanguage;
        }
        switch (enumRepository.getAreaScope(cityId)){
            case 0:return DOMESTIC_DEF_DISPATCHERLANGUAGE;
//            case 1:return OVERSEAS_DEF_DISPATCHERLANGUAGE;
        }
        return "";
    }

}
