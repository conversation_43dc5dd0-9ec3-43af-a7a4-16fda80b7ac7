package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.DiscardRecruitingVehSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.DiscardRecruitingVehSOAResponseType;
import com.ctrip.dcs.tms.transport.application.command.DrvVehRecruitingCommandService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DiscardRecruitingVehExecutor extends AbstractRpcExecutor<DiscardRecruitingVehSOARequestType, DiscardRecruitingVehSOAResponseType> implements Validator<DiscardRecruitingVehSOARequestType> {

    @Autowired
    DrvVehRecruitingCommandService commandService;

    @Override
    public DiscardRecruitingVehSOAResponseType execute(DiscardRecruitingVehSOARequestType requestType) {
        DiscardRecruitingVehSOAResponseType responseType = new DiscardRecruitingVehSOAResponseType();
        Result<Boolean> result = commandService.discardRecruitingVeh(requestType.getRecruitingId(),requestType.getPermissionCodes(),requestType.getModifyUser());
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<DiscardRecruitingVehSOARequestType> validator) {
        validator.ruleFor("recruitingId").notNull();
        validator.ruleFor("modifyUser").notNull();
    }
}
