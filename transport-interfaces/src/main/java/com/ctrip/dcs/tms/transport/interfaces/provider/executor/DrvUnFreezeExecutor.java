package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 司机解冻
 */
@Component
public class DrvUnFreezeExecutor extends AbstractRpcExecutor<DrvUnFreezeSOARequestType, DrvUnFreezeSOAResponseType> implements Validator<DrvUnFreezeSOARequestType> {

    @Autowired
    private TmsDrvFreezeCommandService commandService;

    @Override
    public DrvUnFreezeSOAResponseType execute(DrvUnFreezeSOARequestType requestType) {
        DrvUnFreezeSOAResponseType responseType = new DrvUnFreezeSOAResponseType();
        Result<Boolean> result = commandService.drvUnFreeze(requestType);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(responseType);
        }else {
            return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
        }
    }
}
