package com.ctrip.dcs.tms.transport.interfaces.provider.executor.saas;

import com.ctrip.dcs.tms.transport.api.saas.QueryDriverForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverForSaasResponseType;
import com.ctrip.dcs.tms.transport.api.saas.SaasDriverSoaDTO;
import com.ctrip.dcs.tms.transport.api.saas.SaasTransportGroupSoaDTO;
import com.ctrip.dcs.tms.transport.application.dto.SaasDriverDTO;
import com.ctrip.dcs.tms.transport.application.dto.SaasTransportGroupDTO;
import com.ctrip.dcs.tms.transport.application.query.IQueryDriverForSaasService;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.LocalCollectionUtils;
import com.ctrip.dcs.tms.transport.interfaces.bridge.DriverAndDriverGuideMergeProcessService;
import com.ctrip.dcs.tms.transport.interfaces.bridge.ProductLineBridgeManagement;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class QueryDriverForSaasExecutor extends AbstractRpcExecutor<QueryDriverForSaasRequestType, QueryDriverForSaasResponseType> implements Validator<QueryDriverForSaasRequestType> {

    @Autowired
    ProductLineBridgeManagement productLineBridgeManagement;

    @Override
    public QueryDriverForSaasResponseType execute(QueryDriverForSaasRequestType queryDriverForSaasRequestType) {
        return productLineBridgeManagement.queryDriverForSaas(queryDriverForSaasRequestType);
    }

    @Override
    public void validate(AbstractValidator<QueryDriverForSaasRequestType> validator) {
        validator.ruleFor("driverIds").notNull().notEmpty();
    }
}
