package com.ctrip.dcs.tms.transport.interfaces.bridge.executorservice;

import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleIdForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleIdForSaasResponseType;
import com.ctrip.dcs.tms.transport.application.query.IQueryVehicleForSaasService;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class QueryVehicleIdForSaasExecutorService {
    @Autowired
    private IQueryVehicleForSaasService queryVehicleForSaasService;

    public QueryVehicleIdForSaasResponseType execute(QueryVehicleIdForSaasRequestType requestType) {
        try{
            List<Long> vehicleIds = queryVehicleForSaasService.queryVehicleId(requestType.getVehicleLicense(),requestType.getSupplierId());
            return ServiceResponseUtils.success(getResponse(vehicleIds));
        }catch (Exception e){
            return ServiceResponseUtils.fail(new QueryVehicleIdForSaasResponseType());
        }
    }

    /**
     * 封装结果
     * @param vehicleIds
     * @return
     */
    public QueryVehicleIdForSaasResponseType getResponse(List<Long> vehicleIds){
        QueryVehicleIdForSaasResponseType responseType = new QueryVehicleIdForSaasResponseType();
        responseType.setVehicleIds(vehicleIds);
        return responseType;
    }
}
