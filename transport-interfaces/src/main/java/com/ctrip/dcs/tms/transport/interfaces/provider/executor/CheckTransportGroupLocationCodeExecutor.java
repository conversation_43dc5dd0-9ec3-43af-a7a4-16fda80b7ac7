package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.pms.product.api.*;
import com.ctrip.dcs.pms.product.api.dto.*;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.infrastructure.constant.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;
import java.util.stream.*;

/**
 * <AUTHOR>
 * @Date 2020/4/29 17:50
 */
@Component
public class CheckTransportGroupLocationCodeExecutor extends AbstractRpcExecutor<CheckTransportGroupLocationCodeSOARequestType, CheckTransportGroupLocationCodeSOAResponseType> implements Validator<CheckTransportGroupLocationCodeSOARequestType> {

    @Autowired
    private TransportGroupQueryService transportGroupQueryService;
    @Autowired
    private PmsProductServiceClientProxy pmsProductServiceClientProxy;

    @Override
    public CheckTransportGroupLocationCodeSOAResponseType execute(CheckTransportGroupLocationCodeSOARequestType requestType) {
        CheckTransportGroupLocationCodeSOAResponseType responseType = new CheckTransportGroupLocationCodeSOAResponseType();
        //目前一个运力组只关联一个点位城市，所以此运力组关联的资源皆是此城市相关的
        List<SkuInfoSOADTO> skuList = transportGroupQueryService.querySkuInfoByTransportGroupId(requestType.transportGroupId);
        if (CollectionUtils.isEmpty(skuList)) {
            return ServiceResponseUtils.success(responseType);
        }
        List<Long> skuIdList = skuList.stream().map(SkuInfoSOADTO::getSkuId).collect(Collectors.toList());
        QueryProductSkuListRequestType skuListRequestType = new QueryProductSkuListRequestType();
        SkuListSearchFilterDTO filterDTO = new SkuListSearchFilterDTO();
        filterDTO.setSkuIds(skuIdList);
        List<BatchProductPropertyDTO> propertyList = Lists.newArrayListWithCapacity(1);
        BatchProductPropertyDTO batchProductPropertyDTO = new BatchProductPropertyDTO();
        batchProductPropertyDTO.setCode("fixed_location_code");
        batchProductPropertyDTO.setValueCodes(ImmutableList.of(requestType.getLocationCode()));
        propertyList.add(batchProductPropertyDTO);
        filterDTO.setPropertyList(propertyList);
        skuListRequestType.setInclusionFilter(filterDTO);
        QueryProductSkuListResponseType skuListResponseType;
        PaginatorDTO paginatorDTO = new PaginatorDTO();
        paginatorDTO.setPageNo(1);
        paginatorDTO.setPageSize(100);
        skuListRequestType.setPagingSetting(paginatorDTO);
        try {
            skuListResponseType = pmsProductServiceClientProxy.queryProductSkuList(skuListRequestType);
        } catch (Exception e) {
            return ServiceResponseUtils.fail(responseType, ServiceResponseConstants.ResStatus.EXCEPTION_CODE, e.getLocalizedMessage());
        }
        if (skuListResponseType == null || CollectionUtils.isEmpty(skuListResponseType.getSkuList())) {
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType, ServiceResponseConstants.ResStatus.EXCEPTION_CODE,TmsTransportConstant.HAVE_POINT_SKU);
    }

    @Override
    public void validate(AbstractValidator<CheckTransportGroupLocationCodeSOARequestType> validator, CheckTransportGroupLocationCodeSOARequestType req) {
        validator.ruleFor("transportGroupId").notNull();
        validator.ruleFor("locationCode").notNull().notEmpty();
    }

}