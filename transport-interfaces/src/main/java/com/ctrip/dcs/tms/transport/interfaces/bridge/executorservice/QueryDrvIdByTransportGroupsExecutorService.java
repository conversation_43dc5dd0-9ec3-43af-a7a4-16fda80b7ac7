package com.ctrip.dcs.tms.transport.interfaces.bridge.executorservice;

import com.ctrip.dcs.tms.transport.api.model.QueryDrvIdByTransportGroupsRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvIdByTransportGroupsResponseType;
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 根据上线运力组id查询关联的司机id
 *
 * <AUTHOR> ZhangZhen
 * @create 2023/8/7 16:09
 */
@Component
public class QueryDrvIdByTransportGroupsExecutorService{

    @Autowired
    private TransportGroupQueryService transportGroupQueryService;


    public QueryDrvIdByTransportGroupsResponseType execute(QueryDrvIdByTransportGroupsRequestType req) {
        QueryDrvIdByTransportGroupsResponseType responseType = new QueryDrvIdByTransportGroupsResponseType();
        responseType.setDrvIdList(transportGroupQueryService.queryDrvIdByTransportGroups(req.getTransportGroupIdList(),req.getTemporaryDispatchMark()));
        return ServiceResponseUtils.success(responseType);
    }

}
