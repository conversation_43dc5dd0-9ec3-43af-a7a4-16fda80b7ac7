package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.DiscardRecruitingDrvSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.DiscardRecruitingDrvSOAResponseType;
import com.ctrip.dcs.tms.transport.application.command.DrvVehRecruitingCommandService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DiscardRecruitingDrvExecutor extends AbstractRpcExecutor<DiscardRecruitingDrvSOARequestType, DiscardRecruitingDrvSOAResponseType> implements Validator<DiscardRecruitingDrvSOARequestType> {

    @Autowired
    DrvVehRecruitingCommandService commandService;

    @Override
    public DiscardRecruitingDrvSOAResponseType execute(DiscardRecruitingDrvSOARequestType requestType) {
        DiscardRecruitingDrvSOAResponseType responseType = new DiscardRecruitingDrvSOAResponseType();
        Result<Boolean> result = commandService.discardRecruitingDrv(requestType.getRecruitingId(),requestType.getPermissionCodes(),requestType.getModifyUser());
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<DiscardRecruitingDrvSOARequestType> validator) {
        validator.ruleFor("recruitingId").notNull();
        validator.ruleFor("modifyUser").notNull();
    }
}
