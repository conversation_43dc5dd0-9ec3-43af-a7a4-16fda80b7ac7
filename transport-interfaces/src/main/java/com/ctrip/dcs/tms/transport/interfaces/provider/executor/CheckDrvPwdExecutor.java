package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
*  司机端密码校验
* <AUTHOR>
* @date 2020/5/13 16:45
*/
@Component
public class CheckDrvPwdExecutor extends AbstractRpcExecutor<CheckDrvPwdSOARequestType, CheckDrvPwdSOAResponseType> implements Validator<CheckDrvPwdSOARequestType> {

    @Autowired
    private DriverQueryService queryService;

    @Override
    public CheckDrvPwdSOAResponseType execute(CheckDrvPwdSOARequestType requestType) {
        CheckDrvPwdSOAResponseType responseType = new CheckDrvPwdSOAResponseType();
        Result<String> result = queryService.checkDrvPwd(requestType.getLoginAccount(),requestType.getLoginPwd());
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }else {
            return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<CheckDrvPwdSOARequestType> validator) {
        validator.ruleFor("loginAccount").notNull().notEmpty();
    }
}
