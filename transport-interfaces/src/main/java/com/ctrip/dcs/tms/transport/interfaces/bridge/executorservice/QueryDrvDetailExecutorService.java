package com.ctrip.dcs.tms.transport.interfaces.bridge.executorservice;


import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailSOAResponseType;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 司机详情
 */
@Component
public class QueryDrvDetailExecutorService {

    @Autowired
    DriverQueryService driverQueryService;


    public QueryDrvDetailSOAResponseType execute(QueryDrvDetailSOARequestType queryDrvDetailRequestType) {
        QueryDrvDetailSOAResponseType responseType = new QueryDrvDetailSOAResponseType();
        Result<QueryDrvDetailSOAResponseType> result = driverQueryService.queryDrvDetail(queryDrvDetailRequestType.getDrvId());
        if (result.isSuccess()) {
            responseType.setData(result.getData().getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType,"400",result.getMsg());
    }
}
