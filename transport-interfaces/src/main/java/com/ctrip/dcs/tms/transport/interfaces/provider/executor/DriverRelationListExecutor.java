package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.common.base.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 绑定司机关系列表
 */
@Component
public class DriverRelationListExecutor extends AbstractRpcExecutor<DriverRelationListRequestSOAType, DriverRelationListResponseSOAType> implements Validator<DriverRelationListRequestSOAType> {

    @Autowired
    private TransportGroupQueryService transportGroupQueryService;

    @Override
    public DriverRelationListResponseSOAType execute(DriverRelationListRequestSOAType requestSOAType) {
        DriverRelationListResponseSOAType responseSOAType = new DriverRelationListResponseSOAType();
        Result<PageHolder<DriverRelationSOADTO>> pageHolderResult = transportGroupQueryService.queryRelationDrvList(requestSOAType);
        if (!pageHolderResult.isSuccess()) {
            return ServiceResponseUtils.fail(responseSOAType,pageHolderResult.getCode(),pageHolderResult.getMsg());
        }
        PaginationDTO paginationDTO = ServiceResponseUtils.newPagination(pageHolderResult.getData().getPageIndex(),
                pageHolderResult.getData().getPageSize(), pageHolderResult.getData().getTotalSize());
        responseSOAType.setPagination(paginationDTO);
        responseSOAType.setData(pageHolderResult.getData().getData());
        return ServiceResponseUtils.success(responseSOAType);
    }

}