package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2020/11/23 15:44
 */
@Component
public class QueryOptionalTransportGroupModeExecutor extends AbstractRpcExecutor<QueryOptionalTransportGroupModeSOARequestType, QueryOptionalTransportGroupModeSOAResponseType> implements Validator<QueryOptionalTransportGroupModeSOARequestType> {

    @Autowired
    private TransportGroupQueryService transportGroupQueryService;

    @Override
    public QueryOptionalTransportGroupModeSOAResponseType execute(QueryOptionalTransportGroupModeSOARequestType queryOptionalTransportGroupModeSOARequestType) {
        QueryOptionalTransportGroupModeSOAResponseType responseType = new QueryOptionalTransportGroupModeSOAResponseType();
        Result<List<Long>> result =  transportGroupQueryService.queryOptionalTransportGroupMode(queryOptionalTransportGroupModeSOARequestType);
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }else {
            return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<QueryOptionalTransportGroupModeSOARequestType> validator, QueryOptionalTransportGroupModeSOARequestType req) {
        validator.ruleFor("supplierId").notNull();
        validator.ruleFor("salesMode").notNull();
    }
}
