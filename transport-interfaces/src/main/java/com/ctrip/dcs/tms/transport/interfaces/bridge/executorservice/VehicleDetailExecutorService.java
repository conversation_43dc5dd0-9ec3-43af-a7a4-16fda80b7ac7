package com.ctrip.dcs.tms.transport.interfaces.bridge.executorservice;

import com.ctrip.dcs.tms.transport.api.model.VehicleDetailDTO;
import com.ctrip.dcs.tms.transport.api.model.VehicleDetailSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.VehicleDetailSOAResponseType;
import com.ctrip.dcs.tms.transport.application.query.VehicleQueryService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class VehicleDetailExecutorService {

    @Autowired
    private VehicleQueryService vehicleQueryService;

    public VehicleDetailSOAResponseType execute(VehicleDetailSOARequestType vehicleDetailSOARequestType) {
        VehicleDetailSOAResponseType vehicleDetailSOAResponseType = new VehicleDetailSOAResponseType();
        Result<VehicleDetailDTO> result = vehicleQueryService.queryVehicleDetail(vehicleDetailSOARequestType.getVehicleId().longValue());
        if (result.isSuccess()) {
            vehicleDetailSOAResponseType.setData(result.getData());
            return ServiceResponseUtils.success(vehicleDetailSOAResponseType);
        }
        return ServiceResponseUtils.fail(vehicleDetailSOAResponseType,"400",result.getMsg());
    }

}
