package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.DrvDispatchCityGraySOARequestType;
import com.ctrip.dcs.tms.transport.api.model.DrvDispatchCityGraySOAResponseType;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DrvDispatchCityGrayExecutor extends AbstractRpcExecutor<DrvDispatchCityGraySOARequestType, DrvDispatchCityGraySOAResponseType> implements Validator<DrvDispatchCityGraySOARequestType> {

    @Autowired
    DriverQueryService driverQueryService;

    @Override
    public DrvDispatchCityGraySOAResponseType execute(DrvDispatchCityGraySOARequestType requestType) {
        DrvDispatchCityGraySOAResponseType responseType = new DrvDispatchCityGraySOAResponseType();
        Result<Boolean> result = driverQueryService.drvDispatchCityGray(requestType.getCityId());
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<DrvDispatchCityGraySOARequestType> validator) {
        validator.ruleFor("cityId").notNull();
    }
}
