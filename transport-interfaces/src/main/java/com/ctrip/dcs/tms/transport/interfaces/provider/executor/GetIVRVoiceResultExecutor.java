package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.dianping.cat.message.Event;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverDomainService;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CatEventType;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CommonConfig;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.model.GetIVRVoiceResultRequestType;
import com.ctrip.model.GetIVRVoiceResultResponseType;
import com.ctrip.model.QueryCallPhoneForVerifyResultRequestType;
import com.ctrip.model.QueryCallPhoneForVerifyResultResponseType;
import com.dianping.cat.Cat;

@Component
public class GetIVRVoiceResultExecutor extends AbstractRpcExecutor<GetIVRVoiceResultRequestType, GetIVRVoiceResultResponseType> implements Validator<GetIVRVoiceResultRequestType> {
    private static final Logger logger = LoggerFactory.getLogger(GetIVRVoiceResultExecutor.class);

    @Autowired
    private CommonConfig config;
    @Autowired
    private DriverDomainService driverDomainService;

    @Override
    public GetIVRVoiceResultResponseType execute(GetIVRVoiceResultRequestType getIVRVoiceResultRequestType) {
        GetIVRVoiceResultResponseType soaResponseType = new GetIVRVoiceResultResponseType();

        // 检查是否启用了兜底逻辑
        Boolean fallbackEnabled = config.getIvrVoiceResultFallbackEnabled();
        if (fallbackEnabled != null && fallbackEnabled) {
            // 兜底逻辑启用，直接返回成功
            Cat.logEvent(CatEventType.IVR_VOICE_RESULT, "fallback_enabled", Event.SUCCESS, "taskId:" + getIVRVoiceResultRequestType.getTaskId());
            logger.info("GetIVRVoiceResult", "Fallback enabled, returning success directly for taskId={}", getIVRVoiceResultRequestType.getTaskId());
            soaResponseType.setResult("success");
            return soaResponseType;
        }

        try {
            QueryCallPhoneForVerifyResultRequestType queryCallPhoneForVerifyResultRequestType = new QueryCallPhoneForVerifyResultRequestType();
            queryCallPhoneForVerifyResultRequestType.setCallTaskId(getIVRVoiceResultRequestType.getTaskId());

            Cat.logEvent(CatEventType.IVR_VOICE_RESULT, "query_call_result");
            QueryCallPhoneForVerifyResultResponseType result = driverDomainService.queryCallPhoneForVerifyResult(queryCallPhoneForVerifyResultRequestType);
            String callResultStatus = result.getCallResultStatus();

            if (StringUtils.isBlank(callResultStatus)) {
                Cat.logEvent(CatEventType.IVR_VOICE_RESULT, "pending_status");
                soaResponseType.setResult("pending");
            } else {
                boolean isSuccess = config.getCallResultStatus().contains(callResultStatus);
                Cat.logEvent(CatEventType.IVR_VOICE_RESULT, isSuccess ? "success_status" : "fail_status", isSuccess ? Event.SUCCESS : "1", "callResultStatus:" + callResultStatus);
                soaResponseType.setResult(isSuccess ? "success" : "fail");
            }
        } catch (Exception e) {
            logger.error("GetIVRVoiceResult error", e);
            soaResponseType.setResult("pending");
        }
        return soaResponseType;
    }
}
