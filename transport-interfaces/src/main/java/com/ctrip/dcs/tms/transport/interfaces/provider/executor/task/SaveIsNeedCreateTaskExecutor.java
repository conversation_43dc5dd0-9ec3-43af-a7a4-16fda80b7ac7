package com.ctrip.dcs.tms.transport.interfaces.provider.executor.task;


import com.ctrip.dcs.tms.transport.api.model.SaveIsNeedCreateTaskRequestType;
import com.ctrip.dcs.tms.transport.api.model.SaveIsNeedCreateTaskResponseType;
import com.ctrip.dcs.tms.transport.task.application.command.TaskCommandService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SaveIsNeedCreateTaskExecutor extends AbstractRpcExecutor<SaveIsNeedCreateTaskRequestType, SaveIsNeedCreateTaskResponseType> implements
  Validator<SaveIsNeedCreateTaskRequestType> {

  @Autowired
  TaskCommandService taskCommandService;

  public SaveIsNeedCreateTaskResponseType execute(SaveIsNeedCreateTaskRequestType requestType) {
    Result<Boolean> result = taskCommandService.saveIsNeedCreateTask(requestType);
    SaveIsNeedCreateTaskResponseType responseType = new SaveIsNeedCreateTaskResponseType();
    return ServiceResponseUtils.success(responseType);
  }
}
