package com.ctrip.dcs.tms.transport.interfaces.schedule;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.PhoneBridgeServiceProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.AreaScopeTypeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DrvMobileNumberAuthReqDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.IdentificationResultDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CommonConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.StringUtil;
import com.ctrip.igt.framework.common.result.Result;

import cn.hutool.core.util.BooleanUtil;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

@Component
public class DriverMobileNumberRealNameAuthenticationSchedule {

  @Autowired
  QueryAllDrvForSchedule queryAllDrvForSchedule;

  @Autowired
  PhoneBridgeServiceProxy phoneBridgeServiceProxy;

  @Autowired
  CommonConfig config;


  @QSchedule("drv.mobile.number.real.name.authentication.job")
  public void auth(Parameter parameter) throws Exception {
      queryAllDrvForSchedule.doDrvAction(parameter, drv -> {
          if (Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(), drv.getInternalScope())) {
              if (config.getFilterOnlineDrv().contains(drv.getDrvStatus())) {
                  doProcess(drv);
              }
          }
      });
  }

  private void doProcess(DrvDriverPO drv) {
      Result<IdentificationResultDTO> identificationResultDTOResult = phoneBridgeServiceProxy.queryIdentificationCallback(drv.getDrvPhone());
      Integer result = getResult(identificationResultDTOResult);
      if (!config.getResultCodeList().contains(result)) {
          return;
      }
      phoneBridgeServiceProxy.batchIdentification(DrvMobileNumberAuthReqDTO.builder()
      .mobilePhone(drv.getDrvPhone())
      .igtCode(drv.getIgtCode())
      .driverName(drv.getDrvName())
      .driverId(drv.getDrvId())
      .idCard(drv.getDrvIdcard())
      .idCardImgUrl(drv.getIdcardImg())
      .idCardBackImgUrl(drv.getIdcardBackImg())
      .scenePhotoUrl(StringUtil.isEmpty(drv.getScenePhoto()) ? drv.getDrvHeadImg() : drv.getScenePhoto())
      .build());
  }

    private static Integer getResult(Result<IdentificationResultDTO> identificationResultDTOResult) {
        Boolean success = identificationResultDTOResult.isSuccess();
        if (BooleanUtil.isTrue(success)) {
            IdentificationResultDTO data = identificationResultDTOResult.getData();
            return data.getResultCode();
        }
        return 0;
    }
}
