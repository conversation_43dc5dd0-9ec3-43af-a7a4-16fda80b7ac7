package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctrip.platform.dal.dao.annotation.*;
import com.google.common.base.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 添加司机
 */
@Component
public class AddDriverExecutor extends AbstractRpcExecutor<DrvAddSOARequestType, DrvAddSOAResponseType> implements Validator<DrvAddSOARequestType> {

    @Autowired
    private DriverCommandService commandService;

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public DrvAddSOAResponseType execute(DrvAddSOARequestType addDrvRequestType){
        DrvAddSOAResponseType responseType = new DrvAddSOAResponseType();
        Result<Boolean> result = commandService.addDrv(addDrvRequestType);
        if (result.isSuccess()) {
            responseType.setMessage(result.getMsg());
            responseType.setHasTip(Strings.isNullOrEmpty(result.getMsg()));
            return ServiceResponseUtils.success(responseType);
        }else {
            return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
        }
    }
}
