package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.common.base.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.google.common.collect.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * 请假详情查询
 * <AUTHOR>
 * @Date 2020/2/24 15:08
 */
@Component
public class QueryDrvLeaveDetailExecutor extends AbstractRpcExecutor<QueryDrvLeaveDetailSOARequestType, QueryDrvLeaveDetailSOAResponseType> implements Validator<QueryDrvLeaveDetailSOARequestType> {

    @Autowired
    private DriverLeaveQueryService driverLeaveQueryService;

    @Override
    public QueryDrvLeaveDetailSOAResponseType execute(QueryDrvLeaveDetailSOARequestType queryDrvLeaveDetailRequestType) {
        QueryDrvLeaveDetailSOAResponseType responseType = new QueryDrvLeaveDetailSOAResponseType();
        List<DrvLeaveDetailSOAType> list = Lists.newArrayList();
        DrvDriverLeavePO driverLeavePO = requestTypeToPO(queryDrvLeaveDetailRequestType);
        Result<PageHolder<DrvLeaveDetailPO>> pageHolderResult = driverLeaveQueryService.queryDrvLeaveDetail(driverLeavePO, queryDrvLeaveDetailRequestType.getPaginator());
        for (DrvLeaveDetailPO drvLeaveDetailPO : pageHolderResult.getData().getData()) {
            list.add(poToDetailType(drvLeaveDetailPO));
        }
        PaginationDTO paginationDTO = ServiceResponseUtils.newPagination(pageHolderResult.getData().getPageIndex(),
                pageHolderResult.getData().getPageSize(), pageHolderResult.getData().getTotalSize());
        responseType.setPagination(paginationDTO);
        responseType.setData(list);
        return ServiceResponseUtils.success(responseType);
    }

    private DrvDriverLeavePO requestTypeToPO(QueryDrvLeaveDetailSOARequestType requestType){
        DrvDriverLeavePO driverLeavePO = new DrvDriverLeavePO();
        driverLeavePO.setDrvId(requestType.getDrvId());
        return driverLeavePO;
    }

    private DrvLeaveDetailSOAType poToDetailType(DrvLeaveDetailPO po){
        DrvLeaveDetailSOAType drvLeaveDetailType = new DrvLeaveDetailSOAType();
        //主键id
        drvLeaveDetailType.setId(po.getId());
        //司机id
        drvLeaveDetailType.setDrvId(po.getDrvId());
        //请假申请时间
        drvLeaveDetailType.setDatachangeCreatetime(DateUtil.timestampToString(po.getDatachangeCreatetime(), DateUtil.YYYYMMDDHHMMSS));
        //请假开始时间
        drvLeaveDetailType.setLeaveBeginTime(DateUtil.timestampToString(po.getLeaveBeginTime(), DateUtil.YYYYMMDDHHMMSS));
        //请假结束时间
        drvLeaveDetailType.setLeaveEndTime(DateUtil.timestampToString(po.getLeaveEndTime(), DateUtil.YYYYMMDDHHMMSS));
        //请假原因
        drvLeaveDetailType.setLeaveReason(po.getLeaveReason());
        drvLeaveDetailType.setCreateUser(po.getCreateUser());
        drvLeaveDetailType.setModifyUser(po.getModifyUser());
        drvLeaveDetailType.setOperateType(po.getOperateType());
        drvLeaveDetailType.setStatus(po.getLeaveStatus());
        //销假时间
        drvLeaveDetailType.setDatachangeDeltime(DateUtil.timestampToString(po.getDatachangeDeltime(), DateUtil.YYYYMMDDHHMMSS));
        //是否已销假
        drvLeaveDetailType.setIsActive( po.getActive() ? 1:0);
        drvLeaveDetailType.setDatachangeLasttime(DateUtil.timestampToString(po.getDatachangeLasttime(), DateUtil.YYYYMMDDHHMMSS));
        return drvLeaveDetailType;
    }
}
