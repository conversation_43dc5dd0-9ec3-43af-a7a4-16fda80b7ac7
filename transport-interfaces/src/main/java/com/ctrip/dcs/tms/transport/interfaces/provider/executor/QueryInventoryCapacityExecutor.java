package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryInventoryCapacitySOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryInventoryCapacitySOAResponseType;
import com.ctrip.dcs.tms.transport.application.query.EffectiveCapacityQueryService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class QueryInventoryCapacityExecutor extends AbstractRpcExecutor<QueryInventoryCapacitySOARequestType, QueryInventoryCapacitySOAResponseType> implements Validator<QueryInventoryCapacitySOARequestType> {

    @Autowired
    EffectiveCapacityQueryService service;

    @Override
    public QueryInventoryCapacitySOAResponseType execute(QueryInventoryCapacitySOARequestType requestType) {
        QueryInventoryCapacitySOAResponseType responseType = new QueryInventoryCapacitySOAResponseType();
        Result<QueryInventoryCapacitySOAResponseType> result = service.queryInventoryCapacity(requestType);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(result.getData());
        }
        return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<QueryInventoryCapacitySOARequestType> validator) {

    }
}
