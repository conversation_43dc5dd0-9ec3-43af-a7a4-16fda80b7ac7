package com.ctrip.dcs.tms.transport.interfaces.schedule;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.dcs.tms.transport.interfaces.schedule.udl.DataUdlFillSchedule;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.google.common.collect.Lists;

import lombok.SneakyThrows;
import qunar.tc.schedule.Parameter;
import qunar.tc.schedule.TaskHolder;
import qunar.tc.schedule.TaskMonitor;

@Service
public class QueryAllDrvForSchedule {

    @Autowired
    private DrvDrvierRepository driverRepository;

    private static final Logger LOGGER = LoggerFactory.getLogger(QueryAllDrvForSchedule.class);
    private static final int PAGE_SIZE = 10;

    @SneakyThrows
    public void doDrvAction(Parameter parameter, DrvQueryCallable call) {
        // 解析参数
        List<Long> driverIdList = parseDriverIdList(parameter);
        Long startId = Optional.ofNullable(parameter.getString("startId")).map(Long::valueOf).orElse(0L);
        String offsetStr = parameter.getString("offset");
        String beginDate = Optional.ofNullable(parameter.getString("beginDate")).orElse("");
        String endDate = Optional.ofNullable(parameter.getString("endDate")).orElse("");

        // 设置查询起始ID
        AtomicReference<Long> drvId = new AtomicReference<>(startId);

        // 根据不同条件查询和处理司机数据
        if (CollectionUtils.isNotEmpty(driverIdList)) {
            // 1. 按司机ID列表直接查询
            processDriversByIdList(driverIdList, call);
        } else if (StringUtils.isNotBlank(offsetStr)) {
            // 2. 按偏移量计算日期范围查询
            Integer offset = Integer.valueOf(offsetStr);
            endDate = getEndDate();
            beginDate = getOffsetDate(endDate, offset);
            processDriversByDateRange(drvId, beginDate, endDate, call);
        } else if (StringUtils.isNotBlank(beginDate) && StringUtils.isNotBlank(endDate)) {
            // 3. 按指定日期范围查询
            processDriversByDateRange(drvId, beginDate, endDate, call);
        } else {
            // 4. 查询所有司机
            processAllDrivers(drvId, call);
        }
    }

    @SneakyThrows
    public void doDrvAction(DataUdlFillSchedule.UdlCallable call) {
        AtomicReference<Long> drvId = new AtomicReference<>(0L);
        int pageNo = 1;
        int pageSize = 10;
        List<DrvDriverPO> driverPoList;
        do{
            log("===================id: {}, ========================", drvId.get());
            driverPoList =
                    driverRepository.queryDrvListByDriverIdFromAndPage(drvId.get(), pageNo, pageSize);
            driverPoList.forEach(drvPo -> drvId.set(Math.max(drvId.get(), drvPo.getDrvId())));
            driverPoList.stream().filter(DrvDriverPO::getActive).forEach(drvPo -> {
                try {
                    call.call(drvPo);
                } catch (Exception e) {
                    log("===================update failed: {} ========================", e.getMessage());
                }
            });
        }while (CollectionUtils.isNotEmpty(driverPoList));
    }

    private List<Long> parseDriverIdList(Parameter parameter) {
        return Optional.ofNullable(parameter.getString("driverIds"))
                .map(o -> o.split(","))
                .map(o -> Arrays.stream(o).map(Long::valueOf).collect(Collectors.toList()))
                .orElse(Lists.newArrayList());
    }

    private void processDriversByIdList(List<Long> driverIdList, DrvQueryCallable call) {
        List<DrvDriverPO> driverPoList = driverRepository.getDrvDriverPoList(driverIdList);
        execute(call, driverPoList);
    }

    private void processDriversByDateRange(AtomicReference<Long> drvId, String beginDate, String endDate, DrvQueryCallable call) {
        List<DrvDriverPO> driverPoList;
        int pageNo = 1;
        do {
            log("===================id: {}, ========================", drvId.get());
            driverPoList = getDrvList(drvId, beginDate, endDate, pageNo, PAGE_SIZE);
            execute(call, driverPoList);
        } while (CollectionUtils.isNotEmpty(driverPoList));
    }

    private void processAllDrivers(AtomicReference<Long> drvId, DrvQueryCallable call) {
        List<DrvDriverPO> driverPoList;
        int pageNo = 1;
        do {
            log("===================id: {}, ========================", drvId.get());
            driverPoList = getDrvList(drvId, null, null, pageNo, PAGE_SIZE);
            execute(call, driverPoList);
        } while (CollectionUtils.isNotEmpty(driverPoList));
    }

    private String getOffsetDate(String endDate, Integer offset) {
        if (StringUtils.isBlank(endDate) || offset == null) {
            return "";
        }

        Date date = DateUtil.stringToDate(endDate, DateUtil.YYYYMMDDHHMMSS);
        if (date == null) {
            return "";
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, -offset);
        // 设置为当天开始时间 00:00:00
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);

        return DateUtil.dateToString(cal.getTime(), DateUtil.YYYYMMDDHHMMSS);
    }

    private String getEndDate() {
        // 获取当天的结束时间 yyyy-MM-dd 23:59:59 格式
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);

        return DateUtil.dateToString(cal.getTime(), DateUtil.YYYYMMDDHHMMSS);
    }

    @SneakyThrows
    private List<DrvDriverPO> getDrvList(AtomicReference<Long> drvId, String beginDate, String endDate, int pageNo, int pageSize) {
        List<DrvDriverPO> driverPoList = driverRepository.queryDrvListByDriverIdFromAndPage(drvId.get(), beginDate, endDate, pageNo, pageSize);
        updateMaxDriverId(drvId, driverPoList);
        return driverPoList;
    }

    private void updateMaxDriverId(AtomicReference<Long> drvId, List<DrvDriverPO> driverPoList) {
        if (CollectionUtils.isNotEmpty(driverPoList)) {
            driverPoList.forEach(drvPo -> drvId.set(Math.max(drvId.get(), drvPo.getDrvId())));
        }
    }

    public void execute(DrvQueryCallable call, List<DrvDriverPO> driverPoList) {
        if (CollectionUtils.isEmpty(driverPoList)) {
            return;
        }
        driverPoList.stream()
                .filter(DrvDriverPO::getActive)
                .forEach(drvPo -> {
                    try {
                        call.call(drvPo);
                    } catch (Exception e) {
                        log("===================update failed: {} ========================", e.getMessage());
                    }
                });
    }

    public void log(String format, Object... arg) {
        try {
            Optional.ofNullable(TaskHolder.getKeeper())
                    .map(TaskMonitor::getLogger)
                    .ifPresent(logger -> logger.info(format, arg));
        } catch (Exception e) {
            LOGGER.warn("log fail", e);
        }
        LOGGER.info(format, arg);
    }

    public interface DrvQueryCallable {
        void call(DrvDriverPO po);
    }
}
