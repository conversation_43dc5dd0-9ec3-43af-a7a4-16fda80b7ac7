package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.OverseasOCRRecognitionSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.OverseasOCRRecognitionSOAResponseType;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
　* @description: 境外OCR识别
　* <AUTHOR>
　* @date 2023/6/8 15:59
*/
@Component
public class OverseasOCRRecognitionExecutor extends AbstractRpcExecutor<OverseasOCRRecognitionSOARequestType, OverseasOCRRecognitionSOAResponseType> implements Validator<OverseasOCRRecognitionSOARequestType> {

    @Resource
    private DriverQueryService driverQueryService;

    @Override
    public OverseasOCRRecognitionSOAResponseType execute(OverseasOCRRecognitionSOARequestType requestType) {
        OverseasOCRRecognitionSOAResponseType responseType = new OverseasOCRRecognitionSOAResponseType();
        Result<OverseasOCRRecognitionSOAResponseType> result = driverQueryService.overseasOCRRecognition(requestType);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(result.getData());
        }
        return ServiceResponseUtils.fail(responseType, ServiceResponseConstants.ResStatus.EXCEPTION_CODE, result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<OverseasOCRRecognitionSOARequestType> validator) {
        validator.ruleFor("ocrImgUrl").notNull();
        validator.ruleFor("ocrType").notNull();
        validator.ruleFor("cityId").notNull();
    }

}