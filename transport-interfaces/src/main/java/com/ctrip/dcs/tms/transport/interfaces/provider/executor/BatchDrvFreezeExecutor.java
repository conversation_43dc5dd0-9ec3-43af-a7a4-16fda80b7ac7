package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * 批量冻结司机
 */
@Component
public class BatchDrvFreezeExecutor extends AbstractRpcExecutor<DrvFreezeBatchSOARequestType, DrvFreezeBatchSOAResponseType> implements Validator<DrvFreezeBatchSOARequestType> {

    @Autowired
    private TmsDrvFreezeCommandService commandService;

    @Override
    public DrvFreezeBatchSOAResponseType execute(DrvFreezeBatchSOARequestType requestType) {
        DrvFreezeBatchSOAResponseType responseType = new DrvFreezeBatchSOAResponseType();
        Result<List<QueryDoDrvFreezeSOADTO>> result = commandService.batchDrvFreeze(requestType);
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }else {
            return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
        }
    }
}
