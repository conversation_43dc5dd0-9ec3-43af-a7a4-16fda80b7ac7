package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.tms.transport.application.query.InternationalEntryService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.model.ComplianceRuleGrayCheckRequestType;
import com.ctrip.model.ComplianceRuleGrayCheckResponseType;

/**
 * 合规规则灰度检查
 */
@Component
public class ComplianceRuleGrayCheckExecutor extends AbstractRpcExecutor<ComplianceRuleGrayCheckRequestType, ComplianceRuleGrayCheckResponseType> implements Validator<ComplianceRuleGrayCheckRequestType> {
    @Autowired
    private InternationalEntryService internationalEntryService;

    @Override
    public ComplianceRuleGrayCheckResponseType execute(ComplianceRuleGrayCheckRequestType requestType) {
        ComplianceRuleGrayCheckResponseType responseType = new ComplianceRuleGrayCheckResponseType();
        try {
            Result<Boolean> result = internationalEntryService.isInComplianceRuleGary(requestType.getCityId(), requestType.getSupplierId());
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        } catch (Exception e) {
            return ServiceResponseUtils.fail(responseType, "500", "合规规则灰度检查失败: " + e.getMessage());
        }
    }
}
