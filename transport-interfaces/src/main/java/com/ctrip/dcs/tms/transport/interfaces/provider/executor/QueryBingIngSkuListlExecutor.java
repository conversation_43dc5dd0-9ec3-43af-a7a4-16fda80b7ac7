package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.common.base.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 查询商品列表
 */
@Component
public class QueryBingIngSkuListlExecutor extends AbstractRpcExecutor<QueryBingIngSkuSOARequestType, QueryBingIngSkuSOAResponseType> implements Validator<QueryBingIngSkuSOARequestType> {

    @Autowired
    TransportGroupQueryService transportGroupQueryService;

    @Override
    public QueryBingIngSkuSOAResponseType execute(QueryBingIngSkuSOARequestType queryBingIngSkuSOARequestType) {
        QueryBingIngSkuSOAResponseType responseType = new QueryBingIngSkuSOAResponseType();
        Result<PageHolder<QueryBingIngSkuSOADTO>> pageHolderResult = transportGroupQueryService.queryBindSkuRelationList(queryBingIngSkuSOARequestType);
        PaginationDTO paginationDTO = ServiceResponseUtils.newPagination(pageHolderResult.getData().getPageIndex(),
                pageHolderResult.getData().getPageSize(), pageHolderResult.getData().getTotalSize());
        responseType.setData(pageHolderResult.getData().getData());
        responseType.setPagination(paginationDTO);
        return ServiceResponseUtils.success(responseType);
    }
}
