package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehVehiclePO;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRepository;
import com.ctrip.dcs.tms.transport.infrastructure.wrapper.BatchRequestGatewayWrapper;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.concurrent.threadpool.ThreadPoolBuilder;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;

/**
　* @description: 更新车辆信息 by 车辆ID， 任务
　* <AUTHOR>
　* @date 2024/11/29
*/
@Component
public class UpdateVehicleInfoByIdSchedule {

    private static final Logger logger = LoggerFactory.getLogger(UpdateVehicleInfoByIdSchedule.class);

    @Resource
    VehicleRepository vehicleRepository;

    private static final Integer UPDATE_BATCH_SIZE = 50;

    /**
     * 更新车辆信息 by 车辆ID， 任务
     * @param parameter
     * @throws Exception
     */
    @QSchedule("update.vehicle.info.by.vehicleId")
    public void updateVehicleInfoById(Parameter parameter) throws Exception {
        String vehicleParams = parameter == null ? StringUtils.EMPTY : parameter.getString("vehicles");
        logger.info("update.vehicle.info.by.vehicleId", "vehicleParams:{}", vehicleParams);
        ExecutorService executorService = null;
        try {
            executorService = fetchExecutor();
            List<VehVehiclePO> vehVehiclePOS = JsonUtil.fromJson(vehicleParams, new TypeReference<List<VehVehiclePO>>() { });
            //批量批次更新
            new BatchRequestGatewayWrapper<VehVehiclePO, Integer>(executorService, UPDATE_BATCH_SIZE) {
                @Override
                protected List<Integer> doRequest(List<VehVehiclePO> vehiclePOS, Object... param) {
                    return doBatchUpdateVehicles(vehiclePOS);
                }
            }.batchRequest(vehVehiclePOS);
        } finally {
            //定时任务触发的频次不高
            if (Objects.nonNull(executorService) && !executorService.isShutdown()) {
                executorService.shutdown();
            }
        }
    }

    private List<Integer> doBatchUpdateVehicles(List<VehVehiclePO> vehiclePOS) {
        vehiclePOS.forEach(po -> vehicleRepository.updateOfflineVehicleById(po));
        return Lists.newArrayList();
    }

    private synchronized ExecutorService fetchExecutor() {
        return ThreadPoolBuilder.pool().setCoreSize(4).setMaxSize(4).setDaemon(false).setKeepAliveSecs(50)
                .setUseTtl(true).setWorkQueue(new ArrayBlockingQueue<>(50))
                .setThreadNamePrefix("update.vehicle.info.by.vehicleId").build();
    }

}
