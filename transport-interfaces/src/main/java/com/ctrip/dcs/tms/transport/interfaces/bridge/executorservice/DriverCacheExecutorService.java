package com.ctrip.dcs.tms.transport.interfaces.bridge.executorservice;

import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOAResponseType;
import com.ctrip.dcs.tms.transport.application.dto.QueryDriverFromCacheParamDTO;
import com.ctrip.dcs.tms.transport.application.query.DriverCacheAgent;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.RateLimit;
import com.ctrip.dcs.tms.transport.infrastructure.common.monitoring.TransportMetric;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.DriverCacheQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import com.google.common.base.Strings;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 2020-12-02 17:52
 * 2021-10-28 19:32 改版
 *
 * <AUTHOR>
 */
@Component
public class DriverCacheExecutorService {

    private static final Logger logger = LoggerFactory.getLogger(DriverCacheExecutorService.class);

    @Autowired
    private DriverCacheAgent driverCacheAgent;

    @Autowired
    private DriverCacheQconfig driverCacheQconfig;


    public DriverInfoSOAResponseType execute(DriverInfoSOARequestType req) {
        DriverInfoSOAResponseType soaResponseType = new DriverInfoSOAResponseType();
        if (checkParamsProtect(req) || reqLimitProtect(req)) {
            return ServiceResponseUtils.fail(soaResponseType, Constant.REJECTED_STATEMENT);
        }
        List<DriverInfo> driverList = driverCacheAgent.queryDriver(getQueryDriverFromCacheParamDTO(req));
        if (CollectionUtils.isEmpty(driverList)) {
            TransportMetric.noResultCounter.inc();
        } else {
            TransportMetric.hasResultCounter.inc();
        }
        soaResponseType.setDriverList(driverList);
        return ServiceResponseUtils.success(soaResponseType);
    }

    private QueryDriverFromCacheParamDTO getQueryDriverFromCacheParamDTO(DriverInfoSOARequestType req){
        QueryDriverFromCacheParamDTO paramDTO = new QueryDriverFromCacheParamDTO();
        paramDTO.setDriverName(req.getDriverName());
        paramDTO.setDriverPhone(req.getDriverPhone());
        paramDTO.setDriverIds(req.getDriverIds());
        paramDTO.setStatus(req.getStatus());
        paramDTO.setLevel(req.getLevel());
        paramDTO.setCoopModes(req.getCoopModes());
        paramDTO.setQueryTargetTransportMode(req.isQueryTargetTransportMode());
        paramDTO.setTransportGroupIds(req.getTransportGroupIds());
        return paramDTO;
    }

    private boolean checkParamsProtect(DriverInfoSOARequestType req) {
        boolean checkProtectResult = Strings.isNullOrEmpty(req.getDriverIds()) && Strings.isNullOrEmpty(req.getDriverName()) &&
                Strings.isNullOrEmpty(req.getDriverPhone()) && Strings.isNullOrEmpty(req.getTransportGroupIds());
        if (checkProtectResult) {
            TransportMetric.cacheProtectRefusedCounter.inc();
            logger.warn(DriverCacheAgent.DRV_CACHE_WARN_TITLE, "REFUSE: missing required parameters req:{}", JsonUtil.toJson(req));
        }
        return checkProtectResult;
    }

    private boolean reqLimitProtect(DriverInfoSOARequestType req) {
        if (!driverCacheQconfig.getFusingLimitSwitch() || RateLimit.fusingLimiter.tryAcquire()) {
            return false;
        }
        TransportMetric.limitRequestCounter.inc();
        logger.warn(DriverCacheAgent.DRV_CACHE_ERROR_TITLE, "REFUSE: more than limit req：{}", JsonUtil.toJson(req));
        return true;
    }

}
