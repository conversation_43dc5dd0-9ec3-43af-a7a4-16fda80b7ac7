package com.ctrip.dcs.tms.transport.interfaces.provider.executor;


import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.common.base.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.google.common.collect.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * 司机列表
 */
@Component
public class QueryDrvExecutor extends AbstractRpcExecutor<QueryDrvSOARequestType, QueryDrvSOAResponseType> implements Validator<QueryDrvSOARequestType> {

    @Autowired
    private DriverQueryService driverQueryService;

    @Autowired
    private ProductionLineUtil productionLineUtil;

    @Override
    public QueryDrvSOAResponseType execute(QueryDrvSOARequestType queryDrvRequestType) {
        QueryDrvSOAResponseType responseType = new QueryDrvSOAResponseType();
        Result<PageHolder<DrvDriverSOAResponseDTO>> pageHolderResult = driverQueryService.queryDrvList(buildQueryDrvDO(queryDrvRequestType));
        PaginationDTO paginationDTO = ServiceResponseUtils.newPagination(pageHolderResult.getData().getPageIndex(),
                pageHolderResult.getData().getPageSize(), pageHolderResult.getData().getTotalSize());
        responseType.setData(pageHolderResult.getData().getData());
        responseType.setPagination(paginationDTO);
        return ServiceResponseUtils.success(responseType);
    }

    private QueryDrvDO buildQueryDrvDO(QueryDrvSOARequestType requestType) {
        QueryDrvDO queryDrvDO = new QueryDrvDO();
        BeanUtils.copyProperties(requestType,queryDrvDO);
        queryDrvDO.setSupplierId(requestType.getSupplierId());
        queryDrvDO.setCityId(requestType.getCityId());
        queryDrvDO.setDrvName(requestType.getDrvName());
        queryDrvDO.setDrvId(requestType.getDrvId());
        queryDrvDO.setDrvLanguage(requestType.getDrvLanguage());
        if(StringUtils.isNotEmpty(requestType.getDrvPhone())) {
            String[] phoneArr = requestType.getDrvPhone().split(",|，");
            List<String> drvPhoneList = Lists.newArrayListWithCapacity(phoneArr.length);
            for (String str : phoneArr) {
                drvPhoneList.add(TmsTransUtil.encrypt(str, KeyType.Phone));
            }
            queryDrvDO.setDrvPhoneList(drvPhoneList);
            queryDrvDO.setDrvPhone(null);
        }
        if(StringUtils.isNotEmpty(requestType.getDrvIdcard())){
            queryDrvDO.setDrvIdcard(TmsTransUtil.encrypt(requestType.getDrvIdcard(), KeyType.Identity_Card));
        }
        queryDrvDO.setStatus(requestType.getDrvStatus());
        queryDrvDO.setPage(requestType.getPaginator().getPageNo());
        queryDrvDO.setSize(requestType.getPaginator().getPageSize());
        queryDrvDO.setTransportGroupId(requestType.getTransportGroupId());
        queryDrvDO.setDrvFrom(requestType.getDrvFrom());
        queryDrvDO.setCoopMode(requestType.getCoopMode());
        if(StringUtils.isNotEmpty(requestType.getRegistStartDate())){
            queryDrvDO.setRegistStartDate(DateUtil.string2Timestamp(requestType.getRegistStartDate(),DateUtil.YYYYMMDDHHMMSS));
        }
        if(StringUtils.isNotEmpty(requestType.getRegistEndDate())){
            queryDrvDO.setRegistEndDate(DateUtil.string2Timestamp(requestType.getRegistEndDate(),DateUtil.YYYYMMDDHHMMSS));
        }
        if(StringUtils.isNotEmpty(requestType.getOnlineStartDate()) && StringUtils.isNotEmpty(requestType.getOnlineEndDate())){
            queryDrvDO.setOnlineStartDate(DateUtil.string2Timestamp(requestType.getOnlineStartDate(),DateUtil.YYYYMMDDHHMMSS));
            queryDrvDO.setOnlineEndDate(DateUtil.string2Timestamp(requestType.getOnlineEndDate(),DateUtil.YYYYMMDDHHMMSS));
        }
        queryDrvDO.setDrvIdList(requestType.getDrvIdList());
        queryDrvDO.setProLineIdList(productionLineUtil.getIncludeProductionLineList(requestType.getProLineList()));
        queryDrvDO.setRaisingPickUp(requestType.isRaisingPickUp());
        queryDrvDO.setChildSeat(requestType.isChildSeat());
        queryDrvDO.setActive(requestType.isActive());
        queryDrvDO.setRelationType(requestType.getRelationType() == null? TmsTransportConstant.DrvDispatchRelationTypeEnum.CORRELATION.getCode():requestType.getRelationType());
        queryDrvDO.setTemporaryDispatchMark(requestType.getTemporaryDispatchMark());
        return queryDrvDO;
    }
}
