package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class QueryDrvSafetyCenterExecutorTest {

    @InjectMocks
    QueryDrvSafetyCenterExecutor executor;
    @Mock
    private DrvDrvierRepository repository;
    @Mock
    private TmsDrvFreezeRepository freezeRepository;

    @Mock
    private DrvSafetyService drvSafetyService;
    @Mock
    private DriverLeaveQueryService driverLeaveQueryService;
    @Mock
    private TmsVerifyEventRepository eventRepository;

    @Test
    public void execute() {
        QueryDrvSafetyCenterSOARequestType soaRequestType = new QueryDrvSafetyCenterSOARequestType();
        soaRequestType.setDrvId(1L);
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setVehicleId(1L);
        drvDriverPO.setDrvStatus(2);
        Mockito.when(repository.queryByPk(1L)).thenReturn(drvDriverPO);
        TmsDrvFreezePO tmsDrvFreezePO = new TmsDrvFreezePO();
        tmsDrvFreezePO.setDrvId(1L);
        tmsDrvFreezePO.setFreezeStatus(2);
        tmsDrvFreezePO.setFirstFreezeTime(DateUtil.string2Timestamp("2021-08-12 12:12:12",DateUtil.YYYYMMDDHHMMSS));
        tmsDrvFreezePO.setFreezeHour(99999);
        tmsDrvFreezePO.setUnfreezeAction(1);
        tmsDrvFreezePO.setFreezeOrderSet(1);
        tmsDrvFreezePO.setFreezeReason("[{\"freezeTime\":\"2020-06-15\",\"freezeReason\":\"其他\"}]");
        Mockito.when(freezeRepository.queryByPk(1L)).thenReturn(tmsDrvFreezePO);
        List<TmsVerifyEventPO> tmsVerifyEventPOList = Lists.newArrayList();
        TmsVerifyEventPO tmsVerifyEventPO = new TmsVerifyEventPO();
        tmsVerifyEventPO.setVerifySourceId(1L);
        tmsVerifyEventPO.setVerifySourceType(1);
        tmsVerifyEventPO.setVerifyReasonStatus(2);
        tmsVerifyEventPO.setNoticeTimes(1);
        tmsVerifyEventPO.setVerifyEndTime(DateUtil.string2Timestamp("2021-08-17 12:12:12",DateUtil.YYYYMMDDHHMMSS));
        tmsVerifyEventPOList.add(tmsVerifyEventPO);
//        Mockito.when(eventRepository.queryWaitVerifyEvent(Arrays.asList(1L), TmsTransportConstant.VerifyTypeEnum.FACE.getCode(), TmsTransportConstant.VerifyStatusEnum.NO_VERIFY.getCode(), null, true)).thenReturn(tmsVerifyEventPOList);
        Result<Boolean> result = Result.Builder.<Boolean>newResult().success().withData(false).build();
        Mockito.when(drvSafetyService.queryUploadReportStatus(1L)).thenReturn(result);
        QueryDrvSafetyCenterSOAResponseType soaResponseType =  executor.execute(soaRequestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }

    @Test
    public void execute2() {
        QueryDrvSafetyCenterSOARequestType soaRequestType = new QueryDrvSafetyCenterSOARequestType();
        soaRequestType.setDrvId(1L);
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setVehicleId(1L);
        drvDriverPO.setDrvStatus(1);
        Mockito.when(repository.queryByPk(1L)).thenReturn(drvDriverPO);
        DrvLeaveDetailPO drvLeaveDetailPO = new DrvLeaveDetailPO();
        drvLeaveDetailPO.setDrvId(1L);
        drvLeaveDetailPO.setLeaveEndTime(DateUtil.string2Timestamp("2021-08-17 12:12:12",DateUtil.YYYYMMDDHHMMSS));
        drvLeaveDetailPO.setLeaveBeginTime(DateUtil.string2Timestamp("2021-08-17 12:12:12",DateUtil.YYYYMMDDHHMMSS));
        Mockito.when(driverLeaveQueryService.getDrvLeaveLately(1L)).thenReturn(drvLeaveDetailPO);
        List<TmsVerifyEventPO> tmsVerifyEventPOList = Lists.newArrayList();
        TmsVerifyEventPO tmsVerifyEventPO = new TmsVerifyEventPO();
        tmsVerifyEventPO.setVerifySourceId(1L);
        tmsVerifyEventPO.setVerifySourceType(1);
        tmsVerifyEventPO.setVerifyReasonStatus(2);
        tmsVerifyEventPO.setNoticeTimes(1);
        tmsVerifyEventPO.setVerifyEndTime(DateUtil.string2Timestamp("2021-08-17 12:12:12",DateUtil.YYYYMMDDHHMMSS));
        tmsVerifyEventPOList.add(tmsVerifyEventPO);
//        Mockito.when(eventRepository.queryWaitVerifyEvent(Arrays.asList(1L), TmsTransportConstant.VerifyTypeEnum.FACE.getCode(), TmsTransportConstant.VerifyStatusEnum.NO_VERIFY.getCode(), null, true)).thenReturn(tmsVerifyEventPOList);
        Result<Boolean> result = Result.Builder.<Boolean>newResult().success().withData(false).build();
        Mockito.when(drvSafetyService.queryUploadReportStatus(1L)).thenReturn(result);
        QueryDrvSafetyCenterSOAResponseType soaResponseType = executor.execute(soaRequestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }
}
