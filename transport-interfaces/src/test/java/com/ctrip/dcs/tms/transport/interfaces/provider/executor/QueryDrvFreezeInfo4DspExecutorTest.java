package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class QueryDrvFreezeInfo4DspExecutorTest {

    @InjectMocks
    private QueryDrvFreezeInfo4DspExecutor queryDrvFreezeInfo4DspExecutor;

    @Mock
    private DrvFreezeQueryService drvFreezeQueryService;

    private QueryDrvFreezeInfoForDspSOARequestType dspSOARequestType = new QueryDrvFreezeInfoForDspSOARequestType();

    @Before
    public void ready() {
        String drvIds = "12,23,45";
        dspSOARequestType.setDriverIds(drvIds);
        Mockito.when(drvFreezeQueryService.queryDrvFreezeInfoList(BaseUtil.getLongSet(drvIds))).thenReturn(Lists.newArrayList());
    }

    @Test
    public void executeTest() {
        QueryDrvFreezeInfoForDspSOAResponseType soaResponseType =  queryDrvFreezeInfo4DspExecutor.execute(dspSOARequestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }

    @Test
    public void validateTest() {
        queryDrvFreezeInfo4DspExecutor.validate(new AbstractValidator(QueryDrvFreezeInfoForDspSOARequestType.class), dspSOARequestType);
        Assert.assertTrue(true);
    }

}