package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.PersonRecruitingAddSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.PersonRecruitingAddSOAResponseType;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.PersonRecruitingRepository;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.sql.SQLException;

@RunWith(MockitoJUnitRunner.class)
public class PersonRecruitingAddEventExecutorTest{

    @InjectMocks
    PersonRecruitingAddEventExecutor executor;
    @Mock
    PersonRecruitingRepository personRecruitingRepository;

    @Test
    public void testExecute() throws SQLException {
        PersonRecruitingAddSOARequestType soaRequestType = new PersonRecruitingAddSOARequestType();
        soaRequestType.setIntentionCityCode("1");
        soaRequestType.setCreateTime("2024-05-06 12:12:12");
        soaRequestType.setUpdateTime("2024-05-06 12:12:12");
        soaRequestType.setAccount("111");
        Mockito.when(personRecruitingRepository.insert(Mockito.any())).thenReturn(1L);
        PersonRecruitingAddSOAResponseType responseType =  executor.execute(soaRequestType);
        Assert.assertTrue(responseType != null);
    }

    @Test
    public void testExecute1() throws SQLException {
        PersonRecruitingAddSOARequestType soaRequestType = new PersonRecruitingAddSOARequestType();
        soaRequestType.setIntentionCityCode("1");
        Mockito.when(personRecruitingRepository.insert(Mockito.any())).thenReturn(0L);
        PersonRecruitingAddSOAResponseType responseType =  executor.execute(soaRequestType);
        Assert.assertTrue(responseType != null);
    }
}