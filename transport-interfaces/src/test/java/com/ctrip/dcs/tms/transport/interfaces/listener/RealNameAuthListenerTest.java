package com.ctrip.dcs.tms.transport.interfaces.listener;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.PhoneBridgeServiceProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.AreaScopeTypeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DrvMobileNumberAuthDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DrvMobileNumberAuthReqDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.igt.framework.common.result.Result;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import qunar.tc.qmq.Message;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class RealNameAuthListenerTest {

    @InjectMocks
    private RealNameAuthListener realNameAuthListener;

    @Mock
    private DrvDrvierRepository drvDrvierRepository;

    @Mock
    private PhoneBridgeServiceProxy phoneBridgeServiceProxy;
    
    @Mock
    private Message message;
    
    private List<Long> driverIds;
    private List<DrvDriverPO> driverPOList;
    private DrvDriverPO domesticDriver;
    private DrvDriverPO foreignDriver;

    @Before
    public void setUp() {
        // 测试数据准备
        driverIds = new ArrayList<>();
        driverIds.add(1001L);
        driverIds.add(1002L);
        
        // 国内司机
        domesticDriver = new DrvDriverPO();
        domesticDriver.setDrvId(1001L);
        domesticDriver.setDrvName("国内测试司机");
        domesticDriver.setDrvPhone("13800138000");
        domesticDriver.setIgtCode("86");
        domesticDriver.setDrvIdcard("110101199001011234");
        domesticDriver.setIdcardImg("http://example.com/idcard_front.jpg");
        domesticDriver.setIdcardBackImg("http://example.com/idcard_back.jpg");
        domesticDriver.setScenePhoto("http://example.com/scene_photo.jpg");
        domesticDriver.setDrvHeadImg("http://example.com/head_photo.jpg");
        domesticDriver.setInternalScope(AreaScopeTypeEnum.DOMESTIC.getCode());
        
        // 国外司机
        foreignDriver = new DrvDriverPO();
        foreignDriver.setDrvId(1002L);
        foreignDriver.setDrvName("国外测试司机");
        foreignDriver.setDrvPhone("*************");
        foreignDriver.setIgtCode("44");
        foreignDriver.setDrvIdcard("********");
        foreignDriver.setIdcardImg("http://example.com/idcard_front_foreign.jpg");
        foreignDriver.setIdcardBackImg("http://example.com/idcard_back_foreign.jpg");
        foreignDriver.setScenePhoto(null); // 测试无场景照片的情况
        foreignDriver.setDrvHeadImg("http://example.com/head_photo_foreign.jpg");
        foreignDriver.setInternalScope(AreaScopeTypeEnum.OVERSEAS.getCode());
        
        driverPOList = new ArrayList<>();
        driverPOList.add(domesticDriver);
        driverPOList.add(foreignDriver);
        
        // Mock消息属性
        when(message.getStringProperty("drvIdList")).thenReturn(JsonUtil.toJson(driverIds));
        
        // Mock repository返回
        when(drvDrvierRepository.queryDrvList(driverIds)).thenReturn(driverPOList);
        
        // Mock服务调用返回
        when(phoneBridgeServiceProxy.batchIdentification(any())).thenReturn(Result.Builder.<DrvMobileNumberAuthDTO>newResult().success().build());
    }

    @Test
    public void testRealNameAuth_Success() {
        // 执行测试方法
        realNameAuthListener.realNameAuth(message);
        
        // 验证只处理国内司机
        ArgumentCaptor<DrvMobileNumberAuthReqDTO> dtoCaptor = ArgumentCaptor.forClass(DrvMobileNumberAuthReqDTO.class);
        verify(phoneBridgeServiceProxy, times(1)).batchIdentification(dtoCaptor.capture());
        
        // 验证请求参数
        DrvMobileNumberAuthReqDTO capturedDTO = dtoCaptor.getValue();
        assertEquals(domesticDriver.getDrvId(), capturedDTO.getDriverId());
        assertEquals(domesticDriver.getDrvName(), capturedDTO.getDriverName());
        assertEquals(domesticDriver.getDrvPhone(), capturedDTO.getMobilePhone());
        assertEquals(domesticDriver.getIgtCode(), capturedDTO.getIgtCode());
        assertEquals(domesticDriver.getDrvIdcard(), capturedDTO.getIdCard());
        assertEquals(domesticDriver.getIdcardImg(), capturedDTO.getIdCardImgUrl());
        assertEquals(domesticDriver.getIdcardBackImg(), capturedDTO.getIdCardBackImgUrl());
        assertEquals(domesticDriver.getScenePhoto(), capturedDTO.getScenePhotoUrl());
    }

    @Test
    public void testRealNameAuth_EmptyDriverList() {
        // 重置Mock，模拟空司机列表
        when(drvDrvierRepository.queryDrvList(driverIds)).thenReturn(Collections.emptyList());
        
        // 执行测试方法
        realNameAuthListener.realNameAuth(message);
        
        // 验证不调用认证服务
        verify(phoneBridgeServiceProxy, never()).batchIdentification(any());
    }

    @Test
    public void testRealNameAuth_NoInternalDriver() {
        // 重置Mock，只有国外司机
        when(drvDrvierRepository.queryDrvList(driverIds)).thenReturn(Collections.singletonList(foreignDriver));
        
        // 执行测试方法
        realNameAuthListener.realNameAuth(message);
        
        // 验证不调用认证服务
        verify(phoneBridgeServiceProxy, never()).batchIdentification(any());
    }

    @Test
    public void testRealNameAuth_NoScenePhoto() {
        // 重置场景照片为空
        domesticDriver.setScenePhoto(null);
        driverPOList = Collections.singletonList(domesticDriver);
        when(drvDrvierRepository.queryDrvList(driverIds)).thenReturn(driverPOList);
        
        // 执行测试方法
        realNameAuthListener.realNameAuth(message);
        
        // 验证调用服务并使用头像替代场景照片
        ArgumentCaptor<DrvMobileNumberAuthReqDTO> dtoCaptor = ArgumentCaptor.forClass(DrvMobileNumberAuthReqDTO.class);
        verify(phoneBridgeServiceProxy).batchIdentification(dtoCaptor.capture());
        
        DrvMobileNumberAuthReqDTO capturedDTO = dtoCaptor.getValue();
        assertEquals(domesticDriver.getDrvHeadImg(), capturedDTO.getScenePhotoUrl());
    }

    @Test
    public void testRealNameAuth_EmptyMessage() {
        // 重置Mock，模拟空消息
        when(message.getStringProperty("drvIdList")).thenReturn(null);
        
        // 执行测试方法
        realNameAuthListener.realNameAuth(message);
        
        // 验证不调用查询和认证服务
        verify(drvDrvierRepository, never()).queryDrvList((List<Long>) any());
        verify(phoneBridgeServiceProxy, never()).batchIdentification(any());
    }

    @Test
    public void testRealNameAuth_MultipleDrivers() {
        // 创建多个国内司机
        DrvDriverPO anotherDomesticDriver = new DrvDriverPO();
        anotherDomesticDriver.setDrvId(1003L);
        anotherDomesticDriver.setDrvName("另一个国内司机");
        anotherDomesticDriver.setDrvPhone("13911112222");
        anotherDomesticDriver.setIgtCode("86");
        anotherDomesticDriver.setDrvIdcard("110101199001013456");
        anotherDomesticDriver.setIdcardImg("http://example.com/idcard_front2.jpg");
        anotherDomesticDriver.setIdcardBackImg("http://example.com/idcard_back2.jpg");
        anotherDomesticDriver.setScenePhoto("http://example.com/scene_photo2.jpg");
        anotherDomesticDriver.setInternalScope(AreaScopeTypeEnum.DOMESTIC.getCode());
        
        // 更新测试数据
        driverPOList.add(anotherDomesticDriver);
        driverIds.add(1003L);
        when(message.getStringProperty("drvIdList")).thenReturn(JsonUtil.toJson(driverIds));
        when(drvDrvierRepository.queryDrvList(driverIds)).thenReturn(driverPOList);
        
        // 执行测试方法
        realNameAuthListener.realNameAuth(message);
        
        // 验证调用两次认证服务（仅处理两个国内司机）
        verify(phoneBridgeServiceProxy, times(2)).batchIdentification(any());
    }
} 