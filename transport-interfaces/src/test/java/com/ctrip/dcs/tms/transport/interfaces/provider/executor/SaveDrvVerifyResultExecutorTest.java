package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.common.result.Result;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class SaveDrvVerifyResultExecutorTest {

    @InjectMocks
    SaveDrvVerifyResultExecutor executor;
    @Mock
    TmsVerifyEventCommandService commandService;

    @Test
    public void execute() {
        SaveDrvVerifyResultRequestType requestType = new SaveDrvVerifyResultRequestType();
        requestType.setDriverId(1L);
        Result<Boolean> result = Result.Builder.<Boolean>newResult().success().withData(true).build();
        Mockito.when( commandService.saveDrvVerifyResult(requestType)).thenReturn(result);
        SaveDrvVerifyResultResponseType resultResponseType =  executor.execute(requestType);
        Assert.assertTrue(!Objects.isNull(resultResponseType));
    }
}
