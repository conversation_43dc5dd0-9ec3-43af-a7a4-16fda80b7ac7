package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.common.result.Result;
import org.assertj.core.util.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.Objects;
import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class VehicleListExecutorTest {

    @InjectMocks
    VehicleListExecutor executor;
    @Mock
    VehicleQueryService vehicleQueryService;

    @Test
    public void execute() {
        QueryVehicleSOARequestType requestType = new QueryVehicleSOARequestType();
        PaginatorDTO paginatorDTO = new PaginatorDTO();
        paginatorDTO.setPageNo(1);
        paginatorDTO.setPageSize(1);
        requestType.setPaginator(paginatorDTO);
        Result<Integer> resultCount = Result.Builder.<Integer>newResult().success().withData(0).build();
        Mockito.when(vehicleQueryService.queryVehicleCount(requestType)).thenReturn(resultCount);
        QueryVehicleSOAResponseType responseType =  executor.execute(requestType);
        Assert.assertTrue(!Objects.isNull(responseType));
    }

    @Test
    public void test1() {
        QueryVehicleSOARequestType requestType = new QueryVehicleSOARequestType();
        requestType.setVehicleLicense("111");
        PaginatorDTO paginatorDTO = new PaginatorDTO();
        paginatorDTO.setPageNo(1);
        paginatorDTO.setPageSize(1);
        requestType.setPaginator(paginatorDTO);
        Result<Integer> resultCount = Result.Builder.<Integer>newResult().success().withData(0).build();
        Result<List<VehicleListSOADTO>> listResult = Result.Builder.<List<VehicleListSOADTO>>newResult().success().withData(Lists.newArrayList()).build();
//        Mockito.when(vehicleQueryService.queryVehicleList(requestType)).thenReturn(listResult);
        Mockito.when(vehicleQueryService.queryVehicleCount(requestType)).thenThrow(new RuntimeException("VehicleListExecutor queryVehicleList error"));
        try {
            QueryVehicleSOAResponseType responseType =  executor.execute(requestType);
            Assert.assertTrue(!Objects.isNull(responseType));
        }catch (Exception e){

        }
    }
}
