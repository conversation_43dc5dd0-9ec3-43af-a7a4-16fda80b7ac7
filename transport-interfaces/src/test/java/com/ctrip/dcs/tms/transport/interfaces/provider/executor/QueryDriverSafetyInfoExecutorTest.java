package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import org.apache.commons.lang.time.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;
import org.springframework.beans.factory.annotation.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class QueryDriverSafetyInfoExecutorTest {

    @InjectMocks
    private QueryDriverSafetyInfoExecutor queryDriverSafetyInfoExecutor;

    @Mock
    private DrvDrvierRepository drvDrvierRepository;

    @Mock
    private DriverSafetyCommandService driverSafetyCommandService;

    @Mock
    private EpidemicPreventionControlQconfig epidemicPreventionControlQconfig;

    @Mock
    private DrvEpidemicPreventionControlInfoRepository drvEpidemicPreventionControlInfoRepository;

    @Mock
    private TmsTransportQconfig tmsTransportQconfig;

    @Mock
    private SensitiveDataControl control;
    @Test
    public void executorTest() {
        QueryDriverSafetyInfoSOARequestType requestType = new QueryDriverSafetyInfoSOARequestType();
        requestType.setDriverId(1L);
        QueryDriverSafetyInfoSOAResponseType soaResponseType =  queryDriverSafetyInfoExecutor.execute(requestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }

    @Test
    public void executorTest1() {
        QueryDriverSafetyInfoSOARequestType requestType = new QueryDriverSafetyInfoSOARequestType();
        requestType.setDriverId(1L);
        QueryDriverSafetyInfoSOAResponseType soaResponseType = queryDriverSafetyInfoExecutor.execute(requestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }
}