package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DrvRecruitingCertificateCheckResultScheduleTest {
    @InjectMocks
    DrvRecruitingCertificateCheckResultSchedule drvRecruitingCertificateCheckResultSchedule;
    @Mock
    private TmsCertificateCheckRepository checkRepository;
    @Mock
    TmsTransportQconfig qconfig;
    @Mock
    DrvDrvierRepository repository;
    @Mock
    CertificateCheckQueryService checkQueryService;
    @Mock
    DrvRecruitingRepository recruitingRepository;
    @Mock
    VehicleRecruitingRepository vehicleRecruitingRepository;
    @Mock
    VehicleRepository vehicleRepository;
    @Mock
    RecruitingCommandService recruitingCommandService;
    @Mock
    TmsQmqProducerCommandService qmqProducerCommandService;

    @Test
    public void testnetDrvLicenseDataCheck(){
        Mockito.when(checkQueryService.checkFromCityPlatform(Mockito.anyString())).thenReturn(true);
        DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
        drvRecruitingPO.setVersionFlag(3);
        drvRecruitingPO.setCityId(1L);
        drvRecruitingPO.setDrvName("Tom");
        Mockito.when(recruitingRepository.queryByPK(Mockito.anyLong())).thenReturn(drvRecruitingPO);
        Long id = 1L;
        Long checkId = 1L;
        Integer checkType = 1;
        String checkKeyword = "1234564";
        Integer checkStatus = 1;
        boolean result = drvRecruitingCertificateCheckResultSchedule.netDrvLicenseDataCheck(id,checkId,checkType,checkKeyword,checkStatus);
        Assert.assertTrue(!result);
    }

    @Test
    public void netDrvLicenseDataCheck(){
        Long id = 1L;
        Long checkId = 1L;
        Integer checkType = 1;
        String checkKeyword = "1234564";
        Integer checkStatus = 1;
        boolean result = drvRecruitingCertificateCheckResultSchedule.netDrvLicenseDataCheck(id,checkId,checkType,checkKeyword,checkStatus);
        Assert.assertTrue(!result);
    }

    @Test
    public void netDrvLicenseDataCheck1(){
        Long id = 1L;
        Long checkId = 1L;
        Integer checkType = 1;
        String checkKeyword = "1234564";
        Integer checkStatus = 1;
        DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
        drvRecruitingPO.setDrvRecruitingId(1L);
        drvRecruitingPO.setVersionFlag(1);
        Mockito.when(recruitingRepository.queryByPK(checkId)).thenReturn(drvRecruitingPO);
        boolean result = drvRecruitingCertificateCheckResultSchedule.netDrvLicenseDataCheck(id,checkId,checkType,checkKeyword,checkStatus);
        Assert.assertTrue(!result);
    }

    @Test
    public void drvLicenseDataCheck(){
        Long id = 1L;
        Long checkId = 1L;
        Integer checkType = 1;
        Integer checkStatus = 1;
        DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
        drvRecruitingPO.setDrvRecruitingId(1L);
        drvRecruitingPO.setVersionFlag(1);
        drvRecruitingPO.setSupplierId(1L);
        Mockito.when(recruitingRepository.queryByPK(checkId)).thenReturn(drvRecruitingPO);
        boolean result = drvRecruitingCertificateCheckResultSchedule.drvLicenseDataCheck(id,checkId,checkType,checkStatus);
        Assert.assertTrue(!result);
    }

    @Test
    public void drvLicenseDataCheck1(){
        Long id = 1L;
        Long checkId = 1L;
        Integer checkType = 1;
        Integer checkStatus = 1;
        boolean result = drvRecruitingCertificateCheckResultSchedule.drvLicenseDataCheck(id,checkId,checkType,checkStatus);
        Assert.assertTrue(!result);
    }
}
