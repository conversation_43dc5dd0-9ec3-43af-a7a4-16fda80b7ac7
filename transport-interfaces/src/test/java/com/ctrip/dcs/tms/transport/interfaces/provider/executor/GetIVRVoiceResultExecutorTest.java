package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverDomainService;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CommonConfig;
import com.ctrip.model.GetIVRVoiceResultRequestType;
import com.ctrip.model.GetIVRVoiceResultResponseType;
import com.ctrip.model.QueryCallPhoneForVerifyResultRequestType;
import com.ctrip.model.QueryCallPhoneForVerifyResultResponseType;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GetIVRVoiceResultExecutorTest {

    @InjectMocks
    private GetIVRVoiceResultExecutor executor;

    @Mock
    private CommonConfig config;

    @Mock
    private DriverDomainService driverDomainService;

    private QueryCallPhoneForVerifyResultResponseType responseType;

    @Before
    public void setUp() {
        responseType = new QueryCallPhoneForVerifyResultResponseType();
        when(driverDomainService.queryCallPhoneForVerifyResult(any(QueryCallPhoneForVerifyResultRequestType.class)))
                .thenReturn(responseType);
        // Default fallback to disabled
        when(config.getIvrVoiceResultFallbackEnabled()).thenReturn(false);
    }

    @Test
    public void testExecute_CallResultStatusMatches() {
        // Setup
        GetIVRVoiceResultRequestType requestType = new GetIVRVoiceResultRequestType();
        requestType.setTaskId(123L);

        responseType.setCallResultStatus("0");
        when(config.getCallResultStatus()).thenReturn(Collections.singletonList("0"));

        // Execute
        GetIVRVoiceResultResponseType result = executor.execute(requestType);

        // Verify
        Assert.assertEquals(result.getResult(),"success");

        // Verify the correct task ID was passed to the service
        ArgumentCaptor<QueryCallPhoneForVerifyResultRequestType> captor =
                ArgumentCaptor.forClass(QueryCallPhoneForVerifyResultRequestType.class);
        Mockito.verify(driverDomainService).queryCallPhoneForVerifyResult(captor.capture());
        Assert.assertEquals(Long.valueOf(123L), captor.getValue().getCallTaskId());
    }

    @Test
    public void testExecute_CallResultStatusDoesNotMatch() {
        // Setup
        GetIVRVoiceResultRequestType requestType = new GetIVRVoiceResultRequestType();
        requestType.setTaskId(123L);

        responseType.setCallResultStatus("1");
        when(config.getCallResultStatus()).thenReturn(Collections.singletonList("0"));

        // Execute
        GetIVRVoiceResultResponseType result = executor.execute(requestType);

        // Verify
        Assert.assertEquals(result.getResult(),"fail");
    }

    @Test
    public void testExecute_MultipleAllowedStatuses() {
        // Setup
        GetIVRVoiceResultRequestType requestType = new GetIVRVoiceResultRequestType();
        requestType.setTaskId(123L);

        responseType.setCallResultStatus("1");
        when(config.getCallResultStatus()).thenReturn(Arrays.asList("0", "1", "2"));

        // Execute
        GetIVRVoiceResultResponseType result = executor.execute(requestType);

        // Verify
        Assert.assertEquals(result.getResult(),"success");
    }

    @Test
    public void testExecute_FallbackEnabled() {
        // Setup
        GetIVRVoiceResultRequestType requestType = new GetIVRVoiceResultRequestType();
        requestType.setTaskId(123L);

        // Enable fallback
        when(config.getIvrVoiceResultFallbackEnabled()).thenReturn(true);

        // Execute
        GetIVRVoiceResultResponseType result = executor.execute(requestType);

        // Verify
        Assert.assertEquals("success", result.getResult());

        // Verify that the service was not called
        Mockito.verify(driverDomainService, Mockito.never()).queryCallPhoneForVerifyResult(any());
    }
}
