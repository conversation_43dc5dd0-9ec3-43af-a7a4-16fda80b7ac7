package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.common.result.Result;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class DrvHealthPunchAddExecutorTest {

    @InjectMocks
    DrvHealthPunchAddExecutor executor;
    @Mock
    private DriverCommandService driverCommandService;

    @Test
    public void execute() {
        DrvHealthPunchAddRequestType requestType = new DrvHealthPunchAddRequestType();
        requestType.setDrvId(1L);
        requestType.setHealthCode(1);
        Result<Boolean> result = Result.Builder.<Boolean>newResult().success().build();
        Mockito.when(driverCommandService.drvHealthPunchAdd(requestType)).thenReturn(result);
        DrvHealthPunchAddResponseType responseType =  executor.execute(requestType);
        Assert.assertTrue(!Objects.isNull(responseType));
    }
}
