package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl.*;
import org.assertj.core.util.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.sql.*;
import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class CertificateCheckScheduleTest {

    @InjectMocks
    CertificateCheckSchedule schedule;
    @Mock
    private TmsCertificateCheckRepositoryImpl checkRepository;
    @Mock
    private TmsBackgroundChecksRepository backgroundChecksRepository;
    @Mock
    TmsTransportQconfig qconfig;
    @Mock
    DrvDrvierRepository repository;
    @Mock
    CertificateCheckQueryService checkQueryService;
    @Mock
    DrvRecruitingRepository recruitingRepository;
    @Mock
    VehicleRecruitingRepository vehicleRecruitingRepository;

    @Test
    public void idCardBackGroundSchedule() {
    }

    @Test
    public void idCardBackGroundMethodSchedule() throws SQLException {
        List<TmsCertificateCheckPO> tmsCertificateCheckPOS = Lists.newArrayList();
        TmsCertificateCheckPO tmsCertificateCheckPO = new TmsCertificateCheckPO();
        tmsCertificateCheckPO.setId(1L);
        tmsCertificateCheckPO.setCheckType(1);
        tmsCertificateCheckPO.setCertificateType(1);
        tmsCertificateCheckPO.setCheckStatus(4);
        tmsCertificateCheckPO.setCheckId(1L);
        TmsCertificateCheckPO tmsCertificateCheckPO1 = new TmsCertificateCheckPO();
        tmsCertificateCheckPO1.setId(2L);
        tmsCertificateCheckPO1.setCheckType(1);
        tmsCertificateCheckPO1.setCertificateType(2);
        tmsCertificateCheckPO1.setCheckStatus(4);
        tmsCertificateCheckPO1.setCheckId(1L);
        TmsCertificateCheckPO tmsCertificateCheckPO2 = new TmsCertificateCheckPO();
        tmsCertificateCheckPO2.setId(2L);
        tmsCertificateCheckPO2.setCheckType(1);
        tmsCertificateCheckPO2.setCertificateType(3);
        tmsCertificateCheckPO2.setCheckStatus(4);
        tmsCertificateCheckPO2.setCheckId(1L);
        tmsCertificateCheckPOS.add(tmsCertificateCheckPO2);
        Mockito.when(checkRepository.countIdCardCheckByCheckIng(Lists.newArrayList(),1, Lists.newArrayList(),4)).thenReturn(1);
        Mockito.when(checkRepository.queryIdCardCheckByCheckIng(Lists.newArrayList(),1, Lists.newArrayList(),4,1,50)).thenReturn(tmsCertificateCheckPOS);
        TmsCertificateCheckPO checkPO = new TmsCertificateCheckPO();
//        Mockito.when(checkQueryService.refreshIdCardCheckIng(checkPO, Maps.newConcurrentMap())).thenReturn(Boolean.TRUE);
//        Mockito.when(recruitingRepository.updateCheckStatus(Arrays.asList(1L),1)).thenReturn(1);
//        Mockito.when(backgroundChecksRepository.queryBackgroundByPersons(Arrays.asList("222"))).thenReturn(Lists.newArrayList());
        schedule.idCardBackGroundMethodSchedule("",1);
        Assert.assertTrue(true);
    }

    @Test
    public void idCardBackGroundMethodSchedule1() throws SQLException {
        List<TmsCertificateCheckPO> tmsCertificateCheckPOS = Lists.newArrayList();
        TmsCertificateCheckPO tmsCertificateCheckPO = new TmsCertificateCheckPO();
        tmsCertificateCheckPO.setId(1L);
        tmsCertificateCheckPO.setCheckType(1);
        tmsCertificateCheckPO.setCertificateType(2);
        tmsCertificateCheckPO.setCheckStatus(4);
        tmsCertificateCheckPO.setCheckId(1L);
        tmsCertificateCheckPOS.add(tmsCertificateCheckPO);
        Mockito.when(checkRepository.countIdCardCheckByCheckIng(Lists.newArrayList(),1, Lists.newArrayList(),4)).thenReturn(1);
        Mockito.when(checkRepository.queryIdCardCheckByCheckIng(Lists.newArrayList(),1, Lists.newArrayList(),4,1,50)).thenReturn(tmsCertificateCheckPOS);
        TmsCertificateCheckPO checkPO = new TmsCertificateCheckPO();
//        Mockito.when(checkQueryService.refreshIdCardCheckIng(checkPO, Maps.newConcurrentMap())).thenReturn(Boolean.TRUE);
//        Mockito.when(recruitingRepository.updateCheckStatus(Arrays.asList(1L),1)).thenReturn(1);
//        Mockito.when(backgroundChecksRepository.queryBackgroundByPersons(Arrays.asList("222"))).thenReturn(Lists.newArrayList());
        schedule.idCardBackGroundMethodSchedule("",1);
        Assert.assertTrue(true);
    }

    @Test
    public void idCardBackGroundMethodSchedule3() throws SQLException {
        List<TmsCertificateCheckPO> tmsCertificateCheckPOS = Lists.newArrayList();
        TmsCertificateCheckPO tmsCertificateCheckPO = new TmsCertificateCheckPO();
        tmsCertificateCheckPO.setId(1L);
        tmsCertificateCheckPO.setCheckType(1);
        tmsCertificateCheckPO.setCertificateType(3);
        tmsCertificateCheckPO.setCheckStatus(4);
        tmsCertificateCheckPO.setCheckId(1L);
        tmsCertificateCheckPOS.add(tmsCertificateCheckPO);
        Mockito.when(checkRepository.countIdCardCheckByCheckIng(Lists.newArrayList(),1, Lists.newArrayList(),4)).thenReturn(1);
        Mockito.when(checkRepository.queryIdCardCheckByCheckIng(Lists.newArrayList(),1, Lists.newArrayList(),4,1,50)).thenReturn(tmsCertificateCheckPOS);
        TmsCertificateCheckPO checkPO = new TmsCertificateCheckPO();
//        Mockito.when(checkQueryService.refreshIdCardCheckIng(checkPO, Maps.newConcurrentMap())).thenReturn(Boolean.TRUE);
//        Mockito.when(recruitingRepository.updateCheckStatus(Arrays.asList(1L),1)).thenReturn(1);
//        Mockito.when(backgroundChecksRepository.queryBackgroundByPersons(Arrays.asList("222"))).thenReturn(Lists.newArrayList());
        schedule.idCardBackGroundMethodSchedule("",1);
        Assert.assertTrue(true);
    }

    @Test
    public void idCardBackGroundMethodSchedule4() throws SQLException {
        List<TmsCertificateCheckPO> tmsCertificateCheckPOS = Lists.newArrayList();
        TmsCertificateCheckPO tmsCertificateCheckPO = new TmsCertificateCheckPO();
        tmsCertificateCheckPO.setId(1L);
        tmsCertificateCheckPO.setCheckType(1);
        tmsCertificateCheckPO.setCertificateType(4);
        tmsCertificateCheckPO.setCheckStatus(4);
        tmsCertificateCheckPO.setCheckId(1L);
        tmsCertificateCheckPOS.add(tmsCertificateCheckPO);
        Mockito.when(checkRepository.countIdCardCheckByCheckIng(Lists.newArrayList(),1, Lists.newArrayList(),4)).thenReturn(1);
        Mockito.when(checkRepository.queryIdCardCheckByCheckIng(Lists.newArrayList(),1, Lists.newArrayList(),4,1,50)).thenReturn(tmsCertificateCheckPOS);
        TmsCertificateCheckPO checkPO = new TmsCertificateCheckPO();
//        Mockito.when(checkQueryService.refreshIdCardCheckIng(checkPO, Maps.newConcurrentMap())).thenReturn(Boolean.TRUE);
//        Mockito.when(recruitingRepository.updateCheckStatus(Arrays.asList(1L),1)).thenReturn(1);
//        Mockito.when(backgroundChecksRepository.queryBackgroundByPersons(Arrays.asList("222"))).thenReturn(Lists.newArrayList());
        schedule.idCardBackGroundMethodSchedule("",1);
        Assert.assertTrue(true);
    }

    @Test
    public void idCardBackGroundMethodSchedule5() throws SQLException {
        List<TmsCertificateCheckPO> tmsCertificateCheckPOS = Lists.newArrayList();
        TmsCertificateCheckPO tmsCertificateCheckPO = new TmsCertificateCheckPO();
//        tmsCertificateCheckPO.setId(1L);
        tmsCertificateCheckPO.setCheckType(1);
        tmsCertificateCheckPO.setCertificateType(5);
        tmsCertificateCheckPO.setCheckStatus(4);
        tmsCertificateCheckPO.setCheckId(1L);
        tmsCertificateCheckPOS.add(tmsCertificateCheckPO);
        Mockito.when(checkRepository.countIdCardCheckByCheckIng(Lists.newArrayList(),1, Lists.newArrayList(),4)).thenReturn(1);
        Mockito.when(checkRepository.queryIdCardCheckByCheckIng(Lists.newArrayList(),1, Lists.newArrayList(),4,1,50)).thenReturn(tmsCertificateCheckPOS);
        TmsCertificateCheckPO checkPO = new TmsCertificateCheckPO();
//        Mockito.when(checkQueryService.refreshIdCardCheckIng(checkPO, Maps.newConcurrentMap())).thenReturn(Boolean.TRUE);
//        Mockito.when(recruitingRepository.updateCheckStatus(Arrays.asList(1L),1)).thenReturn(1);
//        Mockito.when(backgroundChecksRepository.queryBackgroundByPersons(Arrays.asList("222"))).thenReturn(Lists.newArrayList());
        schedule.idCardBackGroundMethodSchedule("",1);
        Assert.assertTrue(true);
    }

    @Test
    public void drvLicenseDataCheck() {
        DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
        drvRecruitingPO.setDrvName("11");
        drvRecruitingPO.setDrvRecruitingId(1L);
        drvRecruitingPO.setApproverStatus(4);
        drvRecruitingPO.setModifyUser("11");
        drvRecruitingPO.setVersionFlag(3);
        drvRecruitingPO.setSupplierId(1L);
        Mockito.when(recruitingRepository.queryByPK(1L)).thenReturn(drvRecruitingPO);
        DrvAuditDTO drvAuditDTO = new DrvAuditDTO();
//        Mockito.when(checkQueryService.refreshDrvLicenseCheckIng(drvAuditDTO)).thenReturn(true);
        Boolean b = schedule.drvLicenseDataCheck(1L,1L,1,1);
        Assert.assertTrue(!b);
    }
}
