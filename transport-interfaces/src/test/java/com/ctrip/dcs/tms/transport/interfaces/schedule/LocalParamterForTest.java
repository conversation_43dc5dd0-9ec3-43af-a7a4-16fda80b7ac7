package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.framework.ucs.client.api.*;
import qunar.tc.qschedule.common.TaskType;
import qunar.tc.schedule.*;

import java.util.*;

public class LocalParamterForTest implements Parameter {

    private Map<String,String> property = new HashMap<>();

    @Override
    public String getJobName() {
        return null;
    }

    @Override
    public String getTaskId() {
        return null;
    }

    @Override
    public Date getCreatedDate() {
        return null;
    }

    @Override
    public TaskType getTaskTriggerType() {
        return null;
    }

    @Override
    public String getString(String s) {
        return property.get(s);
    }

    @Override
    public <T> T getProperty(String s, Class<T> aClass) {
        return null;
    }

    @Override
    public Map<String, String> getLastResultInWorkflow() {
        return null;
    }

    @Override
    public int shardId() {
        return 0;
    }

    @Override
    public int shards() {
        return 0;
    }

    @Override
    public UcsJobHelper getJobHelper() {
        return null;
    }

    @Override
    public List<Integer> getShardList() {
        return null;
    }

    @Override
    public Map<String, String> getUserParamters() {
        return Collections.emptyMap();
    }

    public Map<String, String> getProperty() {
        return property;
    }

    public void setProperty(Map<String, String> property) {
        this.property = property;
    }
}
