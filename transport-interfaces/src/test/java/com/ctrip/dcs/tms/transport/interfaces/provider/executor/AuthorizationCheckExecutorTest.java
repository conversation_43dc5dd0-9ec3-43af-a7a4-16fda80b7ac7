package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class AuthorizationCheckExecutorTest {

    @InjectMocks
    private AuthorizationCheckExecutor authorizationCheckExecutor;

    @Mock
    private AuthorizationCheckService authorizationCheckService;

    @Test
    public void executeTest() {
        AuthorizationSOAResponseType so  = authorizationCheckExecutor.execute(new AuthorizationSOARequestType());
        Assert.assertTrue(!Objects.isNull(so));
    }

    @Test
    public void validateTest() {
        authorizationCheckExecutor.validate(new AbstractValidator(AuthorizationSOARequestType.class),new AuthorizationSOARequestType());
        Assert.assertTrue(true);
    }

}