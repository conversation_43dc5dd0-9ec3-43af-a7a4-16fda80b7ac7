package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class CheckDrvPenaltyStatusExecutorTest {

    @InjectMocks
    private CheckDrvPenaltyStatusExecutor checkDrvPenaltyStatusExecutor;

    @Mock
    private DriverQueryService driverQueryService;
    @Mock
    private TmsTransportQconfig tmsTransportQconfig;

    @Before
    public void ready() {
        Mockito.when(driverQueryService.queryPenaltyFreezeDrvIdList(Lists.newArrayList(1L))).thenReturn(Result.Builder.<List<Long>>newResult().success().withData(Lists.newArrayList(1L)).build());
    }

    @Test
    public void executeTest() {
        CheckDrvPenaltyStatusSOARequestType soaRequestType = new CheckDrvPenaltyStatusSOARequestType();
        soaRequestType.setDrvIdList(Lists.newArrayList(1L));
        Mockito.when(tmsTransportQconfig.getPenaltySwitch()).thenReturn(true);
        checkDrvPenaltyStatusExecutor.execute(soaRequestType);
        soaRequestType.setDrvIdList(Lists.newArrayList(1L, 2L));
        Mockito.when(driverQueryService.queryPenaltyFreezeDrvIdList(Lists.newArrayList(1L, 2L))).thenReturn(Result.Builder.<List<Long>>newResult().success().withData(Lists.newArrayList(1L, 2L)).build());
        Mockito.when(driverQueryService.queryPenaltyOfflineDrvIdList(Lists.newArrayList(1L, 2L))).thenReturn(Result.Builder.<List<Long>>newResult().success().withData(Lists.newArrayList()).build());
        Mockito.when(driverQueryService.queryDrvNameByIdList(Lists.newArrayList(1L, 2L))).thenReturn(Result.Builder.<List<String>>newResult().success().withData(Lists.newArrayList("")).build());
        checkDrvPenaltyStatusExecutor.execute(soaRequestType);
        Mockito.when(driverQueryService.queryPenaltyFreezeDrvIdList(Lists.newArrayList(1L, 2L))).thenReturn(Result.Builder.<List<Long>>newResult().success().withData(Lists.newArrayList()).build());
        checkDrvPenaltyStatusExecutor.execute(soaRequestType);
        Mockito.when(driverQueryService.queryPenaltyOfflineDrvIdList(Lists.newArrayList(1L, 2L))).thenReturn(Result.Builder.<List<Long>>newResult().success().withData(Lists.newArrayList(1L)).build());
        Mockito.when(driverQueryService.queryPenaltyFreezeDrvIdList(Lists.newArrayList(1L, 2L))).thenReturn(Result.Builder.<List<Long>>newResult().success().withData(Lists.newArrayList(1L)).build());
        Mockito.when(driverQueryService.queryDrvNameByIdList(Lists.newArrayList(1L,1L))).thenReturn(Result.Builder.<List<String>>newResult().success().withData(Lists.newArrayList("")).build());
        checkDrvPenaltyStatusExecutor.execute(soaRequestType);
        Mockito.when(driverQueryService.queryPenaltyFreezeDrvIdList(Lists.newArrayList(1L, 2L))).thenReturn(Result.Builder.<List<Long>>newResult().success().withData(Lists.newArrayList()).build());
        Mockito.when(driverQueryService.queryDrvNameByIdList(Lists.newArrayList(1L))).thenReturn(Result.Builder.<List<String>>newResult().success().withData(Lists.newArrayList("")).build());
        CheckDrvPenaltyStatusSOAResponseType soaResponseType =  checkDrvPenaltyStatusExecutor.execute(soaRequestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }

    @Test
    public void executeTest1() {
        CheckDrvPenaltyStatusSOARequestType soaRequestType = new CheckDrvPenaltyStatusSOARequestType();
        soaRequestType.setDrvIdList(Lists.newArrayList(1L));
        Mockito.when(tmsTransportQconfig.getPenaltySwitch()).thenReturn(false);
        CheckDrvPenaltyStatusSOAResponseType soaResponseType =checkDrvPenaltyStatusExecutor.execute(soaRequestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }

}