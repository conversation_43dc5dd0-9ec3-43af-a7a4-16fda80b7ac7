package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.DrvUnfreezeConfirmOnlineSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.DrvUnfreezeConfirmOnlineSOAResponseType;
import com.ctrip.dcs.tms.transport.application.command.TmsDrvFreezeCommandService;
import com.ctrip.igt.framework.common.result.Result;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Objects;

@RunWith(MockitoJUnitRunner.class)
public class DrvUnfreezeConfirmOnlineExecutorTest {

    @InjectMocks
    DrvUnfreezeConfirmOnlineExecutor executor;
    @Mock
    TmsDrvFreezeCommandService commandService;

    @Test
    public void execute() {
        DrvUnfreezeConfirmOnlineSOARequestType requestType = new DrvUnfreezeConfirmOnlineSOARequestType();
        requestType.setDrvId(1L);
        Result<Boolean> result = Result.Builder.<Boolean>newResult().success().withData(true).build();
        Mockito.when(commandService.drvUnfreezeConfirmOnline(requestType)).thenReturn(result);
        DrvUnfreezeConfirmOnlineSOAResponseType responseType =  executor.execute(requestType);
        Assert.assertTrue(!Objects.isNull(responseType));
    }
}
