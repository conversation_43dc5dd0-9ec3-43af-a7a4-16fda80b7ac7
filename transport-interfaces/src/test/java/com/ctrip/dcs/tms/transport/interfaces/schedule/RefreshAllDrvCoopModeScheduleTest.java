package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class RefreshAllDrvCoopModeScheduleTest {

    @InjectMocks
    RefreshAllDrvCoopModeSchedule schedule;
    @Mock
    DrvDrvierRepository drvierRepository;
    @Mock
    DriverQueryService driverQueryService;
    @Mock
    TransportGroupQueryService transportGroupQueryService;

    @Test
    public void refreshAllDrvCoopModeScheduleMethod() throws Exception{
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        Mockito.when(drvierRepository.queryDrvList(Mockito.anyList())).thenReturn(Arrays.asList(drvDriverPO));
        LocalParamterForTest paramterForTest = new LocalParamterForTest();
        Map<String, String> property = new HashMap<>();
        property.put("drvIds","1");
        paramterForTest.setProperty(property);
        schedule.refreshAllDrvCoopModeSchedule(paramterForTest);
        Assert.assertTrue(true);
    }
    @Test
    public void refreshAllDrvCoopModeScheduleMethod2() throws Exception{
        Mockito.when(drvierRepository.countDrvByMuSelConditions(Mockito.any())).thenReturn(1);
        LocalParamterForTest paramterForTest = new LocalParamterForTest();
        schedule.refreshAllDrvCoopModeSchedule(paramterForTest);
        Assert.assertTrue(true);
    }
    @Test
    public void testupdateCoopmode(){
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        Mockito.when(drvierRepository.queryDrvList(Mockito.anyList())).thenReturn(Arrays.asList(drvDriverPO));
        Mockito.when(drvierRepository.queryDrvId4Cache(Mockito.anyInt(),Mockito.anyInt())).thenReturn(Arrays.asList(1L));
        TransportGroupBasePO transportGroupBasePO = new TransportGroupBasePO();
        transportGroupBasePO.setDrvId(1L);
        Mockito.when(transportGroupQueryService.queryDriverGroupRelationPO(Mockito.any(),Mockito.any())).thenReturn(Arrays.asList(transportGroupBasePO));
        DrvInfoCacheDto drvInfoCacheDto = new DrvInfoCacheDto();
        drvInfoCacheDto.setDrvId(1L);
        Mockito.when(driverQueryService.calculateDrvCoopMode(Mockito.anyLong(),Mockito.anyList())).thenReturn(drvInfoCacheDto);
        schedule.updateCoopmode(1,1000,"1");
        Assert.assertTrue(true);
    }
}
