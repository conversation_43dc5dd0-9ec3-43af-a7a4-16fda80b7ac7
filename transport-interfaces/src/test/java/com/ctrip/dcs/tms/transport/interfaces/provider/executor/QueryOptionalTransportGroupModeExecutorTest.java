package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class QueryOptionalTransportGroupModeExecutorTest {

    @InjectMocks
    QueryOptionalTransportGroupModeExecutor executor;
    @Mock
    private TransportGroupQueryService transportGroupQueryService;

    @Test
    public void execute() {
        QueryOptionalTransportGroupModeSOARequestType soaRequestType = new QueryOptionalTransportGroupModeSOARequestType();
        soaRequestType.setSupplierId(1L);
        soaRequestType.setSalesMode(5);
        List<Long> list = Lists.newArrayList();
        Result<List<Long>> result = Result.Builder.<List<Long>>newResult().success().withData(list).build();;
        Mockito.when(transportGroupQueryService.queryOptionalTransportGroupMode(soaRequestType)).thenReturn(result);
        QueryOptionalTransportGroupModeSOAResponseType soaResponseType =  executor.execute(soaRequestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }
}
