package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.common.result.Result;
import org.assertj.core.util.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.Objects;
import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class BackGroudCheckCallBackExecutorTest {

    @InjectMocks
    BackGroudCheckCallBackExecutor backGroudCheckCallBackExecutor;

    @Mock
    CertificateCheckCommandService service;

    @Test
    public void execute() {
        BackGroudCheckCallBackSOARequestType soaRequestType = new BackGroudCheckCallBackSOARequestType();
        List<DriverBackgroundChecksSOADTO> checksDTOList = Lists.newArrayList();
        DriverBackgroundChecksSOADTO soadto = new DriverBackgroundChecksSOADTO();
        soadto.setPersonId("333");
        soadto.setCaseType("1");
        checksDTOList.add(soadto);
        soaRequestType.setChecksDTOList(checksDTOList);
        Result<Boolean> result = Result.Builder.<Boolean>newResult().success().build();
        Mockito.when(service.insertBackGroundCheck(checksDTOList)).thenReturn(result);
        BackGroudCheckCallBackSOAResponseType soaResponseType =  backGroudCheckCallBackExecutor.execute(soaRequestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }
}
