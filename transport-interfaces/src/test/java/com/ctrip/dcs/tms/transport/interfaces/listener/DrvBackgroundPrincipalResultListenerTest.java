package com.ctrip.dcs.tms.transport.interfaces.listener;

import com.ctrip.dcs.tms.transport.application.command.CertificateCheckCommandService;
import com.ctrip.framework.ucs.client.ShardingKeyValue;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.ReliabilityLevel;
import qunar.tc.qmq.TraceContext;
import qunar.tc.qmq.base.BaseMessage;

import java.util.Date;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@RunWith(MockitoJUnitRunner.class)
public class DrvBackgroundPrincipalResultListenerTest {

    @InjectMocks
    DrvBackgroundPrincipalResultListener listener;
    @Mock
    CertificateCheckCommandService commandService;

    @Test
    public void drvBackgroundPrincipalResultListener() {
        Message message = new Message() {
            @Override
            public String getMessageId() {
                return null;
            }

            @Override
            public String getSubject() {
                return null;
            }

            @Override
            public Date getCreatedTime() {
                return null;
            }

            @Override
            public Date getScheduleReceiveTime() {
                return null;
            }

            @Override
            public void setProperty(String s, boolean b) {

            }

            @Override
            public void setProperty(String s, Boolean aBoolean) {

            }

            @Override
            public void setProperty(String s, int i) {

            }

            @Override
            public void setProperty(String s, Integer integer) {

            }

            @Override
            public void setProperty(String s, long l) {

            }

            @Override
            public void setProperty(String s, Long aLong) {

            }

            @Override
            public void setProperty(String s, float v) {

            }

            @Override
            public void setProperty(String s, Float aFloat) {

            }

            @Override
            public void setProperty(String s, double v) {

            }

            @Override
            public void setProperty(String s, Double aDouble) {

            }

            @Override
            public void setProperty(String s, Date date) {

            }

            @Override
            public void setProperty(String s, String s1) {

            }

            @Override
            public String getStringProperty(String s) {
                return "[{\n" +
                        "\"personId\":\"210xxxxxx\",\n" +
                        "\"caseType\":\"ZT/WB/XD/SD/ZD\",\n" +
                        "\"caseTime\":\"1579239473\",\n" +
                        "\"caseFrom\":\"网办/公安部\",\n" +
                        "\"resultTime\":\"\"\n" +
                        "}]";
            }

            @Override
            public boolean getBooleanProperty(String s) {
                return false;
            }

            @Override
            public Date getDateProperty(String s) {
                return null;
            }

            @Override
            public int getIntProperty(String s) {
                return 0;
            }

            @Override
            public long getLongProperty(String s) {
                return 0;
            }

            @Override
            public float getFloatProperty(String s) {
                return 0;
            }

            @Override
            public double getDoubleProperty(String s) {
                return 0;
            }

            @Override
            public boolean containsKey(String s) {
                return false;
            }

            @Override
            public Message addTag(String s) {
                return null;
            }

            @Override
            public Set<String> getTags() {
                return null;
            }

            @Override
            public Set<String> getBizAttrs() {
                return null;
            }

            @Override
            public Map<String, Object> getAttrs() {
                return null;
            }

            @Override
            public TraceContext getContext() {
                return null;
            }

            @Override
            public void setReliabilityLevel(ReliabilityLevel reliabilityLevel) {

            }

            @Override
            public ReliabilityLevel getReliabilityLevel() {
                return null;
            }

            @Override
            public void autoAck(boolean b) {

            }

            @Override
            public void ack(Throwable throwable) {

            }

            @Override
            public void setDelayTime(Date date) {

            }

            @Override
            public void setDelayTime(long l, TimeUnit timeUnit) {

            }

            @Override
            public int times() {
                return 0;
            }

            @Override
            public void setMaxRetryNum(int i) {

            }

            @Override
            public int getMaxRetryNum() {
                return 1;
            }

            @Override
            public int localRetries() {
                return 0;
            }

            @Override
            public void setDurable(boolean b) {

            }

            @Override
            public boolean isDurable() {
                return false;
            }

            @Override
            public void setStoreAtFailed(boolean b) {

            }

            @Override
            public boolean isStoreAtFailed() {
                return false;
            }

            @Override
            public void setOrderKey(String s) {

            }

            @Override
            public String getOrderKey() {
                return null;
            }

            @Override
            public void setShardingKey(String s) {

            }

            @Override
            public String getShardingKey() {
                return null;
            }

            @Override
            public void setShardingKey(ShardingKeyValue shardingKeyValue) {

            }

            @Override
            public String getPartitionName() {
                return null;
            }

            @Override
            public boolean isCompensation() {
                return false;
            }

            @Override
            public String getSourceRegion() {
                return null;
            }

            @Override
            public String getLane() {
                return "";
            }

            @Override
            public Date getExpiredTime() {
                return null;
            }

            @Override
            public void setNewqmqFlag() {

            }

            @Override
            public boolean isNewqmq() {
                return false;
            }

            @Override
            public <T> T getData(Class<T> aClass) {
                return null;
            }

            @Override
            public void setData(Object o) {

            }

            @Override
            public String getLargeString(String s) {
                return null;
            }

            @Override
            public Object getProperty(String s) {
                return null;
            }

            @Override
            public void setLargeString(String s, String s1) {

            }

            @Override
            public void ack(long l, Throwable throwable) {

            }

            @Override
            public void ack(long l, Throwable throwable, Map<String, String> map) {

            }
        };
        listener.drvBackgroundPrincipalResultListener(message);
        int result = 1;
        Assert.assertEquals(result,1);
    }
}
