package com.ctrip.dcs.tms.transport.interfaces.listener;

import com.ctrip.dcs.tms.transport.application.command.TmsQmqProducerCommandService;
import com.ctrip.dcs.tms.transport.application.query.DriverCacheServiceV2;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TmsModRecordPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.config.ContextConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ModRecordConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.handler.DrvDriverHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.handler.DrvDriverLeaveHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.handler.TmsDrvFreezeHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.handler.TspIntoOrderConfigHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.handler.TspTransportGroupDriverRelationHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.handler.TspTransportGroupHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.handler.TspTransportGroupSkuAreaRelationHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.handler.VehVehicleHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.handler.cache.CacheHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.handler.cache.impl.DefaultCacheHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.ModRecordRespository;
import com.ctrip.igt.framework.dal.DalRepositoryImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import qunar.tc.qmq.base.BaseMessage;

import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class DcstransportdbDataChangedListenerTest {

    @InjectMocks
    private DcstransportdbDataChangedListener dcstransportdbDataChangedListener;

    @Mock
    private ModRecordRespository<TmsModRecordPO> modRecordRespository;
    @Mock
    private TmsTransportQconfig tmsTransportQconfig;
    @Mock
    private ContextConfig contextConfig;
    @Mock
    private DalRepositoryImpl<TmsModRecordPO> dalRepository;

    @Mock
    private DriverCacheServiceV2 driverCacheServiceV2;

    @Mock
    private List<CacheHandler> cacheHandlerList;

    @Mock
    private DefaultCacheHandler defaultCacheHandler;

    @Mock
    private TmsQmqProducerCommandService tmsQmqProducerCommandService;

    @Mock
    private DrvDriverHandler drvDriverHandler;

    @Before
    public void setUp() throws Exception {
        Mockito.when(tmsTransportQconfig.isListenerDataChangedFilterSwitch()).thenReturn(false);
//        Mockito.when(tmsTransportQconfig.getListenerDataChangedTime()).thenReturn(0L);
        Mockito.when(contextConfig.getTmsModRecordHandler(ModRecordConstant.TableName.drvDriver)).thenReturn(drvDriverHandler);
        Mockito.when(contextConfig.getTmsModRecordHandler(ModRecordConstant.TableName.tmsDrvFreeze)).thenReturn(new TmsDrvFreezeHandler());
        Mockito.when(contextConfig.getTmsModRecordHandler(ModRecordConstant.TableName.drvDriverLeave)).thenReturn(new DrvDriverLeaveHandler());
        Mockito.when(contextConfig.getTmsModRecordHandler(ModRecordConstant.TableName.tspIntoOrderConfig)).thenReturn(new TspIntoOrderConfigHandler());
        Mockito.when(contextConfig.getTmsModRecordHandler(ModRecordConstant.TableName.tspTransportGroup)).thenReturn(new TspTransportGroupHandler());
        Mockito.when(contextConfig.getTmsModRecordHandler(ModRecordConstant.TableName.tspTransportGroupDriverRelation)).thenReturn(new TspTransportGroupDriverRelationHandler());
        Mockito.when(contextConfig.getTmsModRecordHandler(ModRecordConstant.TableName.tspTransportGroupSkuAreaRelation)).thenReturn(new TspTransportGroupSkuAreaRelationHandler());
        Mockito.when(contextConfig.getTmsModRecordHandler(ModRecordConstant.TableName.vehVehicle)).thenReturn(new VehVehicleHandler());

        Mockito.when(modRecordRespository.getTmsModRecordRepo()).thenReturn(dalRepository);
        Mockito.when(dalRepository.insert(Mockito.any(TmsModRecordPO.class))).thenReturn(1);
    }

    @Test
    public void dcstransportdbDataChanged() {
        BaseMessage baseMessage = new BaseMessage();
        String tms_drv_freeze = "{\"otterParseTime\":1605074904549,\"orderKeyInfo\":{\"pks\":[],\"schemaName\":\"dcstransportdb\",\"tableName\":\"tms_drv_freeze\"},\"otterSendTime\":1605074904549,\"beforeColumnList\":[{\"isNull\":false,\"name\":\"drv_id\",\"isKey\":true,\"isUpdated\":false,\"value\":\"612402\"},{\"isNull\":false,\"name\":\"supplier_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"30804\"},{\"isNull\":false,\"name\":\"freeze_status\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":true,\"name\":\"first_freeze_time\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"freeze_hour\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"total_freeze_from\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"freeze_reason\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"unfreeze_action\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"freeze_order_set\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"unfreeze_reason\",\"isKey\":false,\"isUpdated\":false,\"value\":\"driver online or offline\"},{\"isNull\":false,\"name\":\"freeze_count\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"send_message_count\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-10-14 19:43:05.410\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-11-10 22:31:34.095\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"郑程\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"曹银萍\"}],\"eventType\":\"UPDATE\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"drv_id\",\"isKey\":true,\"isUpdated\":false,\"value\":\"612402\"},{\"isNull\":false,\"name\":\"supplier_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"30804\"},{\"isNull\":false,\"name\":\"freeze_status\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2\"},{\"isNull\":false,\"name\":\"first_freeze_time\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 14:08:24\"},{\"isNull\":false,\"name\":\"freeze_hour\",\"isKey\":false,\"isUpdated\":true,\"value\":\"24\"},{\"isNull\":false,\"name\":\"total_freeze_from\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2\"},{\"isNull\":false,\"name\":\"freeze_reason\",\"isKey\":false,\"isUpdated\":true,\"value\":\"[{\\\"freezeTime\\\":\\\"2020-11-11\\\",\\\"freezeReason\\\":\\\"订单号: 3089468029, 司机姓名: 郑程, 备注: \\\"}]\"},{\"isNull\":false,\"name\":\"unfreeze_action\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"freeze_order_set\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"unfreeze_reason\",\"isKey\":false,\"isUpdated\":false,\"value\":\"driver online or offline\"},{\"isNull\":false,\"name\":\"freeze_count\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"send_message_count\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-10-14 19:43:05.410\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 14:08:24.498\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"郑程\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"carcheng.zheng\"}],\"tableName\":\"tms_drv_freeze\"}";
        baseMessage.setProperty("dataChange",tms_drv_freeze);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);


        baseMessage = new BaseMessage();
        String drv_driver = "{\"otterParseTime\":1605074904549,\"orderKeyInfo\":{\"pks\":[],\"schemaName\":\"dcstransportdb\",\"tableName\":\"drv_driver\"},\"otterSendTime\":1605074904550,\"beforeColumnList\":[{\"isNull\":false,\"name\":\"drv_id\",\"isKey\":true,\"isUpdated\":false,\"value\":\"612402\"},{\"isNull\":false,\"name\":\"drv_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"郑程\"},{\"isNull\":false,\"name\":\"drv_english_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"supplier_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"30804\"},{\"isNull\":false,\"name\":\"drv_language\",\"isKey\":false,\"isUpdated\":false,\"value\":\"cn\"},{\"isNull\":false,\"name\":\"country_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"country_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"China\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"igt_code\",\"isKey\":false,\"isUpdated\":false,\"value\":\"86\"},{\"isNull\":false,\"name\":\"drv_phone\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1526Qv05645\"},{\"isNull\":false,\"name\":\"login_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"login_pwd\",\"isKey\":false,\"isUpdated\":false,\"value\":\"a88b6d641c315d64507fe10bc77ec775\"},{\"isNull\":false,\"name\":\"ppm_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"**********\"},{\"isNull\":false,\"name\":\"qunar_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"**********\"},{\"isNull\":false,\"name\":\"im_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"CSM0000000212492\"},{\"isNull\":false,\"name\":\"vehicle_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"vehicle_license\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"vehicle_type_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"certificate_number\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"drv_addr\",\"isKey\":false,\"isUpdated\":false,\"value\":\"{\\\"longitude\\\":121.80828,\\\"latitude\\\":31.150098,\\\"name\\\":\\\"浦东国际机场T2-国际/港澳台到达\\\",\\\"address\\\":\\\"上海市-浦东新区-机场镇启航路900路\\\"}\"},{\"isNull\":false,\"name\":\"intend_vehicle_type_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"email\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"wechat\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"work_period\",\"isKey\":false,\"isUpdated\":false,\"value\":\"00:30~00:59,01:00~01:29,01:30~01:59,02:00~02:29,02:30~02:59,03:30~03:59,03:00~03:29,04:00~04:29,06:00~06:29,07:00~07:29,06:30~06:59,08:30~08:59,08:00~08:29,12:30~12:59,17:30~17:29,19:30~19:59,21:30~21:59,20:30~20:59,23:00~23:29\"},{\"isNull\":false,\"name\":\"drv_idcard\",\"isKey\":false,\"isUpdated\":false,\"value\":\"******************\"},{\"isNull\":false,\"name\":\"certi_date\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-05-13\"},{\"isNull\":false,\"name\":\"quasi_driving_type\",\"isKey\":false,\"isUpdated\":false,\"value\":\"C1\"},{\"isNull\":false,\"name\":\"expiry_begin_date\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-05-20\"},{\"isNull\":false,\"name\":\"expiry_end_date\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-06-17\"},{\"isNull\":false,\"name\":\"drvcard_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg.fws.qa.nt.ctripcorp.com/images/0413g120000003rs121D4.png\"},{\"isNull\":false,\"name\":\"idcard_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0410112000846dovs7753.jpg\"},{\"isNull\":false,\"name\":\"drv_head_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0410a1200088cmfz19693.jpg\"},{\"isNull\":false,\"name\":\"people_vehicle_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg.fws.qa.nt.ctripcorp.com/images/0410x12000000347e16C9.png\"},{\"isNull\":false,\"name\":\"net_vehicle_peo_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0410l1200087n96klF61E.jpg\"},{\"isNull\":false,\"name\":\"drv_status\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"freeze_hour\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"freeze_reason\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"freeze_time\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-01-01 12:12:12\"},{\"isNull\":false,\"name\":\"drv_from\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2\"},{\"isNull\":false,\"name\":\"op_from\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"internal_scope\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-05-13 20:10:59.041\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"Annie Cao （曹银萍）\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"曹银萍\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-11-11 11:39:56.305\"},{\"isNull\":false,\"name\":\"salt\",\"isKey\":false,\"isUpdated\":false,\"value\":\"301151\"},{\"isNull\":false,\"name\":\"idcard_back_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0412p1200086sh26587CF.jpg\"},{\"isNull\":false,\"name\":\"coop_mode\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"no_criminal_proof_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"online_time\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-11-11 11:39:56.305\"},{\"isNull\":false,\"name\":\"other_certificate_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"sex\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"nation\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"birthday\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1777-01-01\"},{\"isNull\":false,\"name\":\"idcard_validity\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1777-01-01\"},{\"isNull\":false,\"name\":\"drv_license_number\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"drv_license_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"approve_status\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2\"}],\"eventType\":\"UPDATE\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"drv_id\",\"isKey\":true,\"isUpdated\":false,\"value\":\"612402\"},{\"isNull\":false,\"name\":\"drv_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"郑程\"},{\"isNull\":false,\"name\":\"drv_english_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"supplier_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"30804\"},{\"isNull\":false,\"name\":\"drv_language\",\"isKey\":false,\"isUpdated\":false,\"value\":\"cn\"},{\"isNull\":false,\"name\":\"country_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"country_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"China\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"igt_code\",\"isKey\":false,\"isUpdated\":false,\"value\":\"86\"},{\"isNull\":false,\"name\":\"drv_phone\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1526Qv05645\"},{\"isNull\":false,\"name\":\"login_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"login_pwd\",\"isKey\":false,\"isUpdated\":false,\"value\":\"a88b6d641c315d64507fe10bc77ec775\"},{\"isNull\":false,\"name\":\"ppm_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"**********\"},{\"isNull\":false,\"name\":\"qunar_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"**********\"},{\"isNull\":false,\"name\":\"im_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"CSM0000000212492\"},{\"isNull\":false,\"name\":\"vehicle_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"vehicle_license\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"vehicle_type_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"certificate_number\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"drv_addr\",\"isKey\":false,\"isUpdated\":false,\"value\":\"{\\\"longitude\\\":121.80828,\\\"latitude\\\":31.150098,\\\"name\\\":\\\"浦东国际机场T2-国际/港澳台到达\\\",\\\"address\\\":\\\"上海市-浦东新区-机场镇启航路900路\\\"}\"},{\"isNull\":false,\"name\":\"intend_vehicle_type_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"email\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"wechat\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"work_period\",\"isKey\":false,\"isUpdated\":false,\"value\":\"00:30~00:59,01:00~01:29,01:30~01:59,02:00~02:29,02:30~02:59,03:30~03:59,03:00~03:29,04:00~04:29,06:00~06:29,07:00~07:29,06:30~06:59,08:30~08:59,08:00~08:29,12:30~12:59,17:30~17:29,19:30~19:59,21:30~21:59,20:30~20:59,23:00~23:29\"},{\"isNull\":false,\"name\":\"drv_idcard\",\"isKey\":false,\"isUpdated\":false,\"value\":\"******************\"},{\"isNull\":false,\"name\":\"certi_date\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-05-13\"},{\"isNull\":false,\"name\":\"quasi_driving_type\",\"isKey\":false,\"isUpdated\":false,\"value\":\"C1\"},{\"isNull\":false,\"name\":\"expiry_begin_date\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-05-20\"},{\"isNull\":false,\"name\":\"expiry_end_date\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-06-17\"},{\"isNull\":false,\"name\":\"drvcard_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg.fws.qa.nt.ctripcorp.com/images/0413g120000003rs121D4.png\"},{\"isNull\":false,\"name\":\"idcard_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0410112000846dovs7753.jpg\"},{\"isNull\":false,\"name\":\"drv_head_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0410a1200088cmfz19693.jpg\"},{\"isNull\":false,\"name\":\"people_vehicle_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg.fws.qa.nt.ctripcorp.com/images/0410x12000000347e16C9.png\"},{\"isNull\":false,\"name\":\"net_vehicle_peo_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0410l1200087n96klF61E.jpg\"},{\"isNull\":false,\"name\":\"drv_status\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2\"},{\"isNull\":false,\"name\":\"freeze_hour\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"freeze_reason\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"freeze_time\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-01-01 12:12:12\"},{\"isNull\":false,\"name\":\"drv_from\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2\"},{\"isNull\":false,\"name\":\"op_from\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"internal_scope\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-05-13 20:10:59.041\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"Annie Cao （曹银萍）\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"carcheng.zheng\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 14:08:24.497\"},{\"isNull\":false,\"name\":\"salt\",\"isKey\":false,\"isUpdated\":false,\"value\":\"301151\"},{\"isNull\":false,\"name\":\"idcard_back_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0412p1200086sh26587CF.jpg\"},{\"isNull\":false,\"name\":\"coop_mode\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"no_criminal_proof_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"online_time\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 14:08:24.500\"},{\"isNull\":false,\"name\":\"other_certificate_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"sex\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"nation\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"birthday\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1777-01-01\"},{\"isNull\":false,\"name\":\"idcard_validity\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1777-01-01\"},{\"isNull\":false,\"name\":\"drv_license_number\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"drv_license_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"approve_status\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2\"}],\"tableName\":\"drv_driver\"}";
        baseMessage.setProperty("dataChange",drv_driver);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);

        baseMessage = new BaseMessage();
        drv_driver = "{\"otterParseTime\":1605074904549,\"orderKeyInfo\":{\"pks\":[],\"schemaName\":\"dcstransportdb\",\"tableName\":\"drv_driver\"},\"otterSendTime\":1605074904550,\"beforeColumnList\":[{\"isNull\":false,\"name\":\"drv_id\",\"isKey\":true,\"isUpdated\":false,\"value\":\"612402\"},{\"isNull\":false,\"name\":\"drv_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"郑程\"},{\"isNull\":false,\"name\":\"drv_english_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"supplier_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"30804\"},{\"isNull\":false,\"name\":\"drv_language\",\"isKey\":false,\"isUpdated\":false,\"value\":\"cn\"},{\"isNull\":false,\"name\":\"country_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"country_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"China\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"igt_code\",\"isKey\":false,\"isUpdated\":false,\"value\":\"86\"},{\"isNull\":false,\"name\":\"drv_phone\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1526Qv05645\"},{\"isNull\":false,\"name\":\"login_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"login_pwd\",\"isKey\":false,\"isUpdated\":false,\"value\":\"a88b6d641c315d64507fe10bc77ec775\"},{\"isNull\":false,\"name\":\"ppm_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"**********\"},{\"isNull\":false,\"name\":\"qunar_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"**********\"},{\"isNull\":false,\"name\":\"im_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"CSM0000000212492\"},{\"isNull\":false,\"name\":\"vehicle_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"vehicle_license\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"vehicle_type_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"certificate_number\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"drv_addr\",\"isKey\":false,\"isUpdated\":false,\"value\":\"{\\\"longitude\\\":121.80828,\\\"latitude\\\":31.150098,\\\"name\\\":\\\"浦东国际机场T2-国际/港澳台到达\\\",\\\"address\\\":\\\"上海市-浦东新区-机场镇启航路900路\\\"}\"},{\"isNull\":false,\"name\":\"intend_vehicle_type_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"email\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"wechat\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"work_period\",\"isKey\":false,\"isUpdated\":false,\"value\":\"00:30~00:59,01:00~01:29,01:30~01:59,02:00~02:29,02:30~02:59,03:30~03:59,03:00~03:29,04:00~04:29,06:00~06:29,07:00~07:29,06:30~06:59,08:30~08:59,08:00~08:29,12:30~12:59,17:30~17:29,19:30~19:59,21:30~21:59,20:30~20:59,23:00~23:29\"},{\"isNull\":false,\"name\":\"drv_idcard\",\"isKey\":false,\"isUpdated\":false,\"value\":\"******************\"},{\"isNull\":false,\"name\":\"certi_date\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-05-13\"},{\"isNull\":false,\"name\":\"quasi_driving_type\",\"isKey\":false,\"isUpdated\":false,\"value\":\"C1\"},{\"isNull\":false,\"name\":\"expiry_begin_date\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-05-20\"},{\"isNull\":false,\"name\":\"expiry_end_date\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-06-17\"},{\"isNull\":false,\"name\":\"drvcard_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg.fws.qa.nt.ctripcorp.com/images/0413g120000003rs121D4.png\"},{\"isNull\":false,\"name\":\"idcard_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0410112000846dovs7753.jpg\"},{\"isNull\":false,\"name\":\"drv_head_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0410a1200088cmfz19693.jpg\"},{\"isNull\":false,\"name\":\"people_vehicle_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg.fws.qa.nt.ctripcorp.com/images/0410x12000000347e16C9.png\"},{\"isNull\":false,\"name\":\"net_vehicle_peo_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0410l1200087n96klF61E.jpg\"},{\"isNull\":false,\"name\":\"drv_status\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"freeze_hour\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"freeze_reason\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"freeze_time\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-01-01 12:12:12\"},{\"isNull\":false,\"name\":\"drv_from\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2\"},{\"isNull\":false,\"name\":\"op_from\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"internal_scope\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-05-13 20:10:59.041\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"Annie Cao （曹银萍）\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"曹银萍\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-11-11 11:39:56.305\"},{\"isNull\":false,\"name\":\"salt\",\"isKey\":false,\"isUpdated\":false,\"value\":\"301151\"},{\"isNull\":false,\"name\":\"idcard_back_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0412p1200086sh26587CF.jpg\"},{\"isNull\":false,\"name\":\"coop_mode\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"no_criminal_proof_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"online_time\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-11-11 11:39:56.305\"},{\"isNull\":false,\"name\":\"other_certificate_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"sex\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"nation\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"birthday\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1777-01-01\"},{\"isNull\":false,\"name\":\"idcard_validity\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1777-01-01\"},{\"isNull\":false,\"name\":\"drv_license_number\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"drv_license_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"approve_status\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2\"}],\"eventType\":\"INSERT\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"drv_id\",\"isKey\":true,\"isUpdated\":false,\"value\":\"612402\"},{\"isNull\":false,\"name\":\"drv_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"郑程\"},{\"isNull\":false,\"name\":\"drv_english_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"supplier_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"30804\"},{\"isNull\":false,\"name\":\"drv_language\",\"isKey\":false,\"isUpdated\":false,\"value\":\"cn\"},{\"isNull\":false,\"name\":\"country_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"country_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"China\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"igt_code\",\"isKey\":false,\"isUpdated\":false,\"value\":\"86\"},{\"isNull\":false,\"name\":\"drv_phone\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1526Qv05645\"},{\"isNull\":false,\"name\":\"login_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"login_pwd\",\"isKey\":false,\"isUpdated\":false,\"value\":\"a88b6d641c315d64507fe10bc77ec775\"},{\"isNull\":false,\"name\":\"ppm_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"**********\"},{\"isNull\":false,\"name\":\"qunar_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"**********\"},{\"isNull\":false,\"name\":\"im_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"CSM0000000212492\"},{\"isNull\":false,\"name\":\"vehicle_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"vehicle_license\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"vehicle_type_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"certificate_number\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"drv_addr\",\"isKey\":false,\"isUpdated\":false,\"value\":\"{\\\"longitude\\\":121.80828,\\\"latitude\\\":31.150098,\\\"name\\\":\\\"浦东国际机场T2-国际/港澳台到达\\\",\\\"address\\\":\\\"上海市-浦东新区-机场镇启航路900路\\\"}\"},{\"isNull\":false,\"name\":\"intend_vehicle_type_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"email\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"wechat\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"work_period\",\"isKey\":false,\"isUpdated\":false,\"value\":\"00:30~00:59,01:00~01:29,01:30~01:59,02:00~02:29,02:30~02:59,03:30~03:59,03:00~03:29,04:00~04:29,06:00~06:29,07:00~07:29,06:30~06:59,08:30~08:59,08:00~08:29,12:30~12:59,17:30~17:29,19:30~19:59,21:30~21:59,20:30~20:59,23:00~23:29\"},{\"isNull\":false,\"name\":\"drv_idcard\",\"isKey\":false,\"isUpdated\":false,\"value\":\"******************\"},{\"isNull\":false,\"name\":\"certi_date\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-05-13\"},{\"isNull\":false,\"name\":\"quasi_driving_type\",\"isKey\":false,\"isUpdated\":false,\"value\":\"C1\"},{\"isNull\":false,\"name\":\"expiry_begin_date\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-05-20\"},{\"isNull\":false,\"name\":\"expiry_end_date\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-06-17\"},{\"isNull\":false,\"name\":\"drvcard_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg.fws.qa.nt.ctripcorp.com/images/0413g120000003rs121D4.png\"},{\"isNull\":false,\"name\":\"idcard_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0410112000846dovs7753.jpg\"},{\"isNull\":false,\"name\":\"drv_head_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0410a1200088cmfz19693.jpg\"},{\"isNull\":false,\"name\":\"people_vehicle_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg.fws.qa.nt.ctripcorp.com/images/0410x12000000347e16C9.png\"},{\"isNull\":false,\"name\":\"net_vehicle_peo_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0410l1200087n96klF61E.jpg\"},{\"isNull\":false,\"name\":\"drv_status\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2\"},{\"isNull\":false,\"name\":\"freeze_hour\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"freeze_reason\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"freeze_time\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-01-01 12:12:12\"},{\"isNull\":false,\"name\":\"drv_from\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2\"},{\"isNull\":false,\"name\":\"op_from\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"internal_scope\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-05-13 20:10:59.041\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"Annie Cao （曹银萍）\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"carcheng.zheng\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 14:08:24.497\"},{\"isNull\":false,\"name\":\"salt\",\"isKey\":false,\"isUpdated\":false,\"value\":\"301151\"},{\"isNull\":false,\"name\":\"idcard_back_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0412p1200086sh26587CF.jpg\"},{\"isNull\":false,\"name\":\"coop_mode\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"no_criminal_proof_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"online_time\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 14:08:24.500\"},{\"isNull\":false,\"name\":\"other_certificate_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"sex\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"nation\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"birthday\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1777-01-01\"},{\"isNull\":false,\"name\":\"idcard_validity\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1777-01-01\"},{\"isNull\":false,\"name\":\"drv_license_number\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"drv_license_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"approve_status\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2\"}],\"tableName\":\"drv_driver\"}";
        baseMessage.setProperty("dataChange",drv_driver);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);

        baseMessage = new BaseMessage();
        drv_driver = "{\"otterParseTime\":1605074904549,\"orderKeyInfo\":{\"pks\":[],\"schemaName\":\"dcstransportdb\",\"tableName\":\"drv_driver\"},\"otterSendTime\":1605074904550,\"beforeColumnList\":[{\"isNull\":false,\"name\":\"drv_id\",\"isKey\":true,\"isUpdated\":false,\"value\":\"612402\"},{\"isNull\":false,\"name\":\"drv_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"郑程\"},{\"isNull\":false,\"name\":\"drv_english_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"supplier_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"30804\"},{\"isNull\":false,\"name\":\"drv_language\",\"isKey\":false,\"isUpdated\":false,\"value\":\"cn\"},{\"isNull\":false,\"name\":\"country_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"country_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"China\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"igt_code\",\"isKey\":false,\"isUpdated\":false,\"value\":\"86\"},{\"isNull\":false,\"name\":\"drv_phone\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1526Qv05645\"},{\"isNull\":false,\"name\":\"login_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"login_pwd\",\"isKey\":false,\"isUpdated\":false,\"value\":\"a88b6d641c315d64507fe10bc77ec775\"},{\"isNull\":false,\"name\":\"ppm_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"**********\"},{\"isNull\":false,\"name\":\"qunar_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"**********\"},{\"isNull\":false,\"name\":\"im_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"CSM0000000212492\"},{\"isNull\":false,\"name\":\"vehicle_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"vehicle_license\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"vehicle_type_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"certificate_number\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"drv_addr\",\"isKey\":false,\"isUpdated\":false,\"value\":\"{\\\"longitude\\\":121.80828,\\\"latitude\\\":31.150098,\\\"name\\\":\\\"浦东国际机场T2-国际/港澳台到达\\\",\\\"address\\\":\\\"上海市-浦东新区-机场镇启航路900路\\\"}\"},{\"isNull\":false,\"name\":\"intend_vehicle_type_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"email\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"wechat\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"work_period\",\"isKey\":false,\"isUpdated\":false,\"value\":\"00:30~00:59,01:00~01:29,01:30~01:59,02:00~02:29,02:30~02:59,03:30~03:59,03:00~03:29,04:00~04:29,06:00~06:29,07:00~07:29,06:30~06:59,08:30~08:59,08:00~08:29,12:30~12:59,17:30~17:29,19:30~19:59,21:30~21:59,20:30~20:59,23:00~23:29\"},{\"isNull\":false,\"name\":\"drv_idcard\",\"isKey\":false,\"isUpdated\":false,\"value\":\"******************\"},{\"isNull\":false,\"name\":\"certi_date\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-05-13\"},{\"isNull\":false,\"name\":\"quasi_driving_type\",\"isKey\":false,\"isUpdated\":false,\"value\":\"C1\"},{\"isNull\":false,\"name\":\"expiry_begin_date\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-05-20\"},{\"isNull\":false,\"name\":\"expiry_end_date\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-06-17\"},{\"isNull\":false,\"name\":\"drvcard_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg.fws.qa.nt.ctripcorp.com/images/0413g120000003rs121D4.png\"},{\"isNull\":false,\"name\":\"idcard_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0410112000846dovs7753.jpg\"},{\"isNull\":false,\"name\":\"drv_head_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0410a1200088cmfz19693.jpg\"},{\"isNull\":false,\"name\":\"people_vehicle_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg.fws.qa.nt.ctripcorp.com/images/0410x12000000347e16C9.png\"},{\"isNull\":false,\"name\":\"net_vehicle_peo_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0410l1200087n96klF61E.jpg\"},{\"isNull\":false,\"name\":\"drv_status\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"freeze_hour\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"freeze_reason\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"freeze_time\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-01-01 12:12:12\"},{\"isNull\":false,\"name\":\"drv_from\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2\"},{\"isNull\":false,\"name\":\"op_from\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"internal_scope\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-05-13 20:10:59.041\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"Annie Cao （曹银萍）\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"曹银萍\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-11-11 11:39:56.305\"},{\"isNull\":false,\"name\":\"salt\",\"isKey\":false,\"isUpdated\":false,\"value\":\"301151\"},{\"isNull\":false,\"name\":\"idcard_back_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0412p1200086sh26587CF.jpg\"},{\"isNull\":false,\"name\":\"coop_mode\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"no_criminal_proof_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"online_time\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-11-11 11:39:56.305\"},{\"isNull\":false,\"name\":\"other_certificate_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"sex\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"nation\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"birthday\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1777-01-01\"},{\"isNull\":false,\"name\":\"idcard_validity\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1777-01-01\"},{\"isNull\":false,\"name\":\"drv_license_number\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"drv_license_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"approve_status\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2\"}],\"eventType\":\"DELETE\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"drv_id\",\"isKey\":true,\"isUpdated\":false,\"value\":\"612402\"},{\"isNull\":false,\"name\":\"drv_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"郑程\"},{\"isNull\":false,\"name\":\"drv_english_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"supplier_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"30804\"},{\"isNull\":false,\"name\":\"drv_language\",\"isKey\":false,\"isUpdated\":false,\"value\":\"cn\"},{\"isNull\":false,\"name\":\"country_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"country_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"China\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"igt_code\",\"isKey\":false,\"isUpdated\":false,\"value\":\"86\"},{\"isNull\":false,\"name\":\"drv_phone\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1526Qv05645\"},{\"isNull\":false,\"name\":\"login_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"login_pwd\",\"isKey\":false,\"isUpdated\":false,\"value\":\"a88b6d641c315d64507fe10bc77ec775\"},{\"isNull\":false,\"name\":\"ppm_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"**********\"},{\"isNull\":false,\"name\":\"qunar_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"**********\"},{\"isNull\":false,\"name\":\"im_account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"CSM0000000212492\"},{\"isNull\":false,\"name\":\"vehicle_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"vehicle_license\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"vehicle_type_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"certificate_number\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"drv_addr\",\"isKey\":false,\"isUpdated\":false,\"value\":\"{\\\"longitude\\\":121.80828,\\\"latitude\\\":31.150098,\\\"name\\\":\\\"浦东国际机场T2-国际/港澳台到达\\\",\\\"address\\\":\\\"上海市-浦东新区-机场镇启航路900路\\\"}\"},{\"isNull\":false,\"name\":\"intend_vehicle_type_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"email\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"wechat\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"work_period\",\"isKey\":false,\"isUpdated\":false,\"value\":\"00:30~00:59,01:00~01:29,01:30~01:59,02:00~02:29,02:30~02:59,03:30~03:59,03:00~03:29,04:00~04:29,06:00~06:29,07:00~07:29,06:30~06:59,08:30~08:59,08:00~08:29,12:30~12:59,17:30~17:29,19:30~19:59,21:30~21:59,20:30~20:59,23:00~23:29\"},{\"isNull\":false,\"name\":\"drv_idcard\",\"isKey\":false,\"isUpdated\":false,\"value\":\"******************\"},{\"isNull\":false,\"name\":\"certi_date\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-05-13\"},{\"isNull\":false,\"name\":\"quasi_driving_type\",\"isKey\":false,\"isUpdated\":false,\"value\":\"C1\"},{\"isNull\":false,\"name\":\"expiry_begin_date\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-05-20\"},{\"isNull\":false,\"name\":\"expiry_end_date\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-06-17\"},{\"isNull\":false,\"name\":\"drvcard_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg.fws.qa.nt.ctripcorp.com/images/0413g120000003rs121D4.png\"},{\"isNull\":false,\"name\":\"idcard_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0410112000846dovs7753.jpg\"},{\"isNull\":false,\"name\":\"drv_head_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0410a1200088cmfz19693.jpg\"},{\"isNull\":false,\"name\":\"people_vehicle_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg.fws.qa.nt.ctripcorp.com/images/0410x12000000347e16C9.png\"},{\"isNull\":false,\"name\":\"net_vehicle_peo_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0410l1200087n96klF61E.jpg\"},{\"isNull\":false,\"name\":\"drv_status\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2\"},{\"isNull\":false,\"name\":\"freeze_hour\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"freeze_reason\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"freeze_time\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-01-01 12:12:12\"},{\"isNull\":false,\"name\":\"drv_from\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2\"},{\"isNull\":false,\"name\":\"op_from\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"internal_scope\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-05-13 20:10:59.041\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"Annie Cao （曹银萍）\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"carcheng.zheng\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 14:08:24.497\"},{\"isNull\":false,\"name\":\"salt\",\"isKey\":false,\"isUpdated\":false,\"value\":\"301151\"},{\"isNull\":false,\"name\":\"idcard_back_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"https://dimg04.c-ctrip.com/images/0412p1200086sh26587CF.jpg\"},{\"isNull\":false,\"name\":\"coop_mode\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"no_criminal_proof_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"online_time\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 14:08:24.500\"},{\"isNull\":false,\"name\":\"other_certificate_img\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"sex\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"nation\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"birthday\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1777-01-01\"},{\"isNull\":false,\"name\":\"idcard_validity\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1777-01-01\"},{\"isNull\":false,\"name\":\"drv_license_number\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"drv_license_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"approve_status\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2\"}],\"tableName\":\"drv_driver\"}";
        baseMessage.setProperty("dataChange",drv_driver);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);

        baseMessage = new BaseMessage();
        String tsp_transport_group_driver_relation = "{\"otterParseTime\":1605079430379,\"otterSendTime\":1605079430379,\"beforeColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":true,\"value\":\"60023\"},{\"isNull\":false,\"name\":\"transport_group_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"10617\"},{\"isNull\":false,\"name\":\"drv_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1006771\"},{\"isNull\":false,\"name\":\"active\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:23:50.375\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_474921\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_474921\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:23:50.375\"}],\"eventType\":\"INSERT\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":true,\"value\":\"60023\"},{\"isNull\":false,\"name\":\"transport_group_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"10617\"},{\"isNull\":false,\"name\":\"drv_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1006771\"},{\"isNull\":false,\"name\":\"active\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:23:50.375\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_474921\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_474921\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:23:50.375\"}],\"tableName\":\"tsp_transport_group_driver_relation\"}";
        baseMessage.setProperty("dataChange",tsp_transport_group_driver_relation);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);

        baseMessage = new BaseMessage();
        tsp_transport_group_driver_relation = "{\"otterParseTime\":1605079430379,\"otterSendTime\":1605079430379,\"beforeColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":true,\"value\":\"60023\"},{\"isNull\":false,\"name\":\"transport_group_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"10617\"},{\"isNull\":false,\"name\":\"drv_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1006771\"},{\"isNull\":false,\"name\":\"active\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:23:50.375\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_474921\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_474921\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:23:50.375\"}],\"eventType\":\"UPDATE\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":true,\"value\":\"60023\"},{\"isNull\":false,\"name\":\"transport_group_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"10617\"},{\"isNull\":false,\"name\":\"drv_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1006771\"},{\"isNull\":false,\"name\":\"active\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:23:50.375\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_474921\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_474921\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:23:50.375\"}],\"tableName\":\"tsp_transport_group_driver_relation\"}";
        baseMessage.setProperty("dataChange",tsp_transport_group_driver_relation);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);

        baseMessage = new BaseMessage();
        tsp_transport_group_driver_relation = "{\"otterParseTime\":1605079430379,\"otterSendTime\":1605079430379,\"beforeColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":true,\"value\":\"60023\"},{\"isNull\":false,\"name\":\"transport_group_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"10617\"},{\"isNull\":false,\"name\":\"drv_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1006771\"},{\"isNull\":false,\"name\":\"active\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:23:50.375\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_474921\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_474921\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:23:50.375\"}],\"eventType\":\"DELETE\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":true,\"value\":\"60023\"},{\"isNull\":false,\"name\":\"transport_group_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"10617\"},{\"isNull\":false,\"name\":\"drv_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1006771\"},{\"isNull\":false,\"name\":\"active\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:23:50.375\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_474921\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_474921\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:23:50.375\"}],\"tableName\":\"tsp_transport_group_driver_relation\"}";
        baseMessage.setProperty("dataChange",tsp_transport_group_driver_relation);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);

        baseMessage = new BaseMessage();
        String drv_driver_leave = "{\"otterParseTime\":1605079332128,\"otterSendTime\":1605079332129,\"beforeColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":true,\"value\":\"134529\"},{\"isNull\":false,\"name\":\"drv_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"355441\"},{\"isNull\":false,\"name\":\"drv_name\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"leave_begin_time\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-12 00:00:00\"},{\"isNull\":false,\"name\":\"leave_end_time\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-14 23:59:59\"},{\"isNull\":false,\"name\":\"leave_reason\",\"isKey\":false,\"isUpdated\":true,\"value\":\"车辆故障\"},{\"isNull\":false,\"name\":\"leave_status\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:22:12.122\"},{\"isNull\":false,\"name\":\"operate_type\",\"isKey\":false,\"isUpdated\":true,\"value\":\"3\"},{\"isNull\":false,\"name\":\"active\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":true,\"name\":\"datachange_deltime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_441234\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_441234\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:22:12.122\"}],\"eventType\":\"INSERT\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":true,\"value\":\"134529\"},{\"isNull\":false,\"name\":\"drv_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"355441\"},{\"isNull\":false,\"name\":\"drv_name\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"leave_begin_time\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-12 00:00:00\"},{\"isNull\":false,\"name\":\"leave_end_time\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-14 23:59:59\"},{\"isNull\":false,\"name\":\"leave_reason\",\"isKey\":false,\"isUpdated\":true,\"value\":\"车辆故障\"},{\"isNull\":false,\"name\":\"leave_status\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:22:12.122\"},{\"isNull\":false,\"name\":\"operate_type\",\"isKey\":false,\"isUpdated\":true,\"value\":\"3\"},{\"isNull\":false,\"name\":\"active\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":true,\"name\":\"datachange_deltime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_441234\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_441234\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:22:12.122\"}],\"tableName\":\"drv_driver_leave\"}";
        baseMessage.setProperty("dataChange",drv_driver_leave);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);

        baseMessage = new BaseMessage();
        drv_driver_leave = "{\"otterParseTime\":1605079332128,\"otterSendTime\":1605079332129,\"beforeColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":true,\"value\":\"134529\"},{\"isNull\":false,\"name\":\"drv_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"355441\"},{\"isNull\":false,\"name\":\"drv_name\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"leave_begin_time\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-12 00:00:00\"},{\"isNull\":false,\"name\":\"leave_end_time\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-14 23:59:59\"},{\"isNull\":false,\"name\":\"leave_reason\",\"isKey\":false,\"isUpdated\":true,\"value\":\"车辆故障\"},{\"isNull\":false,\"name\":\"leave_status\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:22:12.122\"},{\"isNull\":false,\"name\":\"operate_type\",\"isKey\":false,\"isUpdated\":true,\"value\":\"3\"},{\"isNull\":false,\"name\":\"active\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":true,\"name\":\"datachange_deltime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_441234\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_441234\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:22:12.122\"}],\"eventType\":\"UPDATE\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":true,\"value\":\"134529\"},{\"isNull\":false,\"name\":\"drv_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"355441\"},{\"isNull\":false,\"name\":\"drv_name\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"leave_begin_time\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-12 00:00:00\"},{\"isNull\":false,\"name\":\"leave_end_time\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-14 23:59:59\"},{\"isNull\":false,\"name\":\"leave_reason\",\"isKey\":false,\"isUpdated\":true,\"value\":\"车辆故障\"},{\"isNull\":false,\"name\":\"leave_status\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:22:12.122\"},{\"isNull\":false,\"name\":\"operate_type\",\"isKey\":false,\"isUpdated\":true,\"value\":\"3\"},{\"isNull\":false,\"name\":\"active\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":true,\"name\":\"datachange_deltime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_441234\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_441234\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:22:12.122\"}],\"tableName\":\"drv_driver_leave\"}";
        baseMessage.setProperty("dataChange",drv_driver_leave);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);

        baseMessage = new BaseMessage();
        drv_driver_leave = "{\"otterParseTime\":1605079332128,\"otterSendTime\":1605079332129,\"beforeColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":true,\"value\":\"134529\"},{\"isNull\":false,\"name\":\"drv_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"355441\"},{\"isNull\":false,\"name\":\"drv_name\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"leave_begin_time\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-12 00:00:00\"},{\"isNull\":false,\"name\":\"leave_end_time\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-14 23:59:59\"},{\"isNull\":false,\"name\":\"leave_reason\",\"isKey\":false,\"isUpdated\":true,\"value\":\"车辆故障\"},{\"isNull\":false,\"name\":\"leave_status\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:22:12.122\"},{\"isNull\":false,\"name\":\"operate_type\",\"isKey\":false,\"isUpdated\":true,\"value\":\"3\"},{\"isNull\":false,\"name\":\"active\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":true,\"name\":\"datachange_deltime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_441234\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_441234\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:22:12.122\"}],\"eventType\":\"DELETE\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":true,\"value\":\"134529\"},{\"isNull\":false,\"name\":\"drv_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"355441\"},{\"isNull\":false,\"name\":\"drv_name\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"leave_begin_time\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-12 00:00:00\"},{\"isNull\":false,\"name\":\"leave_end_time\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-14 23:59:59\"},{\"isNull\":false,\"name\":\"leave_reason\",\"isKey\":false,\"isUpdated\":true,\"value\":\"车辆故障\"},{\"isNull\":false,\"name\":\"leave_status\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:22:12.122\"},{\"isNull\":false,\"name\":\"operate_type\",\"isKey\":false,\"isUpdated\":true,\"value\":\"3\"},{\"isNull\":false,\"name\":\"active\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":true,\"name\":\"datachange_deltime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_441234\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"vbk_441234\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:22:12.122\"}],\"tableName\":\"drv_driver_leave\"}";
        baseMessage.setProperty("dataChange",drv_driver_leave);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);

        baseMessage = new BaseMessage();
        String veh_vehicle = "{\"otterParseTime\":1605079039855,\"otterSendTime\":1605079039856,\"beforeColumnList\":[],\"eventType\":\"INSERT\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"vehicle_id\",\"isKey\":true,\"isUpdated\":true,\"value\":\"1007865\"},{\"isNull\":false,\"name\":\"vehicle_license\",\"isKey\":false,\"isUpdated\":true,\"value\":\"青ALN234\"},{\"isNull\":false,\"name\":\"supplier_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"6206\"},{\"isNull\":false,\"name\":\"country_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"country_name\",\"isKey\":false,\"isUpdated\":true,\"value\":\"中国\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"73\"},{\"isNull\":false,\"name\":\"vehicle_type_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"120\"},{\"isNull\":false,\"name\":\"vehicle_brand_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"197\"},{\"isNull\":false,\"name\":\"vehicle_series\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2565\"},{\"isNull\":false,\"name\":\"vehicle_color_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"39\"},{\"isNull\":false,\"name\":\"vehicle_energy_type\",\"isKey\":false,\"isUpdated\":true,\"value\":\"65\"},{\"isNull\":false,\"name\":\"vin\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"regst_date\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1777-01-01\"},{\"isNull\":false,\"name\":\"using_nature\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"net_tans_ctfct_img\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"vehicle_certi_img\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"vehicle_full_img\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"vehicle_front_img\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"vehicle_back_img\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"vehicle_trunk_img\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"has_drv\",\"isKey\":false,\"isUpdated\":true,\"value\":\"0\"},{\"isNull\":false,\"name\":\"comments\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:17:19.819\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"Admin6206\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"Admin6206\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:17:19.842\"},{\"isNull\":false,\"name\":\"vehicle_status\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"vehicle_license_owner\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"}],\"tableName\":\"veh_vehicle\"}";
        baseMessage.setProperty("dataChange",veh_vehicle);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);

        baseMessage = new BaseMessage();
        veh_vehicle = "{\"otterParseTime\":1605079039855,\"otterSendTime\":1605079039856,\"beforeColumnList\":[],\"eventType\":\"UPDATE\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"vehicle_id\",\"isKey\":true,\"isUpdated\":true,\"value\":\"1007865\"},{\"isNull\":false,\"name\":\"vehicle_license\",\"isKey\":false,\"isUpdated\":true,\"value\":\"青ALN234\"},{\"isNull\":false,\"name\":\"supplier_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"6206\"},{\"isNull\":false,\"name\":\"country_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"country_name\",\"isKey\":false,\"isUpdated\":true,\"value\":\"中国\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"73\"},{\"isNull\":false,\"name\":\"vehicle_type_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"120\"},{\"isNull\":false,\"name\":\"vehicle_brand_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"197\"},{\"isNull\":false,\"name\":\"vehicle_series\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2565\"},{\"isNull\":false,\"name\":\"vehicle_color_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"39\"},{\"isNull\":false,\"name\":\"vehicle_energy_type\",\"isKey\":false,\"isUpdated\":true,\"value\":\"65\"},{\"isNull\":false,\"name\":\"vin\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"regst_date\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1777-01-01\"},{\"isNull\":false,\"name\":\"using_nature\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"net_tans_ctfct_img\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"vehicle_certi_img\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"vehicle_full_img\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"vehicle_front_img\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"vehicle_back_img\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"vehicle_trunk_img\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"has_drv\",\"isKey\":false,\"isUpdated\":true,\"value\":\"0\"},{\"isNull\":false,\"name\":\"comments\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:17:19.819\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"Admin6206\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"Admin6206\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:17:19.842\"},{\"isNull\":false,\"name\":\"vehicle_status\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"vehicle_license_owner\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"}],\"tableName\":\"veh_vehicle\"}";
        baseMessage.setProperty("dataChange",veh_vehicle);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);

        baseMessage = new BaseMessage();
        veh_vehicle = "{\"otterParseTime\":1605079039855,\"otterSendTime\":1605079039856,\"beforeColumnList\":[],\"eventType\":\"DELETE\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"vehicle_id\",\"isKey\":true,\"isUpdated\":true,\"value\":\"1007865\"},{\"isNull\":false,\"name\":\"vehicle_license\",\"isKey\":false,\"isUpdated\":true,\"value\":\"青ALN234\"},{\"isNull\":false,\"name\":\"supplier_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"6206\"},{\"isNull\":false,\"name\":\"country_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"country_name\",\"isKey\":false,\"isUpdated\":true,\"value\":\"中国\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"73\"},{\"isNull\":false,\"name\":\"vehicle_type_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"120\"},{\"isNull\":false,\"name\":\"vehicle_brand_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"197\"},{\"isNull\":false,\"name\":\"vehicle_series\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2565\"},{\"isNull\":false,\"name\":\"vehicle_color_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"39\"},{\"isNull\":false,\"name\":\"vehicle_energy_type\",\"isKey\":false,\"isUpdated\":true,\"value\":\"65\"},{\"isNull\":false,\"name\":\"vin\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"regst_date\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1777-01-01\"},{\"isNull\":false,\"name\":\"using_nature\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"net_tans_ctfct_img\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"vehicle_certi_img\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"vehicle_full_img\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"vehicle_front_img\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"vehicle_back_img\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"vehicle_trunk_img\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"has_drv\",\"isKey\":false,\"isUpdated\":true,\"value\":\"0\"},{\"isNull\":false,\"name\":\"comments\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:17:19.819\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"Admin6206\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"Admin6206\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:17:19.842\"},{\"isNull\":false,\"name\":\"vehicle_status\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"vehicle_license_owner\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"}],\"tableName\":\"veh_vehicle\"}";
        baseMessage.setProperty("dataChange",veh_vehicle);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);

        baseMessage = new BaseMessage();
        String tsp_transport_group = "{\"otterParseTime\":1605079904324,\"orderKeyInfo\":{\"pks\":[],\"schemaName\":\"dcstransportdb\",\"tableName\":\"tsp_transport_group\"},\"otterSendTime\":1605079904325,\"beforeColumnList\":[{\"isNull\":false,\"name\":\"transport_group_id\",\"isKey\":true,\"isUpdated\":false,\"value\":\"268\"},{\"isNull\":false,\"name\":\"supplier_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"30804\"},{\"isNull\":false,\"name\":\"transport_group_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"变更记录\"},{\"isNull\":false,\"name\":\"transport_group_mode\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1003\"},{\"isNull\":false,\"name\":\"country_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"country_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"dispatcher\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"dispatcher_phone\",\"isKey\":false,\"isUpdated\":false,\"value\":\"188mzkW0305\"},{\"isNull\":false,\"name\":\"dispatcher_language\",\"isKey\":false,\"isUpdated\":false,\"value\":\"cn,kr,jp,ms,es\"},{\"isNull\":false,\"name\":\"take_order_limit_time\",\"isKey\":false,\"isUpdated\":false,\"value\":\"240\"},{\"isNull\":false,\"name\":\"group_status\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-11-02 12:40:00.276\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-11-09 19:52:10.900\"},{\"isNull\":false,\"name\":\"igt_code\",\"isKey\":false,\"isUpdated\":false,\"value\":\"65\"},{\"isNull\":false,\"name\":\"area_group_type\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2\"},{\"isNull\":false,\"name\":\"area_group_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"24\"},{\"isNull\":false,\"name\":\"contract_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"190726\"},{\"isNull\":false,\"name\":\"point_city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"258\"},{\"isNull\":false,\"name\":\"sales_model\",\"isKey\":false,\"isUpdated\":false,\"value\":\"5\"},{\"isNull\":false,\"name\":\"status_disable\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"inform_switch\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"inform_phone\",\"isKey\":false,\"isUpdated\":false,\"value\":\"18810490305\"},{\"isNull\":false,\"name\":\"inform_email\",\"isKey\":false,\"isUpdated\":false,\"value\":\"<EMAIL>\"}],\"eventType\":\"UPDATE\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"transport_group_id\",\"isKey\":true,\"isUpdated\":false,\"value\":\"268\"},{\"isNull\":false,\"name\":\"supplier_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"30804\"},{\"isNull\":false,\"name\":\"transport_group_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"变更记录\"},{\"isNull\":false,\"name\":\"transport_group_mode\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1003\"},{\"isNull\":false,\"name\":\"country_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"country_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"dispatcher\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"dispatcher_phone\",\"isKey\":false,\"isUpdated\":false,\"value\":\"188mzkW0305\"},{\"isNull\":false,\"name\":\"dispatcher_language\",\"isKey\":false,\"isUpdated\":true,\"value\":\"cn,kr,jp,ms,es,hk\"},{\"isNull\":false,\"name\":\"take_order_limit_time\",\"isKey\":false,\"isUpdated\":false,\"value\":\"240\"},{\"isNull\":false,\"name\":\"group_status\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-11-02 12:40:00.276\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:31:44.279\"},{\"isNull\":false,\"name\":\"igt_code\",\"isKey\":false,\"isUpdated\":false,\"value\":\"65\"},{\"isNull\":false,\"name\":\"area_group_type\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2\"},{\"isNull\":false,\"name\":\"area_group_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"24\"},{\"isNull\":false,\"name\":\"contract_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"190726\"},{\"isNull\":false,\"name\":\"point_city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"258\"},{\"isNull\":false,\"name\":\"sales_model\",\"isKey\":false,\"isUpdated\":false,\"value\":\"5\"},{\"isNull\":false,\"name\":\"status_disable\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"inform_switch\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"inform_phone\",\"isKey\":false,\"isUpdated\":false,\"value\":\"18810490305\"},{\"isNull\":false,\"name\":\"inform_email\",\"isKey\":false,\"isUpdated\":false,\"value\":\"<EMAIL>\"}],\"tableName\":\"tsp_transport_group\"}";
        baseMessage.setProperty("dataChange",tsp_transport_group);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);

        baseMessage = new BaseMessage();
        tsp_transport_group = "{\"otterParseTime\":1605079904324,\"orderKeyInfo\":{\"pks\":[],\"schemaName\":\"dcstransportdb\",\"tableName\":\"tsp_transport_group\"},\"otterSendTime\":1605079904325,\"beforeColumnList\":[{\"isNull\":false,\"name\":\"transport_group_id\",\"isKey\":true,\"isUpdated\":false,\"value\":\"268\"},{\"isNull\":false,\"name\":\"supplier_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"30804\"},{\"isNull\":false,\"name\":\"transport_group_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"变更记录\"},{\"isNull\":false,\"name\":\"transport_group_mode\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1003\"},{\"isNull\":false,\"name\":\"country_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"country_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"dispatcher\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"dispatcher_phone\",\"isKey\":false,\"isUpdated\":false,\"value\":\"188mzkW0305\"},{\"isNull\":false,\"name\":\"dispatcher_language\",\"isKey\":false,\"isUpdated\":false,\"value\":\"cn,kr,jp,ms,es\"},{\"isNull\":false,\"name\":\"take_order_limit_time\",\"isKey\":false,\"isUpdated\":false,\"value\":\"240\"},{\"isNull\":false,\"name\":\"group_status\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-11-02 12:40:00.276\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-11-09 19:52:10.900\"},{\"isNull\":false,\"name\":\"igt_code\",\"isKey\":false,\"isUpdated\":false,\"value\":\"65\"},{\"isNull\":false,\"name\":\"area_group_type\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2\"},{\"isNull\":false,\"name\":\"area_group_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"24\"},{\"isNull\":false,\"name\":\"contract_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"190726\"},{\"isNull\":false,\"name\":\"point_city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"258\"},{\"isNull\":false,\"name\":\"sales_model\",\"isKey\":false,\"isUpdated\":false,\"value\":\"5\"},{\"isNull\":false,\"name\":\"status_disable\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"inform_switch\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"inform_phone\",\"isKey\":false,\"isUpdated\":false,\"value\":\"18810490305\"},{\"isNull\":false,\"name\":\"inform_email\",\"isKey\":false,\"isUpdated\":false,\"value\":\"<EMAIL>\"}],\"eventType\":\"DELETE\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"transport_group_id\",\"isKey\":true,\"isUpdated\":false,\"value\":\"268\"},{\"isNull\":false,\"name\":\"supplier_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"30804\"},{\"isNull\":false,\"name\":\"transport_group_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"变更记录\"},{\"isNull\":false,\"name\":\"transport_group_mode\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1003\"},{\"isNull\":false,\"name\":\"country_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"country_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"dispatcher\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"dispatcher_phone\",\"isKey\":false,\"isUpdated\":false,\"value\":\"188mzkW0305\"},{\"isNull\":false,\"name\":\"dispatcher_language\",\"isKey\":false,\"isUpdated\":true,\"value\":\"cn,kr,jp,ms,es,hk\"},{\"isNull\":false,\"name\":\"take_order_limit_time\",\"isKey\":false,\"isUpdated\":false,\"value\":\"240\"},{\"isNull\":false,\"name\":\"group_status\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-11-02 12:40:00.276\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:31:44.279\"},{\"isNull\":false,\"name\":\"igt_code\",\"isKey\":false,\"isUpdated\":false,\"value\":\"65\"},{\"isNull\":false,\"name\":\"area_group_type\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2\"},{\"isNull\":false,\"name\":\"area_group_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"24\"},{\"isNull\":false,\"name\":\"contract_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"190726\"},{\"isNull\":false,\"name\":\"point_city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"258\"},{\"isNull\":false,\"name\":\"sales_model\",\"isKey\":false,\"isUpdated\":false,\"value\":\"5\"},{\"isNull\":false,\"name\":\"status_disable\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"inform_switch\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"inform_phone\",\"isKey\":false,\"isUpdated\":false,\"value\":\"18810490305\"},{\"isNull\":false,\"name\":\"inform_email\",\"isKey\":false,\"isUpdated\":false,\"value\":\"<EMAIL>\"}],\"tableName\":\"tsp_transport_group\"}";
        baseMessage.setProperty("dataChange",tsp_transport_group);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);

        baseMessage = new BaseMessage();
        tsp_transport_group = "{\"otterParseTime\":1605079904324,\"orderKeyInfo\":{\"pks\":[],\"schemaName\":\"dcstransportdb\",\"tableName\":\"tsp_transport_group\"},\"otterSendTime\":1605079904325,\"beforeColumnList\":[{\"isNull\":false,\"name\":\"transport_group_id\",\"isKey\":true,\"isUpdated\":false,\"value\":\"268\"},{\"isNull\":false,\"name\":\"supplier_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"30804\"},{\"isNull\":false,\"name\":\"transport_group_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"变更记录\"},{\"isNull\":false,\"name\":\"transport_group_mode\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1003\"},{\"isNull\":false,\"name\":\"country_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"country_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"dispatcher\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"dispatcher_phone\",\"isKey\":false,\"isUpdated\":false,\"value\":\"188mzkW0305\"},{\"isNull\":false,\"name\":\"dispatcher_language\",\"isKey\":false,\"isUpdated\":false,\"value\":\"cn,kr,jp,ms,es\"},{\"isNull\":false,\"name\":\"take_order_limit_time\",\"isKey\":false,\"isUpdated\":false,\"value\":\"240\"},{\"isNull\":false,\"name\":\"group_status\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-11-02 12:40:00.276\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-11-09 19:52:10.900\"},{\"isNull\":false,\"name\":\"igt_code\",\"isKey\":false,\"isUpdated\":false,\"value\":\"65\"},{\"isNull\":false,\"name\":\"area_group_type\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2\"},{\"isNull\":false,\"name\":\"area_group_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"24\"},{\"isNull\":false,\"name\":\"contract_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"190726\"},{\"isNull\":false,\"name\":\"point_city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"258\"},{\"isNull\":false,\"name\":\"sales_model\",\"isKey\":false,\"isUpdated\":false,\"value\":\"5\"},{\"isNull\":false,\"name\":\"status_disable\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"inform_switch\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"inform_phone\",\"isKey\":false,\"isUpdated\":false,\"value\":\"18810490305\"},{\"isNull\":false,\"name\":\"inform_email\",\"isKey\":false,\"isUpdated\":false,\"value\":\"<EMAIL>\"}],\"eventType\":\"INSERT\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"transport_group_id\",\"isKey\":true,\"isUpdated\":false,\"value\":\"268\"},{\"isNull\":false,\"name\":\"supplier_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"30804\"},{\"isNull\":false,\"name\":\"transport_group_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"变更记录\"},{\"isNull\":false,\"name\":\"transport_group_mode\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1003\"},{\"isNull\":false,\"name\":\"country_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"country_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"dispatcher\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"dispatcher_phone\",\"isKey\":false,\"isUpdated\":false,\"value\":\"188mzkW0305\"},{\"isNull\":false,\"name\":\"dispatcher_language\",\"isKey\":false,\"isUpdated\":true,\"value\":\"cn,kr,jp,ms,es,hk\"},{\"isNull\":false,\"name\":\"take_order_limit_time\",\"isKey\":false,\"isUpdated\":false,\"value\":\"240\"},{\"isNull\":false,\"name\":\"group_status\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-11-02 12:40:00.276\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:31:44.279\"},{\"isNull\":false,\"name\":\"igt_code\",\"isKey\":false,\"isUpdated\":false,\"value\":\"65\"},{\"isNull\":false,\"name\":\"area_group_type\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2\"},{\"isNull\":false,\"name\":\"area_group_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"24\"},{\"isNull\":false,\"name\":\"contract_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"190726\"},{\"isNull\":false,\"name\":\"point_city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"258\"},{\"isNull\":false,\"name\":\"sales_model\",\"isKey\":false,\"isUpdated\":false,\"value\":\"5\"},{\"isNull\":false,\"name\":\"status_disable\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0\"},{\"isNull\":false,\"name\":\"inform_switch\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"inform_phone\",\"isKey\":false,\"isUpdated\":false,\"value\":\"18810490305\"},{\"isNull\":false,\"name\":\"inform_email\",\"isKey\":false,\"isUpdated\":false,\"value\":\"<EMAIL>\"}],\"tableName\":\"tsp_transport_group\"}";
        baseMessage.setProperty("dataChange",tsp_transport_group);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);

        baseMessage = new BaseMessage();
        String tsp_into_order_config = "{\"otterParseTime\":1605080021464,\"orderKeyInfo\":{\"pks\":[],\"schemaName\":\"dcstransportdb\",\"tableName\":\"tsp_into_order_config\"},\"otterSendTime\":1605080021465,\"beforeColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":true,\"value\":\"179\"},{\"isNull\":false,\"name\":\"country_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"country_name\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"258\"},{\"isNull\":false,\"name\":\"location_code\",\"isKey\":false,\"isUpdated\":true,\"value\":\"FOC\"},{\"isNull\":false,\"name\":\"transport_group_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"268\"},{\"isNull\":false,\"name\":\"location_type\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"config\",\"isKey\":false,\"isUpdated\":true,\"value\":\"[{\\\"time\\\":\\\"02:00-02:59\\\",\\\"orderCount\\\":1}]\"},{\"isNull\":false,\"name\":\"active\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-05 17:04:18.086\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-05 17:04:18.134\"}],\"eventType\":\"UPDATE\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":true,\"value\":\"179\"},{\"isNull\":false,\"name\":\"country_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"country_name\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"258\"},{\"isNull\":false,\"name\":\"location_code\",\"isKey\":false,\"isUpdated\":true,\"value\":\"FOC\"},{\"isNull\":false,\"name\":\"transport_group_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"268\"},{\"isNull\":false,\"name\":\"location_type\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"config\",\"isKey\":false,\"isUpdated\":true,\"value\":\"[{\\\"time\\\":\\\"02:00-02:59\\\",\\\"orderCount\\\":2}]\"},{\"isNull\":false,\"name\":\"active\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-05 17:04:18.086\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:33:41.442\"}],\"tableName\":\"tsp_into_order_config\"}";
        baseMessage.setProperty("dataChange",tsp_into_order_config);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);

        baseMessage = new BaseMessage();
        tsp_into_order_config = "{\"otterParseTime\":1605080021464,\"orderKeyInfo\":{\"pks\":[],\"schemaName\":\"dcstransportdb\",\"tableName\":\"tsp_into_order_config\"},\"otterSendTime\":1605080021465,\"beforeColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":false,\"value\":\"179\"},{\"isNull\":false,\"name\":\"country_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"country_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"258\"},{\"isNull\":false,\"name\":\"location_code\",\"isKey\":false,\"isUpdated\":false,\"value\":\"FOC\"},{\"isNull\":false,\"name\":\"transport_group_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"268\"},{\"isNull\":false,\"name\":\"location_type\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"config\",\"isKey\":false,\"isUpdated\":false,\"value\":\"[{\\\"time\\\":\\\"02:00-02:59\\\",\\\"orderCount\\\":1}]\"},{\"isNull\":false,\"name\":\"active\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-11-05 17:04:18.086\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-11-05 17:04:18.134\"}],\"eventType\":\"DELETE\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":false,\"value\":\"179\"},{\"isNull\":false,\"name\":\"country_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"country_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"258\"},{\"isNull\":false,\"name\":\"location_code\",\"isKey\":false,\"isUpdated\":false,\"value\":\"FOC\"},{\"isNull\":false,\"name\":\"transport_group_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"268\"},{\"isNull\":false,\"name\":\"location_type\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"config\",\"isKey\":false,\"isUpdated\":true,\"value\":\"[{\\\"time\\\":\\\"02:00-02:59\\\",\\\"orderCount\\\":2}]\"},{\"isNull\":false,\"name\":\"active\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-11-05 17:04:18.086\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:33:41.442\"}],\"tableName\":\"tsp_into_order_config\"}";
        baseMessage.setProperty("dataChange",tsp_into_order_config);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);

        baseMessage = new BaseMessage();
        tsp_into_order_config = "{\"otterParseTime\":1605080021464,\"orderKeyInfo\":{\"pks\":[],\"schemaName\":\"dcstransportdb\",\"tableName\":\"tsp_into_order_config\"},\"otterSendTime\":1605080021465,\"beforeColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":false,\"value\":\"179\"},{\"isNull\":false,\"name\":\"country_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"country_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"258\"},{\"isNull\":false,\"name\":\"location_code\",\"isKey\":false,\"isUpdated\":false,\"value\":\"FOC\"},{\"isNull\":false,\"name\":\"transport_group_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"268\"},{\"isNull\":false,\"name\":\"location_type\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"config\",\"isKey\":false,\"isUpdated\":false,\"value\":\"[{\\\"time\\\":\\\"02:00-02:59\\\",\\\"orderCount\\\":1}]\"},{\"isNull\":false,\"name\":\"active\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-11-05 17:04:18.086\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-11-05 17:04:18.134\"}],\"eventType\":\"INSERT\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":false,\"value\":\"179\"},{\"isNull\":false,\"name\":\"country_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"country_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"258\"},{\"isNull\":false,\"name\":\"location_code\",\"isKey\":false,\"isUpdated\":false,\"value\":\"FOC\"},{\"isNull\":false,\"name\":\"transport_group_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"268\"},{\"isNull\":false,\"name\":\"location_type\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"config\",\"isKey\":false,\"isUpdated\":true,\"value\":\"[{\\\"time\\\":\\\"02:00-02:59\\\",\\\"orderCount\\\":2}]\"},{\"isNull\":false,\"name\":\"active\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2020-11-05 17:04:18.086\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":false,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:33:41.442\"}],\"tableName\":\"tsp_into_order_config\"}";
        baseMessage.setProperty("dataChange",tsp_into_order_config);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);

        baseMessage = new BaseMessage();
        String tsp_transport_group_sku_area_relation = "{\"otterParseTime\":1605080426782,\"orderKeyInfo\":{\"pks\":[],\"schemaName\":\"dcstransportdb\",\"tableName\":\"tsp_transport_group_sku_area_relation\"},\"otterSendTime\":1605080426783,\"beforeColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":true,\"value\":\"2460\"},{\"isNull\":false,\"name\":\"transport_group_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"268\"},{\"isNull\":false,\"name\":\"sku_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"373303\"},{\"isNull\":false,\"name\":\"service_area_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"0\"},{\"isNull\":false,\"name\":\"active\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:40:26.755\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:40:26.755\"},{\"isNull\":false,\"name\":\"service_area_type\",\"isKey\":false,\"isUpdated\":true,\"value\":\"0\"}],\"eventType\":\"INSERT\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":true,\"value\":\"2460\"},{\"isNull\":false,\"name\":\"transport_group_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"268\"},{\"isNull\":false,\"name\":\"sku_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"373303\"},{\"isNull\":false,\"name\":\"service_area_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"0\"},{\"isNull\":false,\"name\":\"active\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:40:26.755\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:40:26.755\"},{\"isNull\":false,\"name\":\"service_area_type\",\"isKey\":false,\"isUpdated\":true,\"value\":\"0\"}],\"tableName\":\"tsp_transport_group_sku_area_relation\"}";
        baseMessage.setProperty("dataChange",tsp_transport_group_sku_area_relation);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);

        baseMessage = new BaseMessage();
        tsp_transport_group_sku_area_relation = "{\"otterParseTime\":1605080426782,\"orderKeyInfo\":{\"pks\":[],\"schemaName\":\"dcstransportdb\",\"tableName\":\"tsp_transport_group_sku_area_relation\"},\"otterSendTime\":1605080426783,\"beforeColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":true,\"value\":\"2460\"},{\"isNull\":false,\"name\":\"transport_group_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"268\"},{\"isNull\":false,\"name\":\"sku_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"373303\"},{\"isNull\":false,\"name\":\"service_area_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"0\"},{\"isNull\":false,\"name\":\"active\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:40:26.755\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:40:26.755\"},{\"isNull\":false,\"name\":\"service_area_type\",\"isKey\":false,\"isUpdated\":true,\"value\":\"0\"}],\"eventType\":\"UPDATE\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":true,\"value\":\"2460\"},{\"isNull\":false,\"name\":\"transport_group_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"268\"},{\"isNull\":false,\"name\":\"sku_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"373303\"},{\"isNull\":false,\"name\":\"service_area_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"0\"},{\"isNull\":false,\"name\":\"active\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:40:26.755\"},{\"isNull\":false,\"name\":\"create_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"modify_user\",\"isKey\":false,\"isUpdated\":true,\"value\":\"李伟\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2020-11-11 15:40:26.755\"},{\"isNull\":false,\"name\":\"service_area_type\",\"isKey\":false,\"isUpdated\":true,\"value\":\"0\"}],\"tableName\":\"tsp_transport_group_sku_area_relation\"}";
        baseMessage.setProperty("dataChange",tsp_transport_group_sku_area_relation);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);

        Assert.assertTrue(true);
    }
    @Test
    public void testTmsCertificateCheckHandle_driver(){
        BaseMessage baseMessage = new BaseMessage();
        String tms_certificate_check = "{\"otterParseTime\":1605080426782,\"orderKeyInfo\":{\"pks\":[],\"schemaName\":\"dcstransportdb\",\"tableName\":\"tms_certificate_check\"},\"otterSendTime\":1605080426783,\"beforeColumnList\":[{\"isNull\":false,\"name\":\"check_type\",\"isKey\":true,\"isUpdated\":true,\"value\":\"2\"},{\"isNull\":false,\"name\":\"check_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"0\"}],\"eventType\":\"UPDATE\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"check_type\",\"isKey\":true,\"isUpdated\":true,\"value\":\"2\"},{\"isNull\":false,\"name\":\"check_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"}],\"tableName\":\"tms_certificate_check\"}";
        baseMessage.setProperty("dataChange",tms_certificate_check);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);
    }
    @Test
    public void testTmsCertificateCheckHandle_vehicle(){
        BaseMessage baseMessage = new BaseMessage();
        String tms_certificate_check = "{\"otterParseTime\":1605080426782,\"orderKeyInfo\":{\"pks\":[],\"schemaName\":\"dcstransportdb\",\"tableName\":\"tms_certificate_check\"},\"otterSendTime\":1605080426783,\"beforeColumnList\":[{\"isNull\":false,\"name\":\"check_type\",\"isKey\":true,\"isUpdated\":true,\"value\":\"3\"},{\"isNull\":false,\"name\":\"check_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"}],\"eventType\":\"UPDATE\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"check_type\",\"isKey\":true,\"isUpdated\":true,\"value\":\"3\"},{\"isNull\":false,\"name\":\"check_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"}],\"tableName\":\"tms_certificate_check\"}";
        baseMessage.setProperty("dataChange",tms_certificate_check);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);
    }
    @Test
    public void testTmsCertificateCheckHandle_table(){
        BaseMessage baseMessage = new BaseMessage();
        String tms_certificate_check = "{\"otterParseTime\":1605080426782,\"orderKeyInfo\":{\"pks\":[],\"schemaName\":\"dcstransportdb\",\"tableName\":\"error\"},\"otterSendTime\":1605080426783,\"beforeColumnList\":[{\"isNull\":false,\"name\":\"check_type\",\"isKey\":true,\"isUpdated\":true,\"value\":\"3\"},{\"isNull\":false,\"name\":\"check_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"}],\"eventType\":\"UPDATE\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"check_type\",\"isKey\":true,\"isUpdated\":true,\"value\":\"3\"},{\"isNull\":false,\"name\":\"check_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"}],\"tableName\":\"error\"}";
        baseMessage.setProperty("dataChange",tms_certificate_check);
        dcstransportdbDataChangedListener.dcstransportdbDataChanged(baseMessage);
    }
}
