package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.common.result.Result;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class ResetDrvPwdExecutorTest {

    @InjectMocks
    private ResetDrvPwdExecutor resetDrvPwdExecutor;

    @Mock
    private DriverAccountCommandService accountCmdService;

    @Before
    public void setUp() throws Exception {

        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setInternalScope(0);
        drvDriverPO.setDrvPhone("1233");
        Result<DrvDriverPO> build = Result.Builder.<DrvDriverPO>newResult().success().withData(drvDriverPO).build();
        Mockito.when(accountCmdService.resetPwd(Mockito.anyString())).thenReturn(build);

    }

    @Test
    public void execute() {
        ResetDrvPwdRequestType resetDrvPwdRequestType = new ResetDrvPwdRequestType();
        resetDrvPwdRequestType.setHybridLoginAccount("ss");
        ResetDrvPwdResponseType resetDrvPwdResponseType =  resetDrvPwdExecutor.execute(resetDrvPwdRequestType);
        Assert.assertTrue(!Objects.isNull(resetDrvPwdResponseType));
    }
}