package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.scm.sdk.domain.ContractRepository;
import com.ctrip.dcs.scm.sdk.domain.ServiceProviderRepository;
import com.ctrip.dcs.scm.sdk.domain.contract.Contract;
import com.ctrip.dcs.scm.sdk.domain.serviceprovider.ServiceProvider;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.base.*;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class QueryTransportGroupListExecutorTest {

    @InjectMocks
    QueryTransportGroupListExecutor executor;

    @Mock
    private TransportGroupQueryService transportGroupQueryService;

    @Mock
    private EnumRepository enumRepository;

    @Mock
    private ProductionLineUtil productionLineUtil;

    @Mock
    private ContractRepository contractRepository;

    @Mock
    private ServiceProviderRepository serviceProviderRepository;

    @Test
    public void getServiceProviderName() {
        TspTransportGroupPO transportGroupPO = new TspTransportGroupPO();
        transportGroupPO.setContractId(1L);
        List<TspTransportGroupPO> groupPOList = Lists.newArrayList(transportGroupPO);
        PageHolder pageHolder = PageHolder.of(groupPOList).pageIndex(1).pageSize(1).totalSize(10).build();
        Mockito.when(transportGroupQueryService.queryTransportGroupList(Mockito.any(TspTransportGroupPO.class), Mockito.any(QueryTransportGroupListSOARequestType.class))).thenReturn(Result.Builder.<PageHolder<TspTransportGroupPO>>newResult()
                .success()
                .withData(pageHolder)
                .build());
        Contract contract = Contract.newBuilder().withSalesMode(1).build();
        Mockito.when(contractRepository.findOne(1L)).thenReturn(contract);
        Mockito.when(enumRepository.getServiceProviderByContractId(1L)).thenReturn(ServiceProvider.newBuilder().build());
        QueryTransportGroupListSOAResponseType soaResponseType =  executor.execute(new QueryTransportGroupListSOARequestType());
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }

    @Test
    public void poToListTypeTest() {
        Mockito.when(enumRepository.getTransportGroupMode()).thenReturn(Maps.newHashMap());
        Mockito.when(enumRepository.getTransportGroupStatusMap()).thenReturn(Maps.newHashMap());
        TspTransportGroupPO po = new TspTransportGroupPO();
        po.setCategorySynthesizeCode(4);
        Mockito.when(enumRepository.getServiceProviderByContractId(po.getContractId())).thenReturn(ServiceProvider.newBuilder().build());
        Mockito.when(productionLineUtil.getShowProductionLineList(po.getCategorySynthesizeCode())).thenReturn(Lists.newArrayList(3));
        Assert.assertTrue(3 == executor.poToListType(po).getProLineId());
    }


}
