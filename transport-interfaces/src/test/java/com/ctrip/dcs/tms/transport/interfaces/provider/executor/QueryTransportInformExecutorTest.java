package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryTransportInformRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryTransportInformResponseType;
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR> ZhangZhen
 * @create 2023/7/3 17:17
 */
@RunWith(MockitoJUnitRunner.class)
public class QueryTransportInformExecutorTest {

    @InjectMocks
    private QueryTransportInformExecutor executor;

    @Mock
    private TransportGroupQueryService transportGroupQueryService;

    @Test
    public void executeSuccessTest() {
        QueryTransportInformRequestType req = getReq();
        QueryTransportInformResponseType resp = new QueryTransportInformResponseType();
        Mockito.when(transportGroupQueryService.queryTransportInform(req.getTransportGroupId())).thenReturn(Result.Builder.<QueryTransportInformResponseType>newResult().success().withData(resp).build());
        QueryTransportInformResponseType res = executor.execute(req);
        Assert.assertTrue(res != null);
        Assert.assertTrue(res.getResponseResult() != null);
        Assert.assertTrue(ServiceResponseConstants.ResStatus.SUCCESS_CODE.equals(res.getResponseResult().getReturnCode()));
    }

    @Test
    public void executeFailTest() {
        QueryTransportInformRequestType req = getReq();
        QueryTransportInformResponseType resp = new QueryTransportInformResponseType();
        Mockito.when(transportGroupQueryService.queryTransportInform(req.getTransportGroupId())).thenReturn(Result.Builder.<QueryTransportInformResponseType>newResult().fail().withData(resp).withCode("123").withMsg("321").build());
        QueryTransportInformResponseType res = executor.execute(req);
        Assert.assertTrue(res != null);
        Assert.assertTrue(res.getResponseResult() != null);
        Assert.assertTrue("123".equals(res.getResponseResult().getReturnCode()));
        Assert.assertTrue("321".equals(res.getResponseResult().getReturnMessage()));
    }

    private QueryTransportInformRequestType getReq() {
        QueryTransportInformRequestType req = new QueryTransportInformRequestType();
        req.setTransportGroupId(1L);
        return req;
    }

}