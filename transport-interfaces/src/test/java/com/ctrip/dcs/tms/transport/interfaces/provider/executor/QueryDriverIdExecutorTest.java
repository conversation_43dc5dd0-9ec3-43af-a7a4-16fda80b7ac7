package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryDriverIdCountSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDriverIdCountSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryDriverIdSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDriverIdSOAResponseType;
import com.ctrip.dcs.tms.transport.api.resource.driver.DrvBase;
import com.ctrip.dcs.tms.transport.application.convert.DrvResourceConverter;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.QueryDrvResourceConditionDTO;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.ctrip.igt.framework.infrastructure.exception.ServiceValidationException;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> ZhangZhen
 * @create 2023/3/7 16:12
 */
@RunWith(MockitoJUnitRunner.class)
public class QueryDriverIdExecutorTest {

    @InjectMocks
    private QueryDriverIdExecutor queryDriverIdExecutor;

    @Mock
    private DriverQueryService driverQueryService;

    @Mock
    private DrvResourceConverter drvResourceConverter;

    @Test
    public void test() {
        QueryDriverIdSOAResponseType responseType = queryDriverIdExecutor.execute(new QueryDriverIdSOARequestType());
        Assert.assertTrue(responseType != null);
        Assert.assertTrue(responseType.getResponseResult().getReturnCode() == ServiceResponseConstants.ResStatus.SUCCESS_CODE);
    }

    @Test
    public void test1() {
        List<DrvBase> drvBaseList = Lists.newArrayList();
        DrvBase drvBase1 = new DrvBase();
        drvBase1.setDrvId(1L);
        drvBaseList.add(drvBase1);
        DrvBase drvBase2 = new DrvBase();
        drvBase2.setDrvId(2L);
        drvBaseList.add(drvBase2);
        Mockito.when(driverQueryService.queryDrvBaseResource(null)).thenReturn(drvBaseList);
        QueryDriverIdSOAResponseType responseType = queryDriverIdExecutor.execute(new QueryDriverIdSOARequestType());
        Assert.assertTrue(responseType != null);
        Assert.assertTrue(responseType.getResponseResult().getReturnCode() == ServiceResponseConstants.ResStatus.SUCCESS_CODE);
        Assert.assertTrue(responseType.getBoundaryDrvId() == 2L);
    }

    @Test(expected = ServiceValidationException.class)
    public void onExecuting() {
        QueryDriverIdSOARequestType req = new QueryDriverIdSOARequestType();
        queryDriverIdExecutor.onExecuting(req);
    }

    @Test(expected = ServiceValidationException.class)
    public void onExecuting1() {
        QueryDriverIdSOARequestType req = new QueryDriverIdSOARequestType();
        req.setCountryIdList(Lists.newArrayList(1L));
        queryDriverIdExecutor.onExecuting(req);
    }

    @Test(expected = ServiceValidationException.class)
    public void onExecuting2() {
        QueryDriverIdSOARequestType req = new QueryDriverIdSOARequestType();
        req.setCountryIdList(Lists.newArrayList(1L));
        req.setBoundaryDrvId(-1L);
        queryDriverIdExecutor.onExecuting(req);
    }


    @Test(expected = ServiceValidationException.class)
    public void onExecuting3() {
        QueryDriverIdSOARequestType req = new QueryDriverIdSOARequestType();
        req.setCountryIdList(Lists.newArrayList(1L));
        req.setBoundaryDrvId(1L);
        queryDriverIdExecutor.onExecuting(req);
    }

    @Test(expected = ServiceValidationException.class)
    public void onExecuting4() {
        QueryDriverIdSOARequestType req = new QueryDriverIdSOARequestType();
        req.setCountryIdList(Lists.newArrayList(1L));
        req.setBoundaryDrvId(1L);
        req.setPageSize(0);
        queryDriverIdExecutor.onExecuting(req);
    }

    @Test
    public void onExecuting5() {
        QueryDriverIdSOARequestType req = new QueryDriverIdSOARequestType();
        req.setCountryIdList(Lists.newArrayList(1L));
        req.setBoundaryDrvId(1L);
        req.setPageSize(1);
        queryDriverIdExecutor.onExecuting(req);
    }

    @Test
    public void onExecuting7() {
        QueryDriverIdSOARequestType req = new QueryDriverIdSOARequestType();
        req.setCountryIdList(Lists.newArrayList(1L));
        req.setBoundaryDrvId(1L);
        req.setPageSize(200);
        queryDriverIdExecutor.onExecuting(req);
    }

}