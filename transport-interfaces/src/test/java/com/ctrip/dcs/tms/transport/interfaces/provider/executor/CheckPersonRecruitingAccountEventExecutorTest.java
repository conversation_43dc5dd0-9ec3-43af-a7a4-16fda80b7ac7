package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.CheckPersonRecruitingAccountSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.CheckPersonRecruitingAccountSOAResponseType;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.PersonRecruitingRepository;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class CheckPersonRecruitingAccountEventExecutorTest{

    @InjectMocks
    CheckPersonRecruitingAccountEventExecutor executor;
    @Mock
    PersonRecruitingRepository personRecruitingRepository;

    @Test
    public void testExecute() {
        CheckPersonRecruitingAccountSOARequestType soaRequestType = new CheckPersonRecruitingAccountSOARequestType();
        soaRequestType.setAccount("111");
        Mockito.when(personRecruitingRepository.checkPhoneCount(Mockito.anyString())).thenReturn(1);
        CheckPersonRecruitingAccountSOAResponseType soaResponseType =  executor.execute(soaRequestType);
        Assert.assertTrue(soaResponseType != null);
    }

    @Test
    public void testExecute1() {
        CheckPersonRecruitingAccountSOARequestType soaRequestType = new CheckPersonRecruitingAccountSOARequestType();
        soaRequestType.setAccount("111");
        Mockito.when(personRecruitingRepository.checkPhoneCount(Mockito.anyString())).thenReturn(0);
        CheckPersonRecruitingAccountSOAResponseType soaResponseType =  executor.execute(soaRequestType);
        Assert.assertTrue(soaResponseType != null);
    }
}