package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import org.assertj.core.util.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.sql.*;
import java.util.Arrays;
import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class VehicleRecruitingCheckStatusScheduleTest extends Mockito {


    @InjectMocks
    VehicleRecruitingCheckStatusSchedule schedule;

    @Mock
    VehicleRecruitingRepository recruitingRepository;
    @Mock
    TmsCertificateCheckRepository checkRepository;
    @Mock
    private TmsQmqProducerCommandService tmsQmqProducerCommandService;

    @Test
    public void vehicleRecruitingCount0() throws SQLException {
        when( recruitingRepository.countApproveIngVehicleRecruiting(Arrays.asList(1L), TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode(),2)).thenReturn(0);
        schedule.vehicleRecruitingCheckStatusScheduleMethod("1");
        Assert.assertTrue(true);
    }

    @Test
    public void vehicleRecruitingCheckStatusListEmilp() throws SQLException {
        when( recruitingRepository.countApproveIngVehicleRecruiting(Arrays.asList(1L), TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode(),2)).thenReturn(1);
        List<VehicleRecruitingPO> recruitingPOS = Lists.newArrayList();
        VehicleRecruitingPO recruitingPO = new VehicleRecruitingPO();
        recruitingPO.setVehicleFrom(1);
        recruitingPO.setVehicleId(1L);
        recruitingPOS.add(recruitingPO);
        when(recruitingRepository.queryApproveIngVehicleRecruiting(Arrays.asList(1L), TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode(),2)).thenReturn(recruitingPOS);
        List<TmsCertificateCheckPO> checkPOList = Lists.newArrayList();
        TmsCertificateCheckPO tmsCertificateCheckPO = new TmsCertificateCheckPO();
        tmsCertificateCheckPO.setCheckStatus(1);
        checkPOList.add(tmsCertificateCheckPO);
//        when(checkRepository.queryCertificateByCheckId(1L,TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode())).thenReturn(checkPOList);
//        when(recruitingRepository.updateCheckStatus(Arrays.asList(1L),1)).thenReturn(1);
        schedule.vehicleRecruitingCheckStatusScheduleMethod("1");
        Assert.assertTrue(true);
    }
}
