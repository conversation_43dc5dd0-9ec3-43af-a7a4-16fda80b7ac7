package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.common.result.Result;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class UpdateDrvVehRecruitingFromH5ExecutorTest {

    @InjectMocks
    UpdateDrvVehRecruitingFromH5Executor executor;
    @Mock
    private DrvVehRecruitingCommandService commandService;

    @Test
    public void execute() {
        DrvVehRecruitingUpdateFromH5RequestType requestType = new DrvVehRecruitingUpdateFromH5RequestType();
        Result<Boolean> result = Result.Builder.<Boolean>newResult().success().withData(true).build();
        Mockito.when(commandService.updateDrvVehRecruitingFromH5(requestType)).thenReturn(result);
        DrvVehRecruitingUpdateFromH5ResponseType responseType =  executor.execute(requestType);
        Assert.assertTrue(!Objects.isNull(responseType));
    }
}
