package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class OcrLimitCheckExecutorTest {

    @InjectMocks
    private OcrLimitCheckExecutor ocrLimitCheckExecutor;

    @Mock
    private TmsTransportQconfig qconfig;

    @Test
    public void executeTest() {
        OcrLimitCheckSOARequestType soaRequestType = new OcrLimitCheckSOARequestType();
        OcrLimitCheckSOAResponseType soaResponseType =  ocrLimitCheckExecutor.execute(soaRequestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }

}