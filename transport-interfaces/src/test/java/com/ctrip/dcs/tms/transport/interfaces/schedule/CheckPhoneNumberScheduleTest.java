package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.basebiz.callcenter.splitservice.contract.EnumPhoneType;
import com.ctrip.basebiz.callcenter.splitservice.contract.NumberDTO;
import com.ctrip.dcs.geo.domain.repository.LocationRepository;
import com.ctrip.dcs.geo.domain.value.Location;
import com.ctrip.dcs.tms.transport.application.command.CommonCommandService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.http.NepheleHttpService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvRecruitingPO;
import com.ctrip.dcs.tms.transport.infrastructure.gateway.PhoneNumberServiceGateway;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvRecruitingRepository;
import com.ctrip.ibu.platform.shark.sdk.utils.JsonUtils;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import qunar.tc.schedule.MockParameter;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/12 00:02
 * @description
 */
@RunWith(MockitoJUnitRunner.class)
public class CheckPhoneNumberScheduleTest {
    @InjectMocks
    CheckPhoneNumberSchedule schedule;

    @Mock
    private LocationRepository locationRepository;
    @Mock
    private PhoneNumberServiceGateway phoneNumberServiceGateway;
    @Mock
    private DrvDrvierRepository drvDrvierRepository;
    @Mock
    private DrvRecruitingRepository drvRecruitingRepository;
    @Mock
    private NepheleHttpService nepheleHttpService;
    @Mock
    private CommonCommandService commonCommandService;

    @Test
    public void testCheckRecruitDriverPhone_RealDriver() throws Exception {
        Mockito.when(drvDrvierRepository.countAll()).thenReturn(1L);
        Mockito.when(drvDrvierRepository.queryFromByPage(Mockito.anyLong(), Mockito.anyInt())).thenReturn(ImmutableList.of(mockDriverPO(DrvDriverPO.class)));
        Mockito.when(phoneNumberServiceGateway.decryptPhone(Mockito.anyString())).thenReturn("01316787788");
        Mockito.when(phoneNumberServiceGateway.splitPhoneNumber(Mockito.anyString(), Mockito.anyString())).thenReturn(mockNumberDTO());
        Mockito.when(nepheleHttpService.upload(Mockito.any(), Mockito.anyString())).thenReturn("123456");
        Mockito.when(commonCommandService.sendEmail(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(Result.Builder.<Boolean>newResult().success().build());

        String param = JsonUtils.toJson(ImmutableMap.of("driverType", "REAL_DRIVER"));
        MockParameter mockParameter = new MockParameter(param);
        int total = schedule.checkRecruitDriverPhone(mockParameter);
        Assert.assertEquals(1, total);

        DrvRecruitingPO mock = mockDriverPO(DrvRecruitingPO.class);
        mock.setDrvRecruitingId(1L);
        Mockito.when(drvRecruitingRepository.countAll()).thenReturn(1L);
        Mockito.when(drvRecruitingRepository.queryFromByPage(Mockito.anyLong(), Mockito.anyInt())).thenReturn(ImmutableList.of(mock));
        Mockito.when(phoneNumberServiceGateway.decryptPhone(Mockito.anyString())).thenReturn("1316787788");
        Mockito.when(phoneNumberServiceGateway.splitPhoneNumber(Mockito.anyString(), Mockito.anyString())).thenReturn(mockNumberDTO());

        Location location = Mockito.mock(Location.class);
        Mockito.lenient().when(location.isChineseHMT()).thenReturn(true);
        Mockito.lenient().when(locationRepository.findOne(Mockito.anyInt(), Mockito.anyLong())).thenReturn(location);
        param = JsonUtils.toJson(ImmutableMap.of("driverType", "RECRUIT_DRIVER", "exportConfig", "{\"exportExcel\":true,\"exportFieldList\":[\"driverId\",\"cityId\",\"originAreaCode\",\"decryptPhone\",\"ctripFmtPhone\"],\"subject\":\"哈哈哈\",\"receiver\":\"<EMAIL>,<EMAIL>\"}"));
        mockParameter = new MockParameter(param);
        total = schedule.checkRecruitDriverPhone(mockParameter);
        Assert.assertEquals(1, total);

        mock.setDrvPhone(StringUtils.EMPTY);
        total = schedule.checkRecruitDriverPhone(mockParameter);
        Assert.assertEquals(1, total);
    }

    private Optional<NumberDTO> mockNumberDTO() {
        NumberDTO dto = new NumberDTO();
        dto.setBodyNumber("1316787788");
        dto.setPhoneType(EnumPhoneType.CN);
        dto.setMobile(true);
        dto.setValid(false);
        dto.setCountryCode("86");
        return Optional.of(dto);
    }

    private <T> T mockDriverPO(Class<T> clazz) {
        DrvDriverPO po = new DrvDriverPO();
        po.setDrvId(1L);
        po.setDrvName("TEST");
        po.setCountryId(1L);
        po.setCountryName("China");
        po.setIgtCode("86");
        po.setDrvPhone("13167877877");
        po.setCityId(2L);
        po.setSupplierId(1L);
        String json = JsonUtils.toJson(po);
        return JsonUtils.fromJson(json, clazz);
    }
}
