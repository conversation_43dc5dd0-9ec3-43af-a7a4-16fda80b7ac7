package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class QueryVehicleInfoExecutorTest {
    @InjectMocks
    QueryVehicleInfoExecutor queryVehicleInfoExecutor;
    @Mock
    IQueryVehicleInfoService queryVehicleInfoService;
    @Test
    public void testQueryVehicleByVehicleNo(){
        Mockito.when(queryVehicleInfoService.queryVehicleByVehicleNo(Mockito.anyString())).thenReturn(new VehCacheDTO());
        QueryVehicleInfoRequestType requestType = new QueryVehicleInfoRequestType();
        requestType.setVehicleNo("京A88888");
        QueryVehicleInfoResponseType responseType = queryVehicleInfoExecutor.execute(requestType);
        Assert.notNull(responseType);
    }
    @Test
    public void testQueryVehicleById(){
        Mockito.when(queryVehicleInfoService.queryVehicleById(Mockito.anyLong())).thenReturn(new VehCacheDTO());
        QueryVehicleInfoRequestType requestType = new QueryVehicleInfoRequestType();
        requestType.setVehicleId(1L);
        QueryVehicleInfoResponseType responseType = queryVehicleInfoExecutor.execute(requestType);
        Assert.notNull(responseType);
    }
    @Test
    public void paramNull(){
        QueryVehicleInfoRequestType requestType = new QueryVehicleInfoRequestType();
        QueryVehicleInfoResponseType responseType = queryVehicleInfoExecutor.execute(requestType);
        Assert.notNull(responseType);
    }
}
