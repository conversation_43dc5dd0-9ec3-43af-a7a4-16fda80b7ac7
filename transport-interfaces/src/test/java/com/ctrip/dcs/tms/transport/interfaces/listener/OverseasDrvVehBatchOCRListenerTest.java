package com.ctrip.dcs.tms.transport.interfaces.listener;

import com.ctrip.dcs.tms.transport.api.model.OverseasOCRRecognitionSOAResponseType;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehVehiclePO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRepository;
import com.ctrip.framework.ucs.client.ShardingKeyValue;
import com.ctrip.igt.framework.common.result.Result;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.ReliabilityLevel;
import qunar.tc.qmq.TraceContext;

import java.util.Date;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@RunWith(MockitoJUnitRunner.class)
public class OverseasDrvVehBatchOCRListenerTest{


    @InjectMocks
    OverseasDrvVehBatchOCRListener listener;

    @Mock
    private DrvDrvierRepository repository;
    @Mock
    private VehicleRepository vehicleRepository;
    @Mock
    private DriverQueryService driverQueryService;
    @Mock
    private EnumRepository enumRepository;


    @Test
    public void testHandle() {
        Message message = new Message() {
            @Override
            public String getMessageId() {
                return "111";
            }

            @Override
            public String getSubject() {
                return null;
            }

            @Override
            public Date getCreatedTime() {
                return null;
            }

            @Override
            public Date getScheduleReceiveTime() {
                return null;
            }

            @Override
            public void setProperty(String s, boolean b) {

            }

            @Override
            public void setProperty(String s, Boolean aBoolean) {

            }

            @Override
            public void setProperty(String s, int i) {

            }

            @Override
            public void setProperty(String s, Integer integer) {

            }

            @Override
            public void setProperty(String s, long l) {

            }

            @Override
            public void setProperty(String s, Long aLong) {

            }

            @Override
            public void setProperty(String s, float v) {

            }

            @Override
            public void setProperty(String s, Float aFloat) {

            }

            @Override
            public void setProperty(String s, double v) {

            }

            @Override
            public void setProperty(String s, Double aDouble) {

            }

            @Override
            public void setProperty(String s, Date date) {

            }

            @Override
            public void setProperty(String s, String s1) {

            }

            @Override
            public String getStringProperty(String s) {
                return "1";
            }

            @Override
            public boolean getBooleanProperty(String s) {
                return false;
            }

            @Override
            public Date getDateProperty(String s) {
                return null;
            }

            @Override
            public int getIntProperty(String s) {
                return 1;
            }

            @Override
            public long getLongProperty(String s) {
                return 1L;
            }

            @Override
            public float getFloatProperty(String s) {
                return 0;
            }

            @Override
            public double getDoubleProperty(String s) {
                return 0;
            }

            @Override
            public boolean containsKey(String s) {
                return false;
            }

            @Override
            public Message addTag(String s) {
                return null;
            }

            @Override
            public Set<String> getTags() {
                return null;
            }

            @Override
            public Set<String> getBizAttrs() {
                return null;
            }

            @Override
            public Map<String, Object> getAttrs() {
                return null;
            }

            @Override
            public TraceContext getContext() {
                return null;
            }

            @Override
            public void setReliabilityLevel(ReliabilityLevel reliabilityLevel) {

            }

            @Override
            public ReliabilityLevel getReliabilityLevel() {
                return null;
            }

            @Override
            public void autoAck(boolean b) {

            }

            @Override
            public void ack(Throwable throwable) {

            }

            @Override
            public void setDelayTime(Date date) {

            }

            @Override
            public void setDelayTime(long l, TimeUnit timeUnit) {

            }

            @Override
            public int times() {
                return 0;
            }

            @Override
            public void setMaxRetryNum(int i) {

            }

            @Override
            public int getMaxRetryNum() {
                return 0;
            }

            @Override
            public int localRetries() {
                return 0;
            }

            @Override
            public void setDurable(boolean b) {

            }

            @Override
            public boolean isDurable() {
                return false;
            }

            @Override
            public void setStoreAtFailed(boolean b) {

            }

            @Override
            public boolean isStoreAtFailed() {
                return false;
            }

            @Override
            public void setOrderKey(String s) {

            }

            @Override
            public String getOrderKey() {
                return null;
            }

            @Override
            public void setShardingKey(String s) {

            }

            @Override
            public String getShardingKey() {
                return null;
            }

            @Override
            public void setShardingKey(ShardingKeyValue shardingKeyValue) {

            }

            @Override
            public String getPartitionName() {
                return null;
            }

            @Override
            public boolean isCompensation() {
                return false;
            }

            @Override
            public String getSourceRegion() {
                return null;
            }

            @Override
            public String getLane() {
                return "";
            }

            @Override
            public Date getExpiredTime() {
                return null;
            }

            @Override
            public void setNewqmqFlag() {

            }

            @Override
            public boolean isNewqmq() {
                return false;
            }

            @Override
            public <T> T getData(Class<T> aClass) {
                return null;
            }

            @Override
            public void setData(Object o) {

            }

            @Override
            public String getLargeString(String s) {
                return null;
            }

            @Override
            public Object getProperty(String s) {
                return null;
            }

            @Override
            public void setLargeString(String s, String s1) {

            }

            @Override
            public void ack(long l, Throwable throwable) {

            }

            @Override
            public void ack(long l, Throwable throwable, Map<String, String> map) {

            }
        };
//        Mockito.when(repository.queryByPk(1L)).thenReturn(null);
        listener.handle(message);
        Assert.assertTrue("111".equals(message.getMessageId()));
    }

    @Test
    public void testDrvOCRMethod() {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setCityId(359L);
        drvDriverPO.setDrvcardImg("111");
        drvDriverPO.setDrvName("111");
        Mockito.when(repository.queryByPk(1L)).thenReturn(drvDriverPO);
        OverseasOCRRecognitionSOAResponseType responseType = new OverseasOCRRecognitionSOAResponseType();
        responseType.setDrvName("111");
        Result<OverseasOCRRecognitionSOAResponseType> soaResponseTypeResult = Result.Builder.<OverseasOCRRecognitionSOAResponseType>newResult().success().withData(responseType).build();
        Mockito.when(driverQueryService.overseasOCRRecognition(Mockito.any())).thenReturn(soaResponseTypeResult);
        Boolean result = listener.drvOCRMethod(1L);
        Assert.assertTrue(result);
    }

    @Test
    public void testDrvOCRMethod1() {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setCityId(359L);
        drvDriverPO.setDrvcardImg("111");
        drvDriverPO.setDrvName("111");
        drvDriverPO.setInternalScope(0);
        Mockito.when(repository.queryByPk(1L)).thenReturn(drvDriverPO);
        Boolean result = listener.drvOCRMethod(1L);
        Assert.assertTrue(!result);

    }


    @Test
    public void testDrvOCRMethod2() {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setCityId(359L);
        drvDriverPO.setDrvcardImg("111");
        drvDriverPO.setDrvName("111");
        Mockito.when(repository.queryByPk(1L)).thenReturn(drvDriverPO);
        OverseasOCRRecognitionSOAResponseType responseType = new OverseasOCRRecognitionSOAResponseType();
        responseType.setDrvName("111");
        Result<OverseasOCRRecognitionSOAResponseType> soaResponseTypeResult = Result.Builder.<OverseasOCRRecognitionSOAResponseType>newResult().fail().withData(responseType).build();
        Mockito.when(driverQueryService.overseasOCRRecognition(Mockito.any())).thenReturn(soaResponseTypeResult);
        Boolean result = listener.drvOCRMethod(1L);
        Assert.assertTrue(!result);
    }


    @Test
    public void testDrvOCRMethod3() {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setCityId(359L);
        drvDriverPO.setDrvcardImg("111");
        drvDriverPO.setDrvName("111");
        Mockito.when(repository.queryByPk(1L)).thenReturn(drvDriverPO);
        OverseasOCRRecognitionSOAResponseType responseType = new OverseasOCRRecognitionSOAResponseType();
        Result<OverseasOCRRecognitionSOAResponseType> soaResponseTypeResult = Result.Builder.<OverseasOCRRecognitionSOAResponseType>newResult().success().withData(responseType).build();
        Mockito.when(driverQueryService.overseasOCRRecognition(Mockito.any())).thenReturn(soaResponseTypeResult);
        Boolean result = listener.drvOCRMethod(1L);
        Assert.assertTrue(!result);
    }

    @Test
    public void testVehOCRMethod() {
        VehVehiclePO vehVehiclePO = new VehVehiclePO();
        vehVehiclePO.setCityId(359L);
        vehVehiclePO.setVehicleFullImg("111");
        vehVehiclePO.setVehicleColorId(1L);
        vehVehiclePO.setVehicleLicense("111");
        OverseasOCRRecognitionSOAResponseType responseType = new OverseasOCRRecognitionSOAResponseType();
        responseType.setDrvName("111");
        responseType.setVehicleColor("11");
        responseType.setVehicleLicense("11");
        Result<OverseasOCRRecognitionSOAResponseType> soaResponseTypeResult = Result.Builder.<OverseasOCRRecognitionSOAResponseType>newResult().success().withData(responseType).build();
        Mockito.when(driverQueryService.overseasOCRRecognition(Mockito.any())).thenReturn(soaResponseTypeResult);
        Mockito.when(enumRepository.getAreaScope(359L)).thenReturn(1);
        Mockito.when(enumRepository.getColorKey(1L)).thenReturn("11");
        Mockito.when(vehicleRepository.queryByPk(1L)).thenReturn(vehVehiclePO);
        Boolean result = listener.vehOCRMethod(1L);
        Assert.assertTrue(result);
    }

    @Test
    public void testVehOCRMethod1() {
        VehVehiclePO vehVehiclePO = new VehVehiclePO();
        vehVehiclePO.setCityId(359L);
        vehVehiclePO.setVehicleFullImg("111");
        vehVehiclePO.setVehicleColorId(1L);
        vehVehiclePO.setVehicleLicense("111");
        OverseasOCRRecognitionSOAResponseType responseType = new OverseasOCRRecognitionSOAResponseType();
        responseType.setDrvName("111");
        responseType.setVehicleColor("11");
        responseType.setVehicleLicense("11");
        Mockito.when(vehicleRepository.queryByPk(1L)).thenReturn(null);
        Boolean result = listener.vehOCRMethod(1L);
        Assert.assertTrue(!result);
    }


    @Test
    public void testVehOCRMethod2() {
        VehVehiclePO vehVehiclePO = new VehVehiclePO();
        vehVehiclePO.setCityId(359L);
        vehVehiclePO.setVehicleFullImg("111");
        vehVehiclePO.setVehicleColorId(1L);
        vehVehiclePO.setVehicleLicense("111");
        OverseasOCRRecognitionSOAResponseType responseType = new OverseasOCRRecognitionSOAResponseType();
        responseType.setDrvName("111");
        responseType.setVehicleColor("11");
        responseType.setVehicleLicense("11");
        Result<OverseasOCRRecognitionSOAResponseType> soaResponseTypeResult = Result.Builder.<OverseasOCRRecognitionSOAResponseType>newResult().success().withData(responseType).build();
//        Mockito.when(driverQueryService.overseasOCRRecognition(Mockito.any())).thenReturn(soaResponseTypeResult);
        Mockito.when(enumRepository.getAreaScope(359L)).thenReturn(0);
        Mockito.when(vehicleRepository.queryByPk(1L)).thenReturn(vehVehiclePO);
        Boolean result = listener.vehOCRMethod(1L);
        Assert.assertTrue(!result);
    }

    @Test
    public void testVehOCRMethod3() {
        VehVehiclePO vehVehiclePO = new VehVehiclePO();
        vehVehiclePO.setCityId(359L);
        vehVehiclePO.setVehicleFullImg("111");
        vehVehiclePO.setVehicleColorId(1L);
        vehVehiclePO.setVehicleLicense("111");
        OverseasOCRRecognitionSOAResponseType responseType = new OverseasOCRRecognitionSOAResponseType();
        responseType.setDrvName("111");
        responseType.setVehicleColor("11");
        responseType.setVehicleLicense("11");
        Result<OverseasOCRRecognitionSOAResponseType> soaResponseTypeResult = Result.Builder.<OverseasOCRRecognitionSOAResponseType>newResult().fail().withData(responseType).build();
//        Mockito.when(driverQueryService.overseasOCRRecognition(Mockito.any())).thenReturn(soaResponseTypeResult);
        Mockito.when(enumRepository.getAreaScope(359L)).thenReturn(0);
        Mockito.when(vehicleRepository.queryByPk(1L)).thenReturn(vehVehiclePO);
        Boolean result = listener.vehOCRMethod(1L);
        Assert.assertTrue(!result);
    }


    @Test
    public void testVehOCRMethod4() {
        VehVehiclePO vehVehiclePO = new VehVehiclePO();
        vehVehiclePO.setCityId(359L);
        vehVehiclePO.setVehicleFullImg("111");
        vehVehiclePO.setVehicleColorId(1L);
        vehVehiclePO.setVehicleLicense("111");
        OverseasOCRRecognitionSOAResponseType responseType = new OverseasOCRRecognitionSOAResponseType();
        responseType.setDrvName("111");
        Result<OverseasOCRRecognitionSOAResponseType> soaResponseTypeResult = Result.Builder.<OverseasOCRRecognitionSOAResponseType>newResult().success().withData(responseType).build();
        Mockito.when(driverQueryService.overseasOCRRecognition(Mockito.any())).thenReturn(soaResponseTypeResult);
        Mockito.when(enumRepository.getAreaScope(359L)).thenReturn(1);
        Mockito.when(vehicleRepository.queryByPk(1L)).thenReturn(vehVehiclePO);
        Boolean result = listener.vehOCRMethod(1L);
        Assert.assertTrue(!result);
    }

    @Test
    public void testVehOCRMethod5() {
        VehVehiclePO vehVehiclePO = new VehVehiclePO();
        vehVehiclePO.setCityId(359L);
        vehVehiclePO.setVehicleFullImg("111");
        vehVehiclePO.setVehicleColorId(1L);
        vehVehiclePO.setVehicleLicense("111");
        OverseasOCRRecognitionSOAResponseType responseType = new OverseasOCRRecognitionSOAResponseType();
        responseType.setDrvName("111");
        responseType.setVehicleColor("1111");
        responseType.setVehicleLicense("222");
        Result<OverseasOCRRecognitionSOAResponseType> soaResponseTypeResult = Result.Builder.<OverseasOCRRecognitionSOAResponseType>newResult().success().withData(responseType).build();
        Mockito.when(driverQueryService.overseasOCRRecognition(Mockito.any())).thenReturn(soaResponseTypeResult);
        Mockito.when(enumRepository.getAreaScope(359L)).thenReturn(1);
        Mockito.when(enumRepository.getColorKey(1L)).thenReturn("11");
        Mockito.when(vehicleRepository.queryByPk(1L)).thenReturn(vehVehiclePO);
        Boolean result = listener.vehOCRMethod(1L);
        Assert.assertTrue(result);
    }

    @Test
    public void getOCRRes() {
        OverseasOCRRecognitionSOAResponseType responseType = new OverseasOCRRecognitionSOAResponseType();
        responseType.setDrvName("111");
        responseType.setVehicleColor("1111");
        responseType.setVehicleLicense("222");
        Result<OverseasOCRRecognitionSOAResponseType> soaResponseTypeResult = Result.Builder.<OverseasOCRRecognitionSOAResponseType>newResult().fail().withData(responseType).build();
        Mockito.when(driverQueryService.overseasOCRRecognition(Mockito.any())).thenReturn(soaResponseTypeResult);
        OverseasOCRRecognitionSOAResponseType result = listener.getOCRRes("11",1L,1,"11",1L);
        Assert.assertTrue(result==null);
    }

    @Test
    public void getOCRRes1() {
        OverseasOCRRecognitionSOAResponseType responseType = new OverseasOCRRecognitionSOAResponseType();
        responseType.setDrvName("111");
        responseType.setVehicleColor("1111");
        responseType.setVehicleLicense("222");
        Result<OverseasOCRRecognitionSOAResponseType> soaResponseTypeResult = Result.Builder.<OverseasOCRRecognitionSOAResponseType>newResult().success().withData(null).build();
        Mockito.when(driverQueryService.overseasOCRRecognition(Mockito.any())).thenReturn(soaResponseTypeResult);
        OverseasOCRRecognitionSOAResponseType result = listener.getOCRRes("11",1L,1,"11",1L);
        Assert.assertTrue(result==null);
    }
}
