package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.application.command.CommonCommandService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.model.SendVoiceCodeRequestType;
import com.ctrip.model.SendVoiceCodeResponseType;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SendVoiceCodeExecutorTest {

    @InjectMocks
    private SendVoiceCodeExecutor executor;

    @Mock
    private CommonCommandService commonCommandService;

    @Before
    public void setUp() {
        // 基础设置
    }

    @Test
    public void testExecute_Success() {
        // 准备
        SendVoiceCodeRequestType requestType = new SendVoiceCodeRequestType();
        requestType.setCountryCode("86");
        requestType.setMobilePhone("13800138000");
        
        Mockito.when(commonCommandService.sendVoiceCode("13800138000", "86", requestType.getSite()))
               .thenReturn(Result.Builder.<String>newResult().success().build());

        // 执行
        SendVoiceCodeResponseType result = executor.execute(requestType);

        // 验证
        Assert.assertTrue(result.getResponseResult().isSuccess());
        Mockito.verify(commonCommandService).sendVoiceCode("13800138000", "86", requestType.getSite());
    }

    @Test
    public void testExecute_DifferentCountryCode() {
        // 准备
        SendVoiceCodeRequestType requestType = new SendVoiceCodeRequestType();
        requestType.setCountryCode("1");
        requestType.setMobilePhone("4155552671");
        
        Mockito.when(commonCommandService.sendVoiceCode("4155552671", "1", requestType.getSite()))
               .thenReturn(Result.Builder.<String>newResult().success().build());

        // 执行
        SendVoiceCodeResponseType result = executor.execute(requestType);

        // 验证
        Assert.assertTrue(result.getResponseResult().isSuccess());
        Mockito.verify(commonCommandService).sendVoiceCode("4155552671", "1", requestType.getSite());
    }
    
    @Test
    public void testExecute_Failure() {
        // 准备
        SendVoiceCodeRequestType requestType = new SendVoiceCodeRequestType();
        requestType.setCountryCode("86");
        requestType.setMobilePhone("13800138000");

        Mockito.when(commonCommandService.sendVoiceCode("13800138000", "86", requestType.getSite()))
               .thenReturn(Result.Builder.<String>newResult().fail().withCode("ERROR_CODE").withMsg("错误信息").build());

        // 执行
        SendVoiceCodeResponseType result = executor.execute(requestType);

        // 验证
        Assert.assertFalse(result.getResponseResult().isSuccess());
        Assert.assertEquals("ERROR_CODE", result.getResponseResult().getReturnCode());
        Assert.assertEquals("错误信息", result.getResponseResult().getReturnMessage());
        Mockito.verify(commonCommandService).sendVoiceCode("13800138000", "86", requestType.getSite());
    }
}
