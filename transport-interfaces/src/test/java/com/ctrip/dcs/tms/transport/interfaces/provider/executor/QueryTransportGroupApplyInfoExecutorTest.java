package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class QueryTransportGroupApplyInfoExecutorTest {

    @InjectMocks
    QueryTransportGroupApplyInfoExecutor executor;
    @Mock
    private TransportGroupQueryService transportGroupQueryService;

    @Test
    public void execute() {
        QueryTransportGroupApplyInfoSOARequestType requestType = new QueryTransportGroupApplyInfoSOARequestType();
        QueryTransportGroupApplyInfoSOAResponseType responseType = new QueryTransportGroupApplyInfoSOAResponseType();
        ResponseResult responseResult = new ResponseResult();
        responseResult.setReturnCode("200");
        responseType.setData(Lists.newArrayList());
        responseType.setResponseResult(responseResult);
        Result<QueryTransportGroupApplyInfoSOAResponseType> result = Result.Builder.<QueryTransportGroupApplyInfoSOAResponseType>newResult().success().withData(responseType).build();;
        Mockito.when(transportGroupQueryService.queryTransportGroupApplyInfo(requestType)).thenReturn(result);
        QueryTransportGroupApplyInfoSOAResponseType soaResponseType =  executor.execute(requestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }
}
