package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryVehRecognitionStatusRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryVehRecognitionStatusResponseType;
import com.ctrip.dcs.tms.transport.application.query.VehicleQueryService;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.framework.common.result.Result;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class QueryVehRecognitionStatusExecutorTest {

    @InjectMocks
    QueryVehRecognitionStatusExecutor executor;
    @Mock
    VehicleQueryService vehicleQueryService;

    @Test
    public void execute() {
        QueryVehRecognitionStatusRequestType requestType = new QueryVehRecognitionStatusRequestType();
        requestType.setVehicleLicense("11");
        QueryVehRecognitionStatusResponseType responseType = new QueryVehRecognitionStatusResponseType();
        ResponseResult responseResult = new ResponseResult();
        responseResult.setReturnCode("200");
        responseType.setResponseResult(responseResult);
        responseType.setVehicleFullImg("111");
        Result<QueryVehRecognitionStatusResponseType> result = Result.Builder.<QueryVehRecognitionStatusResponseType>newResult().success().withData(responseType).build();
        Mockito.when(vehicleQueryService.queryVehRecognitionStatus(requestType.getVehicleLicense())).thenReturn(result);
        QueryVehRecognitionStatusResponseType responseType1 = executor.execute(requestType);
        Assert.assertTrue(responseType1 != null);
    }
}