package com.ctrip.dcs.tms.transport.interfaces.provider;

import com.ctrip.dcs.tms.transport.api.model.DiscardDrvSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.DiscardDrvSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.DiscardVehSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.DiscardVehSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.DrvPreCheckRequestType;
import com.ctrip.dcs.tms.transport.api.model.GenerateVehicleGlobalIdRequestType;
import com.ctrip.dcs.tms.transport.api.model.GenerateVehicleGlobalIdResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryDriverIdCountSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDriverIdSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryTransportGroupListSOARequestType;
import com.ctrip.dcs.tms.transport.application.query.impl.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ErrorCodeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl.*;
import com.ctrip.dcs.tms.transport.interfaces.provider.executor.*;
import com.ctriposs.baiji.rpc.common.types.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class TmsTransportServiceImplTest {

    @InjectMocks
    private TmsTransportServiceImpl tmsTransportService;
    @InjectMocks
    private TmsTransportServiceImpl driverCommandService;
    @InjectMocks
    private TmsTransportServiceImpl driverQueryService;
    @InjectMocks
    private TmsTransportServiceImpl commandService;
    @InjectMocks
    private TmsTransportServiceImpl queryService;
    @InjectMocks
    private QueryTransportGroupListExecutor queryTransportGroupListExecutor;
    @Mock
    private TransportGroupQueryServiceImpl transportGroupQueryService;
    @Mock
    private EnumRepositoryImpl enumRepository;

    @Test
    public void checkHealth() throws Exception {
        CheckHealthRequestType checkHealthRequestType = new CheckHealthRequestType();
        CheckHealthResponseType checkHealthResponseType = tmsTransportService.checkHealth(checkHealthRequestType);
        Assert.assertNotNull(checkHealthResponseType);
    }

    @Test(expected = Exception.class)
    public void toTest() throws Exception {
        tmsTransportService.queryDriverIdCount(new QueryDriverIdCountSOARequestType());
    }

    @Test(expected = Exception.class)
    public void toTest1() throws Exception {
        tmsTransportService.queryDriverId(new QueryDriverIdSOARequestType());
    }

    @Test
    public void discardDriver() throws Exception {
        DiscardDrvSOARequestType discardDriverRequestType = new DiscardDrvSOARequestType();
        DiscardDrvSOAResponseType discardDriverResponseType = tmsTransportService.discardDrv(discardDriverRequestType);
        Assert.assertNotNull(discardDriverResponseType);
        Assert.assertEquals(discardDriverResponseType.getResponseResult().getReturnCode(), ErrorCodeEnum.TRANSPORT_UN_SUPPORT_DRIVER_DISCARD.getCode());
    }

    @Test
    public void discardVehicle() throws Exception {
        DiscardVehSOARequestType discardVehicleRequestType = new DiscardVehSOARequestType();
        DiscardVehSOAResponseType discardVehicleResponseType = tmsTransportService.discardVeh(discardVehicleRequestType);
        Assert.assertNotNull(discardVehicleResponseType);
        Assert.assertEquals(discardVehicleResponseType.getResponseResult().getReturnCode(), ErrorCodeEnum.TRANSPORT_UN_SUPPORT_VEHICLE_DISCARD.getCode());
    }

    @Test(expected = Exception.class)
    public void generateVehicleGlobalId() throws Exception {
        GenerateVehicleGlobalIdResponseType generateVehicleGlobalIdResponseType =
          tmsTransportService.generateVehicleGlobalId(new GenerateVehicleGlobalIdRequestType());
        Assert.assertNotNull(generateVehicleGlobalIdResponseType);
    }



}
