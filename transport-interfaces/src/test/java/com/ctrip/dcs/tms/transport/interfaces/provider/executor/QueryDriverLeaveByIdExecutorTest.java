package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryDriverLeaveByIdRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDriverLeaveByIdResponseType;
import com.ctrip.dcs.tms.transport.application.dto.DriverLeaveDTO;
import com.ctrip.dcs.tms.transport.application.query.DriverLeaveQueryService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class QueryDriverLeaveByIdExecutorTest {
    @InjectMocks
    QueryDriverLeaveByIdExecutor executor;
    @Mock
    private DriverLeaveQueryService driverLeaveQueryService;
    @Test
    public void test(){
        QueryDriverLeaveByIdRequestType requestType = new QueryDriverLeaveByIdRequestType();
        requestType.setDriverLeaveId(11L);
        requestType.setDriverId(12L);
        QueryDriverLeaveByIdResponseType responseType = executor.execute(requestType);
        Assert.assertTrue(responseType.getData() == null);
        Mockito.when(driverLeaveQueryService.queryDriverLeaveById(Mockito.any(),Mockito.any())).thenReturn(new DriverLeaveDTO());
         responseType = executor.execute(requestType);
        Assert.assertTrue(responseType.getData() != null);
    }
}
