package com.ctrip.dcs.tms.transport.interfaces.interceptor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.interfaces.provider.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class WhiteListInterceptorTest {

    @InjectMocks
    private WhiteListInterceptor interceptor;

    @Mock
    private DrvDrvierRepository drvDrvierRepository;

    @Test
    public void handleTest() {
        QueryDriverSafetyInfoSOARequestType requestType = new QueryDriverSafetyInfoSOARequestType();
        QueryDriverSafetyInfoExecutor executor = new QueryDriverSafetyInfoExecutor();
        try {
            interceptor.handle(executor, requestType);
        } catch (Exception e) {
        }
    }

}