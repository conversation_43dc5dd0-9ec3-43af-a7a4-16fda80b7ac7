package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.scm.sdk.domain.ContractRepository;
import com.ctrip.dcs.scm.sdk.domain.ServiceProviderRepository;
import com.ctrip.dcs.scm.sdk.domain.contract.Contract;
import com.ctrip.dcs.tms.transport.api.model.QueryTransportGroupDetailSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryTransportGroupDetailSOAResponseType;
import com.ctrip.dcs.tms.transport.application.query.InOrderConfigQueryService;
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService;
import com.ctrip.dcs.tms.transport.application.query.WorkShiftQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspIntoOrderConfigPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupWorkShiftPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.dcs.scm.sdk.domain.ServiceProviderRepository;
import com.ctrip.igt.framework.common.result.Result;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Objects;

@RunWith(MockitoJUnitRunner.class)
public class QueryTransportGroupDetailExecutorTest {

    @InjectMocks
    QueryTransportGroupDetailExecutor executor;

    @Mock
    private TransportGroupQueryService transportGroupQueryService;

    @Mock
    private InOrderConfigQueryService inOrderConfigQueryService;

    @Mock
    private WorkShiftQueryService workShiftQueryService;

    @Mock
    private EnumRepository enumRepository;

    @Mock
    private ProductionLineUtil productionLineUtil;
    @Mock
    private ContractRepository contractRepository;
    @Mock
    private ServiceProviderRepository serviceProviderRepository;


    @Test
    public void execute() {

        QueryTransportGroupDetailSOARequestType requestType = new QueryTransportGroupDetailSOARequestType();
        requestType.setTransportGroupId(1L);
        TspTransportGroupPO transportGroupPO = new TspTransportGroupPO();
        transportGroupPO.setTransportGroupId(1L);
        transportGroupPO.setSupplierId(1L);
        transportGroupPO.setPointCityId(1L);
        transportGroupPO.setContractId(1L);
        Result<TspTransportGroupPO> tspTransportGroupPOResult = Result.Builder.<TspTransportGroupPO>newResult().success().withData(transportGroupPO).build();
        Mockito.when(transportGroupQueryService.queryTransportGroupDetail(1L)).thenReturn(tspTransportGroupPOResult);
        List<TspIntoOrderConfigPO> configPOS = Lists.newArrayList();
        TspIntoOrderConfigPO configPO = new TspIntoOrderConfigPO();
        configPO.setTransportGroupId(1L);
        configPO.setCityId(2L);
        configPOS.add(configPO);
        Result<List<TspIntoOrderConfigPO>> inOrderConfigListResul = Result.Builder.<List<TspIntoOrderConfigPO>>newResult().success().withData(configPOS).build();
        List<TspTransportGroupWorkShiftPO> shiftPOList = Lists.newArrayList();
        TspTransportGroupWorkShiftPO shiftPO = new TspTransportGroupWorkShiftPO();
        shiftPO.setTransportGroupId(1L);
        shiftPO.setId(1L);
        shiftPOList.add(shiftPO);
        Result<List<TspTransportGroupWorkShiftPO>> shiftList = Result.Builder.<List<TspTransportGroupWorkShiftPO>>newResult().success().withData(shiftPOList).build();
        Mockito.when(inOrderConfigQueryService.queryInOrderConfigs(1L, TmsTransportConstant.IntoOrderConfigActiveEnum.VALID.getCode())).thenReturn(inOrderConfigListResul);
        Mockito.when(workShiftQueryService.queryWorkShifts(1L,TmsTransportConstant.WorkShiftActiveEnum.VALID.getCode())).thenReturn(shiftList);
        Mockito.when(enumRepository.getSupplierName(1L)).thenReturn("shiftList");
        Contract contract = Contract.newBuilder().withId(1L).withSalesMode(1).withName("11").build();
        Mockito.when(contractRepository.findOne(transportGroupPO.getContractId())).thenReturn(contract);

        QueryTransportGroupDetailSOAResponseType soaResponseType =  executor.execute(requestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }
}
