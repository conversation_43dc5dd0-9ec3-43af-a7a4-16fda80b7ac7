package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class BatchUpdateCertificateCheckScheduleTest {


    @InjectMocks
    BatchUpdateCertificateCheckSchedule schedule;

    @Mock
    private TmsCertificateCheckRepository checkRepository;
    @Mock
    private TmsQmqProducerCommandService tmsQmqProducerCommandService;
    @Mock
    DrvDrvierRepository repository;

    @Test
    public void updateCertificateCheckScheduleMethod() {
        List<TmsCertificateCheckPO> checkPOS = Lists.newArrayList();
        TmsCertificateCheckPO tmsCertificateCheckPO = new TmsCertificateCheckPO();
        tmsCertificateCheckPO.setId(1L);
        tmsCertificateCheckPO.setCheckType(2);
        tmsCertificateCheckPO.setCertificateType(3);
        tmsCertificateCheckPO.setCheckStatus(3);
        checkPOS.add(tmsCertificateCheckPO);
        Mockito.when(checkRepository.queryCerCheckListByIds(Arrays.asList(1L))).thenReturn(checkPOS);
        Mockito.when(checkRepository.batchUpdateCheckStatus(Arrays.asList(1L),1)).thenReturn(1);
        schedule.updateCertificateCheckScheduleMethod("1",1);
        Assert.assertTrue(true);
    }

    @Test
    public void updateCertificateCheckScheduleMethodVeh() {
        List<TmsCertificateCheckPO> checkPOS = Lists.newArrayList();
        TmsCertificateCheckPO tmsCertificateCheckPO = new TmsCertificateCheckPO();
        tmsCertificateCheckPO.setId(1L);
        tmsCertificateCheckPO.setCheckType(3);
        tmsCertificateCheckPO.setCertificateType(4);
        tmsCertificateCheckPO.setCheckStatus(3);
        tmsCertificateCheckPO.setCheckId(1L);
        checkPOS.add(tmsCertificateCheckPO);
        Mockito.when(checkRepository.queryCerCheckListByIds(Arrays.asList(1L))).thenReturn(checkPOS);
        Mockito.when(checkRepository.batchUpdateCheckStatus(Arrays.asList(1L),1)).thenReturn(1);
        List<DrvDriverPO> drvDriverPOS = Lists.newArrayList();
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setVehicleId(1L);
        drvDriverPOS.add(drvDriverPO);
        Mockito.when(repository.queryDrvByVehicleIds(Arrays.asList(1L))).thenReturn(drvDriverPOS);
        schedule.updateCertificateCheckScheduleMethod("1",1);
        Assert.assertTrue(true);
    }
}
