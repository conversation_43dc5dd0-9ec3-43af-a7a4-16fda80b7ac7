package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.dcs.tms.transport.application.query.CertificateCheckQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class ApproveCertificateCheckResultScheduleTest {

    @InjectMocks
    ApproveCertificateCheckResultSchedule schedule;
    @Mock
    TmsTransportApproveRepository approveRepository;
    @Mock
    DrvDrvierRepository repository;
    @Mock
    CertificateCheckQueryService checkQueryService;


    @Test
    public void certificateCheckResultScheduleMethod() {
        List<TmsTransportApprovePO> approvePOList = Lists.newArrayList();
        TmsTransportApprovePO tmsTransportApprovePO = new TmsTransportApprovePO();
        tmsTransportApprovePO.setId(1L);
        List<TmsCertificateCheckPO> checkPOS = Lists.newArrayList();
        TmsCertificateCheckPO checkPO = new TmsCertificateCheckPO();
        checkPOS.add(checkPO);
        tmsTransportApprovePO.setCertificateCheckResult(JsonUtil.toJson(checkPOS));
        approvePOList.add(tmsTransportApprovePO);
        Mockito.when(approveRepository.queryApproveIngBySourceId(Arrays.asList(1L), 1, TmsTransportConstant.TransportApproveStatusEnum.WAITAPPROVE.getCode())).thenReturn(approvePOList);
        schedule.certificateCheckResultScheduleMethod("1",1);
        Assert.assertTrue(true);
    }

    @Test
    public void certificateCheckResultScheduleMethod1() {
        List<TmsTransportApprovePO> approvePOList = Lists.newArrayList();
        TmsTransportApprovePO tmsTransportApprovePO = new TmsTransportApprovePO();
        tmsTransportApprovePO.setId(1L);
        List<TmsCertificateCheckPO> checkPOS = Lists.newArrayList();
        TmsCertificateCheckPO checkPO = new TmsCertificateCheckPO();
        checkPO.setCheckStatus(TmsTransportConstant.CheckStatusEnum.CHECKING.getCode());
        checkPOS.add(checkPO);
        tmsTransportApprovePO.setCertificateCheckResult(JsonUtil.toJson(checkPOS));
        approvePOList.add(tmsTransportApprovePO);
        Mockito.when(approveRepository.queryApproveIngBySourceId(Arrays.asList(1L), 1, TmsTransportConstant.TransportApproveStatusEnum.WAITAPPROVE.getCode())).thenReturn(approvePOList);
        schedule.certificateCheckResultScheduleMethod("1",1);
        Assert.assertTrue(true);
    }

    @Test
    public void drvLicenseDataCheck() {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvLicenseNumber("111");
        drvDriverPO.setDrvLicenseName("111");
        Mockito.when(repository.queryByPk(1L)).thenReturn(drvDriverPO);
        TmsCertificateCheckPO checkPO =  schedule.drvLicenseDataCheck(1L);
        Assert.assertTrue(checkPO == null);
    }
}
