package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.scm.merchant.interfaces.dto.*;
import com.ctrip.dcs.scm.merchant.interfaces.message.*;
import com.ctrip.dcs.scm.sdk.domain.CategoryRepository;
import com.ctrip.dcs.scm.sdk.domain.category.Category;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl.EnumRepositoryHelper;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class QueryCategoryExecutorTest {

    @InjectMocks
    private QueryCategoryExecutor queryCategoryExecutor;

    @Mock
    private CategoryRepository categoryRepository;

    @Mock
    private DcsScmMerchantServiceClientProxy dcsScmMerchantServiceClientProxy;

    @Mock
    private EnumRepositoryHelper helper;

    @Test
    public void executorTest() {
        QueryCategorySOARequestType requestType = new QueryCategorySOARequestType();
        requestType.setCityIdList(Lists.newArrayList(1L));
        requestType.setSupplierId(1L);
        QueryCategorySOAResponseType soaResponseType =  queryCategoryExecutor.execute(requestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }

    @Test
    public void executorTest1() throws Exception {
        QueryCategorySOARequestType requestType = new QueryCategorySOARequestType();
        requestType.setCityIdList(Lists.newArrayList(1L));
        requestType.setSupplierId(1L);
        QueryContractListRequestType contractListRequestType = new QueryContractListRequestType();
        contractListRequestType.setRetrievalItems(ImmutableList.of("contract.servedscope"));
        ContractListQueryFilterDTO filterDTO = new ContractListQueryFilterDTO();
        filterDTO.setSupplierIds(ImmutableList.of(1L));
        contractListRequestType.setInclusionFilter(filterDTO);
        QueryContractListResponseType responseType = new QueryContractListResponseType();
        List<ContractDTO> contracts = Lists.newArrayList();
        ContractDTO contractDTO = new ContractDTO();
        List<ServedScopesDTO> scopesDTOS = Lists.newArrayList();
        ServedScopesDTO scopesDTO = new ServedScopesDTO();
        scopesDTO.setCityIds(Lists.newArrayList(1L,2L));
        scopesDTO.setCategoryCode("jnt");
        scopesDTOS.add(scopesDTO);
        ServedScopesDTO scopesDTO1 = new ServedScopesDTO();
        scopesDTO1.setCityIds(Lists.newArrayList(3L,2L));
        scopesDTO1.setCategoryCode("day");
        scopesDTOS.add(scopesDTO1);
        contractDTO.setServedScope(scopesDTOS);
        contracts.add(contractDTO);
        responseType.setContracts(contracts);

        String localCode = "zh-cn";
//        Mockito.when(helper.getLocaleCode()).thenReturn(localCode);

//        Mockito.when(dcsScmMerchantServiceClientProxy.queryContractList(contractListRequestType)).thenReturn(responseType);
        Category category = Category.newBuilder().withCode("jnt").withId(1L).build();
//        Mockito.when(categoryRepository.findOneByCategoryCode("jnt", localCode)).thenReturn(category);
        Category category2 = Category.newBuilder().withCode("day").withId(2L).build();
//        Mockito.when(categoryRepository.findOneByCategoryCode("day", localCode)).thenReturn(category2);
        Category category1 = Category.newBuilder().withCode("rtn").withId(5L).build();
//        Mockito.when(categoryRepository.findOneByCategoryCode("rtn", localCode)).thenReturn(category1);
        QueryCategorySOAResponseType soaResponseType = queryCategoryExecutor.execute(requestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }

}
