package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.basicdatadomain.interfaces.dto.CarDTO;
import com.ctrip.dcs.basicdatadomain.interfaces.dto.VehicleModelInfoDTO;
import com.ctrip.dcs.basicdatadomain.interfaces.message.QueryVehicleModelInfoResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryVehicleBaseSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryVehicleBaseSOAResponseType;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.Dcsbasicdatadomain;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.VehicleCoreService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehVehiclePO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRepository;
import com.ctrip.dcs.tms.transport.interfaces.bridge.ProductLineBridgeManagement;
import com.ctrip.dcs.vehicle.core.contract.QueryVehicleCategoryRelationsRequest;
import com.ctrip.dcs.vehicle.core.contract.QueryVehicleCategoryRelationsResponse;
import com.ctrip.dcs.vehicle.core.contract.VehicleCategoryRelationContract;
import com.ctrip.dcs.vehicle.core.contract.VehicleCategoryRelationContract;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Objects;

import static org.mockito.ArgumentMatchers.any;

@RunWith(MockitoJUnitRunner.class)
public class VehicleBaseListExecutorTest {

    @InjectMocks
    private VehicleBaseListExecutor vehicleBaseListExecutor;

    @Mock
    private EnumRepository enumRepository;

    @Mock
    private VehicleRepository vehicleRepository;

    @Mock
    private ProductionLineUtil productionLineUtil;

    @Mock
    VehicleCoreService vehicleCoreService;
    @Mock
    Dcsbasicdatadomain dcsbasicdatadomain;

    @Mock
    ProductLineBridgeManagement productLineBridgeManagement;

    @Test
    public void executeTest() {
        Mockito.when(productLineBridgeManagement.queryVehicleListBySupplierId(any())).thenReturn(ServiceResponseUtils.success(new QueryVehicleBaseSOAResponseType()));
        QueryVehicleBaseSOARequestType requestType = new QueryVehicleBaseSOARequestType();
        requestType.setSupplierId(1L);
        QueryVehicleBaseSOAResponseType soaResponseType =  vehicleBaseListExecutor.execute(requestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }

//    @Test
//    public void getResultData() {
//        QueryVehicleBaseSOARequestType requestType = new QueryVehicleBaseSOARequestType();
//        requestType.setSupplierId(1L);
//        List<VehVehiclePO> vehVehiclePOList = Lists.newArrayList();
//        VehVehiclePO vehVehiclePO = new VehVehiclePO();
//        vehVehiclePO.setVehicleTypeId(1L);
//        vehVehiclePOList.add(vehVehiclePO);
//        QueryVehicleCategoryRelationsResponse relationsResponse = new QueryVehicleCategoryRelationsResponse();
//        relationsResponse.setCode("200");
//        List<VehicleCategoryRelationContract> data = Lists.newArrayList();
//        VehicleCategoryRelationContract relationDTO = new VehicleCategoryRelationContract();
//        relationDTO.setRelationCode("0");
//        relationDTO.setTargetVehicleCategoryCode("1");
//        data.add(relationDTO);
//        relationsResponse.setData(data);
//        Mockito.when(vehicleCoreService.queryVehicleCategoryRelations(Mockito.any())).thenReturn(relationsResponse);
//        List<VehVehiclePO> soaResponseType =  vehicleBaseListExecutor.getResultData(1L,vehVehiclePOList,1L);
//        Assert.assertTrue(soaResponseType.size() > 0);
//    }

//    @Test
//    public void getResultData1() {
//        QueryVehicleBaseSOARequestType requestType = new QueryVehicleBaseSOARequestType();
//        requestType.setSupplierId(1L);
//        List<VehVehiclePO> vehVehiclePOList = Lists.newArrayList();
//        VehVehiclePO vehVehiclePO = new VehVehiclePO();
//        vehVehiclePO.setVehicleTypeId(1L);
//        vehVehiclePOList.add(vehVehiclePO);
//        QueryVehicleCategoryRelationsResponse relationsResponse = new QueryVehicleCategoryRelationsResponse();
//        relationsResponse.setCode("2001");
//        List<VehicleCategoryRelationContract> data = Lists.newArrayList();
//        VehicleCategoryRelationContract relationDTO = new VehicleCategoryRelationContract();
//        relationDTO.setRelationCode("0");
//        relationDTO.setTargetVehicleCategoryCode("1");
//        data.add(relationDTO);
//        relationsResponse.setData(data);
//        Mockito.when(vehicleCoreService.queryVehicleCategoryRelations(Mockito.any())).thenReturn(relationsResponse);
//        Mockito.when(dcsbasicdatadomain.queryVehicleModelInfo(Mockito.any())).thenReturn(null);
//        List<VehVehiclePO> soaResponseType =  vehicleBaseListExecutor.getResultData(1L,vehVehiclePOList,1L);
//        Assert.assertTrue(soaResponseType.size() > 0);
//    }


//    @Test
//    public void getResultData2() {
//        QueryVehicleBaseSOARequestType requestType = new QueryVehicleBaseSOARequestType();
//        requestType.setSupplierId(1L);
//        List<VehVehiclePO> vehVehiclePOList = Lists.newArrayList();
//        VehVehiclePO vehVehiclePO = new VehVehiclePO();
//        vehVehiclePO.setVehicleTypeId(1L);
//        vehVehiclePOList.add(vehVehiclePO);
//        QueryVehicleCategoryRelationsResponse relationsResponse = new QueryVehicleCategoryRelationsResponse();
//        relationsResponse.setCode("200");
//        List<VehicleCategoryRelationContract> data = Lists.newArrayList();
//        VehicleCategoryRelationContract relationDTO = new VehicleCategoryRelationContract();
//        relationDTO.setRelationCode("0");
//        relationDTO.setTargetVehicleCategoryCode("2");
//        data.add(relationDTO);
//        relationsResponse.setData(data);
//        Mockito.when(vehicleCoreService.queryVehicleCategoryRelations(Mockito.any())).thenReturn(relationsResponse);
//        List<VehVehiclePO> soaResponseType =  vehicleBaseListExecutor.getResultData(1L,vehVehiclePOList,1L);
//        Assert.assertTrue(soaResponseType.size() == 0);
//    }


//    @Test
//    public void getResultData3() {
//        QueryVehicleBaseSOARequestType requestType = new QueryVehicleBaseSOARequestType();
//        requestType.setSupplierId(1L);
//        List<VehVehiclePO> vehVehiclePOList = Lists.newArrayList();
//        VehVehiclePO vehVehiclePO = new VehVehiclePO();
//        vehVehiclePO.setVehicleTypeId(1L);
//        vehVehiclePOList.add(vehVehiclePO);
//        QueryVehicleCategoryRelationsResponse relationsResponse = new QueryVehicleCategoryRelationsResponse();
//        relationsResponse.setCode("200");
//        List<VehicleCategoryRelationContract> data = Lists.newArrayList();
//        VehicleCategoryRelationContract relationDTO = new VehicleCategoryRelationContract();
//        relationDTO.setRelationCode("2");
//        relationDTO.setTargetVehicleCategoryCode("1");
//        data.add(relationDTO);
//        relationsResponse.setData(data);
//        Mockito.when(vehicleCoreService.queryVehicleCategoryRelations(Mockito.any())).thenReturn(relationsResponse);
//        Mockito.when(dcsbasicdatadomain.queryVehicleModelInfo(Mockito.any())).thenReturn(null);
//        List<VehVehiclePO> soaResponseType =  vehicleBaseListExecutor.getResultData(1L,vehVehiclePOList,1L);
//        Assert.assertTrue(soaResponseType.size() > 0);
//    }

//    @Test
//    public void filtrationVehicleSeries() {
//        QueryVehicleBaseSOARequestType requestType = new QueryVehicleBaseSOARequestType();
//        requestType.setSupplierId(1L);
//        List<VehVehiclePO> vehVehiclePOList = Lists.newArrayList();
//        VehVehiclePO vehVehiclePO = new VehVehiclePO();
//        vehVehiclePO.setVehicleTypeId(1L);
//        vehVehiclePO.setVehicleSeries(1L);
//        vehVehiclePOList.add(vehVehiclePO);
//        QueryVehicleCategoryRelationsResponse relationsResponse = new QueryVehicleCategoryRelationsResponse();
//        relationsResponse.setCode("200");
//        List<VehicleCategoryRelationContract> data = Lists.newArrayList();
//        VehicleCategoryRelationContract relationDTO = new VehicleCategoryRelationContract();
//        relationDTO.setRelationCode("2");
//        relationDTO.setTargetVehicleCategoryCode("1");
//        data.add(relationDTO);
//        relationsResponse.setData(data);
//        QueryVehicleModelInfoResponseType modelInfoResponseType = new QueryVehicleModelInfoResponseType();
//        ResponseResult responseResult = new ResponseResult();
//        responseResult.setReturnCode("200");
//        modelInfoResponseType.setResponseResult(responseResult);
//        List<VehicleModelInfoDTO> results = Lists.newArrayList();
//        VehicleModelInfoDTO vehicleModelInfoDTO = new VehicleModelInfoDTO();
//        List<CarDTO> carList = Lists.newArrayList();
//        CarDTO carDTO = new CarDTO();
//        carDTO.setId(1L);
//        carList.add(carDTO);
//        vehicleModelInfoDTO.setCarList(carList);
//        results.add(vehicleModelInfoDTO);
//        modelInfoResponseType.setResults(results);
//        Mockito.when(dcsbasicdatadomain.queryVehicleModelInfo(Mockito.any())).thenReturn(modelInfoResponseType);
//        List<VehVehiclePO> soaResponseType =  vehicleBaseListExecutor.filtrationVehicleSeries(1L,1L,vehVehiclePOList);
//        Assert.assertTrue(soaResponseType.size() > 0);
//    }

//    @Test
//    public void filtrationVehicleSeries1() {
//        QueryVehicleBaseSOARequestType requestType = new QueryVehicleBaseSOARequestType();
//        requestType.setSupplierId(1L);
//        List<VehVehiclePO> vehVehiclePOList = Lists.newArrayList();
//        VehVehiclePO vehVehiclePO = new VehVehiclePO();
//        vehVehiclePO.setVehicleTypeId(1L);
//        vehVehiclePO.setVehicleSeries(1L);
//        vehVehiclePOList.add(vehVehiclePO);
//        QueryVehicleCategoryRelationsResponse relationsResponse = new QueryVehicleCategoryRelationsResponse();
//        relationsResponse.setCode("200");
//        List<VehicleCategoryRelationContract> data = Lists.newArrayList();
//        VehicleCategoryRelationContract relationDTO = new VehicleCategoryRelationContract();
//        relationDTO.setRelationCode("2");
//        relationDTO.setTargetVehicleCategoryCode("1");
//        data.add(relationDTO);
//        relationsResponse.setData(data);
//        QueryVehicleModelInfoResponseType modelInfoResponseType = new QueryVehicleModelInfoResponseType();
//        ResponseResult responseResult = new ResponseResult();
//        responseResult.setReturnCode("200");
////        modelInfoResponseType.setResponseResult(responseResult);
//        List<VehicleModelInfoDTO> results = Lists.newArrayList();
//        VehicleModelInfoDTO vehicleModelInfoDTO = new VehicleModelInfoDTO();
//        List<CarDTO> carList = Lists.newArrayList();
//        CarDTO carDTO = new CarDTO();
//        carDTO.setId(1L);
//        carList.add(carDTO);
//        vehicleModelInfoDTO.setCarList(carList);
//        results.add(vehicleModelInfoDTO);
//        modelInfoResponseType.setResults(results);
//        Mockito.when(dcsbasicdatadomain.queryVehicleModelInfo(Mockito.any())).thenReturn(modelInfoResponseType);
//        List<VehVehiclePO> soaResponseType =  vehicleBaseListExecutor.filtrationVehicleSeries(1L,1L,vehVehiclePOList);
//        Assert.assertTrue(soaResponseType.size() > 0);
//    }

//    @Test
//    public void filtrationVehicleSeries2() {
//        QueryVehicleBaseSOARequestType requestType = new QueryVehicleBaseSOARequestType();
//        requestType.setSupplierId(1L);
//        List<VehVehiclePO> vehVehiclePOList = Lists.newArrayList();
//        VehVehiclePO vehVehiclePO = new VehVehiclePO();
//        vehVehiclePO.setVehicleTypeId(1L);
//        vehVehiclePO.setVehicleSeries(1L);
//        vehVehiclePOList.add(vehVehiclePO);
//        QueryVehicleCategoryRelationsResponse relationsResponse = new QueryVehicleCategoryRelationsResponse();
//        relationsResponse.setCode("200");
//        List<VehicleCategoryRelationContract> data = Lists.newArrayList();
//        VehicleCategoryRelationContract relationDTO = new VehicleCategoryRelationContract();
//        relationDTO.setRelationCode("2");
//        relationDTO.setTargetVehicleCategoryCode("1");
//        data.add(relationDTO);
//        relationsResponse.setData(data);
//        QueryVehicleModelInfoResponseType modelInfoResponseType = new QueryVehicleModelInfoResponseType();
//        ResponseResult responseResult = new ResponseResult();
//        responseResult.setReturnCode("2001");
//        modelInfoResponseType.setResponseResult(responseResult);
//        List<VehicleModelInfoDTO> results = Lists.newArrayList();
//        VehicleModelInfoDTO vehicleModelInfoDTO = new VehicleModelInfoDTO();
//        List<CarDTO> carList = Lists.newArrayList();
//        CarDTO carDTO = new CarDTO();
//        carDTO.setId(1L);
//        carList.add(carDTO);
//        vehicleModelInfoDTO.setCarList(carList);
//        results.add(vehicleModelInfoDTO);
//        modelInfoResponseType.setResults(results);
//        Mockito.when(dcsbasicdatadomain.queryVehicleModelInfo(Mockito.any())).thenReturn(modelInfoResponseType);
//        List<VehVehiclePO> soaResponseType =  vehicleBaseListExecutor.filtrationVehicleSeries(1L,1L,vehVehiclePOList);
//        Assert.assertTrue(soaResponseType.size() > 0);
//    }

//    @Test
//    public void filtrationVehicleSeries3() {
//        QueryVehicleBaseSOARequestType requestType = new QueryVehicleBaseSOARequestType();
//        requestType.setSupplierId(1L);
//        List<VehVehiclePO> vehVehiclePOList = Lists.newArrayList();
//        VehVehiclePO vehVehiclePO = new VehVehiclePO();
//        vehVehiclePO.setVehicleTypeId(1L);
//        vehVehiclePO.setVehicleSeries(1L);
//        vehVehiclePOList.add(vehVehiclePO);
//        QueryVehicleCategoryRelationsResponse relationsResponse = new QueryVehicleCategoryRelationsResponse();
//        relationsResponse.setCode("200");
//        List<VehicleCategoryRelationContract> data = Lists.newArrayList();
//        VehicleCategoryRelationContract relationDTO = new VehicleCategoryRelationContract();
//        relationDTO.setRelationCode("2");
//        relationDTO.setTargetVehicleCategoryCode("1");
//        data.add(relationDTO);
//        relationsResponse.setData(data);
//        QueryVehicleModelInfoResponseType modelInfoResponseType = new QueryVehicleModelInfoResponseType();
//        ResponseResult responseResult = new ResponseResult();
//        responseResult.setReturnCode("200");
//        modelInfoResponseType.setResponseResult(responseResult);
//        List<VehicleModelInfoDTO> results = Lists.newArrayList();
//        VehicleModelInfoDTO vehicleModelInfoDTO = new VehicleModelInfoDTO();
//        List<CarDTO> carList = Lists.newArrayList();
//        CarDTO carDTO = new CarDTO();
//        carDTO.setId(1L);
//        carList.add(carDTO);
//        vehicleModelInfoDTO.setCarList(carList);
//        results.add(vehicleModelInfoDTO);
////        modelInfoResponseType.setResults(results);
//        Mockito.when(dcsbasicdatadomain.queryVehicleModelInfo(Mockito.any())).thenReturn(modelInfoResponseType);
//        List<VehVehiclePO> soaResponseType =  vehicleBaseListExecutor.filtrationVehicleSeries(1L,1L,vehVehiclePOList);
//        Assert.assertTrue(soaResponseType.size() > 0);
//    }

//    @Test
//    public void filtrationVehicleSeries4() {
//        QueryVehicleBaseSOARequestType requestType = new QueryVehicleBaseSOARequestType();
//        requestType.setSupplierId(1L);
//        List<VehVehiclePO> vehVehiclePOList = Lists.newArrayList();
//        VehVehiclePO vehVehiclePO = new VehVehiclePO();
//        vehVehiclePO.setVehicleTypeId(1L);
//        vehVehiclePO.setVehicleSeries(1L);
//        vehVehiclePOList.add(vehVehiclePO);
//        QueryVehicleCategoryRelationsResponse relationsResponse = new QueryVehicleCategoryRelationsResponse();
//        relationsResponse.setCode("200");
//        List<VehicleCategoryRelationContract> data = Lists.newArrayList();
//        VehicleCategoryRelationContract relationDTO = new VehicleCategoryRelationContract();
//        relationDTO.setRelationCode("2");
//        relationDTO.setTargetVehicleCategoryCode("1");
//        data.add(relationDTO);
//        relationsResponse.setData(data);
//        QueryVehicleModelInfoResponseType modelInfoResponseType = new QueryVehicleModelInfoResponseType();
//        ResponseResult responseResult = new ResponseResult();
//        responseResult.setReturnCode("200");
//        modelInfoResponseType.setResponseResult(responseResult);
//        List<VehicleModelInfoDTO> results = Lists.newArrayList();
//        VehicleModelInfoDTO vehicleModelInfoDTO = new VehicleModelInfoDTO();
//        List<CarDTO> carList = Lists.newArrayList();
//        CarDTO carDTO = new CarDTO();
//        carDTO.setId(1L);
//        carList.add(carDTO);
////        vehicleModelInfoDTO.setCarList(carList);
//        results.add(vehicleModelInfoDTO);
//        modelInfoResponseType.setResults(results);
//        Mockito.when(dcsbasicdatadomain.queryVehicleModelInfo(Mockito.any())).thenReturn(modelInfoResponseType);
//        List<VehVehiclePO> soaResponseType =  vehicleBaseListExecutor.filtrationVehicleSeries(1L,1L,vehVehiclePOList);
//        Assert.assertTrue(soaResponseType.size() > 0);
//    }
//
//    @Test
//    public void filtrationVehicleSeries5() {
//        QueryVehicleBaseSOARequestType requestType = new QueryVehicleBaseSOARequestType();
//        requestType.setSupplierId(1L);
//        List<VehVehiclePO> vehVehiclePOList = Lists.newArrayList();
//        VehVehiclePO vehVehiclePO = new VehVehiclePO();
//        vehVehiclePO.setVehicleTypeId(1L);
//        vehVehiclePO.setVehicleSeries(1L);
//        vehVehiclePOList.add(vehVehiclePO);
//        QueryVehicleCategoryRelationsResponse relationsResponse = new QueryVehicleCategoryRelationsResponse();
//        relationsResponse.setCode("200");
//        List<VehicleCategoryRelationContract> data = Lists.newArrayList();
//        VehicleCategoryRelationContract relationDTO = new VehicleCategoryRelationContract();
//        relationDTO.setRelationCode("2");
//        relationDTO.setTargetVehicleCategoryCode("1");
//        data.add(relationDTO);
//        relationsResponse.setData(data);
//        QueryVehicleModelInfoResponseType modelInfoResponseType = new QueryVehicleModelInfoResponseType();
//        ResponseResult responseResult = new ResponseResult();
//        responseResult.setReturnCode("200");
//        modelInfoResponseType.setResponseResult(responseResult);
//        List<VehicleModelInfoDTO> results = Lists.newArrayList();
//        VehicleModelInfoDTO vehicleModelInfoDTO = new VehicleModelInfoDTO();
//        List<CarDTO> carList = Lists.newArrayList();
//        CarDTO carDTO = new CarDTO();
//        carDTO.setId(2L);
//        carList.add(carDTO);
//        vehicleModelInfoDTO.setCarList(carList);
//        results.add(vehicleModelInfoDTO);
//        modelInfoResponseType.setResults(results);
//        Mockito.when(dcsbasicdatadomain.queryVehicleModelInfo(Mockito.any())).thenReturn(modelInfoResponseType);
//        List<VehVehiclePO> soaResponseType =  vehicleBaseListExecutor.filtrationVehicleSeries(1L,1L,vehVehiclePOList);
//        Assert.assertTrue(soaResponseType.size() == 0);
//    }

}
