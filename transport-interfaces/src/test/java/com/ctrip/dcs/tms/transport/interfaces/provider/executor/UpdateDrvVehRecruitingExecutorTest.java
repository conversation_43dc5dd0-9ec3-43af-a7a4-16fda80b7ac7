package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.common.result.Result;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class UpdateDrvVehRecruitingExecutorTest {

    @InjectMocks
    private UpdateDrvVehRecruitingExecutor updateDrvVehRecruitingExecutor;

    @Mock
    private DrvVehRecruitingCommandService commandService;

    @Test
    public void executorTest() {
        DrvVehRecruitingUpdateSOARequestType requestType = new DrvVehRecruitingUpdateSOARequestType();
        Mockito.when(commandService.updateDrvVehRecruiting(requestType)).thenReturn(Result.Builder.<Boolean>newResult().success().withMsg("2121").withData(true).build());
        DrvVehRecruitingUpdateSOAResponseType soaResponseType =  updateDrvVehRecruitingExecutor.execute(requestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }
}