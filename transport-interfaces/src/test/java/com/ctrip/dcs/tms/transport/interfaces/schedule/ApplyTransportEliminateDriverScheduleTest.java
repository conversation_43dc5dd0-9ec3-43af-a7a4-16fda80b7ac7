package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.dcs.tms.transport.application.command.TmsQmqProducerCommandService;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupDriverRelationPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TspTransportGroupDriverRelationRepository;
import com.google.common.collect.Maps;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class ApplyTransportEliminateDriverScheduleTest {

    @InjectMocks
    ApplyTransportEliminateDriverSchedule schedule;
    @Mock
    private TspTransportGroupDriverRelationRepository relationRepository;

    @Mock
    private TmsTransportQconfig qconfig;

    @Mock
    TransportGroupQueryService queryService;
    @Mock
    DriverQueryService driverQueryService;
    @Mock
    private TmsQmqProducerCommandService tmsQmqProducerCommandService;

    @Test
    public void applyTransportEliminateDriverScheduleMethod() {
        Mockito.when(relationRepository.queryApplySuccessTransportCount(Arrays.asList(1L))).thenReturn(1);
        List<TspTransportGroupDriverRelationPO> driverRelationPOList = Lists.newArrayList();
        TspTransportGroupDriverRelationPO relationPO = new TspTransportGroupDriverRelationPO();
        relationPO.setDrvId(1L);
        relationPO.setTransportGroupId(1L);
        relationPO.setApplyStatus(2);
        relationPO.setDatachangeLasttime(DateUtil.string2Timestamp("2023-02-02 12:12:12",DateUtil.YYYYMMDDHHMMSS));
        driverRelationPOList.add(relationPO);
        Mockito.when(relationRepository.queryApplySuccessTransportList(Arrays.asList(1L),1,50)).thenReturn(driverRelationPOList);
        Mockito.when(queryService.grayTransportId(1L)).thenReturn(true);
        Mockito.when(qconfig.getApplyTransportDurationCThreshold()).thenReturn(2);
        Map<Long,Long> calculateMap = Maps.newHashMap();
        calculateMap.put(1L,10L);
        Map<Long, Timestamp> drvCalculateBeginTimeMap = com.google.common.collect.Maps.newHashMap();
        drvCalculateBeginTimeMap.put(1L,DateUtil.string2Timestamp("2023-02-02 12:12:12",DateUtil.YYYYMMDDHHMMSS));
//        Mockito.when(driverQueryService.checkApplyDriverLeaveDuration(new ArrayList<>(1),null, DateUtil.getNow(),drvCalculateBeginTimeMap)).thenReturn(calculateMap);
        Boolean result = schedule.applyTransportEliminateDriverScheduleMethod("1");
        Assert.assertTrue(result);
    }
}
