package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.dcs.tms.transport.application.command.CommonCommandService;
import com.ctrip.dcs.tms.transport.application.command.TmsDrvFreezeCommandService;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TmsDrvFreezePO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.ApprovalProcessAuthQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.EmailTemplateQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsDrvFreezeRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRepository;
import com.ctrip.framework.ucs.client.api.UcsJobHelper;
import com.google.common.collect.Maps;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import qunar.tc.schedule.Parameter;

import java.util.Date;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class DrvUnfreezeScheduleTest {

    @InjectMocks
    DrvUnfreezeSchedule schedule;
    @Mock
    TmsDrvFreezeRepository freezeRepository;

    @Mock
    CommonCommandService commandService;

    @Mock
    DrvDrvierRepository drvierRepository;
    @Mock
    private ApprovalProcessAuthQconfig approvalProcessAuthQconfig;
    @Mock
    private TmsDrvFreezeCommandService freezeCommandService;
    @Mock
    private EnumRepository enumRepository;
    @Mock
    private DriverQueryService driverQueryService;
    @Mock
    private VehicleRepository vehicleRepository;
    @Mock
    EmailTemplateQconfig emailTemplateQconfig;
    @Mock
    TmsTransportQconfig tmsTransportQconfig;

    @Test
    public void drvUnfreezeSchedule() throws Exception {
        List<TmsDrvFreezePO> freezePOS = Lists.newArrayList();
        TmsDrvFreezePO tmsDrvFreezePO = new TmsDrvFreezePO();
        tmsDrvFreezePO.setDrvId(1L);
        tmsDrvFreezePO.setFirstFreezeTime(DateUtil.string2Timestamp("2022-11-21 11:12:12",DateUtil.YYYYMMDDHHMMSS));
        tmsDrvFreezePO.setFreezeHour(193);
        tmsDrvFreezePO.setTotalFreezeFrom("1");
        tmsDrvFreezePO.setUnfreezeAction(1);
        tmsDrvFreezePO.setFreezeStatus(2);
        tmsDrvFreezePO.setConfirmOnlineStatus(false);
        freezePOS.add(tmsDrvFreezePO);
        Mockito.when(freezeRepository.queryDrvFreezeAll()).thenReturn(freezePOS);
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setSupplierId(1L);
        drvDriverPO.setDrvStatus(2);
        drvDriverPO.setCityId(1L);
        drvDriverPO.setDrvName("111");
        drvDriverPO.setInternalScope(0);
        Mockito.when(drvierRepository.queryByPk(tmsDrvFreezePO.getDrvId())).thenReturn(drvDriverPO);
        Mockito.when(enumRepository.getSupplierEmail(drvDriverPO.getSupplierId())).thenReturn("122");
        Map<Integer,Integer> map = Maps.newHashMap();
        map.put(1,7);
        map.put(2,24);
        Mockito.when(tmsTransportQconfig.getDrvFreezeTimeConfigMap()).thenReturn(map);
        Mockito.when(approvalProcessAuthQconfig.getVbkUnfreezeMessageCode()).thenReturn("111");
//        Mockito.when(emailTemplateQconfig.getDrvUnfreezeEmailNewSubject()).thenReturn("1111");
//        Mockito.when(emailTemplateQconfig.getDrvUnfreezeEmailNewContent()).thenReturn("111");
        Mockito.when(tmsTransportQconfig.getDrvUnfreezeNewSendEmailSwitch()).thenReturn(true);
//        Mockito.when(emailTemplateQconfig.getDrvUnfreezeEmailNewContent()).thenReturn("111");
//        Mockito.when(tmsTransportQconfig.getDrvUnfreezeNewSendEmailSwitch()).thenReturn(true);
        Mockito.when(emailTemplateQconfig.getDrvUnfreezeEmailOldSubject()).thenReturn("sssdsfsdf%s,%s");
        Mockito.when(emailTemplateQconfig.getDrvUnfreezeEmailOldContent()).thenReturn("sssdsfsdf%s,%s");
        Mockito.when(emailTemplateQconfig.getDrvUnfreezeEmailNewSubject()).thenReturn("sssdsfsdf%s,%s");
        Mockito.when(emailTemplateQconfig.getDrvUnfreezeEmailNewContent()).thenReturn("sssdsfsdf%s,%s,%s");
        schedule.drvUnfreezeScheduleMethod();
        Boolean result = true;
        Assert.assertEquals(result, true);
    }

    @Test
    public void verdictBoolTest(){
        TmsDrvFreezePO drvFreezePO = new TmsDrvFreezePO();
        drvFreezePO.setTotalFreezeFrom("1");
        drvFreezePO.setUnfreezeAction(1);
        drvFreezePO.setFreezeHour(198);
        drvFreezePO.setConfirmOnlineStatus(false);
        Boolean flag = schedule.verdictBool(drvFreezePO,23,7,24);
        Assert.assertTrue(flag);
    }


    @Test
    public void drvUnfreezeSchedule1() throws Exception {
        List<TmsDrvFreezePO> freezePOS = Lists.newArrayList();
        TmsDrvFreezePO tmsDrvFreezePO = new TmsDrvFreezePO();
        tmsDrvFreezePO.setDrvId(1L);
        tmsDrvFreezePO.setFirstFreezeTime(DateUtil.string2Timestamp("2022-11-11 11:12:12",DateUtil.YYYYMMDDHHMMSS));
        tmsDrvFreezePO.setFreezeHour(193);
        tmsDrvFreezePO.setTotalFreezeFrom("1");
        tmsDrvFreezePO.setUnfreezeAction(1);
        tmsDrvFreezePO.setFreezeStatus(2);
        tmsDrvFreezePO.setConfirmOnlineStatus(false);
        freezePOS.add(tmsDrvFreezePO);
        Mockito.when(freezeRepository.queryDrvFreezeAll()).thenReturn(freezePOS);
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setSupplierId(1L);
        drvDriverPO.setDrvStatus(2);
        drvDriverPO.setCityId(1L);
        drvDriverPO.setDrvName("111");
        drvDriverPO.setInternalScope(0);
        Mockito.when(drvierRepository.queryByPk(tmsDrvFreezePO.getDrvId())).thenReturn(drvDriverPO);
        Mockito.when(enumRepository.getSupplierEmail(drvDriverPO.getSupplierId())).thenReturn("122");
        Map<Integer,Integer> map = Maps.newHashMap();
        map.put(1,7);
        map.put(2,24);
        Mockito.when(tmsTransportQconfig.getDrvFreezeTimeConfigMap()).thenReturn(map);
        Mockito.when(approvalProcessAuthQconfig.getVbkUnfreezeMessageCode()).thenReturn("111");
        Mockito.when(tmsTransportQconfig.getDrvUnfreezeNewSendEmailSwitch()).thenReturn(true);
        Mockito.when(emailTemplateQconfig.getDrvUnfreezeEmailOldSubject()).thenReturn("sssdsfsdf%s,%s");
        Mockito.when(emailTemplateQconfig.getDrvUnfreezeEmailOldContent()).thenReturn("sssdsfsdf%s,%s");
        Mockito.when(emailTemplateQconfig.getDrvUnfreezeEmailNewSubject()).thenReturn("sssdsfsdf%s,%s");
        Mockito.when(emailTemplateQconfig.getDrvUnfreezeEmailNewContent()).thenReturn("sssdsfsdf%s,%s,%s");
        schedule.drvUnfreezeScheduleMethod();
        Boolean result = true;
        Assert.assertEquals(result, true);
    }

    @Test
    public void drvUnfreezeSchedule2() throws Exception {
        List<TmsDrvFreezePO> freezePOS = Lists.newArrayList();
        TmsDrvFreezePO tmsDrvFreezePO = new TmsDrvFreezePO();
        tmsDrvFreezePO.setDrvId(1L);
        tmsDrvFreezePO.setFirstFreezeTime(DateUtil.string2Timestamp("2022-11-11 11:12:12",DateUtil.YYYYMMDDHHMMSS));
        tmsDrvFreezePO.setFreezeHour(193);
        tmsDrvFreezePO.setTotalFreezeFrom("1");
        tmsDrvFreezePO.setUnfreezeAction(1);
        tmsDrvFreezePO.setFreezeStatus(2);
        tmsDrvFreezePO.setConfirmOnlineStatus(false);
        freezePOS.add(tmsDrvFreezePO);
        Mockito.when(freezeRepository.queryDrvFreezeAll()).thenReturn(freezePOS);
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setSupplierId(1L);
        drvDriverPO.setDrvStatus(2);
        drvDriverPO.setCityId(1L);
        drvDriverPO.setDrvName("111");
        drvDriverPO.setInternalScope(1);
        Mockito.when(drvierRepository.queryByPk(tmsDrvFreezePO.getDrvId())).thenReturn(drvDriverPO);
        Mockito.when(enumRepository.getSupplierEmail(drvDriverPO.getSupplierId())).thenReturn("122");
        Map<Integer,Integer> map = Maps.newHashMap();
        map.put(1,7);
        map.put(2,24);
        Mockito.when(tmsTransportQconfig.getDrvFreezeTimeConfigMap()).thenReturn(map);
        schedule.drvUnfreezeScheduleMethod();
        Boolean result = true;
        Assert.assertEquals(result, true);
    }

    @Test
    public void drvUnfreezeSchedule3() throws Exception {
        List<TmsDrvFreezePO> freezePOS = Lists.newArrayList();
        TmsDrvFreezePO tmsDrvFreezePO = new TmsDrvFreezePO();
        tmsDrvFreezePO.setDrvId(1L);
        tmsDrvFreezePO.setFirstFreezeTime(DateUtil.string2Timestamp("2022-11-11 11:12:12",DateUtil.YYYYMMDDHHMMSS));
        tmsDrvFreezePO.setFreezeHour(193);
        tmsDrvFreezePO.setTotalFreezeFrom("2");
        tmsDrvFreezePO.setUnfreezeAction(1);
        tmsDrvFreezePO.setFreezeStatus(2);
        tmsDrvFreezePO.setConfirmOnlineStatus(false);
        freezePOS.add(tmsDrvFreezePO);
        Mockito.when(freezeRepository.queryDrvFreezeAll()).thenReturn(freezePOS);
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setSupplierId(1L);
        drvDriverPO.setDrvStatus(2);
        drvDriverPO.setCityId(1L);
        drvDriverPO.setDrvName("111");
        drvDriverPO.setInternalScope(1);
        Mockito.when(drvierRepository.queryByPk(tmsDrvFreezePO.getDrvId())).thenReturn(drvDriverPO);
        Mockito.when(enumRepository.getSupplierEmail(drvDriverPO.getSupplierId())).thenReturn("122");
        Map<Integer,Integer> map = Maps.newHashMap();
        map.put(1,7);
        map.put(2,24);
        Mockito.when(tmsTransportQconfig.getDrvFreezeTimeConfigMap()).thenReturn(map);
        schedule.drvUnfreezeScheduleMethod();
        Boolean result = true;
        Assert.assertEquals(result, true);
    }
}
