package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryDriverVehicleInfoSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDriverVehicleInfoSOAResponseType;
import com.ctrip.dcs.tms.transport.application.dto.DriverVehicleDTO;
import com.ctrip.dcs.tms.transport.application.query.impl.QueryDriverVehicleInfoService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class QueryDriverVehicleInfoExecutorTest {
    @InjectMocks
    QueryDriverVehicleInfoExecutor executor;
    @Mock
    QueryDriverVehicleInfoService queryDriverVehicleInfoService;
    @Test
    public void execute() {
        DriverVehicleDTO driverVehicleDTO = new DriverVehicleDTO();
        driverVehicleDTO.setVehicleFullImg("VehicleFullImg");
        Mockito.when(queryDriverVehicleInfoService.query(Mockito.any())).thenReturn(driverVehicleDTO);
        QueryDriverVehicleInfoSOARequestType requestType = new QueryDriverVehicleInfoSOARequestType();
        requestType.setDrvId(1111L);
        QueryDriverVehicleInfoSOAResponseType responseType = executor.execute(requestType);
        Assert.assertTrue(responseType.getData().getVehicleFullImg().equals("VehicleFullImg"));
    }
}
