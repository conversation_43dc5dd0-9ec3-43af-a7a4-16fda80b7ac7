package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryTransportGroupInOrderConfigRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryTransportGroupInOrderConfigResponseType;
import com.ctrip.dcs.tms.transport.application.dto.TransportGroupInOrderConfigDTO;
import com.ctrip.dcs.tms.transport.application.dto.TransportGroupTimeSegmentConfigDTO;
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

@RunWith(MockitoJUnitRunner.class)
public class QueryTransportGroupInOrderConfigExecutorTest {
    @InjectMocks
    QueryTransportGroupInOrderConfigExecutor executor;
    @Mock
    private TransportGroupQueryService queryService;
    @Test
    public void execute(){
        TransportGroupInOrderConfigDTO configDTO = new TransportGroupInOrderConfigDTO();
        TransportGroupTimeSegmentConfigDTO transportGroupTimeSegmentConfigDTO = new TransportGroupTimeSegmentConfigDTO();
        transportGroupTimeSegmentConfigDTO.setTime("20:00-23:00");
        configDTO.setTimeSegmentConfigDTOList(Arrays.asList(transportGroupTimeSegmentConfigDTO));
        Mockito.when(queryService.queryInOrderConfig(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any() ,Mockito.any())).thenReturn(Arrays.asList(configDTO));
        QueryTransportGroupInOrderConfigRequestType requestType = new QueryTransportGroupInOrderConfigRequestType();
        requestType.setBookTime("2023-06-16 10:00:00");
        QueryTransportGroupInOrderConfigResponseType result = executor.execute(requestType);
        Assert.assertTrue(result != null);

        requestType.setBookTime(null);
        result = executor.execute(requestType);
        Assert.assertTrue(result != null);
    }
}
