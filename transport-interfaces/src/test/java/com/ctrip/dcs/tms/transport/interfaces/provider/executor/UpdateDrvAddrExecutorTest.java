package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.result.Result;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class UpdateDrvAddrExecutorTest {

    @InjectMocks
    UpdateDrvAddrExecutor executor;
    @Mock
    private DrvDrvierRepository drvDrvierRepository;
    @Mock
    DriverQueryService driverQueryService;

    @Test
    public void getDrvAddrModCount() {
        executor.getDrvAddrModCount("{\"1\":0,\"2\":0,\"3\":0,\"4\":0,\"5\":0,\"6\":0,\"7\":0,\"8\":4,\"9\":0,\"10\":0,\"11\":0,\"12\":0}",1);
        Assert.assertTrue(true);
    }

    @Test
    public void execute() {
        Integer nowMonth = DateUtil.getMonth();
        DrvUpdateAddrRequestType requestType = new DrvUpdateAddrRequestType();
        requestType.setDrvAddr("111");
        requestType.setDrvId(1L);
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        Mockito.when(drvDrvierRepository.queryByPk(1L)).thenReturn(drvDriverPO);
        QueryDrvAddrModCountSOARequestType soaRequestType = new QueryDrvAddrModCountSOARequestType();
        soaRequestType.setDrvId(1L);
        soaRequestType.setMonth(nowMonth);
        QueryDrvAddrModCountSOAResponseType soaResponseType = new QueryDrvAddrModCountSOAResponseType();
        soaResponseType.setIsModify(false);
        soaResponseType.setModCount(1);
        soaResponseType.setModCountThreshold(1);
        Result<QueryDrvAddrModCountSOAResponseType> responseTypeResult = Result.Builder.<QueryDrvAddrModCountSOAResponseType>newResult().success().withData(soaResponseType).build();
        Mockito.when(driverQueryService.queryDrvAddrModCount(soaRequestType)).thenReturn(responseTypeResult);
        DrvUpdateAddrResponseType so=  executor.execute(requestType);
        Assert.assertTrue(!Objects.isNull(so));
        String string = executor.getDrvAddrModCount("{\"1\":0,\"2\":0,\"3\":0,\"4\":0,\"5\":0,\"6\":0,\"7\":0,\"8\":4,\"9\":0,\"10\":0,\"11\":0,\"12\":0}",1);
        Assert.assertTrue(!string.isEmpty());
    }
}
