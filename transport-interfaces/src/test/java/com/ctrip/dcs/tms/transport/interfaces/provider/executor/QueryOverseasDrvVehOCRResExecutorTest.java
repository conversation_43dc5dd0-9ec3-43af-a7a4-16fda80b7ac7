package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryOverseasDrvVehOCRResRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryOverseasDrvVehOCRResResponseType;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

@RunWith(MockitoJUnitRunner.class)
public class QueryOverseasDrvVehOCRResExecutorTest{


    @InjectMocks
    QueryOverseasDrvVehOCRResExecutor executor;

    @Test
    public void testExecute() {
        QueryOverseasDrvVehOCRResRequestType requestType = new QueryOverseasDrvVehOCRResRequestType();
        requestType.setIds(Arrays.asList(1L));
        requestType.setType(2);
        QueryOverseasDrvVehOCRResResponseType responseType =  executor.execute(requestType);
        Assert.assertTrue(responseType!=null);
    }
}