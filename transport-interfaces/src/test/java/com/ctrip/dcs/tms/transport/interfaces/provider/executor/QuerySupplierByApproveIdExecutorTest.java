package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class QuerySupplierByApproveIdExecutorTest {
    @InjectMocks
    QuerySupplierByApproveIdExecutor executor;
    @Mock
    private TmsTransportApproveQueryService tmsTransportApproveQueryService;
    @Test
    public void test(){
        Map<String,String> map = new HashMap<>();
        map.put("1","1");
        Mockito.when(tmsTransportApproveQueryService.querySupplierByApproveId(Mockito.anyList())).thenReturn(map);
        QuerySupplierByApproveIdSOARequestType requestType = new QuerySupplierByApproveIdSOARequestType();
        requestType.setApproveIds(Arrays.asList(1L,2L));
        QuerySupplierByApproveIdSOAResponseType responseType = executor.execute(requestType);
        Assert.isTrue(responseType != null);
        Assert.isTrue("1".equals(responseType.getApproveSupplierIds().get("1")));
    }
}
