package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.Result;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class DrvFaceVerifyCountTransfiniteExecutorTest {

    @InjectMocks
    DrvFaceVerifyCountTransfiniteExecutor executor;
    @Mock
    TmsVerifyEventQueryService queryService;

    @Test
    public void execute() {
        DrvFaceVerifyCountTransfiniteRequestType requestType = new DrvFaceVerifyCountTransfiniteRequestType();
        requestType.setDriverId(1L);
        Result<Boolean> result = Result.Builder.<Boolean>newResult().success().withData(true).build();
        Mockito.when(queryService.drvFaceVerifyCountTransfinite(requestType.getDriverId())).thenReturn(result);
        DrvFaceVerifyCountTransfiniteResponseType responseType =  executor.execute(requestType);
        Assert.assertTrue(!Objects.isNull(responseType));
    }
}
