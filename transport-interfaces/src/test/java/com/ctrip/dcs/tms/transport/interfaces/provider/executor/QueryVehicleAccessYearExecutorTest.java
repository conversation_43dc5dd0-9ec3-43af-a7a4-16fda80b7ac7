package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OverageDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.ctrip.dcs.tms.transport.api.model.QueryVehicleAccessYearSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryVehicleAccessYearSOAResponseType;
import com.ctrip.dcs.tms.transport.application.query.VehicleQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CommonConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OverageQConfig;
import com.ctrip.igt.framework.common.result.Result;

@RunWith(MockitoJUnitRunner.class)
public class QueryVehicleAccessYearExecutorTest {

    @InjectMocks
    QueryVehicleAccessYearExecutor executor;

    @Mock
    VehicleQueryService vehicleQueryService;
    @Mock
    private OverageQConfig overageQConfig;
    @Mock
    private CommonConfig commonConfig;

    @Test
    public void execute() {

        Mockito.when(commonConfig.getOverageGraySwitch()).thenReturn(true);
        OverageDTO overageDTO = new OverageDTO();
        overageDTO.setCityId(1L);
        overageDTO.setVehicleTypeId(1L);
        overageDTO.setAccessLimit(4.5D);
        overageDTO.setOverage(5D);
        Mockito.when(overageQConfig.getOverageMap(Mockito.anyLong(), Mockito.anyLong())).thenReturn(overageDTO);

        List<Long> cistList = new ArrayList<>();
        cistList.add(1L);
        Mockito.when(commonConfig.getCityIdList()).thenReturn(cistList);

        QueryVehicleAccessYearSOARequestType soaRequestType = new QueryVehicleAccessYearSOARequestType();
        soaRequestType.setCityId(1L);
        soaRequestType.setVehicleTypeId(1L);
        QueryVehicleAccessYearSOAResponseType soaResponseType = executor.execute(soaRequestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }
}
