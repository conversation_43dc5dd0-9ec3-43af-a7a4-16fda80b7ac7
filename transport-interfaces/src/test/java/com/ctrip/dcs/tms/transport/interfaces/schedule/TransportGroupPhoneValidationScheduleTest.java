package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.dcs.tms.transport.application.command.CommonCommandService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.http.NepheleHttpService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.MobileHelper;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportGroupRepository;
import com.ctrip.ibu.platform.shark.sdk.utils.JsonUtils;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import qunar.tc.schedule.MockParameter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * 运力组手机号校验任务测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class TransportGroupPhoneValidationScheduleTest {

    @InjectMocks
    private TransportGroupPhoneValidationSchedule schedule;

    @Mock
    private TransportGroupRepository transportGroupRepository;

    @Mock
    private MobileHelper mobileHelper;

    @Mock
    private CommonCommandService commonCommandService;

    @Mock
    private TmsTransportQconfig tmsTransportQconfig;

    @Mock
    private NepheleHttpService nepheleHttpService;

    /**
     * 测试校验所有运力组手机号
     */
    @Test
    public void testValidateAllTransportGroupPhones() throws Exception {
        // 模拟查询运力组
        TspTransportGroupPO group1 = createMockTransportGroup(1L, "测试运力组1", "86", "encrypted_phone_1", "86", "encrypted_standby_1");
        TspTransportGroupPO group2 = createMockTransportGroup(2L, "测试运力组2", "86", "encrypted_phone_2", null, null);
        Mockito.when(transportGroupRepository.queryTransportGroupByIdFromAndPage(Mockito.anyLong(), Mockito.isNull(), Mockito.isNull(), Mockito.eq(1), Mockito.anyInt()))
                .thenReturn(Arrays.asList(group1, group2))
                .thenReturn(new ArrayList<>()); // 第二次调用返回空列表，表示没有更多数据

        // 模拟手机号校验结果，一个有效一个无效
        Mockito.when(mobileHelper.isMobileValid(Mockito.eq("86"), Mockito.eq("encrypted_phone_1"), Mockito.anyLong()))
                .thenReturn(Result.Builder.<Boolean>newResult().success().withData(true).build());
        Mockito.when(mobileHelper.isMobileValid(Mockito.eq("86"), Mockito.eq("encrypted_phone_2"), Mockito.anyLong()))
                .thenReturn(Result.Builder.<Boolean>newResult().fail().build());
        Mockito.when(mobileHelper.isMobileValid(Mockito.eq("86"), Mockito.eq("encrypted_standby_1"), Mockito.anyLong()))
                .thenReturn(Result.Builder.<Boolean>newResult().success().withData(true).build());

        // Mock Nephele upload service
        Mockito.when(nepheleHttpService.upload(any(byte[].class), anyString()))
                .thenReturn("https://nephele-example.com/file.xlsx");

        // 模拟发送邮件结果
        Mockito.when(commonCommandService.sendEmail(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(Result.Builder.<Boolean>newResult().success().withData(true).build());

        String param = JsonUtils.toJson(ImmutableMap.of("pageSize", "100","emailReceivers","<EMAIL>","sleepSeconds","0","emailSubject","运力组手机号校验结果"));

        // 模拟解密手机号
        MockParameter parameter = new MockParameter(param);

        // 执行测试
        schedule.validateTransportGroupPhones(parameter);

        // 验证调用
        Mockito.verify(transportGroupRepository, Mockito.times(2)).queryTransportGroupByIdFromAndPage(Mockito.anyLong(), Mockito.isNull(), Mockito.isNull(), Mockito.eq(1), Mockito.anyInt());
        Mockito.verify(mobileHelper, Mockito.times(3)).isMobileValid(Mockito.anyString(), Mockito.anyString(), Mockito.anyLong());
        Mockito.verify(nepheleHttpService, Mockito.times(1)).upload(any(byte[].class), anyString());
        Mockito.verify(commonCommandService, Mockito.times(1)).sendEmail(Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
    }

    /**
     * 测试校验指定运力组手机号
     */
    @Test
    public void testValidateSpecificTransportGroupPhones() throws Exception {
        // 模拟查询指定运力组
        TspTransportGroupPO group = createMockTransportGroup(1L, "测试运力组1", "86", "encrypted_phone_1", "86", "encrypted_standby_1");
        Mockito.when(transportGroupRepository.queryTspTransportByIds(Mockito.anyList()))
                .thenReturn(Lists.newArrayList(group));

        // 模拟手机号校验结果，一个有效一个无效
        Mockito.when(mobileHelper.isMobileValid(Mockito.eq("86"), Mockito.eq("encrypted_phone_1"), Mockito.anyLong()))
                .thenReturn(Result.Builder.<Boolean>newResult().success().withData(true).build());
        Mockito.when(mobileHelper.isMobileValid(Mockito.eq("86"), Mockito.eq("encrypted_standby_1"), Mockito.anyLong()))
                .thenReturn(Result.Builder.<Boolean>newResult().fail().build());

        // Mock Nephele upload service
        Mockito.when(nepheleHttpService.upload(any(byte[].class), anyString()))
                .thenReturn("https://nephele-example.com/file.xlsx");

        // 模拟发送邮件结果
        Mockito.when(commonCommandService.sendEmail(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(Result.Builder.<Boolean>newResult().success().withData(true).build());


        String param = JsonUtils.toJson(ImmutableMap.of("transportGroupIds", "1","emailReceivers","<EMAIL>"));
        // 设置参数
        MockParameter parameter = new MockParameter(param);
        // 执行测试
        schedule.validateTransportGroupPhones(parameter);

        // 验证调用
        Mockito.verify(transportGroupRepository, Mockito.times(1)).queryTspTransportByIds(Mockito.anyList());
        Mockito.verify(mobileHelper, Mockito.times(2)).isMobileValid(Mockito.anyString(), Mockito.anyString(), Mockito.anyLong());
        Mockito.verify(nepheleHttpService, Mockito.times(1)).upload(any(byte[].class), anyString());
        Mockito.verify(commonCommandService, Mockito.times(1)).sendEmail(Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
    }

    /**
     * 测试无收件人情况
     */
    @Test
    public void testValidateWithoutEmailReceivers() throws Exception {
        // 模拟查询运力组
        TspTransportGroupPO group = createMockTransportGroup(1L, "测试运力组1", "86", "encrypted_phone_1", null, null);
        Mockito.when(transportGroupRepository.queryTspTransportByIds(Mockito.anyList()))
                .thenReturn(Lists.newArrayList(group));

        // 模拟手机号校验结果为无效
        Mockito.when(mobileHelper.isMobileValid(Mockito.eq("86"), Mockito.eq("encrypted_phone_1"), Mockito.anyLong()))
                .thenReturn(Result.Builder.<Boolean>newResult().fail().build());

        String param = JsonUtils.toJson(ImmutableMap.of("transportGroupIds", "1"));
        // 设置参数，不设置emailReceivers
        MockParameter parameter = new MockParameter(param);

        // 执行测试
        schedule.validateTransportGroupPhones(parameter);

        // 验证调用 - 不应该调用发送邮件
        Mockito.verify(commonCommandService, Mockito.never()).sendEmail(Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
    }

    /**
     * 测试无效手机号情况
     */
    @Test
    public void testValidateEmptyPhone() throws Exception {
        // 模拟查询运力组 - 设置空手机号
        TspTransportGroupPO group = createMockTransportGroup(1L, "测试运力组1", "86", "", "86", null);
        Mockito.when(transportGroupRepository.queryTspTransportByIds(Mockito.anyList()))
                .thenReturn(Lists.newArrayList(group));

        String param = JsonUtils.toJson(ImmutableMap.of("transportGroupIds", "1","emailReceivers","<EMAIL>"));
        // 设置参数
        MockParameter parameter = new MockParameter(param);

        // 执行测试
        schedule.validateTransportGroupPhones(parameter);

        // 不应该调用手机号校验
        Mockito.verify(mobileHelper, Mockito.never()).isMobileValid(Mockito.anyString(), Mockito.anyString(), Mockito.anyLong());
        // 应该发送邮件通知
        Mockito.verify(commonCommandService, Mockito.times(0)).sendEmail(Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
    }

    /**
     * 创建模拟的运力组对象
     */
    private TspTransportGroupPO createMockTransportGroup(Long id, String name, String igtCode, String phone, String standbyIgtCode, String standbyPhone) {
        TspTransportGroupPO group = new TspTransportGroupPO();
        group.setTransportGroupId(id);
        group.setTransportGroupName(name);
        group.setIgtCode(igtCode);
        group.setDispatcherPhone(phone);
        group.setStandbyIgtCode(standbyIgtCode);
        group.setStandbyPhone(standbyPhone);
        group.setPointCityId(2L);
        return group;
    }

    /**
     * 测试Excel生成和上传功能
     */
    @Test
    public void testExcelGenerationAndUpload() throws Exception {
        // 模拟查询运力组
        TspTransportGroupPO group = createMockTransportGroup(1L, "测试运力组1", "86", "encrypted_phone_1", null, null);
        Mockito.when(transportGroupRepository.queryTspTransportByIds(Mockito.anyList()))
                .thenReturn(Lists.newArrayList(group));

        // 模拟手机号校验结果为无效
        Mockito.when(mobileHelper.isMobileValid(Mockito.eq("86"), Mockito.eq("encrypted_phone_1"), Mockito.anyLong()))
                .thenReturn(Result.Builder.<Boolean>newResult().fail().withCode("INVALID_FORMAT").withMsg("手机号格式不正确").build());

        // Mock Nephele upload service
        Mockito.when(nepheleHttpService.upload(any(byte[].class), anyString()))
                .thenReturn("https://nephele-example.com/file.xlsx");

        // 设置参数
        String param = JsonUtils.toJson(ImmutableMap.of("transportGroupIds", "1","emailReceivers","<EMAIL>"));
        MockParameter parameter = new MockParameter(param);

        // 执行测试
        schedule.validateTransportGroupPhones(parameter);

        // 验证调用
        Mockito.verify(nepheleHttpService, Mockito.times(1)).upload(any(byte[].class), anyString());
        Mockito.verify(commonCommandService, Mockito.times(1)).sendEmail(Mockito.anyString(), Mockito.anyString(),
                Mockito.argThat(content -> content.contains("https://nephele-example.com/file.xlsx")));
    }
}