package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.sql.Date;
import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class QuerySafetyDriverInfoExecutorTest {

    @InjectMocks
    QuerySafetyDriverInfoExecutor executor;

    @Mock
    private VehicleRepository vehicleRepository;

    @Mock
    private DrvDrvierRepository drvDrvierRepository;

    @Mock
    private TmsCertificateCheckRepository tmsCertificateCheckRepository;

    @Mock
    private TmsTransportQconfig tmsTransportQconfig;

    @Test
    public void execute() {
        QuerySafetyDriverInfoSOARequestType rfequestType = new QuerySafetyDriverInfoSOARequestType();
        rfequestType.setDrvId(1L);
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setDrvHeadImg("11");
        drvDriverPO.setDrvName("2");
        drvDriverPO.setVehicleId(1L);
        drvDriverPO.setCertiDate(new Date(1111));
        VehicleDetailDTO vehicleDetailDTO = new VehicleDetailDTO();
        vehicleDetailDTO.setVehicleId(1L);
        vehicleDetailDTO.setVehicleLicense("11");
        Mockito.when(vehicleRepository.queryVehicleDetail(1L)).thenReturn(vehicleDetailDTO);
        Mockito.when(drvDrvierRepository.queryByPk(1L)).thenReturn(drvDriverPO);
        TmsCertificateCheckPO checkPO = new TmsCertificateCheckPO();
        checkPO.setId(1L);
        Mockito.when(tmsCertificateCheckRepository.queryCertificateCheckByCondition(1L,2,5)).thenReturn(checkPO);
        QuerySafetyDriverInfoSOAResponseType soaResponseType =  executor.execute(rfequestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }
}
