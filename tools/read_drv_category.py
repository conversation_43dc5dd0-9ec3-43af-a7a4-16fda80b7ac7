import pandas as pd
import os
import json

def split_and_write_json(data, max_length=4000, output_file='output.json'):
    # 将数据分割为多个JSON块，每个块不超过max_length字符
    chunks = []
    current_chunk = []

    for item in data:
        temp_chunk = current_chunk + [item]
        temp_json_str = json.dumps(temp_chunk, ensure_ascii=False)

        if len(temp_json_str) <= max_length:
            current_chunk.append(item)
        else:
            chunks.append(current_chunk)
            current_chunk = [item]

    if current_chunk:
        chunks.append(current_chunk)

    # 将每个块写入文件
    with open(output_file, 'w', encoding='utf-8') as f:
        for chunk in chunks:
            json_str = json.dumps(chunk, ensure_ascii=False)
            f.write(json_str + '\n')

# 获取当前工作目录
current_directory = os.getcwd()

# 文件的相对路径
relative_path = 'tools/drv.csv'  # 替换为您的文件名

# 组合成完整的相对路径
file_path = os.path.join(current_directory, relative_path)

# 读取CSV文件
df = pd.read_csv(file_path)

# 选择需要的列，并重命名为所需的键
df = df.rename(columns={'司机id': 'drvId'})

# 添加固定的 'categorySynthesizeCode' 值
df['categorySynthesizeCode'] = 9

# 生成所需的结构
drv_infos = df[['drvId', 'categorySynthesizeCode']].to_dict(orient='records')

# 将数据分割并写入文件
split_and_write_json(drv_infos, max_length=4000, output_file='output.json')
