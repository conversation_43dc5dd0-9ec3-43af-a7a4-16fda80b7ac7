import requests

# 设置请求的URL
url = "http://localhost:8080/querySupplierConfigInfo"

# 设置请求的头部（可选）
headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer YOUR_ACCESS_TOKEN"  # 如果需要认证
}

# 设置请求的主体数据
data = 	{"supplierId":1,"temporaryDispatchMark":0,"productLineRoute":"day"}


# 发起POST请求
response = requests.post(url, headers=headers, json=data)

# 检查请求是否成功
if response.status_code == 200:
    print("请求成功")
    print("响应数据:", response.json())
else:
    print("请求失败，状态码:", response.status_code)
