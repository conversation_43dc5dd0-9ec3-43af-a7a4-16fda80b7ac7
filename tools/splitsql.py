import os
import re

def split_update_statements(input_filename, base_output_filename):
    # 获取当前目录路径
    current_directory = os.getcwd()
    input_file_path = os.path.join(current_directory, input_filename)

    with open(input_file_path, 'r', encoding='utf-8') as file:
        content = file.read()

    # 正则表达式匹配 UPDATE 语句
    update_pattern = r"UPDATE drv_driver SET category_synthesize_code = 9, modify_user = '钱程' WHERE drv_id IN \((.*?)\);"
    matches = re.findall(update_pattern, content, re.DOTALL)

    all_ids = []
    for match in matches:
        ids = [drv_id.strip() for drv_id in match.split(',')]
        all_ids.extend(ids)

    # 将所有ID分成每1000个一组
    batch_size = 10000
    for i in range(0, len(all_ids), batch_size):
        batch_ids = all_ids[i:i + batch_size]
        output_filename = f"{base_output_filename}_{i // batch_size + 1}.txt"
        output_file_path = os.path.join(current_directory, output_filename)

        with open(output_file_path, 'w', encoding='utf-8') as file:
            for drv_id in batch_ids:
                file.write(f"UPDATE drv_driver SET category_synthesize_code = 9, modify_user = '钱程' WHERE drv_id IN ({drv_id});\n")

# 使用示例
input_filename = 'tools/gray-sql'  # 当前目录中的输入文件名
base_output_filename = 'output'  # 输出文件的基础名称
split_update_statements(input_filename, base_output_filename)
