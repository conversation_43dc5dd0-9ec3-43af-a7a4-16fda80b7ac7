package com.ctrip.dcs.tms.transport.infrastructure.port.repository;


import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.dal.*;

import java.sql.*;
import java.util.*;

public interface TmsRecruitingApproveStepRepository {
    TmsRecruitingApproveStepPO queryByPk(Long id);

    int update(TmsRecruitingApproveStepPO stepPO);

    Long insert(TmsRecruitingApproveStepPO stepPO) throws SQLException;

    List<TmsRecruitingApproveStepPO> queryApproveStepList(Long approveSourceId,Integer approveType,Integer approveFrom);

    int updateApproveStatus(Long id,Integer approveStatus,String approveReason,Integer accountType,String modifyUser);

    int updateWaitApproveStatus(Long approveSourceId,Integer approveType,Integer approveFrom,String modifyUser);

    List<TmsRecruitingApproveStepPO> queryApproveStepList(Long approveSourceId,Integer approveType);

    List<TmsRecruitingApproveStepPO> queryNoThroughReason(List<Long> approveSourceId,Integer approveType,Integer approveFrom);

    List<TmsRecruitingApproveStepPO> queryApproveStepByIds(List<Long> ids);

    int updateWaitApproveStatusFromPass(Long approveSourceId,Integer approveType,Integer approveItem, Integer approveFrom,String modifyUser,String approveReason);

    int updateSupplierFinalPassStatus(Long approveSourceId,Integer approveType,Integer approveItem);

    List<TmsRecruitingApproveStepPO> queryApproveStepList(Long approveSourceId,Integer approveType,Integer approveItem, Integer approveFrom);

    List<TmsRecruitingApproveStepPO> queryApproveStepByItems(Long approveSourceId,Integer approveType,List<Integer> approveItems);

    int updateApproveStatusByIds(List<Long> ids,Integer approveStatus,String modifyUser);


}
