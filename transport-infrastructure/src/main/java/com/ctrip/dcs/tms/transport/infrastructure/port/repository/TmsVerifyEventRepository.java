package com.ctrip.dcs.tms.transport.infrastructure.port.repository;


import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.dal.*;

import java.sql.*;
import java.util.*;

public interface TmsVerifyEventRepository {
    TmsVerifyEventPO queryByPk(Long id);

    long insert(TmsVerifyEventPO verifyEventPO) throws SQLException;

    List<TmsVerifyEventPO> queryWaitVerifyEvent(List<Long> verifySourceId, Integer verifyType, Integer verifyStatus, Integer verifyReasonStatus, Boolean verifyTimeFlag);

    int batchUpdate(List<TmsVerifyEventPO> eventPOList);

    List<TmsVerifyEventPO> queryVerifyedEvent(Long verifySourceId, Integer verifyType, Integer verifyReasonStatus);

    int updateVerifyEventCheckTime(Long id, String verifyCheckTime,String modifyUser) throws SQLException;

    int updatemsVerifyEventResult(Long verifySourceId, Integer verifyType, String verifyResult, String failReason, String modifyUser) throws SQLException;

    List<TmsVerifyEventPO> queryVerifyEventByIds(List<Long> ids);

    int updateVerifyEventByIds(List<Long> ids,String verifyResult, String failReason, String modifyUser) throws SQLException;

    List<TmsVerifyEventPO> queryVerifyedEventBySourceId(Long verifySourceId, Integer verifyType, Integer verifyReasonStatus);

    List<TmsVerifyEventPO> queryVerifyedEventBySourceIdOneMonth(Long verifySourceId, Integer verifyType, Integer verifyReasonStatus);
}
