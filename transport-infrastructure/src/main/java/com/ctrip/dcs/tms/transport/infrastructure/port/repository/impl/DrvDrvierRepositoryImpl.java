package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.dcs.tms.transport.api.model.DriverRelationListRequestSOAType;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverDomainServiceProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.CommunicationsDrvInfoPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DriverCacheInfoPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvInfoPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.AreaScopeTypeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CatEventType;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CommonEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.converter.DrvConverter;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DrvCacheConditionQuery;
import com.ctrip.dcs.tms.transport.infrastructure.common.udl.UDLHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.CtripCommonUtils;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.TmsTransUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DriverGroupRelationRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.DrvInfoParam;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.QueryDriverConditionDTO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.QueryDrvByMuSelDO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.QueryDrvDO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.QueryDrvListByAppDO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.QueryDrvResourceConditionDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.DalRepositoryImpl;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.DalRowMapper;
import com.ctrip.platform.dal.dao.KeyHolder;
import com.ctrip.platform.dal.dao.StatementParameters;
import com.ctrip.platform.dal.dao.helper.DalDefaultJpaMapper;
import com.ctrip.platform.dal.dao.sqlbuilder.FreeSelectSqlBuilder;
import com.ctrip.platform.dal.dao.sqlbuilder.FreeUpdateSqlBuilder;
import com.ctrip.platform.dal.dao.sqlbuilder.SelectSqlBuilder;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.dianping.cat.Cat;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Repository(value = "drvDrvierRepository")
public class DrvDrvierRepositoryImpl implements DrvDrvierRepository {

    private static final Logger logger = LoggerFactory.getLogger(DrvDrvierRepositoryImpl.class);

    private DalRepository<DrvDriverPO> drvDriverRepo;

    private DalRepository<DriverCacheInfoPO> driverCacheInfoRepo;

    private DalRepository<CommunicationsDrvInfoPO> communicationsDrvInfoPODalRepo;

    @Autowired
    DriverGroupRelationRepository driverGroupRelationRepository;

    private DalRowMapper<DrvDriverPO> drvDriverPORowMapper;

    private DalRowMapper<DriverCacheInfoPO> driverCacheInfoPODalRowMapper;

    private DalRowMapper<CommunicationsDrvInfoPO> communicationsDrvInfoPODalRowMapper;

    @Autowired
    DriverDomainServiceProxy driverDomainServiceProxy;

    @Autowired
    UDLHandler udlHandler;

    private static final String DEFAULT_TABLENAME = "drv_driver";

    public DrvDrvierRepositoryImpl() throws SQLException {
        drvDriverRepo = new DalRepositoryImpl<>(DrvDriverPO.class);
        driverCacheInfoRepo = new DalRepositoryImpl<>(DriverCacheInfoPO.class);
        communicationsDrvInfoPODalRepo =  new DalRepositoryImpl<>(CommunicationsDrvInfoPO.class);
        this.drvDriverPORowMapper = new DalDefaultJpaMapper<>(DrvDriverPO.class);
        this.driverCacheInfoPODalRowMapper = new DalDefaultJpaMapper<>(DriverCacheInfoPO.class);
        this.communicationsDrvInfoPODalRowMapper = new DalDefaultJpaMapper<>(CommunicationsDrvInfoPO.class);
    }

    @Override
    public int queryDrvApplyStatus(Long drvId,Integer transportGroupMode) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<Integer> builder = new FreeSelectSqlBuilder<>();
            builder.simpleType().requireSingle().nullable();
            StringBuilder sqlStr = new StringBuilder();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            sqlStr.append("select max(temp.apply_status) from (select dr.apply_status from tsp_transport_group_driver_relation dr inner join tsp_transport_group tg on dr.transport_group_id = tg.transport_group_id and tg.transport_group_mode = ? where dr.drv_id = ? and dr.active = 1 group by dr.apply_status) temp");
            parameters.set(i++, "tg.transport_group_mode", Types.BIGINT, transportGroupMode);
            parameters.set(i++, "dr.drv_id", Types.BIGINT, drvId);
            builder.setTemplate(sqlStr.toString());
            Integer applyStatus = drvDriverRepo.getQueryDao().query(builder, parameters, hints);
            return Objects.nonNull(applyStatus) ? applyStatus : TmsTransportConstant.ApplyStatusEnum.NOT.getCode();
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryDrvApplyStatus error", e);
        }
    }

    @Override
    public DalRepository<DrvDriverPO> getDrvDriverRepo() {
        return drvDriverRepo;
    }

    @Override
    public DrvDriverPO queryByPk(Long drvId) {
        return drvDriverRepo.queryByPk(drvId);
    }

    @Override
    public int batchUpdateDrv(List<DrvDriverPO> drvDriverPOList) {
        int[] count = drvDriverRepo.batchUpdate(drvDriverPOList);
        if(count.length > 0){
            return 1;
        }
        return 0;
    }

    @Override
    public Long addDrv(DrvDriverPO po) throws SQLException {
        KeyHolder keyHolder = new KeyHolder();
        driverDomainServiceProxy.generateGlobalId(po);
        po.setProviderDataLocation(udlHandler.getDrvUdlByCityId(po.getCityId()));
        drvDriverRepo.insert(new DalHints(), keyHolder, po);
        return keyHolder.getKey().longValue();
    }

    @Override
    public Integer updateDrv(DrvDriverPO doo) {
        return drvDriverRepo.update(doo);
    }

    public Result<QueryDrvDO> checkNewCondition(QueryDrvDO srcDrvDO) {
        if (srcDrvDO.getStatus() != null || srcDrvDO.getCoopMode() != null ||
            srcDrvDO.getChildSeat() != null || srcDrvDO.getRaisingPickUp() != null ||
            srcDrvDO.getTemporaryDispatchMark()!=null ||
            CollectionUtils.isNotEmpty(srcDrvDO.getDrvIdList()) ||
            CollectionUtils.isNotEmpty(srcDrvDO.getDrvPhoneList()) ||
            CollectionUtils.isNotEmpty(srcDrvDO.getProLineIdList()) ||
            StringUtils.isNotEmpty(srcDrvDO.getDrvName()) ||
            StringUtils.isNotEmpty(srcDrvDO.getIgtCode()) ||
            StringUtils.isNotEmpty(srcDrvDO.getDrvPhone()) ||
            StringUtils.isNotEmpty(srcDrvDO.getDrvIdcard()) ||
            StringUtils.isNotEmpty(srcDrvDO.getDrvLanguage()) ||
            StringUtils.isNotEmpty(srcDrvDO.getVehicleLicense()) ||
            (srcDrvDO.getDrvId() != null && srcDrvDO.getDrvId() > 0) ||
            (srcDrvDO.getCityId() != null && srcDrvDO.getCityId() > 0) ||
            (srcDrvDO.getDrvFrom() != null && srcDrvDO.getDrvFrom() > 0) ||
            (srcDrvDO.getSupplierId() != null && srcDrvDO.getSupplierId() > 0) ||
            (srcDrvDO.getVehicleTypeId() != null && srcDrvDO.getVehicleTypeId() > 0) ||
            (srcDrvDO.getRegistStartDate() != null && srcDrvDO.getRegistEndDate() != null) ||
            (srcDrvDO.getOnlineStartDate() != null && srcDrvDO.getOnlineEndDate() != null)) {
            return Result.Builder.<QueryDrvDO>newResult().fail().withData(srcDrvDO).build();
        }
        QueryDrvDO newCondition = new QueryDrvDO();
        newCondition.setDrvIdList(queryDrvIdByCondition(srcDrvDO));
        newCondition.setActive(srcDrvDO.getActive());
        return Result.Builder.<QueryDrvDO>newResult().success().withData(newCondition).build();
    }

    public List<Long> queryDrvIdByCondition(QueryDrvDO srcDrvDO) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<Long>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("SELECT drv_id FROM drv_driver WHERE active = ? ORDER BY datachange_lasttime DESC");
            StatementParameters parameters = new StatementParameters();
            parameters.set(1, "active", Types.BIT, CtripCommonUtils.queryActiveChoose(srcDrvDO.getActive()));
            builder.atPage(srcDrvDO.getPage(), srcDrvDO.getSize());
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(drvDriverPORowMapper);
            builder.simpleType().nullable();
            return drvDriverRepo.getQueryDao().query(builder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryDrvIdByCondition error", e);
        }
    }

    @Override
    public List<DrvDriverPO> queryDrvList(QueryDrvDO srcDrvDO) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            if (srcDrvDO.getTransportGroupId() == null || srcDrvDO.getTransportGroupId() <= 0) {
                // 如果不含其他条件 Result = True，反之 Result = False
                Result<QueryDrvDO> conditionRes = checkNewCondition(srcDrvDO);
                // 如果不含其他条件，会重新构建出一个新的QueryParams用于避免影响外部查询结果,否则返回原QueryParams
                QueryDrvDO drvDO = conditionRes.getData();
                SelectSqlBuilder builder = new SelectSqlBuilder();
                builder.selectAll();
                if (drvDO.getDrvId() != null && drvDO.getDrvId() > 0) {
                    builder.equalNullable("drv_id", drvDO.getDrvId(), Types.BIGINT);
                }
                builder.and().inNullable("drv_id", drvDO.getDrvIdList(), Types.BIGINT);
                if (drvDO.getSupplierId() != null && drvDO.getSupplierId() > 0) {
                    builder.and().equalNullable("supplier_id", drvDO.getSupplierId(), Types.BIGINT);
                }
                if (drvDO.getCityId() != null && drvDO.getCityId() > 0) {
                    builder.and().equalNullable("city_id", drvDO.getCityId(), Types.BIGINT);
                }
                if (StringUtils.isNotEmpty(drvDO.getDrvName())) {
                    builder.and().equalNullable("drv_name", drvDO.getDrvName(), Types.VARCHAR);
                }
                if (StringUtils.isNotEmpty(drvDO.getVehicleLicense())) {
                    builder.and().likeNullable("vehicle_license",drvDO.getVehicleLicense() + "%", Types.VARCHAR);
                }
                if (drvDO.getVehicleTypeId() != null && drvDO.getVehicleTypeId() > 0) {
                    builder.and().equalNullable("vehicle_type_id", drvDO.getVehicleTypeId(), Types.INTEGER);
                }
                if (drvDO.getStatus() != null) {
                    builder.and().equalNullable("drv_status", drvDO.getStatus(), Types.INTEGER);
                }
                if (drvDO.getDrvFrom() != null && drvDO.getDrvFrom() > 0) {
                    builder.and().equalNullable("drv_from", drvDO.getDrvFrom(), Types.INTEGER);
                }
                if (CollectionUtils.isNotEmpty(drvDO.getDrvPhoneList())) {
                    builder.and().inNullable("drv_phone", drvDO.getDrvPhoneList(), Types.VARCHAR);
                }
                if (StringUtils.isNotEmpty(drvDO.getDrvPhone())) {
                  builder.and().equalNullable("drv_phone", drvDO.getDrvPhone(), Types.VARCHAR);
                }
                if (StringUtils.isNotEmpty(drvDO.getIgtCode())) {
                  builder.and().equalNullable("igt_code", drvDO.getIgtCode(), Types.VARCHAR);
                }
                if (StringUtils.isNotEmpty(drvDO.getDrvIdcard())) {
                    builder.and().equalNullable("drv_idcard", drvDO.getDrvIdcard(), Types.VARCHAR);
                }
                if (StringUtils.isNotEmpty(drvDO.getDrvLanguage())) {
                    builder.and().likeNullable("drv_language", "%" + drvDO.getDrvLanguage() + "%", Types.VARCHAR);
                }
                if (drvDO.getCoopMode() != null) {
                    builder.and().equalNullable("coop_mode", drvDO.getCoopMode(), Types.TINYINT);
                }
                if (drvDO.getRegistStartDate() != null && drvDO.getRegistEndDate() != null) {
                    builder.and().between("datachange_createtime", drvDO.getRegistStartDate(), drvDO.getRegistEndDate(), Types.TIMESTAMP);
                }
                if (drvDO.getOnlineStartDate() != null && drvDO.getOnlineEndDate() != null) {
                    builder.and().between("online_time", drvDO.getOnlineStartDate(), drvDO.getOnlineEndDate(), Types.TIMESTAMP);
                }
                if (CollectionUtils.isNotEmpty(drvDO.getProLineIdList())) {
                    builder.and().in("category_synthesize_code", drvDO.getProLineIdList(), Types.TINYINT);
                }
                if (drvDO.getRaisingPickUp() != null) {
                    builder.and().equal("raising_pick_up", drvDO.getRaisingPickUp(), Types.BIT);
                }
                if (drvDO.getChildSeat() != null) {
                    builder.and().equal("child_seat", drvDO.getChildSeat(), Types.BIT);
                }
                if (drvDO.getTemporaryDispatchMark() != null) {
                    builder.and();
                    builder.equal("temporary_dispatch_mark", drvDO.getTemporaryDispatchMark(), Types.TINYINT);
                }
                builder.and().equal("active", CtripCommonUtils.queryActiveChoose(drvDO.getActive()), Types.BIT);
                builder.orderBy("datachange_lasttime", false);
                // 含条件 - 需要翻页
                if (!conditionRes.isSuccess()) {
                    builder.atPage(drvDO.getPage(), drvDO.getSize());
                    return queryPage(builder, hints);
                }
                return drvDriverRepo.getDao().query(builder, hints);
            } else {
                List<DrvDriverPO> driverPOList = Lists.newArrayList();
                return driverPOList;
            }
        } catch (Exception e) {
            logger.error("DrvDriverDao error:", e);
            return Collections.emptyList();
        }
    }

    /**
     * 先查出司机ID列表再根据司机ID查出司机信息
     * @param builder builder
     * @param hints hints
     * @return List<DrvDriverPO>
     * @throws SQLException SQLException
     */
    protected List<DrvDriverPO> queryPage(SelectSqlBuilder builder, DalHints hints) throws SQLException {
        builder.select("drv_id");
        List<DrvDriverPO> resultList = drvDriverRepo.getDao().query(builder, hints);
        if (CollectionUtils.isEmpty(resultList)) {
            return resultList;
        }
        return queryDrvList(resultList.stream().map(DrvDriverPO::getDrvId).collect(Collectors.toList())).stream().sorted(
          Comparator.comparing(DrvDriverPO::getDatachangeLasttime).reversed()).collect(
          Collectors.toList());
    }

    @Override
    public List<DrvInfoPO> queryDrvInfoList(List<Long> drvIds) throws SQLException {
        try {
            if (drvIds == null || drvIds.isEmpty()) {
                return Collections.emptyList();
            }
            DalHints hints = DalHints.createIfAbsent(null);
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("select d.*,v.vehicle_brand_id,v.vehicle_series from drv_driver d left join veh_vehicle v on d.vehicle_id=v.vehicle_id where d.drv_id in (?) and d.active=1 ");
            parameters.setInParameter(i++, "d.drv_id", Types.BIGINT, drvIds);
            return drvDriverRepo.getQueryDao().query(sqlStr.toString(), parameters, hints, DrvInfoPO.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public DrvInfoPO queryDrvInfo(DrvInfoParam drvInfoParam) throws SQLException {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<DrvInfoPO> sqlBuilder = new FreeSelectSqlBuilder<>();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("select d.*,v.vehicle_brand_id,v.vehicle_series from drv_driver d left join veh_vehicle v on d.vehicle_id=v.vehicle_id where 1=1 and d.active= 1  ");
            if (drvInfoParam.getDrvId() != null) {
                sqlStr.append(" and d.drv_id = ?");
                parameters.set(i++, "d.drv_id", Types.BIGINT, drvInfoParam.getDrvId());
            }
            if (drvInfoParam.getDrvPhone() != null && drvInfoParam.getIgtCode() != null) {
                sqlStr.append(" and d.drv_phone = ?");
                parameters.set(i++, "d.drv_phone", Types.VARCHAR, drvInfoParam.getDrvPhone());
                sqlStr.append(" and d.igt_code = ?");
                parameters.set(i++, "d.igt_code", Types.VARCHAR, drvInfoParam.getIgtCode());
            }
            if (drvInfoParam.getLoginAccount() != null) {
                sqlStr.append(" and d.login_account = ?");
                parameters.set(i++, "d.login_account", Types.VARCHAR, drvInfoParam.getLoginAccount());
            }
            if (drvInfoParam.getQunarAccout() != null) {
                sqlStr.append(" and d.qunar_account = ?");
                parameters.set(i++, "d.qunar_account", Types.VARCHAR, drvInfoParam.getQunarAccout());
            }
            if (!Strings.isNullOrEmpty(drvInfoParam.getEmail())) {
                sqlStr.append(" and d.email = ?");
                parameters.set(i++, "d.email", Types.VARCHAR, drvInfoParam.getEmail());
            }
            sqlBuilder.setTemplate(sqlStr.toString()).requireSingle().nullable();
            DalRowMapper<DrvInfoPO> drvInfoPORowMapper = new DalDefaultJpaMapper<>(DrvInfoPO.class);
            sqlBuilder.mapWith(drvInfoPORowMapper);
            return drvDriverRepo.getQueryDao().query(sqlBuilder, parameters, hints);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Integer countDrvList(QueryDrvDO drvDO) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            int count = 0;
            if (drvDO.getTransportGroupId() == null || drvDO.getTransportGroupId() <= 0) {
                SelectSqlBuilder builder = new SelectSqlBuilder();
                builder.selectCount();
                if (drvDO.getDrvId() != null && drvDO.getDrvId() > 0) {
                    builder.equalNullable("drv_id", drvDO.getDrvId(), Types.BIGINT);
                }
                builder.and().inNullable("drv_id", drvDO.getDrvIdList(), Types.BIGINT);
                if (drvDO.getSupplierId() != null && drvDO.getSupplierId() > 0) {
                    builder.and();
                    builder.equalNullable("supplier_id", drvDO.getSupplierId(), Types.BIGINT);
                }
                if (drvDO.getCityId() != null && drvDO.getCityId() > 0) {
                    builder.and();
                    builder.equalNullable("city_id", drvDO.getCityId(), Types.BIGINT);
                }
                if (StringUtils.isNotEmpty(drvDO.getDrvName())) {
                    builder.and();
                    builder.equalNullable("drv_name",  drvDO.getDrvName(), Types.VARCHAR);
                }
                if (StringUtils.isNotEmpty(drvDO.getVehicleLicense())) {
                    builder.and();
                    builder.likeNullable("vehicle_license", drvDO.getVehicleLicense() + "%", Types.VARCHAR);
                }
                if (drvDO.getVehicleTypeId() != null && drvDO.getVehicleTypeId() > 0) {
                    builder.and();
                    builder.equalNullable("vehicle_type_id", drvDO.getVehicleTypeId(), Types.INTEGER);
                }
                if (drvDO.getStatus() != null) {
                    builder.and();
                    builder.equalNullable("drv_status", drvDO.getStatus(), Types.INTEGER);
                }
                if (drvDO.getDrvFrom() != null && drvDO.getDrvFrom() > 0) {
                    builder.and();
                    builder.equalNullable("drv_from", drvDO.getDrvFrom(), Types.INTEGER);
                }
                if (CollectionUtils.isNotEmpty(drvDO.getDrvPhoneList())) {
                    builder.and();
                    builder.inNullable("drv_phone", drvDO.getDrvPhoneList(), Types.VARCHAR);
                }
                if (StringUtils.isNotEmpty(drvDO.getDrvPhone())) {
                    builder.and();
                    builder.equalNullable("drv_phone", drvDO.getDrvPhone(), Types.VARCHAR);
                }
                if (StringUtils.isNotEmpty(drvDO.getIgtCode())) {
                    builder.and();
                    builder.equalNullable("igt_code", drvDO.getIgtCode(), Types.VARCHAR);
                }
                if (StringUtils.isNotEmpty(drvDO.getDrvIdcard())) {
                    builder.and();
                    builder.equalNullable("drv_idcard", drvDO.getDrvIdcard(), Types.VARCHAR);
                }
                if (StringUtils.isNotEmpty(drvDO.getDrvLanguage())) {
                    builder.and();
                    builder.likeNullable("drv_language", "%" + drvDO.getDrvLanguage() + "%", Types.VARCHAR);
                }
                if (drvDO.getCoopMode() != null) {
                    builder.and();
                    builder.equalNullable("coop_mode", drvDO.getCoopMode(), Types.TINYINT);
                }
                if (drvDO.getRegistStartDate() != null && drvDO.getRegistEndDate() != null) {
                    builder.and();
                    builder.between("datachange_createtime", drvDO.getRegistStartDate(), drvDO.getRegistEndDate(), Types.TIMESTAMP);
                }
                if (drvDO.getOnlineStartDate() != null && drvDO.getOnlineEndDate() != null) {
                    builder.and();
                    builder.between("online_time", drvDO.getOnlineStartDate(), drvDO.getOnlineEndDate(), Types.TIMESTAMP);
                }
                if (CollectionUtils.isNotEmpty(drvDO.getProLineIdList())) {
                    builder.and();
                    builder.in("category_synthesize_code", drvDO.getProLineIdList(), Types.TINYINT);
                }
                if (drvDO.getRaisingPickUp() != null) {
                    builder.and();
                    builder.equal("raising_pick_up", drvDO.getRaisingPickUp(), Types.BIT);
                }
                if (drvDO.getChildSeat() != null) {
                    builder.and();
                    builder.equal("child_seat", drvDO.getChildSeat(), Types.BIT);
                }
                if (drvDO.getTemporaryDispatchMark() != null) {
                    builder.and();
                    builder.equal("temporary_dispatch_mark", drvDO.getTemporaryDispatchMark(), Types.TINYINT);
                }
                builder.and().equal("active", CtripCommonUtils.queryActiveChoose(drvDO.getActive()), Types.BIT);
                count = drvDriverRepo.getDao().count(builder, hints).intValue();
            }
            return count;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 批量变更司机状态（上线/下线）
     *
     * @param drvIds
     * @param status
     * @param modifyUser
     * @return
     */
    @Override
    public int updateDrvStatus(List<Long> drvIds, Integer status, String modifyUser) {
        return updateDrvStatus(drvIds,null,status,modifyUser);
    }

    @Override
    public int unbindCarforDrv(List<Long> drvIds, String modifyUser) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeUpdateSqlBuilder sqlBuilder = new FreeUpdateSqlBuilder();
            sqlBuilder.setTemplate("UPDATE drv_driver d left join veh_vehicle v on d.vehicle_id = v.vehicle_id set d.vehicle_id = ?,d.vehicle_license= ?,d.vehicle_type_id= ? ,v.has_drv= ?,d.modify_user = ?,v.modify_user = ?,d.veh_bind_time = ? where d.drv_id in (?)");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.set(i++, "d.vehicle_id", Types.BIGINT, 0);
            parameters.set(i++, "d.vehicle_license", Types.VARCHAR, "");
            parameters.set(i++, "d.vehicle_type_id", Types.BIGINT, 0);
            parameters.set(i++, "v.has_drv", Types.BIT, 0);
            parameters.set(i++, "d.modify_user", Types.VARCHAR, modifyUser);
            parameters.set(i++, "v.modify_user", Types.VARCHAR, modifyUser);
            parameters.set(i++, "d.veh_bind_time", Types.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
            parameters.setInParameter(i++, "d.drv_id", Types.BIGINT, drvIds);
            return drvDriverRepo.getQueryDao().update(sqlBuilder, parameters, hints);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<DrvDriverPO> queryDrvList(List<Long> drvIds) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<DrvDriverPO>> sqlBuilder = new FreeSelectSqlBuilder<>();
            sqlBuilder.mapWith(this.drvDriverPORowMapper)
                    .selectAll().from(DEFAULT_TABLENAME)
                    .where()
                    .in("drv_id", drvIds, Types.BIGINT).ignoreNull();
            return drvDriverRepo.getQueryDao().query(sqlBuilder, hints);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 批量冻结司机
     *
     * @param drvIds
     * @param status
     * @param freezeHour
     * @param freezeReason
     * @param modifyUser
     * @return
     */
    @Override
    public int freezeDrv(List<Long> drvIds, Integer status, Integer freezeHour, String freezeReason, String modifyUser) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeUpdateSqlBuilder sqlBuilder = new FreeUpdateSqlBuilder();
            sqlBuilder.update(DEFAULT_TABLENAME)
                    .set("drv_status", "freeze_hour", "freeze_reason", "modify_user", "datachange_lasttime")
                    .set("drv_status", status, Types.TINYINT)
                    .set("freeze_hour",freezeHour,Types.INTEGER)
                    .set("freeze_reason",freezeReason,Types.VARCHAR)
                    .set("modify_user", modifyUser, Types.VARCHAR)
                    .set("datachange_lasttime", new Timestamp(System.currentTimeMillis()), Types.TIMESTAMP)
                    .where()
                    .in("drv_id", drvIds, Types.BIGINT);

            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.set(i++, "drv_status", Types.TINYINT, status);
            parameters.set(i++, "freeze_hour", Types.INTEGER, freezeHour);
            parameters.set(i++, "freeze_reason", Types.VARCHAR, freezeReason);
            parameters.set(i++, "modify_user", Types.VARCHAR, modifyUser);
            parameters.set(i++, "datachange_lasttime", Types.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
            parameters.setInParameter(i++, "drv_id", Types.BIGINT, drvIds);

            return drvDriverRepo.getQueryDao().update(sqlBuilder, hints);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<DrvDriverPO> queryBundleStatusDrvList(List<Long> drvIds, Long supplierId, DriverRelationListRequestSOAType requestSOAType) {
        try {
            if (CollectionUtils.isEmpty(drvIds)) {
                drvIds = null;
            }
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            if (requestSOAType.getIsHasRelation().intValue() == 0) {
                builder.in("drv_id", drvIds, Types.BIGINT);
            } else {
                builder.notInNullable("drv_id", drvIds, Types.BIGINT);
                builder.and();
                builder.equalNullable("supplier_id", supplierId, Types.BIGINT);
            }
            builder.and();
            builder.inNullable("city_id", requestSOAType.getCityId(), Types.BIGINT);
            if (CollectionUtils.isNotEmpty(requestSOAType.getDrvLanguage())) {
                for (int i = 0; i < requestSOAType.getDrvLanguage().size(); i++) {
                    builder.and();
                    builder.likeNullable("drv_language", toLikeStr(requestSOAType.getDrvLanguage().get(i)), Types.VARCHAR);
                }
            }
            builder.and();
            builder.inNullable("vehicle_type_id", requestSOAType.getVehicleTypeId(), Types.BIGINT);
            builder.and();
            builder.likeNullable("drv_name", toLikeStr(requestSOAType.getDrvName()), Types.VARCHAR);
            builder.and();
            builder.likeNullable("drv_phone", toLikeStr(requestSOAType.getDrvPhone()), Types.VARCHAR);
            builder.and();
            builder.equalNullable("coop_mode", requestSOAType.getCoopMode(), Types.INTEGER);
            builder.and();
            builder.equalNullable("drv_status", requestSOAType.getDrvStatus(), Types.INTEGER);
            builder.and();
            builder.equalNullable("active", Boolean.TRUE, Types.BIT);
            int page = 1;
            int size = 20;
            if (requestSOAType.getPaginator() != null) {
                page = requestSOAType.getPaginator().getPageNo();
                size = requestSOAType.getPaginator().getPageSize();
            }
            builder.orderBy("datachange_lasttime", false);
            builder.atPage(page, size);
            return drvDriverRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryBundleStatusDrvList error", e);
        }
    }

    private String toLikeStr(String likeStr) {
        if (Strings.isNullOrEmpty(likeStr)) {
            return null;
        }
        return likeStr + "%";
    }

    @Override
    public int queryBundleStatusDrvCount(List<Long> drvIds, Long supplierId, DriverRelationListRequestSOAType requestSOAType) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            if (CollectionUtils.isEmpty(drvIds)) {
                drvIds = null;
            }
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            if (requestSOAType.getIsHasRelation().intValue() == 0) {
                builder.in("drv_id", drvIds, Types.BIGINT);
            } else {
                builder.notInNullable("drv_id", drvIds, Types.BIGINT);
                builder.and();
                builder.equalNullable("supplier_id", supplierId, Types.BIGINT);
            }
            builder.and();
            builder.inNullable("city_id", requestSOAType.getCityId(), Types.BIGINT);
            if (CollectionUtils.isNotEmpty(requestSOAType.getDrvLanguage())) {
                for (int i = 0; i < requestSOAType.getDrvLanguage().size(); i++) {
                    builder.and();
                    builder.likeNullable("drv_language", toLikeStr(requestSOAType.getDrvLanguage().get(i)), Types.VARCHAR);
                }
            }
            builder.and();
            builder.inNullable("vehicle_type_id", requestSOAType.getVehicleTypeId(), Types.BIGINT);
            builder.and();
            builder.likeNullable("drv_name", toLikeStr(requestSOAType.getDrvName()), Types.VARCHAR);
            builder.and();
            builder.likeNullable("drv_phone", toLikeStr(requestSOAType.getDrvPhone()), Types.VARCHAR);
            builder.and();
            builder.equalNullable("coop_mode", requestSOAType.getCoopMode(), Types.INTEGER);
            builder.and();
            builder.equalNullable("drv_status", requestSOAType.getDrvStatus(), Types.INTEGER);
            builder.and();
            builder.equalNullable("active", Boolean.TRUE, Types.BIT);
            return drvDriverRepo.getDao().count(builder, hints).intValue();
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryBundleStatusDrvCount error", e);
        }
    }

    @Override
    public Boolean checkDrvOnly(String str, Integer type) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectCount();
        if (Objects.equals(type, TmsTransportConstant.DrvOnlyTypeEnum.PHONE.getCode())) {
            builder.and();
            builder.equal("drv_phone", str, Types.VARCHAR, false);
        }
        if (Objects.equals(type, TmsTransportConstant.DrvOnlyTypeEnum.IDCARD.getCode())) {
            builder.and();
            builder.equal("drv_idcard", str, Types.VARCHAR, false);
        }
        if (Objects.equals(type, TmsTransportConstant.DrvOnlyTypeEnum.LOGIN_ACCOUNT.getCode())) {
            builder.and();
            builder.equal("login_account", str, Types.VARCHAR, false);
        }
        if (Objects.equals(type, TmsTransportConstant.DrvOnlyTypeEnum.EMIAL.getCode())) {
            builder.and();
            builder.equal("email", str, Types.VARCHAR, false);
        }
        if (Objects.equals(type, TmsTransportConstant.DrvOnlyTypeEnum.PAIAY_ACCOUNT.getCode())) {
            builder.and();
            builder.equal("paiay_account", str, Types.VARCHAR, false);
        }
        if (Objects.equals(type, TmsTransportConstant.DrvOnlyTypeEnum.PAIAY_EMAIL.getCode())) {
            builder.and();
            builder.equal("paiay_email", str, Types.VARCHAR, false);
        }
        builder.and();
        builder.equalNullable("active", Boolean.TRUE, Types.BIT);
        if (drvDriverRepo.getDao().count(builder, hints).intValue() > 0) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public int updateDrvAddr(Long drvId, String drvAddr,String addrModCount,String modifyUser) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        builder.setTemplate("update drv_driver set drv_addr = ?,addr_mod_count = ?,modify_user = ? where drv_id = ?");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.setSensitive(i++, "drv_addr", Types.VARCHAR, drvAddr);
        parameters.setSensitive(i++, "addr_mod_count", Types.VARCHAR, addrModCount);
        parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
        parameters.setSensitive(i++, "drv_id", Types.BIGINT, drvId);

        return drvDriverRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public int updateDrvIntendVehicleType(Long drvId, String intendVehicleTypeId, String modifyUser) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        builder.setTemplate("update drv_driver set intend_vehicle_type_id = ?,modify_user= ? where drv_id = ?");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        if (Strings.isNullOrEmpty(intendVehicleTypeId)) {
            parameters.setSensitive(i++, "intend_vehicle_type_id", Types.VARCHAR, "");
        }else {
            parameters.setSensitive(i++, "intend_vehicle_type_id", Types.VARCHAR, intendVehicleTypeId);
        }
        parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
        parameters.setSensitive(i++, "drv_id", Types.BIGINT, drvId);

        return drvDriverRepo.getQueryDao().update(builder, parameters, hints);
    }


    @Override
    public int updateDrvVehicleType(Long vehicleId, Long vehicleTypeId) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        builder.setTemplate("update drv_driver set vehicle_type_id = ? where vehicle_id = ?");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.setSensitive(i++, "vehicle_type_id", Types.BIGINT, vehicleTypeId);
        parameters.setSensitive(i++, "vehicle_id", Types.BIGINT, vehicleId);

        return drvDriverRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public List<DrvDriverPO> queryDrvListByApp(QueryDrvListByAppDO appDO) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        if (appDO.getDrvId() != null && appDO.getDrvId() > 0) {
            builder.equalNullable("drv_id", appDO.getDrvId(), Types.BIGINT);
        }
        if (appDO.getDrvIdFrom() != null && appDO.getDrvIdFrom() > 0) {
            builder.and();
            builder.greaterThanEqualsNullable("drv_id", appDO.getDrvIdFrom(), Types.BIGINT);
        }

        if (CollectionUtils.isNotEmpty(appDO.getDrvStatus())) {
            builder.and();
            builder.inNullable("drv_status", appDO.getDrvStatus(), Types.INTEGER, false);
        }
        builder.and();
        builder.equalNullable("active", Boolean.TRUE, Types.BIT);
        int limit = 3000;
        if (appDO.getLimit() != null && appDO.getLimit() > 0) {
            limit = appDO.getLimit();
        }
        builder.atPage(1, limit);
        return drvDriverRepo.getDao().query(builder, hints);
    }

    @Override
    public List<DrvDriverPO> queryDrvListByTgId(Long transportGroupId) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);

            FreeSelectSqlBuilder<List<DrvDriverPO>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("select dd.* from drv_driver dd inner join tsp_transport_group_driver_relation tgdr on tgdr.transport_group_id = ? and tgdr.active = ? and tgdr.drv_id = dd.drv_id and dd.active = 1 ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.set(i++, "tgdr.transport_group_id", Types.BIGINT, transportGroupId);
            parameters.set(i++, "tgdr.active", Types.BIT, 1);
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(drvDriverPORowMapper);
            return drvDriverRepo.getQueryDao().query(builder, parameters, hints);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Long queryDriverByVehicleId(Long vehicleId) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<DrvDriverPO>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("select * from drv_driver where vehicle_id = ? and active = 1 limit 1");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.set(i++, "vehicle_id", Types.BIGINT, vehicleId);
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(drvDriverPORowMapper);
            List<DrvDriverPO> driverPOList = drvDriverRepo.getQueryDao().query(builder, parameters, hints);
            if (CollectionUtils.isEmpty(driverPOList)) {
                return null;
            }
            return driverPOList.get(0).getDrvId();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<DrvDriverPO> queryDrvBySupplierIdList(Long supplierId) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.select("drv_id", "drv_name");
        builder.equal("supplier_id", supplierId, Types.BIGINT);
        builder.and().equal("active",Boolean.TRUE,Types.BIT);
        return drvDriverRepo.getDao().query(builder, hints);
    }

    @Override
    public List<DrvDriverPO> queryDrvByLoginAccount(String loginAccount) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("login_account", loginAccount, Types.VARCHAR);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.orderBy("datachange_lasttime", false);
            return drvDriverRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            logger.error("select driver account error", e);
        }
        return Collections.emptyList();
    }

    @Override
    public int updateDrvAccount(Long drvId, String qunarAccout, String ppmAccout, String ctripAccount) {
        if (Strings.isNullOrEmpty(qunarAccout) && Strings.isNullOrEmpty(ppmAccout) && Strings.isNullOrEmpty(ctripAccount)) {
            return 0;
        }
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            StringBuilder sqlStr = new StringBuilder("UPDATE drv_driver SET datachange_lasttime = ?");
            parameters.set(i++, "datachange_lasttime", Types.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
            if (!Strings.isNullOrEmpty(ppmAccout)) {
                sqlStr.append(",ppm_account = ?");
                parameters.set(i++, "ppm_account", Types.VARCHAR, ppmAccout);
            }
            if (!Strings.isNullOrEmpty(qunarAccout)) {
                sqlStr.append(",qunar_account = ?");
                parameters.set(i++, "qunar_account", Types.VARCHAR, qunarAccout);
            }
            if (!Strings.isNullOrEmpty(ctripAccount)) {
                sqlStr.append(",im_account = ?");
                parameters.set(i++, "im_account", Types.VARCHAR, ctripAccount);
            }
            sqlStr.append(" WHERE drv_id = ? and active = 1 ");
            parameters.set(i++, "drv_id", Types.BIGINT, drvId);
            builder.setTemplate(sqlStr.toString());
            return drvDriverRepo.getQueryDao().update(builder, parameters, hints);
        } catch (SQLException e) {
            logger.error("update driver account error drvID:{} qunarAccout:{} ppmAccout:{} ctripAccount:{}", drvId, qunarAccout, ppmAccout, ctripAccount, e);
        }
        return 0;
    }

    @Override
    public int updateDrvOnlineTime(List<Long> drvIds) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        builder.setTemplate("update drv_driver set online_time = ? where drv_id in (?) ");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.setSensitive(i++, "online_time", Types.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        parameters.setInParameter(i++, "drv_id", Types.BIGINT, drvIds);

        return drvDriverRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public List<DrvDriverPO> queryDrvByMuSelConditions(QueryDrvByMuSelDO muSelDO) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            if(CollectionUtils.isEmpty(muSelDO.getTransportGroupIdList())){
                SelectSqlBuilder builder = new SelectSqlBuilder();
                builder.selectAll();
                if (CollectionUtils.isNotEmpty(muSelDO.getCityIdList())) {
                    builder.and();
                    builder.in("city_id", muSelDO.getCityIdList(), Types.BIGINT);
                }
                if (CollectionUtils.isNotEmpty(muSelDO.getCountryIdList())) {
                    builder.and();
                    builder.in("country_id", muSelDO.getCountryIdList(), Types.BIGINT);
                }
                if (CollectionUtils.isNotEmpty(muSelDO.getSupplierIdList())) {
                    builder.and();
                    builder.in("supplier_id", muSelDO.getSupplierIdList(), Types.BIGINT);
                }
                if (CollectionUtils.isNotEmpty(muSelDO.getVehicleTypeIdList())) {
                    builder.and();
                    builder.in("vehicle_type_id", muSelDO.getVehicleTypeIdList(), Types.BIGINT);
                }
                if (CollectionUtils.isNotEmpty(muSelDO.getCoopModeList())) {
                    builder.and();
                    builder.in("coop_mode", muSelDO.getCoopModeList(), Types.BIGINT);
                }
                if (CollectionUtils.isNotEmpty(muSelDO.getDrvStatusList())) {
                    builder.and();
                    builder.in("drv_status", muSelDO.getDrvStatusList(), Types.INTEGER);
                }
                if(muSelDO.getRegistStartDate()!=null && muSelDO.getRegistEndDate()!=null){
                    builder.and();
                    builder.between("datachange_createtime", muSelDO.getRegistStartDate(),muSelDO.getRegistEndDate(), Types.TIMESTAMP);
                }
                if(CollectionUtils.isNotEmpty(muSelDO.getProLineIdList())){
                    builder.and();
                    builder.in("category_synthesize_code", muSelDO.getProLineIdList(), Types.INTEGER);
                }
                builder.and().equal("active",Boolean.TRUE,Types.BIT);
                builder.orderBy("datachange_lasttime", false);
                builder.atPage(muSelDO.getPage(),muSelDO.getPageSize());
                return drvDriverRepo.getDao().query(builder, hints);
            }else {
                List<DrvDriverPO> driverPOList = Lists.newArrayList();
                return driverPOList;
            }
        } catch (Exception e) {
            logger.error("queryDrvByMuSelConditions error", e);
        }
        return Collections.emptyList();
    }

    @Override
    public int countDrvByMuSelConditions(QueryDrvByMuSelDO muSelDO) {
        DalHints hints = DalHints.createIfAbsent(null);
        int count = 0;
        try {
            if(CollectionUtils.isEmpty(muSelDO.getTransportGroupIdList())){
                SelectSqlBuilder builder = new SelectSqlBuilder();
                builder.selectCount();
                if (CollectionUtils.isNotEmpty(muSelDO.getCityIdList())) {
                    builder.and();
                    builder.in("city_id", muSelDO.getCityIdList(), Types.BIGINT);
                }
                if (CollectionUtils.isNotEmpty(muSelDO.getCountryIdList())) {
                    builder.and();
                    builder.in("country_id", muSelDO.getCountryIdList(), Types.BIGINT);
                }
                if (CollectionUtils.isNotEmpty(muSelDO.getSupplierIdList())) {
                    builder.and();
                    builder.in("supplier_id", muSelDO.getSupplierIdList(), Types.BIGINT);
                }
                if (CollectionUtils.isNotEmpty(muSelDO.getVehicleTypeIdList())) {
                    builder.and();
                    builder.in("vehicle_type_id", muSelDO.getVehicleTypeIdList(), Types.BIGINT);
                }
                if (CollectionUtils.isNotEmpty(muSelDO.getCoopModeList())) {
                    builder.and();
                    builder.in("coop_mode", muSelDO.getCoopModeList(), Types.BIGINT);
                }
                if (CollectionUtils.isNotEmpty(muSelDO.getDrvStatusList())) {
                    builder.and();
                    builder.in("drv_status", muSelDO.getDrvStatusList(), Types.INTEGER);
                }
                if(muSelDO.getRegistStartDate()!=null && muSelDO.getRegistEndDate()!=null){
                    builder.and();
                    builder.between("datachange_createtime", muSelDO.getRegistStartDate(),muSelDO.getRegistEndDate(), Types.TIMESTAMP);
                }
                if(CollectionUtils.isNotEmpty(muSelDO.getProLineIdList())){
                    builder.and();
                    builder.in("category_synthesize_code", muSelDO.getProLineIdList(), Types.INTEGER);
                }
                builder.and().equal("active",Boolean.TRUE,Types.BIT);
                count =  drvDriverRepo.getDao().count(builder, hints).intValue();
            }
            return count;
        } catch (Exception e) {
            logger.error("queryDrvByMuSelConditions error", e);
        }
        return 0;
    }

    @Override
    public int countDrvByOnlineAndfreeze(List<Integer> drvStatus,List<Long> drvList) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            if(CollectionUtils.isNotEmpty(drvList)){
                builder.in("drv_id",drvList,Types.BIGINT);
            }
            builder.and().in("drv_status",drvStatus,Types.INTEGER);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return drvDriverRepo.getDao().count(builder, hints).intValue();
        }catch (Exception e){
            logger.error("countDrvByOnlineAndfreeze error", e);
        }
        return 0;
    }

    @Override
    public List<DrvDriverPO> queryDrvByOnlineAndfreeze(List<Long> paramsDrvList,List<Integer> drvStatus, Integer page, Integer pageSize) {

        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            if(CollectionUtils.isNotEmpty(paramsDrvList)){
                builder.in("drv_id",paramsDrvList,Types.BIGINT);
            }
            builder.and().in("drv_status", drvStatus, Types.INTEGER);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.atPage(page, pageSize);
            return drvDriverRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            logger.error("queryDrvByMuSelConditions error", e);
        }
        return null;
    }

    @Override
    public int countDrvDirtyPhone() {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            builder.like("drv_phone", "0%", Types.VARCHAR);
            return drvDriverRepo.getDao().count(builder, hints).intValue();
        }catch (Exception e){
            logger.error("countDrvDirtyPhone error", e);
        }
        return 0;
    }

    @Override
    public List<DrvDriverPO> queryDrvDirtyPhone(int pageNo, int pageSize) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.like("drv_phone", "0%", Types.VARCHAR);
            builder.atPage(pageNo, pageSize);
            return drvDriverRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            logger.error("queryDrvDirtyPhone error", e);
        }
        return Collections.emptyList();
    }

    @Override
    public DrvDriverPO queryOneDrvByPhone(String phone) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<DrvDriverPO> sqlBuilder = new FreeSelectSqlBuilder<>();
            sqlBuilder.requireFirst().nullable();
            sqlBuilder.setTemplate("select * from drv_driver").where("1=1");
            sqlBuilder.and().equal("drv_phone", phone, Types.VARCHAR);
            sqlBuilder.and().equal("active",Boolean.TRUE, Types.BIT);
            sqlBuilder.mapWith(drvDriverPORowMapper);
            return drvDriverRepo.getQueryDao().query(sqlBuilder, hints);
        } catch (Exception e) {
            logger.error("queryOneDrvByPhone error", e);
        }
        return null;
    }


    @Override
    public List<DrvDriverPO> queryDrvByVehicleIds(List<Long> vehicleIds) {
        if(CollectionUtils.isEmpty(vehicleIds)){
            return Collections.emptyList();
        }
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.select("drv_id","vehicle_id");
            builder.in("vehicle_id",vehicleIds,Types.BIGINT);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return drvDriverRepo.getDao().query(builder,hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<DrvDriverPO> queryByHybridAccount(String hybridAccount) {
        DalHints hints = DalHints.createIfAbsent(null);

        try {
            FreeSelectSqlBuilder<List<DrvDriverPO>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder("select * from drv_driver where (login_account = ? or drv_phone=? or email = ?) and  active = 1");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.set(i++, "login_account", Types.VARCHAR, hybridAccount);
            parameters.set(i++, "drv_phone", Types.VARCHAR, TmsTransUtil.encrypt(hybridAccount, KeyType.Phone));
            parameters.set(i++, "email", Types.VARCHAR, TmsTransUtil.encrypt(hybridAccount, KeyType.Mail));
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(drvDriverPORowMapper);
            return driverCacheInfoRepo.getQueryDao().query(builder, parameters, hints);
        } catch (Exception e) {
            throw new RuntimeException();
        }
    }

    @Override
    public List<DrvDriverPO> queryByNewHybridAccount(String hybridAccount) {
        DalHints hints = DalHints.createIfAbsent(null);

        try {
            FreeSelectSqlBuilder<List<DrvDriverPO>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("SELECT * FROM drv_driver WHERE drv_phone = ? ");
            sqlStr.append("UNION ALL ");
            sqlStr.append("SELECT * FROM drv_driver WHERE email = ? ");
            sqlStr.append("UNION ALL ");
            sqlStr.append("SELECT * FROM drv_driver WHERE login_account = ?");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.set(i++, "drv_phone", Types.VARCHAR, TmsTransUtil.encrypt(hybridAccount, KeyType.Phone));
            parameters.set(i++, "email", Types.VARCHAR, TmsTransUtil.encrypt(hybridAccount, KeyType.Mail));
            parameters.set(i++, "login_account", Types.VARCHAR, hybridAccount);
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(drvDriverPORowMapper);
            List<DrvDriverPO> driverPOList = driverCacheInfoRepo.getQueryDao().query(builder, parameters, hints);
            if (CollectionUtils.isEmpty(driverPOList)) {
                return driverPOList;
            }
            return driverPOList.stream().filter(DrvDriverPO -> DrvDriverPO != null && DrvDriverPO.getActive() != null && DrvDriverPO.getActive()).collect(Collectors.toList());
        } catch (Exception e) {
            throw new BizException();
        }
    }

    @Override
    public List<DrvDriverPO> queryDrvBySupplierIdAndId(List<Long> drvId, Long supplierId) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("drv_id", drvId, Types.BIGINT);
            builder.and().equal("supplier_id", supplierId, Types.BIGINT);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return drvDriverRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            throw new RuntimeException();
        }
    }

    @Override
    public List<Long> queryDrvIdByCondition(String name, String phone) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<Long>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("SELECT drv_id FROM drv_driver WHERE 1 = 1 ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            if (!Strings.isNullOrEmpty(name)) {
                sqlStr.append("AND drv_name = ? ");
                parameters.set(i++, "drv_name", Types.VARCHAR,name);
            }
            if (!Strings.isNullOrEmpty(phone)) {
                sqlStr.append("AND drv_phone = ? ");
                parameters.set(i++, "drv_phone", Types.VARCHAR,phone);
            }
            sqlStr.append(" AND active = 1 ");
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(drvDriverPORowMapper);
            builder.simpleType().nullable();
            return drvDriverRepo.getQueryDao().query(builder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryDrvIdByCondition error", e);
        }
    }

    @Override
    public List<Long> queryDrvId4Cache(int pageNo, int size) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<Long>> builder = new FreeSelectSqlBuilder<>();
            builder.setTemplate("SELECT drv_id FROM drv_driver where active = 1 ");
            builder.mapWith(drvDriverPORowMapper);
            builder.atPage(pageNo,size);
            builder.simpleType().nullable();
            return drvDriverRepo.getQueryDao().query(builder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryDrvId4WarmUp error", e);
        }
    }

    @Override
    public int updateDrvWorkPeriod(List<Long> drvIds, String workPeriod, String modifyUser){
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            builder.setTemplate("update drv_driver set work_period = ? ,modify_user = ?  where drv_id in (?)");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "work_period", Types.VARCHAR, workPeriod);
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
            parameters.setInParameter(i++, "drv_id", Types.BIGINT, drvIds);
            return drvDriverRepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException("updateDrvWorkPeriod error", e);
        }
    }

    @Override
    public List<Long> queryPenaltyOfflineDrvIdList(List<Long> drvIds) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<Long>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("SELECT drv_id FROM drv_driver WHERE 1 = 1 ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            sqlStr.append("AND drv_id IN (?) ");
            parameters.setInParameter(i++, "drv_id", Types.BIGINT, drvIds);
            sqlStr.append("AND op_from = ? ");
            parameters.set(i++, "op_from", Types.TINYINT, CommonEnum.FreezeOPFromEnum.PUNISH.getValue());
            sqlStr.append(" AND active = 1 ");
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(drvDriverPORowMapper);
            builder.simpleType().nullable();
            return drvDriverRepo.getQueryDao().query(builder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryPenaltyOfflineDrvIdList error", e);
        }
    }

    @Override
    public List<String> queryDrvNameByIdList(List<Long> drvIds) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<String>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("SELECT drv_name FROM drv_driver WHERE 1 = 1 ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            sqlStr.append("AND drv_id IN (?) ");
            parameters.setInParameter(i++, "drv_id", Types.BIGINT, drvIds);
            sqlStr.append(" AND active = 1 ");
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(drvDriverPORowMapper);
            builder.simpleType().nullable();
            return drvDriverRepo.getQueryDao().query(builder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryDrvNameByIdList error", e);
        }
    }


    @Override
    public int updateDrvStatus(List<Long> drvIds, Integer opFrom, Integer status, String modifyUser) {
        if (CollectionUtils.isEmpty(drvIds)) {
            return 0;
        }
        boolean setOpFrom = opFrom != null;
        String[] params;
        if (setOpFrom) {
            params = new String[]{"drv_status", "modify_user", "op_from", "datachange_lasttime"};
        } else {
            params = new String[]{"drv_status", "modify_user", "datachange_lasttime"};
        }
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeUpdateSqlBuilder sqlBuilder = new FreeUpdateSqlBuilder();
            sqlBuilder.update(DEFAULT_TABLENAME)
                    .set(params)
                    .where()
                    .in("drv_id").and().equal("active");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.set(i++, "drv_status", Types.TINYINT, status);
            parameters.set(i++, "modify_user", Types.VARCHAR, modifyUser);
            if (setOpFrom) {
                parameters.set(i++, "op_from", Types.TINYINT, opFrom);
            }
            parameters.set(i++, "datachange_lasttime", Types.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
            parameters.setInParameter(i++, "drv_id", Types.BIGINT, drvIds);
            parameters.set(i++, "active", Types.BIT,Boolean.TRUE);
            return drvDriverRepo.getQueryDao().update(sqlBuilder, parameters, hints);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<CommunicationsDrvInfoPO> queryDriverInfo4TrafficAgency(Long cityId, List<Long> drvIds, Integer status, int pageNo, int size) {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeSelectSqlBuilder<List<CommunicationsDrvInfoPO>> builder = new FreeSelectSqlBuilder<>();
        StringBuilder sqlStr = new StringBuilder("SELECT drv.*,veh.vehicle_license,veh.regst_date, veh.vehicle_color_id, veh.vehicle_brand_id,veh.vehicle_series, veh.vin FROM drv_driver drv JOIN veh_vehicle veh ON drv.vehicle_id = veh.vehicle_id WHERE ");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        sqlStr.append(" drv.city_id = ? ");
        parameters.set(i++, "drv.city_id", Types.BIGINT, cityId);
        sqlStr.append(" AND drv.drv_status = ?");
        parameters.set(i++, "drv.drv_status", Types.INTEGER, status);
        if (CollectionUtils.isNotEmpty(drvIds)) {
            sqlStr.append(" AND drv.drv_id in (?) ");
            parameters.setInParameter(i++, "drv.drv_id", Types.BIGINT, drvIds);
        }
        sqlStr.append(" AND drv.active = 1 AND veh.active = 1  ");
        builder.setTemplate(sqlStr.toString());
        List<CommunicationsDrvInfoPO> driverPOList;
        try {
            builder.atPage(pageNo, size);
            builder.mapWith(communicationsDrvInfoPODalRowMapper);
            driverPOList = communicationsDrvInfoPODalRepo.getQueryDao().query(builder, parameters, hints);
        } catch (SQLException e) {
            logger.error("queryDriverInfo4TrafficAgency Error", "cityId:{} drvId:{} status:{} pageNo:{} size:{}", cityId, drvIds, status, pageNo, size);
            return Lists.newArrayList();
        }
        return driverPOList;
    }

    @Override
    public int updateDrvApproveStatus(List<Long> drvIds, Integer approveStatus, String modifyUser) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        builder.setTemplate("update drv_driver set approve_status = ?,modify_user = ? where drv_id in (?) ");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.setSensitive(i++, "approve_status", Types.INTEGER,approveStatus);
        parameters.setSensitive(i++, "modify_user", Types.VARCHAR,modifyUser);
        parameters.setInParameter(i++, "drv_id", Types.BIGINT, drvIds);

        return drvDriverRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public int updateDrvPhone(Long driverId, String phone,String modifyUser) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        builder.setTemplate("update drv_driver set drv_phone = ?,modify_user = ? where drv_id = ? ");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.set(i++, "drv_phone", Types.VARCHAR,phone);
        parameters.set(i++, "modify_user", Types.VARCHAR,modifyUser);
        parameters.set(i++, "drv_id", Types.BIGINT, driverId);
        return drvDriverRepo.getQueryDao().update(builder, parameters, hints);
    }


    @Override
    public List<DrvDriverPO> queryOnlineDrvByCityAndVehicleType(Long cityId, Long vehicleTypeId, Integer drvStatus) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("city_id", cityId, Types.BIGINT);
            builder.and().equal("vehicle_type_id", vehicleTypeId, Types.BIGINT);
            builder.and().equal("drv_status", drvStatus, Types.INTEGER);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return drvDriverRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            throw new RuntimeException();
        }
    }
    
    @Override
    public int countDriverEnglishNameIsEmpty() {
        DalHints hints = DalHints.createIfAbsent(null);
        try{
            SelectSqlBuilder builder=new SelectSqlBuilder();
            builder.selectCount();
            builder.isNull( "drv_english_name" ).or().equal( "drv_english_name","",Types.VARCHAR );
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return drvDriverRepo.getDao().count( builder,hints).intValue();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
            
        }
    }
    
    @Override
    public List<DrvDriverPO> queryDriverEnglishNameIsEmpty(int pageNo, int pageSize){
        DalHints hints = DalHints.createIfAbsent(null);
        try{
            SelectSqlBuilder builder=new SelectSqlBuilder();
            builder.select( "drv_id","drv_name" );
            // drv_english_name is null or drv_english_name = ''
            builder.isNull( "drv_english_name" ).or().equal( "drv_english_name","",Types.VARCHAR );
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.atPage( pageNo,pageSize );
            return drvDriverRepo.getDao().query( builder,hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<DrvDriverPO> queryDrvByPage(Long drvId) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeSelectSqlBuilder<List<DrvDriverPO>> builder = new FreeSelectSqlBuilder<>();
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT * FROM drv_driver WHERE drv_id > ? and active = 1 ORDER BY drv_id LIMIT 500");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.set(i,"drv_id", Types.BIGINT, drvId);
        builder.setTemplate(sqlStr.toString());
        builder.mapWith(drvDriverPORowMapper);
        List<DrvDriverPO> driverPOList = drvDriverRepo.getQueryDao().query(builder,parameters, hints);
        return driverPOList;
    }

    @Override
    public List<DrvDriverPO> queryDrvIdAndVehicleIdByCondition(QueryDriverConditionDTO req) {
        DrvCacheConditionQuery query = DrvConverter.buildQuery(req);
        if (CollectionUtils.isEmpty(query.getDrvIdList())) {
            return doQueryDrvIdAndVehicleIdByCondition(query);
        }
        List<List<Long>> drvIdListArr = Lists.partition(query.getDrvIdList(), Constant.SQL_REQUEST_ID_LIMIT_ROW_COUNT);
        List<DrvDriverPO> res = Lists.newArrayListWithExpectedSize(query.getDrvIdList().size());
        for (List<Long> drvIdList : drvIdListArr) {
            query.setDrvIdList(drvIdList);
            res.addAll(doQueryDrvIdAndVehicleIdByCondition(query));
        }
        return res;
    }

    private List<DrvDriverPO> doQueryDrvIdAndVehicleIdByCondition(DrvCacheConditionQuery query) {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.select("drv_id", "vehicle_id");
        try {
            if (CollectionUtils.isNotEmpty(query.getDrvIdList())) {
                builder.and();
                builder.in("drv_id", query.getDrvIdList(), Types.BIGINT);
            }
            if (CollectionUtils.isNotEmpty(query.getCoopModeList())) {
                builder.and();
                builder.in("coop_mode", query.getCoopModeList(), Types.TINYINT);
            }
            if (!Strings.isNullOrEmpty(query.getDriverName())) {
                builder.and();
                builder.equal("drv_name", query.getDriverName(), Types.VARCHAR);
            }
            if (!Strings.isNullOrEmpty(query.getDriverPhone())) {
                builder.and();
                builder.equal("drv_phone", TmsTransUtil.encrypt(query.getDriverPhone(), KeyType.Phone), Types.VARCHAR);
            }
            if (query.getStatus() != null) {
                builder.and();
                builder.equal("drv_status", query.getStatus(), Types.TINYINT);
            }
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return drvDriverRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            logger.error("QueryDrvIdAndVehicleIdByConditionError", "Params:{}", JsonUtil.toJson(query));
            return Lists.newArrayList();
        }
    }

    private List<DrvDriverPO> doQueryCacheDrvList(List<Long> drvIdList) {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.select("drv_id", "drv_status", "coop_mode", "drv_name", "drv_phone", "igt_code", "vehicle_id", "vehicle_type_id", "intend_vehicle_type_id",
                "city_id", "drv_addr", "work_period", "drv_language", "country_id", "country_name", "supplier_id", "email", "wechat", "datachange_createtime",
                "internal_scope", "drv_head_img", "category_synthesize_code", "online_time", "drv_idcard", "drv_license_number", "certi_date", "expiry_begin_date", "expiry_end_date", "veh_bind_time", "drvcard_img","nation","raising_pick_up","child_seat","driver_net_cert_no",
                "temporary_dispatch_mark","temporary_dispatch_end_datetime","paiay_account","paiay_email","account_type","ppm_account");
        try {
            builder.and();
            builder.in("drv_id", drvIdList, Types.BIGINT);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return drvDriverRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            logger.error("queryCacheDrvListError", "drvIdList:{} error:{}", JsonUtil.toJson(drvIdList), e);
            return Lists.newArrayList();
        }
    }

    @Override
    public List<DrvDriverPO> queryCacheDrvList(Set<Long> drvIdSet) {
        List<List<Long>> drvIdListArr = Lists.partition(Lists.newArrayList(drvIdSet), Constant.SQL_REQUEST_ID_LIMIT_ROW_COUNT);
        List<DrvDriverPO> res = Lists.newArrayListWithCapacity(drvIdSet.size());
        for (List<Long> idList : drvIdListArr) {
            res.addAll(doQueryCacheDrvList(idList));
        }
        return res;
    }

    @Override
    public List<Long> queryDrvListByCity(List<Long> cityIds, List<Integer> drvStatus) {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeSelectSqlBuilder<List<Long>> builder = new FreeSelectSqlBuilder<>();
        try {
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("SELECT drv_id FROM drv_driver WHERE city_id in (?) and drv_status in (?) and active = 1 ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setInParameter(i++, "city_id", Types.BIGINT,cityIds);
            parameters.setInParameter(i++, "drv_status", Types.TINYINT,drvStatus);
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(drvDriverPORowMapper);
            builder.simpleType().nullable();
            return drvDriverRepo.getQueryDao().query(builder, parameters, hints);
        }catch (Exception e){
            logger.error("queryDrvListByCityError", "cityIds:{} error:{}", JsonUtil.toJson(cityIds), e);
           return Lists.newArrayList();
        }
    }

    @Override
    public List<DrvDriverPO> queryDrvResourceByCondition(QueryDrvResourceConditionDTO conditionDTO) {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.select(conditionDTO.getFields());
        try {
            assembleDrvResourceCondition(conditionDTO, builder);
            builder.orderBy("drv_id", Boolean.TRUE);
            return drvDriverRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            logger.error("queryDrvResourceByConditionError", "conditionDTO:{} error:{}", JsonUtil.toJson(conditionDTO), e);
            return Lists.newArrayList();
        }
    }

    @Override
    public int queryDrvResourceCountByCondition(QueryDrvResourceConditionDTO conditionDTO) {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectCount();
        try {
            assembleDrvResourceCondition(conditionDTO, builder);
            return drvDriverRepo.getDao().count(builder, hints).intValue();
        } catch (Exception e) {
            logger.error("queryDrvResourceCountByConditionError", "conditionDTO:{} error:{}", JsonUtil.toJson(conditionDTO), e);
            return 0;
        }
    }

    private void assembleDrvResourceCondition(QueryDrvResourceConditionDTO conditionDTO, SelectSqlBuilder builder) throws SQLException {
        if (CollectionUtils.isNotEmpty(conditionDTO.getDrvIdList())) {
            builder.and();
            builder.in("drv_id", conditionDTO.getDrvIdList(), Types.BIGINT);
        }
        if (CollectionUtils.isNotEmpty(conditionDTO.getDrvNameList())) {
            builder.and();
            builder.in("drv_name", conditionDTO.getDrvNameList(), Types.VARCHAR);
        }
        if (CollectionUtils.isNotEmpty(conditionDTO.getDriverPhoneList())) {
            builder.and();
            builder.in("drv_phone", conditionDTO.getDriverPhoneList(), Types.VARCHAR);
        }
        if (CollectionUtils.isNotEmpty(conditionDTO.getCountryIdList())) {
            builder.and();
            builder.in("country_id", conditionDTO.getCountryIdList(), Types.BIGINT);
        }
        if (CollectionUtils.isNotEmpty(conditionDTO.getVehicleTypeIdList())) {
            builder.and();
            builder.in("vehicle_type_id", conditionDTO.getVehicleTypeIdList(), Types.BIGINT);
        }
        if (CollectionUtils.isNotEmpty(conditionDTO.getCityIdList())) {
            builder.and();
            builder.in("city_id", conditionDTO.getCityIdList(), Types.BIGINT);
        }
        if (CollectionUtils.isNotEmpty(conditionDTO.getSupplierIdList())) {
            builder.and();
            builder.in("supplier_id", conditionDTO.getSupplierIdList(), Types.BIGINT);
        }
        if (CollectionUtils.isNotEmpty(conditionDTO.getDrvStatusList())) {
            builder.and();
            builder.in("drv_status", conditionDTO.getDrvStatusList(), Types.TINYINT);
        }
        if (CollectionUtils.isNotEmpty(conditionDTO.getDrvCoopModeList())) {
            builder.and();
            builder.in("coop_mode", conditionDTO.getDrvCoopModeList(), Types.TINYINT);
        }
        if (CollectionUtils.isNotEmpty(conditionDTO.getProLineIdList())) {
            builder.and();
            builder.in("category_synthesize_code", conditionDTO.getProLineIdList(), Types.TINYINT);
        }
        if (conditionDTO.getBoundaryDrvId() != null) {
            builder.and();
            builder.greaterThan("drv_id", conditionDTO.getBoundaryDrvId(), Types.BIGINT);
        }
        builder.and().equal("active",Boolean.TRUE,Types.BIT);
        if (conditionDTO.getPaginator() != null) {
            builder.atPage(conditionDTO.getPaginator().getPageNo(), conditionDTO.getPaginator().getPageSize());
        }
    }

    @Override
    public int discardDrv(Long drvId,Boolean active, String modifyUser) {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        try {
            builder.setTemplate("update drv_driver set active = ?,modify_user = ? where drv_id = ? ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "active", Types.BIT,active);
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR,modifyUser);
            parameters.setSensitive(i++, "drv_id", Types.BIGINT, drvId);

            return drvDriverRepo.getQueryDao().update(builder, parameters, hints);
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public DrvDriverPO drvDispatchcheckDrvOnly(String str, Integer type) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        try {
            builder.selectAll();
            if (Objects.equals(type, TmsTransportConstant.DrvOnlyTypeEnum.PHONE.getCode())) {
                builder.and();
                builder.equal("drv_phone", str, Types.VARCHAR, false);
            }
            if (Objects.equals(type, TmsTransportConstant.DrvOnlyTypeEnum.EMIAL.getCode())) {
                builder.and();
                builder.equal("email", str, Types.VARCHAR, false);
            }
            builder.and();
            builder.equalNullable("active", Boolean.TRUE, Types.BIT);
            builder.orderBy("drv_id", false);
            List<DrvDriverPO> drvDriverPOS = drvDriverRepo.getDao().query(builder, hints);
            return CollectionUtils.isEmpty(drvDriverPOS) ? null : drvDriverPOS.get(0);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public DrvDriverPO drvDispatchcheckDrvPhoneOnly(String phone) {
        Cat.logEvent(CatEventType.CHECK_DRIVER_PHONE_ONLY, "1");
        if (StringUtils.isBlank(phone)) {
            return null;
        }
        String decryptPhone = TmsTransUtil.decrypt(phone, KeyType.Phone);
        if (decryptPhone.startsWith("0")) {
            return null;
        }
        List<String> checkPhons = Lists.newArrayList(TmsTransUtil.encrypt(decryptPhone, KeyType.Phone), TmsTransUtil.encrypt("0" + decryptPhone, KeyType.Phone), TmsTransUtil.encrypt("00" + decryptPhone, KeyType.Phone));
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        try {
            builder.selectAll();
            builder.and().in("drv_phone", checkPhons, Types.VARCHAR, false);
            builder.and().equalNullable("active", Boolean.TRUE, Types.BIT);
            builder.orderBy("drv_id", false);
            List<DrvDriverPO> drvDriverPOS = drvDriverRepo.getDao().query(builder, hints);
            return CollectionUtils.isEmpty(drvDriverPOS) ? null : drvDriverPOS.get(0);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Boolean checkDrvPhoneOnly(String phone, Long originDrvId) {
        Cat.logEvent(CatEventType.CHECK_DRIVER_PHONE_ONLY, "2");
        if (StringUtils.isBlank(phone)) {
            return false;
        }
        String decryptPhone = TmsTransUtil.decrypt(phone, KeyType.Phone);
        if (decryptPhone.startsWith("0")) {
            return false;
        }
        List<String> checkPhons = Lists.newArrayList(TmsTransUtil.encrypt(decryptPhone, KeyType.Phone), TmsTransUtil.encrypt("0" + decryptPhone, KeyType.Phone), TmsTransUtil.encrypt("00" + decryptPhone, KeyType.Phone));
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        try {
            builder.selectAll();
            builder.and().in("drv_phone", checkPhons, Types.VARCHAR, false);
            builder.and().equalNullable("active", Boolean.TRUE, Types.BIT);
            builder.orderBy("drv_id", false);
            List<DrvDriverPO> drvDriverPOS = drvDriverRepo.getDao().query(builder, hints);
            if (CollectionUtils.isNotEmpty(drvDriverPOS)) {
                return drvDriverPOS.stream().filter(d -> !d.getDrvId().equals(originDrvId)).count() > 0;
            }
            return false;
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Integer countDrvDispatchList(QueryDrvDO drvDO) {
        try {
            if(drvDO.getSupplierId() == null || drvDO.getSupplierId() <= 0 ){
                return 0;
            }
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<Long> builder = new FreeSelectSqlBuilder<>();
            builder.simpleType().requireSingle().nullable();
            StringBuilder sqlStr = new StringBuilder();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            sqlStr.append("select count(1) from tms_drv_dispatch_relation dr LEFT JOIN drv_driver dd on dr.drv_id = dd.drv_id where dr.supplier_id = ? and dr.active = 1  ");
            parameters.set(i++, "dr.supplier_id", Types.BIGINT, drvDO.getSupplierId() == null?0L:drvDO.getSupplierId());
            if (drvDO.getDrvId() != null && drvDO.getDrvId() > 0) {
                sqlStr.append(" and dd.drv_id = ? ");
                parameters.set(i++, "dd.drv_id", Types.BIGINT, drvDO.getDrvId());
            }
            if(CollectionUtils.isNotEmpty(drvDO.getDrvIdList())){
                sqlStr.append(" and dd.drv_id in (?) ");
                parameters.setInParameter(i++, "dd.drv_id", Types.BIGINT, drvDO.getDrvIdList());
            }
            if (drvDO.getCityId() != null && drvDO.getCityId() > 0) {
                sqlStr.append(" and dd.city_id = ? ");
                parameters.set(i++, "dd.city_id", Types.BIGINT, drvDO.getCityId());
            }
            if (StringUtils.isNotEmpty(drvDO.getDrvName())) {
                sqlStr.append(" and dd.drv_name like ? ");
                parameters.set(i++, "dd.drv_name", Types.VARCHAR, drvDO.getDrvName() + "%");
            }
            if (StringUtils.isNotEmpty(drvDO.getVehicleLicense())) {
                sqlStr.append(" and dd.vehicle_license like ? ");
                parameters.set(i++, "dd.vehicle_license", Types.VARCHAR, drvDO.getVehicleLicense() + "%");
            }
            if (drvDO.getVehicleTypeId() != null && drvDO.getVehicleTypeId() > 0) {
                sqlStr.append(" and dd.vehicle_type_id = ? ");
                parameters.set(i++, "dd.vehicle_type_id", Types.BIGINT, drvDO.getVehicleTypeId());
            }
            if (drvDO.getStatus() != null) {
                sqlStr.append(" and dd.drv_status = ? ");
                parameters.set(i++, "dd.drv_status", Types.INTEGER, drvDO.getStatus());
            }
            if (drvDO.getDrvFrom() != null && drvDO.getDrvFrom() > 0) {
                sqlStr.append(" and dd.drv_from = ? ");
                parameters.set(i++, "dd.drv_from", Types.INTEGER, drvDO.getDrvFrom());
            }
            if (CollectionUtils.isNotEmpty(drvDO.getDrvPhoneList())) {
                sqlStr.append(" and dd.drv_phone in (?) ");
                parameters.setInParameter(i++, "dd.drv_phone", Types.VARCHAR, drvDO.getDrvPhoneList());
            }
            if (StringUtils.isNotEmpty(drvDO.getDrvPhone())) {
                sqlStr.append(" and dd.drv_phone = ? ");
                parameters.set(i++, "dd.drv_phone", Types.VARCHAR, drvDO.getDrvPhone());
            }
            if (StringUtils.isNotEmpty(drvDO.getIgtCode())) {
                sqlStr.append(" and dd.igt_code = ? ");
                parameters.set(i++, "dd.igt_code", Types.VARCHAR, drvDO.getIgtCode());
            }
            if (StringUtils.isNotEmpty(drvDO.getDrvIdcard())) {
                sqlStr.append(" and dd.drv_idcard = ? ");
                parameters.set(i++, "dd.drv_idcard", Types.VARCHAR, drvDO.getDrvIdcard());
            }
            if (StringUtils.isNotEmpty(drvDO.getDrvLanguage())) {
                sqlStr.append(" and dd.drv_language like ? ");
                parameters.set(i++, "dd.drv_language", Types.VARCHAR, "%" + drvDO.getDrvLanguage() + "%");
            }
            if (drvDO.getCoopMode() != null) {
                sqlStr.append(" and dd.coop_mode = ? ");
                parameters.set(i++, "dd.coop_mode", Types.TINYINT, drvDO.getCoopMode());
            }
            if (drvDO.getRegistStartDate() != null && drvDO.getRegistEndDate() != null) {
                sqlStr.append(" and (dd.datachange_createtime >= ? and dd.datachange_createtime <= ?) ");
                parameters.set(i++, "dd.datachange_createtime", Types.TIMESTAMP, drvDO.getRegistStartDate());
                parameters.set(i++, "dd.datachange_createtime", Types.TIMESTAMP, drvDO.getRegistEndDate());
            }
            if (drvDO.getOnlineStartDate() != null && drvDO.getOnlineEndDate() != null) {
                sqlStr.append(" and (dd.online_time >= ? and dd.online_time <= ?) ");
                parameters.set(i++, "dd.online_time", Types.TIMESTAMP, drvDO.getOnlineStartDate());
                parameters.set(i++, "dd.online_time", Types.TIMESTAMP, drvDO.getOnlineEndDate());
            }
            if (CollectionUtils.isNotEmpty(drvDO.getProLineIdList())) {
                sqlStr.append(" and dd.category_synthesize_code in (?) ");
                parameters.setInParameter(i++, "dd.category_synthesize_code", Types.TINYINT, drvDO.getProLineIdList());
            }
            if (drvDO.getRaisingPickUp() != null) {
                sqlStr.append(" and dd.raising_pick_up = ? ");
                parameters.set(i++, "dd.raising_pick_up", Types.BIT, drvDO.getRaisingPickUp());
            }
            if (drvDO.getChildSeat() != null) {
                sqlStr.append(" and dd.child_seat = ? ");
                parameters.set(i++, "dd.child_seat", Types.BIT, drvDO.getChildSeat());
            }
            sqlStr.append(" and dd.active = ? ");
            parameters.set(i++, "dd.active", Types.BIT, CtripCommonUtils.queryActiveChoose(drvDO.getActive()));
            builder.setTemplate(sqlStr.toString());
            return drvDriverRepo.getQueryDao().query(builder, parameters, hints).intValue();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<DrvDriverPO> queryDrvDispatchList(QueryDrvDO drvDO) {
        try {
            if(drvDO.getSupplierId() == null || drvDO.getSupplierId() <= 0 ){
                return Lists.newArrayList();
            }
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<DrvDriverPO>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            sqlStr.append("select dd.* from tms_drv_dispatch_relation dr LEFT JOIN drv_driver dd on dr.drv_id = dd.drv_id where dr.supplier_id = ? and dr.active = 1  ");
            parameters.set(i++, "dr.supplier_id", Types.BIGINT, drvDO.getSupplierId() == null?0L:drvDO.getSupplierId());
            if (drvDO.getDrvId() != null && drvDO.getDrvId() > 0) {
                sqlStr.append(" and dd.drv_id = ? ");
                parameters.set(i++, "dd.drv_id", Types.BIGINT, drvDO.getDrvId());
            }
            if(CollectionUtils.isNotEmpty(drvDO.getDrvIdList())){
                sqlStr.append(" and dd.drv_id in (?) ");
                parameters.setInParameter(i++, "dd.drv_id", Types.BIGINT, drvDO.getDrvIdList());
            }
            if (drvDO.getCityId() != null && drvDO.getCityId() > 0) {
                sqlStr.append(" and dd.city_id = ? ");
                parameters.set(i++, "dd.city_id", Types.BIGINT, drvDO.getCityId());
            }
            if (StringUtils.isNotEmpty(drvDO.getDrvName())) {
                sqlStr.append(" and dd.drv_name like ? ");
                parameters.set(i++, "dd.drv_name", Types.VARCHAR, drvDO.getDrvName() + "%");
            }
            if (StringUtils.isNotEmpty(drvDO.getVehicleLicense())) {
                sqlStr.append(" and dd.vehicle_license like ? ");
                parameters.set(i++, "dd.vehicle_license", Types.VARCHAR, drvDO.getVehicleLicense() + "%");
            }
            if (drvDO.getVehicleTypeId() != null && drvDO.getVehicleTypeId() > 0) {
                sqlStr.append(" and dd.vehicle_type_id = ? ");
                parameters.set(i++, "dd.vehicle_type_id", Types.BIGINT, drvDO.getVehicleTypeId());
            }
            if (drvDO.getStatus() != null) {
                sqlStr.append(" and dd.drv_status = ? ");
                parameters.set(i++, "dd.drv_status", Types.INTEGER, drvDO.getStatus());
            }
            if (drvDO.getDrvFrom() != null && drvDO.getDrvFrom() > 0) {
                sqlStr.append(" and dd.drv_from = ? ");
                parameters.set(i++, "dd.drv_from", Types.INTEGER, drvDO.getDrvFrom());
            }
            if (CollectionUtils.isNotEmpty(drvDO.getDrvPhoneList())) {
                sqlStr.append(" and dd.drv_phone in (?) ");
                parameters.setInParameter(i++, "dd.drv_phone", Types.VARCHAR, drvDO.getDrvPhoneList());
            }
            if (StringUtils.isNotEmpty(drvDO.getDrvPhone())) {
                sqlStr.append(" and dd.drv_phone = ? ");
                parameters.set(i++, "dd.drv_phone", Types.VARCHAR, drvDO.getDrvPhone());
            }
            if (StringUtils.isNotEmpty(drvDO.getIgtCode())) {
                sqlStr.append(" and dd.igt_code = ? ");
                parameters.set(i++, "dd.igt_code", Types.VARCHAR, drvDO.getIgtCode());
            }
            if (StringUtils.isNotEmpty(drvDO.getDrvIdcard())) {
                sqlStr.append(" and dd.drv_idcard = ? ");
                parameters.set(i++, "dd.drv_idcard", Types.VARCHAR, drvDO.getDrvIdcard());
            }
            if (StringUtils.isNotEmpty(drvDO.getDrvLanguage())) {
                sqlStr.append(" and dd.drv_language like ? ");
                parameters.set(i++, "dd.drv_language", Types.VARCHAR, "%" + drvDO.getDrvLanguage() + "%");
            }
            if (drvDO.getCoopMode() != null) {
                sqlStr.append(" and dd.coop_mode = ? ");
                parameters.set(i++, "dd.coop_mode", Types.TINYINT, drvDO.getCoopMode());
            }
            if (drvDO.getRegistStartDate() != null && drvDO.getRegistEndDate() != null) {
                sqlStr.append(" and (dd.datachange_createtime >= ? and dd.datachange_createtime <= ?) ");
                parameters.set(i++, "dd.datachange_createtime", Types.TIMESTAMP, drvDO.getRegistStartDate());
                parameters.set(i++, "dd.datachange_createtime", Types.TIMESTAMP, drvDO.getRegistEndDate());
            }
            if (drvDO.getOnlineStartDate() != null && drvDO.getOnlineEndDate() != null) {
                sqlStr.append(" and (dd.online_time >= ? and dd.online_time <= ?) ");
                parameters.set(i++, "dd.online_time", Types.TIMESTAMP, drvDO.getOnlineStartDate());
                parameters.set(i++, "dd.online_time", Types.TIMESTAMP, drvDO.getOnlineEndDate());
            }
            if (CollectionUtils.isNotEmpty(drvDO.getProLineIdList())) {
                sqlStr.append(" and dd.category_synthesize_code in (?) ");
                parameters.setInParameter(i++, "dd.category_synthesize_code", Types.TINYINT, drvDO.getProLineIdList());
            }
            if (drvDO.getRaisingPickUp() != null) {
                sqlStr.append(" and dd.raising_pick_up = ? ");
                parameters.set(i++, "dd.raising_pick_up", Types.BIT, drvDO.getRaisingPickUp());
            }
            if (drvDO.getChildSeat() != null) {
                sqlStr.append(" and dd.child_seat = ? ");
                parameters.set(i++, "dd.child_seat", Types.BIT, drvDO.getChildSeat());
            }
            sqlStr.append(" and dd.active = ? ");
            parameters.set(i++, "dd.active", Types.BIT, CtripCommonUtils.queryActiveChoose(drvDO.getActive()));
            sqlStr.append("order by dd.datachange_lasttime DESC ");
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(drvDriverPORowMapper).atPage(drvDO.getPage(), drvDO.getSize());
            return drvDriverRepo.getQueryDao().query(builder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int syncDrvVehicleLicense(Long drvId, String vehicleLicense, String modifyUser) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            builder.setTemplate("update drv_driver set vehicle_license = ? ,modify_user = ?  where drv_id = ?  and active = 1 and internal_scope = 1 ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "vehicle_license", Types.VARCHAR, vehicleLicense);
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
            parameters.setSensitive(i++, "drv_id", Types.BIGINT, drvId);

            return drvDriverRepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<DrvDriverPO> queryQunarImgList(List<Long> drvId,int pageNo, int pageSize) {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        try {
            builder.select("drv_id","drvcard_img","idcard_img","drv_head_img","people_vehicle_img","net_vehicle_peo_img","idcard_back_img");
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            if(CollectionUtils.isNotEmpty(drvId)){
                builder.and().in("drv_id",drvId,Types.BIGINT);
            }else {
                builder.and().lessThanEquals("drv_id",356093L,Types.BIGINT);
            }
            builder.atPage(pageNo,pageSize);
            return drvDriverRepo.getDao().query(builder, hints);
        }catch (Exception e){
            return Lists.newArrayList();
        }
    }

    @Override
    public int queryCountQunarImg(List<Long> drvId) {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeSelectSqlBuilder<Long> builder = new FreeSelectSqlBuilder<>();
        builder.simpleType().requireSingle().nullable();
        try {
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("SELECT count(1) FROM drv_driver WHERE active = 1 and drv_id <= 356093 ");
            StatementParameters parameters = new StatementParameters();
            builder.setTemplate(sqlStr.toString());
            return drvDriverRepo.getQueryDao().query(builder, parameters, hints).intValue();
        }catch (Exception e){
            return 0;
        }
    }

    @Override
    public int updateDrvImgQToC(DrvDriverPO drvDriverPO) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            if(StringUtils.isEmpty(drvDriverPO.getDrvcardImg()) && StringUtils.isEmpty(drvDriverPO.getIdcardImg()) && StringUtils.isEmpty(drvDriverPO.getDrvHeadImg()) &&
                    StringUtils.isEmpty(drvDriverPO.getPeopleVehicleImg()) && StringUtils.isEmpty(drvDriverPO.getNetVehiclePeoImg())&& StringUtils.isEmpty(drvDriverPO.getIdcardBackImg())){
                return 0;
            }
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("update drv_driver set modify_user = ? ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.set(i++, "modify_user", Types.VARCHAR,TmsTransportConstant.TMS_DEFAULT_USERNAME);
            if(StringUtils.isNotEmpty(drvDriverPO.getDrvcardImg())){
                sqlStr.append(", drvcard_img = ? ");
                parameters.set(i++, "drvcard_img", Types.VARCHAR,drvDriverPO.getDrvcardImg());
            }
            if(StringUtils.isNotEmpty(drvDriverPO.getIdcardImg())){
                sqlStr.append(", idcard_img = ? ");
                parameters.set(i++, "idcard_img", Types.VARCHAR,drvDriverPO.getIdcardImg());
            }
            if(StringUtils.isNotEmpty(drvDriverPO.getDrvHeadImg())){
                sqlStr.append(", drv_head_img = ? ");
                parameters.set(i++, "drv_head_img", Types.VARCHAR,drvDriverPO.getDrvHeadImg());
            }
            if(StringUtils.isNotEmpty(drvDriverPO.getPeopleVehicleImg())){
                sqlStr.append(", people_vehicle_img = ? ");
                parameters.set(i++, "people_vehicle_img", Types.VARCHAR,drvDriverPO.getPeopleVehicleImg());
            }
            if(StringUtils.isNotEmpty(drvDriverPO.getNetVehiclePeoImg())){
                sqlStr.append(", net_vehicle_peo_img = ? ");
                parameters.set(i++, "net_vehicle_peo_img", Types.VARCHAR,drvDriverPO.getNetVehiclePeoImg());
            }
            if(StringUtils.isNotEmpty(drvDriverPO.getIdcardBackImg())){
                sqlStr.append(", idcard_back_img = ? ");
                parameters.set(i++, "idcard_back_img", Types.VARCHAR,drvDriverPO.getIdcardBackImg());
            }
            sqlStr.append(" where drv_id = ? ");
            parameters.set(i++, "drv_id", Types.BIGINT, drvDriverPO.getDrvId());
            builder.setTemplate(sqlStr.toString());
            return drvDriverRepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException("updateDrvImgQToC error", e);
        }
    }

    @Override
    public Integer countDiscardDrvList(QueryDrvDO drvDO) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            builder.equal("supplier_id", drvDO.getSupplierId(), Types.BIGINT);
            if (drvDO.getDrvId() != null && drvDO.getDrvId() > 0) {
                builder.and().equal("drv_id", drvDO.getDrvId(), Types.BIGINT);
            }
            if (StringUtils.isNotEmpty(drvDO.getDrvPhone())) {
                builder.and().equalNullable("drv_phone", drvDO.getDrvPhone(), Types.VARCHAR);
            }
            builder.and().equal("active", Boolean.FALSE, Types.BIT);
            return drvDriverRepo.getDao().count(builder, hints).intValue();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<DrvDriverPO> queryDiscardDrvList(QueryDrvDO drvDO) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("supplier_id", drvDO.getSupplierId(), Types.BIGINT);
            if (drvDO.getDrvId() != null && drvDO.getDrvId() > 0) {
                builder.and().equal("drv_id", drvDO.getDrvId(), Types.BIGINT);
            }
            if (StringUtils.isNotEmpty(drvDO.getDrvPhone())) {
                builder.and().equalNullable("drv_phone", drvDO.getDrvPhone(), Types.VARCHAR);
            }
            builder.and().equal("active", Boolean.FALSE, Types.BIT);
            builder.orderBy("datachange_lasttime", false);
            builder.atPage(drvDO.getPage(), drvDO.getSize());
            return drvDriverRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            logger.error("queryDiscardDrvListError:", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<Long> queryDrvIdByMarkPage(List<Long> drvIds, Integer temporaryDispatchMark) {
        if(CollectionUtils.isEmpty(drvIds)){
            return Lists.newArrayList();
        }
        DalHints hints = DalHints.createIfAbsent(null);
        FreeSelectSqlBuilder<List<Long>> builder = new FreeSelectSqlBuilder<>();
        try {
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("SELECT drv_id FROM drv_driver WHERE drv_id in (?) and temporary_dispatch_mark = ? and active = 1 ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setInParameter(i++, "drv_id", Types.BIGINT,drvIds);
            parameters.set(i++, "temporary_dispatch_mark", Types.TINYINT,temporaryDispatchMark);
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(drvDriverPORowMapper);
            builder.simpleType().nullable();
            return drvDriverRepo.getQueryDao().query(builder, parameters, hints);
        }catch (Exception e){
            logger.error("queryDrvIdByMarkPage", "error:{}", e);
            return Lists.newArrayList();
        }
    }

    @Override
    public int updateTemporaryDispatchMark(List<Long> drvIds,Boolean active ,String modifyUser) {
        if(CollectionUtils.isEmpty(drvIds)){
            return 0;
        }
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            builder.setTemplate("update drv_driver set temporary_dispatch_mark = ? ,modify_user = ?  where drv_id in (?)  and active = ? and internal_scope = 1 ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "temporary_dispatch_mark", Types.INTEGER, TmsTransportConstant.TemporaryDispatchMarkEnum.OFFICIAL.getCode());
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, StringUtils.isEmpty(modifyUser)?TmsTransportConstant.TMS_DEFAULT_USERNAME:modifyUser);
            parameters.setInParameter(i++, "drv_id", Types.BIGINT, drvIds);
            parameters.set(i++, "active", Types.BIT, active);

            return drvDriverRepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<Long> queryDiscardTemDrv(String drvPhone) {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeSelectSqlBuilder<List<Long>> builder = new FreeSelectSqlBuilder<>();
        try {
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("SELECT drv_id FROM drv_driver WHERE drv_phone = ? and temporary_dispatch_mark = 1 and active = 0 ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.set(i++, "drv_phone", Types.VARCHAR,drvPhone);
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(drvDriverPORowMapper);
            builder.simpleType().nullable();
            return drvDriverRepo.getQueryDao().query(builder, parameters, hints);
        }catch (Exception e){
            logger.error("queryDiscardTemDrv", "error:{}", e);
            return Lists.newArrayList();
        }
    }

    @Override
    public List<Long> queryTemporaryDispatchDriver(Timestamp nowDate) {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeSelectSqlBuilder<List<Long>> builder = new FreeSelectSqlBuilder<>();
        try {
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("SELECT drv_id FROM drv_driver WHERE temporary_dispatch_mark = 1 and active = 1 and  temporary_dispatch_end_datetime <= ?");
            StatementParameters parameters = new StatementParameters();
            parameters.set(1, "temporary_dispatch_end_datetime", Types.TIMESTAMP,nowDate);
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(drvDriverPORowMapper);
            builder.simpleType().nullable();
            return drvDriverRepo.getQueryDao().query(builder, parameters, hints);
        }catch (Exception e){
            logger.error("queryTemporaryDispatchDriver", "error:{}", e);
            return Lists.newArrayList();
        }
    }

    /**
     * EXPLAIN SELECT drv_id,supplier_id FROM drv_driver WHERE drv_phone in ('1111111') AND active = 1
     * key: ix_Phone
     * ref: const
     */
    @Override
    public List<DrvDriverPO> queryDriverId(List<String> drvPhoneList) {
         if (CollectionUtils.isEmpty(drvPhoneList)) {
            logger.warn("queryDriverIdWarn", "key parameters missing");
            return Collections.emptyList();
        }
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.select("drv_id", "supplier_id");
            builder.in("drv_phone", drvPhoneList, Types.VARCHAR).and().equal("active", Boolean.TRUE, Types.BIT);
            return drvDriverRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            logger.error("queryDriverIdError", "params:{} error:{}", JsonUtil.toJson(drvPhoneList), e);
            return Collections.emptyList();
        }
    }

    @Override
    public int updatedDvAddPaiayAccount(Long drvId, String paiayAccount, String paiayEmail, String modifyUser) {
        if(drvId == null || StringUtils.isEmpty(paiayAccount) || StringUtils.isEmpty(paiayEmail)){
            return 0;
        }
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            builder.setTemplate("update drv_driver set paiay_account = ?,paiay_email = ?,modify_user = ?  where drv_id = ? ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "paiay_account", paiayAccount);
            parameters.setSensitive(i++, "paiay_email", paiayEmail);
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, StringUtils.isEmpty(modifyUser)?TmsTransportConstant.TMS_DEFAULT_USERNAME:modifyUser);
            parameters.setSensitive(i++, "drv_id", Types.BIGINT, drvId);
            return drvDriverRepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int countDrvAddrModCountNoEmpty() {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<Long> builder = new FreeSelectSqlBuilder<>();
            builder.simpleType().requireSingle().nullable();
            StringBuilder sqlStr = new StringBuilder();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            sqlStr.append("SELECT count(*) FROM drv_driver WHERE addr_mod_count!='' and active = 1 ");
            builder.setTemplate(sqlStr.toString());
            return drvDriverRepo.getQueryDao().query(builder, parameters, hints).intValue();
        } catch (Exception e) {
            logger.error("countDrvAddrModCountNoEmpty", "error:{}", e);
            return 0;
        }
    }

    @Override
    public List<Long> queryDrvAddrModCountNoEmptyList(int pageNo, int pageSize) {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeSelectSqlBuilder<List<Long>> builder = new FreeSelectSqlBuilder<>();
        try {
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("SELECT drv_id FROM drv_driver WHERE addr_mod_count!='' and active = 1 ");
            StatementParameters parameters = new StatementParameters();
            builder.atPage(pageNo, pageSize);
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(drvDriverPORowMapper);
            builder.simpleType().nullable();
            return drvDriverRepo.getQueryDao().query(builder, parameters, hints);
        }catch (Exception e){
            logger.error("queryDrvAddrModCountNoEmptyList", "error:{}", e);
            return Lists.newArrayList();
        }
    }

    @Override
    public int initDrvAddrModCount(List<Long> drvIds,String addrModCount) {
        if(CollectionUtils.isEmpty(drvIds)){
            return 0;
        }
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            builder.setTemplate("update drv_driver set addr_mod_count = ? where drv_id in (?) ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "addr_mod_count", Types.VARCHAR, addrModCount);
            parameters.setInParameter(i++, "drv_id", Types.BIGINT, drvIds);
            return drvDriverRepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int queryDrvUidEmptyCount(List<Long> drvIds, List<Long> cityids) {
        if(CollectionUtils.isEmpty(drvIds) && CollectionUtils.isEmpty(cityids)){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            if (CollectionUtils.isNotEmpty(drvIds)) {
                builder.and().in("drv_id", drvIds, Types.BIGINT);
            }
            if (CollectionUtils.isNotEmpty(cityids)) {
                builder.and().in("city_id", cityids, Types.BIGINT);
            }
            builder.and().in("drv_status",Arrays.asList(1,2),Types.INTEGER);
            builder.and().equal("uid","",Types.VARCHAR);
            builder.and().equal("active", Boolean.TRUE, Types.BIT);
            return drvDriverRepo.getDao().count(builder, hints).intValue();
        } catch (Exception e) {
            logger.error("queryDrvUidEmptyCount:", e);
            return 0;
        }
    }

    @Override
    public List<DrvDriverPO> queryDrvUidEmptyList(List<Long> drvIds, List<Long> cityids,int pageNo,int pageSize) {
        if(CollectionUtils.isEmpty(drvIds) && CollectionUtils.isEmpty(cityids)){
            return Collections.emptyList();
        }
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            if (CollectionUtils.isNotEmpty(drvIds)) {
                builder.and().in("drv_id", drvIds, Types.BIGINT);
            }
            if (CollectionUtils.isNotEmpty(cityids)) {
                builder.and().in("city_id", cityids, Types.BIGINT);
            }
            builder.and().in("drv_status",Arrays.asList(1,2),Types.INTEGER);
            builder.and().equal("uid","",Types.VARCHAR);
            builder.and().equal("active", Boolean.TRUE, Types.BIT);
            builder.atPage(pageNo,pageSize);
            return drvDriverRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            logger.error("queryDrvUidEmptyList:", e);
            return Collections.emptyList();
        }
    }

    @Override
    public int updateDrvUid(Long drvId, String uid, String modifyUser, String ppmAccount, String qunarAccount) {
        if(drvId == null || StringUtils.isEmpty(uid)){
            return 0;
        }
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            builder.append("update drv_driver set uid = ?, modify_user = ? ");
            parameters.setSensitive(i++, "uid", uid);
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, StringUtils.isEmpty(modifyUser)?TmsTransportConstant.TMS_DEFAULT_USERNAME:modifyUser);
            if (StringUtils.isNotBlank(ppmAccount)) {
                parameters.setSensitive(i++, "ppm_account", Types.VARCHAR, ppmAccount);
                builder.append(", ppm_account = ? ");
            }
            if (StringUtils.isNotBlank(qunarAccount)) {
                parameters.setSensitive(i++, "qunar_account", Types.VARCHAR, qunarAccount);
                builder.append(", qunar_account = ? ");
            }
            builder.append("  where drv_id = ? ");
            parameters.setSensitive(i++, "drv_id", Types.BIGINT, drvId);
            return drvDriverRepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @SneakyThrows
    @Override
    public List<DrvDriverPO> queryDrvListByDriverIdFromAndPage(Long drvId, String beginDate, String endDate, int pageNo, int pageSize) {
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        builder.and().greaterThan("drv_id", drvId, Types.BIGINT);
        builder.and().greaterThanEqualsNullable("datachange_lasttime", beginDate, Types.TIMESTAMP);
        builder.and().lessThanEqualsNullable("datachange_lasttime", endDate, Types.TIMESTAMP);
        builder.atPage(pageNo,pageSize);
        builder.orderBy("drv_id", true);
        return drvDriverRepo.getDao().query(builder, DalHints.createIfAbsent(null));
    }

    @SneakyThrows
    @Override
    public List<DrvDriverPO> queryDrvListByDriverIdFromAndPage(Long drvId, int pageNo, int pageSize) {
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        builder.and().greaterThan("drv_id", drvId, Types.BIGINT);
        builder.atPage(pageNo,pageSize);
        builder.orderBy("drv_id", true);
        return drvDriverRepo.getDao().query(builder, DalHints.createIfAbsent(null));
    }


    @Override
    public List<DrvDriverPO> queryRecentDateDrvListByDriverIdFromAndPage(int pageNo, int pageSize,
      Date date) throws SQLException {
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        builder.and().greaterThan("datachange_lasttime", date, Types.DATE);
        builder.atPage(pageNo,pageSize);
        builder.orderBy("datachange_lasttime", true);
        return drvDriverRepo.getDao().query(builder, DalHints.createIfAbsent(null));
    }

    @Override
    public Map<Long, DrvDriverPO> getDrvDriverPoMap(List<Long> drvIds) {
        return Optional.ofNullable(getDrvDriverPoList(drvIds)).orElse(Lists.newArrayList()).stream().collect(Collectors.toMap(DrvDriverPO::getDrvId, drvDriverPO -> drvDriverPO));
    }

    @Override
    public List<Long> queryOfficialDriverIdBySupplierIds(List<Long> supplierIds) {
        if (CollectionUtils.isEmpty(supplierIds)) {
            return Lists.newArrayList();
        }
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.select("drv_id");

            builder.and().equal("active", 1, Types.BIT);
            builder.and().in("supplier_id", supplierIds, Types.BIGINT);
            builder.and().equal("temporary_dispatch_mark", TmsTransportConstant.TemporaryDispatchMarkEnum.OFFICIAL.getCode(), Types.TINYINT);
            List<DrvDriverPO> driverPOList = drvDriverRepo.getDao().query(builder, DalHints.createIfAbsent(null));

            return CollectionUtils.isEmpty(driverPOList) ? Lists.newArrayList() :
                    driverPOList.stream()
                            .map(DrvDriverPO::getDrvId)
                            .collect(Collectors.toList());
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryDriverIdListBySupplierIds error", e);
        }
    }

    @Override
    public List<DrvDriverPO> getDrvDriverPoList(List<Long> drvIds) {
        if (CollectionUtils.isEmpty(drvIds)) {
            return Lists.newArrayList();
        }
        return queryDrvList(drvIds);
    }
    
    @Override
    public List<DrvDriverPO> queryFromByPage(long lastDriverId, int pageSize) {
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll().atPage(1, pageSize);
            builder.orderBy("drv_id", true);
            builder.where("drv_id > " + lastDriverId);
            return drvDriverRepo.getDao().query(builder,DalHints.createIfAbsent(null));
        } catch (Exception e) {
            logger.error("queryDrvDirtyPhone error", e);
        }
        return Collections.emptyList();
    }
    
    @Override
    public long countAll() {
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            return drvDriverRepo.getDao().count(builder,DalHints.createIfAbsent(null)).longValue();
        } catch (Exception e) {
            logger.error("queryDrvDirtyPhone error", e);
        }
        return 0L;
    }

}
