package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.PersonRecruitingPO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.PersonRecruitingRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.DalRepositoryImpl;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.KeyHolder;
import com.ctrip.platform.dal.dao.sqlbuilder.SelectSqlBuilder;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.sql.Types;

@Repository(value = "personRecruitingRepository")
public class PersonRecruitingRepositoryImpl implements PersonRecruitingRepository {

    private static final Logger logger = LoggerFactory.getLogger(PersonRecruitingRepositoryImpl.class);

    private DalRepository<PersonRecruitingPO> personRecruitingRepo;

    public PersonRecruitingRepositoryImpl() throws SQLException {
        personRecruitingRepo = new DalRepositoryImpl<>(PersonRecruitingPO.class);
    }

    @Override
    public DalRepository<PersonRecruitingPO> getPersonRecruitingRepo() {
        return personRecruitingRepo;
    }

    @Override
    public Long insert(PersonRecruitingPO po) throws SQLException {
        KeyHolder keyHolder = new KeyHolder();
        personRecruitingRepo.insert(new DalHints(), keyHolder, po);
        return keyHolder.getKey().longValue();
    }

    @Override
    public int checkPhoneCount(String phone) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            builder.equal("phone", phone, Types.VARCHAR);
            return personRecruitingRepo.getDao().count(builder, hints).intValue();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }
}
