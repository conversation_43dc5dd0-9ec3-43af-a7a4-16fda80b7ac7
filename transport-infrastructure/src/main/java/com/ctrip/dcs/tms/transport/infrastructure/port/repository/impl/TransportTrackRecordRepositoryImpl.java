package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;


import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TransportTrackRecordPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupDriverRelationPO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportTrackRecordRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.DalRepositoryImpl;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.KeyHolder;
import com.ctrip.platform.dal.dao.sqlbuilder.SelectSqlBuilder;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.sql.Types;
import java.util.List;

@Repository(value = "transportTrackRecordRepository")
public class TransportTrackRecordRepositoryImpl implements TransportTrackRecordRepository {

    private DalRepository<TransportTrackRecordPO> drvFreezeRecordRepo;

    public TransportTrackRecordRepositoryImpl() throws SQLException {
        drvFreezeRecordRepo = new DalRepositoryImpl<>(TransportTrackRecordPO.class);
    }

    @Override
    public DalRepository<TransportTrackRecordPO> getTransportTrackRecordRepo() {
        return drvFreezeRecordRepo;
    }

    @Override
    public TransportTrackRecordPO queryByPk(Long id) {
        return drvFreezeRecordRepo.queryByPk(id);
    }

    @Override
    public Long insert(TransportTrackRecordPO po) throws SQLException {
        KeyHolder keyHolder = new KeyHolder();
        drvFreezeRecordRepo.insert(new DalHints(), keyHolder, po);
        return keyHolder.getKey().longValue();
    }

    @Override
    public List<TransportTrackRecordPO> queryTrackByTransportGroupId(Long transportGroupId,Integer operationType) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("transport_group_id", transportGroupId, Types.BIGINT);
            builder.and();
            builder.equal("operation_type", operationType, Types.TINYINT);
            builder.and();
            builder.equal("active", true, Types.BIT);
            builder.orderBy("id", false);
           return drvFreezeRecordRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }
}
