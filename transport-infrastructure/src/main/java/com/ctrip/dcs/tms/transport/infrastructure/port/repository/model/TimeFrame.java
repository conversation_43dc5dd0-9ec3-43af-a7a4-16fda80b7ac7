package com.ctrip.dcs.tms.transport.infrastructure.port.repository.model;

import java.time.*;
import java.time.format.*;
import java.util.*;

/**
 * 时段信息
 * <AUTHOR>
 * @Date 2020/11/19 17:27
 */
public class TimeFrame {

    private LocalTime startTime;

    private LocalTime endTime;

    public TimeFrame(LocalTime startTime, LocalTime endTime) {
        this.startTime = startTime;
        this.endTime = endTime;
    }

    public TimeFrame(String startTime, String endTime, DateTimeFormatter dateTimeFormatter) {
        this.startTime = LocalTime.parse(startTime, dateTimeFormatter);
        this.endTime = LocalTime.parse(endTime, dateTimeFormatter);
    }

    public LocalTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalTime startTime) {
        this.startTime = startTime;
    }

    public LocalTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalTime endTime) {
        this.endTime = endTime;
    }

    /**
     * 是否时段重叠（返回true为重叠）
     * @param other
     * @return
     */
    public boolean isOverlap(TimeFrame other){
        if (Objects.isNull(other) || Objects.isNull(other.getStartTime()) || Objects.isNull(other.getEndTime()) || other.getStartTime().compareTo(other.getEndTime()) == 0) {
            return false;
        }

        if (Objects.isNull(this.startTime) || Objects.isNull(this.endTime) || this.startTime.compareTo(this.endTime) == 0) {
            return false;
        }

        boolean currentFlag = this.startTime.compareTo(this.endTime) < 0;
        boolean otherFlag = other.getStartTime().compareTo(other.getEndTime()) < 0;

        if (currentFlag){
            if (otherFlag){
                return !(this.startTime.compareTo(other.getEndTime()) >= 0 || this.endTime.compareTo(other.getStartTime()) <= 0);
            }else {
                return !(this.startTime.compareTo(other.getEndTime()) >= 0 && this.endTime.compareTo(other.getStartTime()) <= 0);
            }
        }else {
            if (otherFlag){
                return !(this.startTime.compareTo(other.getEndTime()) >= 0 && this.endTime.compareTo(other.getStartTime()) <= 0);
            }else {
                return true;
            }
        }
    }

    /**
     * 有时段重叠则则认为相等
     * @param obj
     * @return
     */
    @Override
    public boolean equals(Object obj) {
        if (Objects.isNull(obj)) {
            return false;
        }
        if (!(obj instanceof TimeFrame)) {
            return false;
        }
        TimeFrame other = (TimeFrame) obj;

        return isOverlap(other);
    }

    @Override
    public int hashCode() {
        return 1;
    }
}
