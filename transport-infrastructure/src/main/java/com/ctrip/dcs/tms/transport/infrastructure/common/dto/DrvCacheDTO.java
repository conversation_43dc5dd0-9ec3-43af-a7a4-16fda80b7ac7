package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

import java.io.*;
import java.util.*;

public class DrvCacheDTO implements Serializable {
    //序列化版本号
    private static final long serialVersionUID = 100000L;

    public Long driverId;

    public String driverName;

    public Integer status;

    public Integer coopMode;

    public String driverPhone;

    public String phoneAreaCode;

    public Long carId;

    public String intendVehicleTypeId;

    public Long cityId;

    public String qunarCityCode;

    public String cityName;

    public Long countryId;

    public String countryName;

    public Long supplierId;

    public String supplierName;

    public Double addressLongitude;

    public Double addressLatitude;

    public List<String> workTimes;

    public String driverLanguage;

    public String email;

    public String wechat;

    public String createTime;

    public Integer internalScope;

    public String picUrl;

    public List<Integer> drvProductionLineCodeList;

    public String drvIdcard;

    public String certiDate;

    public String expiryBeginDate;

    public String expiryEndDate;

    public String drvLicenseNumber;

    public Boolean rideHailingDrvCertValid;

    public String drvConnectAddress;

    public String onlineTime;

    public String vehBindTime;

    public String drvcardImg;

    public String nation;

    public Boolean raisingPickUp;

    public Boolean childSeat;

    public String realPicUrl;
    //网约车驾驶证号
    public String driverNetCertNo;

    public Integer temporaryDispatchMark;

    public String temporaryDispatchEndDatetime;

    public String paiayAccount;

    public String paiayEmail;

    public Integer accountType;

    public String ppmAccount;

    public String getPpmAccount() {
        return ppmAccount;
    }

    public void setPpmAccount(String ppmAccount) {
        this.ppmAccount = ppmAccount;
    }

    public String getPaiayAccount() {
        return paiayAccount;
    }

    public void setPaiayAccount(String paiayAccount) {
        this.paiayAccount = paiayAccount;
    }

    public String getPaiayEmail() {
        return paiayEmail;
    }

    public void setPaiayEmail(String paiayEmail) {
        this.paiayEmail = paiayEmail;
    }

    public Integer getAccountType() {
        return accountType;
    }

    public void setAccountType(Integer accountType) {
        this.accountType = accountType;
    }

    public Integer getTemporaryDispatchMark() {
        return temporaryDispatchMark;
    }

    public void setTemporaryDispatchMark(Integer temporaryDispatchMark) {
        this.temporaryDispatchMark = temporaryDispatchMark;
    }

    public String getTemporaryDispatchEndDatetime() {
        return temporaryDispatchEndDatetime;
    }

    public void setTemporaryDispatchEndDatetime(String temporaryDispatchEndDatetime) {
        this.temporaryDispatchEndDatetime = temporaryDispatchEndDatetime;
    }

    public String getRealPicUrl() {
        return realPicUrl;
    }

    public void setRealPicUrl(String realPicUrl) {
        this.realPicUrl = realPicUrl;
    }

    public Boolean getRaisingPickUp() {
        return raisingPickUp;
    }

    public void setRaisingPickUp(Boolean raisingPickUp) {
        this.raisingPickUp = raisingPickUp;
    }

    public Boolean getChildSeat() {
        return childSeat;
    }

    public void setChildSeat(Boolean childSeat) {
        this.childSeat = childSeat;
    }

    public String getDrvcardImg() {
        return drvcardImg;
    }

    public void setDrvcardImg(String drvcardImg) {
        this.drvcardImg = drvcardImg;
    }

    public Long getDriverId() {
        return driverId;
    }

    public void setDriverId(Long driverId) {
        this.driverId = driverId;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCoopMode() {
        return coopMode;
    }

    public void setCoopMode(Integer coopMode) {
        this.coopMode = coopMode;
    }

    public String getDriverPhone() {
        return driverPhone;
    }

    public void setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone;
    }

    public String getPhoneAreaCode() {
        return phoneAreaCode;
    }

    public void setPhoneAreaCode(String phoneAreaCode) {
        this.phoneAreaCode = phoneAreaCode;
    }

    public Long getCarId() {
        return carId;
    }

    public void setCarId(Long carId) {
        this.carId = carId;
    }

    public String getIntendVehicleTypeId() {
        return intendVehicleTypeId;
    }

    public void setIntendVehicleTypeId(String intendVehicleTypeId) {
        this.intendVehicleTypeId = intendVehicleTypeId;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public String getQunarCityCode() {
        return qunarCityCode;
    }

    public void setQunarCityCode(String qunarCityCode) {
        this.qunarCityCode = qunarCityCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Long getCountryId() {
        return countryId;
    }

    public void setCountryId(Long countryId) {
        this.countryId = countryId;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Double getAddressLongitude() {
        return addressLongitude;
    }

    public void setAddressLongitude(Double addressLongitude) {
        this.addressLongitude = addressLongitude;
    }

    public Double getAddressLatitude() {
        return addressLatitude;
    }

    public void setAddressLatitude(Double addressLatitude) {
        this.addressLatitude = addressLatitude;
    }

    public List<String> getWorkTimes() {
        return workTimes;
    }

    public void setWorkTimes(List<String> workTimes) {
        this.workTimes = workTimes;
    }

    public String getDriverLanguage() {
        return driverLanguage;
    }

    public void setDriverLanguage(String driverLanguage) {
        this.driverLanguage = driverLanguage;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getWechat() {
        return wechat;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public Integer getInternalScope() {
        return internalScope;
    }

    public void setInternalScope(Integer internalScope) {
        this.internalScope = internalScope;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public List<Integer> getDrvProductionLineCodeList() {
        return drvProductionLineCodeList;
    }

    public void setDrvProductionLineCodeList(List<Integer> drvProductionLineCodeList) {
        this.drvProductionLineCodeList = drvProductionLineCodeList;
    }

    public String getDrvIdcard() {
        return drvIdcard;
    }

    public void setDrvIdcard(String drvIdcard) {
        this.drvIdcard = drvIdcard;
    }

    public String getCertiDate() {
        return certiDate;
    }

    public void setCertiDate(String certiDate) {
        this.certiDate = certiDate;
    }

    public String getExpiryBeginDate() {
        return expiryBeginDate;
    }

    public void setExpiryBeginDate(String expiryBeginDate) {
        this.expiryBeginDate = expiryBeginDate;
    }

    public String getExpiryEndDate() {
        return expiryEndDate;
    }

    public void setExpiryEndDate(String expiryEndDate) {
        this.expiryEndDate = expiryEndDate;
    }

    public String getDrvLicenseNumber() {
        return drvLicenseNumber;
    }

    public void setDrvLicenseNumber(String drvLicenseNumber) {
        this.drvLicenseNumber = drvLicenseNumber;
    }

    public Boolean getRideHailingDrvCertValid() {
        return rideHailingDrvCertValid;
    }

    public void setRideHailingDrvCertValid(Boolean rideHailingDrvCertValid) {
        this.rideHailingDrvCertValid = rideHailingDrvCertValid;
    }

    public String getDrvConnectAddress() {
        return drvConnectAddress;
    }

    public void setDrvConnectAddress(String drvConnectAddress) {
        this.drvConnectAddress = drvConnectAddress;
    }

    public String getOnlineTime() {
        return onlineTime;
    }

    public void setOnlineTime(String onlineTime) {
        this.onlineTime = onlineTime;
    }

    public String getVehBindTime() {
        return vehBindTime;
    }

    public void setVehBindTime(String vehBindTime) {
        this.vehBindTime = vehBindTime;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getDriverNetCertNo() {
        return driverNetCertNo;
    }

    public void setDriverNetCertNo(String driverNetCertNo) {
        this.driverNetCertNo = driverNetCertNo;
    }
}