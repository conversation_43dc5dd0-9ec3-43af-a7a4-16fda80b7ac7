package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.sql.Timestamp;
import java.util.List;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant.JS_SPLIT;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant.UNICODE.HOUR;

/**
 * <AUTHOR>
 */
@Data
public class LeaveAndFreezeDTO {

  private Timestamp timeFrom;

  private Timestamp timeTo;

  /**
   * 总计小时数
   */
  private long totalHour;

  /**
   * 类型 1 请假;2 冻结;0  所有类型之和（请假+冻结）
   *
   */
  private TmsTransportConstant.LeaveAndFreezeTypeEmue type;

  /**
   * 子项
   */
  List<LeaveAndFreezeDTO> leavItemList = Lists.newArrayList();
  List<LeaveAndFreezeDTO> freezeItemList = Lists.newArrayList();


  public LeaveAndFreezeDTO() {

  }

  public LeaveAndFreezeDTO(Timestamp timeFrom, Timestamp timeTo, long totalHour, TmsTransportConstant.LeaveAndFreezeTypeEmue type) {
    this.timeFrom = timeFrom;
    this.timeTo = timeTo;
    this.totalHour = totalHour;
    this.type = type;
  }



  public void sumTotalHour(long duration) {
    totalHour += duration;
  }

  public String getFreezeDetail() {
    if (CollectionUtils.isEmpty(freezeItemList)) {
      return "";
    }
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append(TmsTransportConstant.UNICODE.FREEZE_DETAIL).append(JS_SPLIT);
    freezeItemList.forEach(item -> {
      stringBuilder.append(DateUtil.getDateAndHourStr(item.getTimeFrom())).append("--").append(DateUtil.getDateAndHourStr(item.timeTo)).append("  ").append(TmsTransportConstant.UNICODE.TOTAL).append(item.getTotalHour()).append(HOUR).append(JS_SPLIT);
    });
    return stringBuilder.toString();
  }

  public String getLeaveDetail() {
    if (CollectionUtils.isEmpty(leavItemList)) {
      return "";
    }
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append(TmsTransportConstant.UNICODE.LEAVE_DETAIL).append(JS_SPLIT);
    leavItemList.forEach(item -> {
      stringBuilder.append(DateUtil.getDateAndHourStr(item.getTimeFrom())).append("--").append(DateUtil.getDateAndHourStr(item.timeTo)).append("  ").append(
        TmsTransportConstant.UNICODE.TOTAL).append(item.getTotalHour()).append(HOUR).append(JS_SPLIT);
    });
    return stringBuilder.toString();
  }
}
