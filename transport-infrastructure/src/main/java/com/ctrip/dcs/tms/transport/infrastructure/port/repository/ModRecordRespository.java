package com.ctrip.dcs.tms.transport.infrastructure.port.repository;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.dal.*;

import java.sql.*;
import java.util.*;

public interface ModRecordRespository<T> {

    DalRepository<TmsModRecordPO> getTmsModRecordRepo();

    /**
     * 插入新的历史变更记录
     *
     * @param rrdId             原始数据id
     * @param newMod            新数据
     * @param recordTypeEnum    类别
     * @param attributeKeyValue 属性名映射字段 例：<属性名,属性展示描述名称> / <name,姓名>
     */
    boolean insetModRecord(Long rrdId, T newMod, CommonEnum.RecordTypeEnum recordTypeEnum, Map<String, String> attributeKeyValue,String modifyUser);

    /**
     * 新增变更记录
     * @param rrdId
     * @param contentList
     * @param recordTypeEnum
     * @param modTypeEnum
     * @return
     */
    boolean insetModRecord(Long rrdId, List<TmsModContent> contentList, CommonEnum.RecordTypeEnum recordTypeEnum, CommonEnum.ModTypeEnum modTypeEnum,String modifyUser);

    /**
     * 查询变更历史记录
     *
     * @param rrdId      原始数据id
     * @param rrdType        类别
     * @param supplierId 供应商id
     */
    List<TmsModRecordPO> queryModRecordList(Long rrdId, Integer rrdType, Long supplierId);

  List<TmsModRecordPO> queryModRecordList(List<Long> rrdIdList, Integer rrdType);

  Long insetRecruitingRecord(Long rrdId, List<TmsModContent> contentList, CommonEnum.RecordTypeEnum recordTypeEnum, CommonEnum.ModTypeEnum modTypeEnum,String modifyUser) throws SQLException;

    /**
    　* @description: 变更记录分页查询
    　* <AUTHOR>
    　* @date 2022/12/7 15:03
    */
    List<TmsModRecordPO> queryModRecordListPage(Long rrdId, Integer rrdType,int pageNo,int pageSize);

    int queryModRecordListCount(Long rrdId, Integer rrdType);

}
