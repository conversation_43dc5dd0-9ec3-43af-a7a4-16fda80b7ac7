package com.ctrip.dcs.tms.transport.infrastructure.port.repository;


import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.dal.*;

import java.sql.*;
import java.util.*;

public interface DrvRecruitingRepository {

    DalRepository<DrvRecruitingPO> getDrvRecruitingRepo();

    DrvRecruitingPO queryByPK(Long drvRecruitingId);

    int update(DrvRecruitingPO drvRecruitingPO);

    /**
     * 统计手机号记录数量
     * @param drvPhone
     * @return
     */
    DrvRecruitingPO queryOneByRecruitingPhone(String drvPhone);
    /**
     * 新增司机审批
     *
     * @param po
     * @return
     */
    Long addDrvRecruiting(DrvRecruitingPO po) throws SQLException;

    /**
     * 正式车辆信息变更后，同步审批中司机绑定的车辆信息
     * @param vehicleId
     * @param vehicleTypeId
     * @param vehicleLicense
     * @return
     */
    Integer syncDrvFromVeh(Long vehicleId, Long vehicleTypeId,String vehicleLicense,String modifyUser) throws SQLException;

    /**
     * 批量修改司机审批状态
     * @return
     */
    Integer updateDrvRecApproverStatus(List<Long> drvRecruitingIds,Integer approverStatus, String remark, String modifyUser,Integer checkStatus,Boolean approveTimeFlag,Integer bdTurnDownCount) throws SQLException;

    /**
     * 查询车辆id 根据来源查询
     * 如果为自助 查询车辆审批id
     * 如果为人工 查询车辆id
     * */
    List<Long> getVehicleIdList(List<Long> drvRecruitingIds, int drvFrom) throws SQLException;

    /**
     * 查询审批表
     * */
    List<DrvRecruitingPO> getDrvRecruitingList(List<Long> drvRecruitingIds) throws SQLException;

    /**
     * 校验唯一性
     * @param str
     * @param type
     * @return
     */
    Boolean checkDrvOnly(String str, Integer type,List<Integer>  approverStatus) throws SQLException;

    List<DrvRecruitingPO> queryDrvRecruitingBySupplierIdAndId(List<Long> drvRecruitingIds,Long supplierId);

    int updateCheckStatus(List<Long> drvRecruitingIds,int checkStatus) throws SQLException;

    /**
     * <AUTHOR>
     * @Description  正式车辆从工作台招募司机中解绑
     * @Date 9:41 2020/12/29
     * @Param [vehicleid]
     * @return int
     **/
    int unBingVehicleFromDrvRecruiting(List<Long> vehicleid,String modifyUser);

    List<TodoListCountPO> todoListCount(Long supplierId);

    int countApproveIngDrvRecruiting(List<Long> drvIds,Integer approveStatus,Integer checkStatus);

    List<DrvRecruitingPO> queryApproveIngDrvRecruiting(List<Long> drvIds, Integer approveStatus, Integer checkStatus);

    int updateApproveAging(List<Long> drvRecruitingIds,int approveAging) throws SQLException;

    List<DrvRecruitingPO> queryvRecruitingByVehicleId(Long vehicleId);

    int updateDrvCheckStatus(Long recruitingId,int checkStatus,String modifyUser) throws SQLException;


    /**
     * 校验司机唯一性
     * @param str
     * @param type
     * @return
     */
    Boolean checkRecruitingDrvOnly(String str, Integer type) throws SQLException;

    List<DrvRecruitingPO> queryRecruitingDrvByPhone(String drvPhone,Long recruitingId) throws SQLException;

    List<DrvRecruitingPO> queryDomesticRecruitingDrvByPhone(String drvPhone) throws SQLException;

    int updateDrvApproveSchedule(Long recruitingId,Integer accountType,Integer approveSchedule) throws SQLException;

    List<DrvRecruitingPO> queryWaitApproveRecruitingDrvByPage(int pageNo,int pageSize);

    int countWaitApproveRecruitingDrv();

    int updateApproveAging(Long recruitingId) throws SQLException;

    Long findNewDrvRecruitingCount(RecruitingSOARequestDTO recruitingSOARequestDTO, List<Integer> jurisdictionIdList, List<Integer> lineCodeList,Integer accountType) throws SQLException;

    List<DrvRecruitingPO> queryDriverRecruitingInfo(String driverPhone,String driverIdCard,String vehicleLicense,List<Long> drvRecruitingIds);

    /**
    　* @description: 同步废弃招募司机
    　* <AUTHOR>
    　* @date 2022/8/17 14:43
    */
    int syncDiscardvRecruitingDrv(Long drvId,String drvPhone,String drvIdCard,String modifyUser,Boolean active);

    int discardRecruitingDrv(Long recruitingId,Boolean active,String modifyUser);

    List<DrvRecruitingPO> queryvRecruitingByVehicleIds(List<Long> vehicleIds);

    List<DrvRecruitingPO> queryRecruitingDrvByPhoneORId(String drvPhone,Long recruitingId) throws SQLException;

    List<Long> findDrvRecruitingIdList(RecruitingSOARequestDTO recruitingSOARequestDTO, PaginatorDTO paginator, List<Integer> lineCodeList,Integer accountType) throws SQLException;

    List<DrvRecruitingPO> queryDrvRecruitingListByIds(List<Long> drvRecruitingIds) throws SQLException;

    int updateDrvImgQToC(DrvRecruitingPO drvRecruitingPO);

    int countDrvDirtyPhone();

    List<DrvRecruitingPO> queryDrvDirtyPhone(int pageNo, int pageSize);
    
    List<DrvRecruitingPO> queryFromByPage(long lastDriverId, int pageSize);
    
    long countAll();
}
