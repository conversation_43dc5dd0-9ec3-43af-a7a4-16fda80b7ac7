package com.ctrip.dcs.tms.transport.infrastructure.common.udl.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DrvUdlMethodRuleDTO {

  /**
   * 方法类型: update/query
   */
  private String methodType;
  /**
   *  校验比例
   */
    private Integer percentage;
  /**
   * udl校验的key
   */
  private String udlKey;
  /**
   * key的类型：cityId/drvId/page
   */
  private String keyType;

  /**
   * string, Long
   */
  private String keyValueType;
}
