package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

import lombok.Data;

@Data
public class DriverVehicleInfoDTO {
    private String driverId;
    private String name;
    private Integer gender;
    private Integer age;
    private String idCardNo;
    private Integer hasDevice;
    private String driverFirstRegisterAt;
    private Integer orderAmount;
    private Float companyEvaluation;
    private String vehicleId;
    private String vehicleBindingAt;
    private String registerAt;
    private String plateNo;
    private String vehicleBrand;
    private String vehicleModel;
    private String vehicleRegisterAt;
    private String vehicleColor;
    private String vehicleSn;
    private String vehicleMotorSn;
}
