package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TmsDrvInactiveReasonPO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsDrvInactiveReasonRepository;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.base.DalTableOperations;
import com.ctrip.platform.dal.dao.base.SQLArg;
import com.ctrip.platform.dal.dao.base.SQLArgList;
import com.ctrip.platform.dal.dao.client.DalOperationsFactory;
import com.ctrip.platform.dal.dao.sqlbuilder.SelectSqlBuilder;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.sql.Types;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class TmsDrvInactiveReasonRepositoryImpl implements TmsDrvInactiveReasonRepository {

  private final DalTableOperations<TmsDrvInactiveReasonPO> dal;

  public TmsDrvInactiveReasonRepositoryImpl() {
    this.dal = DalOperationsFactory.getDalTableOperations(TmsDrvInactiveReasonPO.class);
  }

  @SneakyThrows
  @Override
  public int insert(TmsDrvInactiveReasonPO po) {
    return this.dal.insert(new DalHints(), po);
  }

  @SneakyThrows
  @Override
  public List<TmsDrvInactiveReasonPO> query(List<Long> drvIdList) {
    try {

      if (CollectionUtils.isEmpty(drvIdList)) {
        return Lists.newArrayList();
      }
      StringBuilder sql = new StringBuilder();
      SQLArgList args = SQLArg.list();
      sql.append("select * from tms_drv_inactive_reason ");

      sql.append(" where drv_id in (?) ");
      args.add(drvIdList);
      sql.append(" and active = ? ");
      args.add(true);

      return dal.query(sql.toString(), DalHints.createIfAbsent(null), args);
    } catch (Exception e) {
      return Lists.newArrayList();
    }
  }

  @SneakyThrows
  @Override
  public int deleteReason(Long drvId, String modifyUser) {
    return this.dal.update(new DalHints(), TmsDrvInactiveReasonPO.builder().drvId(drvId).active(false).modifyUser(modifyUser).build());
  }

  @SneakyThrows
  @Override
  public int deleteReasonByCode(Long drvId, List<Integer> reasonCode, String modifyUser) {
    try {
      StringBuilder sql = new StringBuilder();
      SQLArgList args = SQLArg.list();
      sql.append("update tms_drv_inactive_reason set active = ?, modify_user = ? ");
      args.add(false);
      args.add(modifyUser);
      sql.append(" where drv_id = ? ");
      args.add(drvId);
      sql.append(" and reason_code in (?) ");
      args.add(reasonCode);
      sql.append(" and active = ? ");
      args.add(true);

      return dal.update(sql.toString(), DalHints.createIfAbsent(null), args);
    } catch (Exception e) {
      return 0;
    }
  }
}
