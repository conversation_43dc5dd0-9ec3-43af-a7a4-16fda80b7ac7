package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;


import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.ctriposs.baiji.exception.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.stereotype.*;

import java.sql.*;
import java.util.*;

@Repository(value = "tmsBackgroundChecksRepository")
public class TmsBackgroundChecksRepositoryImpl implements TmsBackgroundChecksRepository {

    private DalRepository<TmsBackgroundChecksPO> checkPORepo;

    public TmsBackgroundChecksRepositoryImpl() throws SQLException {
        checkPORepo = new DalRepositoryImpl<>(TmsBackgroundChecksPO.class);
    }


    @Override
    public int batchInsertCheckRecord(List<TmsBackgroundChecksPO> checksPOList) {
        if(CollectionUtils.isEmpty(checksPOList)){
            return 0;
        }
        int[] count =  checkPORepo.batchInsert(checksPOList);
        if(count.length > 0){
            return count.length;
        }
        return 0;
    }

    @Override
    public List<TmsBackgroundChecksPO> queryBackgroundByPerson(String personId) {
        if(StringUtils.isEmpty(personId)){
            return Collections.emptyList();
        }
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("person_id",personId, Types.VARCHAR);
            builder.orderBy("datachange_lasttime", false);
            return checkPORepo.getDao().query(builder,hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TmsBackgroundChecksPO> queryBackgroundByPersons(List<String> personId) {
        if(CollectionUtils.isEmpty(personId)){
            return Collections.emptyList();
        }
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("person_id",personId, Types.VARCHAR);
            builder.orderBy("datachange_lasttime", false);
            return checkPORepo.getDao().query(builder,hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }
}
