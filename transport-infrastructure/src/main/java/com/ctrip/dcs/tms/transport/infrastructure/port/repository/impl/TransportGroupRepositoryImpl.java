package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.handler.cache.impl.TransportGroupCacheHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.thread.ThreadPoolService;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
//import com.ctrip.frt.xresource.framework.utility.redis.ResourceFillDataAction;
import com.ctrip.frt.xresource.framework.utility.redis.ResourceFillDataAction;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.helper.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.ctriposs.baiji.exception.*;
import com.google.common.base.*;
import com.google.common.collect.*;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.*;
import org.springframework.util.*;

import java.sql.*;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 运力组信息
 * <AUTHOR>
 * @Date 2020/3/3 16:28
 */
@Repository(value = "transportGroupRepository")
public class TransportGroupRepositoryImpl implements TransportGroupRepository {

    private DalRepository<TspTransportGroupPO> tspTransportGroupRepo;

    private static final String DEFAULT_TABLENAME = "tsp_transport_group";
    private static final String DATA_BASE = "dcstransportdb_w";

    private DalRowMapper<TspTransportGroupPO> tspTransportGroupPORowMapper;

    @Autowired
    ThreadPoolService threadPoolService;

    @Autowired
    TmsTransportQconfig tmsTransportQconfig;

    public TransportGroupRepositoryImpl() throws SQLException {
        tspTransportGroupRepo = new DalRepositoryImpl<>(TspTransportGroupPO.class);
        this.tspTransportGroupPORowMapper = new DalDefaultJpaMapper<>(TspTransportGroupPO.class);
    }

    @Override
    public DalRepository<TspTransportGroupPO> getTspTransportGroupRepo() {
        return tspTransportGroupRepo;
    }

    /**
     * 新增运力组
     * @param transportGroupPO
     * @return
     */
    @Override
    public long addTransportGroup(TspTransportGroupPO transportGroupPO){
        try {
            if (transportGroupPO == null) {
                return 0;
            }
            DalHints hints = DalHints.createIfAbsent(null);
            KeyHolder keyHolder = new KeyHolder();
            int count = tspTransportGroupRepo.getDao().insert(hints, keyHolder, transportGroupPO);
            if (count <= 0) {
                throw new RuntimeException(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgAddError));
            }
            return keyHolder.getKey().longValue();
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 更新运力组
     * @param transportGroupPO
     * @return
     */
    @Override
    public void updateTransportGroup(TspTransportGroupPO transportGroupPO){
        try {
            if (transportGroupPO == null) {
                return;
            }
            DalHints hints = DalHints.createIfAbsent(null);
            KeyHolder keyHolder = new KeyHolder();
            tspTransportGroupRepo.getDao().update(hints, transportGroupPO);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 查询运力组详情
     * @param transportGroupId
     * @return
     */
    @Override
    public TspTransportGroupPO queryTransportGroupDetail(Long transportGroupId){
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            return tspTransportGroupRepo.getDao().queryByPk(transportGroupId,hints);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    @Override
    public Long countTransportGroupList(QueryTransportGroupListParam param) {
        try {
            TspTransportGroupPO po = param.getPo();
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<Long> sqlBuilder = new FreeSelectSqlBuilder<>();
            sqlBuilder.simpleType().requireSingle().nullable();
            sqlBuilder.setTemplate("select count(1) from tsp_transport_group").where("1=1");
            sqlBuilder.and().equal("transport_group_id",po.getTransportGroupId(),Types.BIGINT).ignoreNull();
            sqlBuilder.and().equal("supplier_id",po.getSupplierId(),Types.BIGINT).ignoreNull();
            sqlBuilder.and().equal("transport_group_mode",po.getTransportGroupMode(),Types.INTEGER).ignoreNull();
            sqlBuilder.and().equal("group_status",po.getGroupStatus(),Types.SMALLINT).ignoreNull();
            if (!Strings.isNullOrEmpty(po.getTransportGroupName())) {
                sqlBuilder.and().like("transport_group_name","%"+po.getTransportGroupName()+"%",Types.VARCHAR);
            }
            if (!Strings.isNullOrEmpty(po.getDispatcherLanguage())) {
                sqlBuilder.and().like("dispatcher_language","%"+po.getDispatcherLanguage()+"%",Types.VARCHAR);
            }
            if (po.getPointCityId()!=null && po.getPointCityId() > 0 ) {
                sqlBuilder.and().equal("point_city_id",po.getPointCityId(),Types.BIGINT);
            }
            if (po.getSalesModel()!=null) {
                sqlBuilder.and().equal("sales_model",po.getSalesModel(),Types.INTEGER);
            }
            if (!CollectionUtils.isEmpty(param.getContractIdList())) {
                sqlBuilder.and().in("contract_id", param.getContractIdList(), Types.BIGINT);
            }
            if (!CollectionUtils.isEmpty(param.getTransportGroupIdList())) {
                sqlBuilder.and().in("transport_group_id", param.getTransportGroupIdList(), Types.BIGINT);
            }
            if (!CollectionUtils.isEmpty(param.getSupplierIdList())) {
                if(!CollectionUtils.isEmpty(param.getSalfSupplierCityList())){
                    sqlBuilder
                        .and()
                            .leftBracket()
                                .in("supplier_id", param.getSupplierIdList(), Types.BIGINT)
                                .or()
                                .leftBracket()
                                    .in("point_city_id",param.getSalfSupplierCityList(),Types.BIGINT)
                                    .and().equal("transport_group_mode", TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode(),Types.INTEGER)
                                .rightBracket()
                            .rightBracket();
                }else {
                    sqlBuilder.and().in("supplier_id", param.getSupplierIdList(), Types.BIGINT);
                }
            }
            if (!CollectionUtils.isEmpty(param.getVehicleTypeIdList())) {
                sqlBuilder.and().in("vehicle_type_id", param.getVehicleTypeIdList(), Types.BIGINT);
            }
            if (po.getCategorySynthesizeCode() != null) {
                sqlBuilder.and().equal("category_synthesize_code", po.getCategorySynthesizeCode(), Types.INTEGER);
            }
            if (po.getShortTransportGroup() != null) {
                sqlBuilder.and().equal("short_transport_group", po.getShortTransportGroup(), Types.TINYINT);
            }

            return tspTransportGroupRepo.getQueryDao().query(sqlBuilder, hints).longValue();
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 查询运力组列表
     * @param param
     * @return
     */
    @Override
    public List<TspTransportGroupPO> queryTransportGroupList(QueryTransportGroupListParam param) {
        try {
            TspTransportGroupPO po = param.getPo();
            PaginatorDTO pageInfo = param.getPageInfo();
            if (param.getPo() == null) {
                return Lists.newArrayList();
            }

            DalHints hints = DalHints.createIfAbsent(null);

            FreeSelectSqlBuilder<List<TspTransportGroupPO>> sqlBuilder = new FreeSelectSqlBuilder<>();
            sqlBuilder.mapWith(this.tspTransportGroupPORowMapper)
                    .selectAll().from(DEFAULT_TABLENAME)
                    .where("1=1");
            sqlBuilder.and().equal("transport_group_id",po.getTransportGroupId(),Types.BIGINT).ignoreNull();
            sqlBuilder.and().equal("supplier_id",po.getSupplierId(),Types.BIGINT).ignoreNull();

            sqlBuilder.and().equal("transport_group_mode",po.getTransportGroupMode(),Types.INTEGER).ignoreNull();
            sqlBuilder.and().equal("group_status",po.getGroupStatus(),Types.SMALLINT).ignoreNull();
            if (!Strings.isNullOrEmpty(po.getTransportGroupName())) {
                sqlBuilder.and().like("transport_group_name","%"+po.getTransportGroupName()+"%",Types.VARCHAR);
            }
            if (!Strings.isNullOrEmpty(po.getDispatcherLanguage())) {
                sqlBuilder.and().like("dispatcher_language","%"+po.getDispatcherLanguage()+"%",Types.VARCHAR);
            }
            if (po.getPointCityId()!=null && po.getPointCityId() > 0 ) {
                sqlBuilder.and().equal("point_city_id",po.getPointCityId(),Types.BIGINT);
            }
            if (po.getSalesModel()!=null) {
                sqlBuilder.and().equal("sales_model",po.getSalesModel(),Types.INTEGER);
            }
            if (!CollectionUtils.isEmpty(param.getContractIdList())) {
                sqlBuilder.and().in("contract_id", param.getContractIdList(), Types.BIGINT);
            }
            if (!CollectionUtils.isEmpty(param.getTransportGroupIdList())) {
                sqlBuilder.and().in("transport_group_id", param.getTransportGroupIdList(), Types.BIGINT);
            }
            if (!CollectionUtils.isEmpty(param.getSupplierIdList())) {
                if(!CollectionUtils.isEmpty(param.getSalfSupplierCityList())){
                    sqlBuilder
                        .and()
                            .leftBracket()
                                .in("supplier_id", param.getSupplierIdList(), Types.BIGINT)
                                .or()
                                .leftBracket()
                                    .in("point_city_id",param.getSalfSupplierCityList(),Types.BIGINT)
                                    .and().equal("transport_group_mode", TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode(),Types.INTEGER)
                                .rightBracket()
                            .rightBracket();
                }else {
                    sqlBuilder.and().in("supplier_id", param.getSupplierIdList(), Types.BIGINT);
                }
            }
            if (!CollectionUtils.isEmpty(param.getVehicleTypeIdList())) {
                sqlBuilder.and().in("vehicle_type_id", param.getVehicleTypeIdList(), Types.BIGINT);
            }
            if (po.getCategorySynthesizeCode() != null) {
                sqlBuilder.and().equal("category_synthesize_code", po.getCategorySynthesizeCode(), Types.INTEGER);
            }
            if (po.getShortTransportGroup() != null) {
                sqlBuilder.and().equal("short_transport_group", po.getShortTransportGroup(), Types.TINYINT);
            }

            sqlBuilder.orderBy("datachange_lasttime", false);
            if (pageInfo != null) {
                sqlBuilder.atPage(pageInfo.getPageNo(), pageInfo.getPageSize());
            }

            return tspTransportGroupRepo.getQueryDao().query(sqlBuilder, hints);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<TspTransportGroupPO> queryTransportGroupList(Long cityId, Long vehicleTypeId, Integer transportGroupMode) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<TspTransportGroupPO>> sqlBuilder = new FreeSelectSqlBuilder<>();
            sqlBuilder.mapWith(this.tspTransportGroupPORowMapper)
                    .selectAll().from(DEFAULT_TABLENAME)
                    .where("1=1");
            sqlBuilder.and().equal("vehicle_type_id",vehicleTypeId,Types.BIGINT).ignoreNull();
            if(transportGroupMode!=null && transportGroupMode >0){
                sqlBuilder.and().equal("transport_group_mode",transportGroupMode,Types.INTEGER).ignoreNull();
            }
            sqlBuilder.and().equal("point_city_id",cityId,Types.BIGINT).ignoreNull();
            return tspTransportGroupRepo.getQueryDao().query(sqlBuilder, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    /**
     * 查询运力组信息
     * @param queryModel
     * @return
     */
    @Override
    public List<TspTransportGroupPO> queryTransportGroups(QueryTransportGroupModel queryModel){
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<TspTransportGroupPO>> sqlBuilder = new FreeSelectSqlBuilder<>();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("select tg.* from tsp_transport_group tg where 1=1");
            if (queryModel.getTransportGroupId() != null) {
                sqlStr.append(" and tg.transport_group_id = ?");
                parameters.set(i++,"tg.transport_group_id", Types.BIGINT,queryModel.getTransportGroupId());
            }
            if (queryModel.getTransportGroupMode() != null) {
                sqlStr.append(" and tg.transport_group_mode = ?");
                parameters.set(i++,"tg.transport_group_mode", Types.INTEGER,queryModel.getTransportGroupMode());
            }
            if (queryModel.getSupplierId() != null) {
                sqlStr.append(" and tg.supplier_id = ?");
                parameters.set(i++,"tg.supplier_id", Types.BIGINT,queryModel.getSupplierId());
            }
            if (queryModel.getGroupStatus() != null) {
                sqlStr.append(" and tg.group_status = ?");
                parameters.set(i++,"tg.group_status", Types.SMALLINT,queryModel.getGroupStatus());
            }
            if (queryModel.getSkuId() != null) {
                sqlStr.append(" and tg.transport_group_id in (select distinct tgsar.transport_group_id from tsp_transport_group_sku_area_relation tgsar where tgsar.active=1 and tgsar.sku_id = ?)");
                parameters.set(i++,"tgsar.sku_id", Types.BIGINT,queryModel.getSkuId());
            }
            sqlBuilder.setTemplate(sqlStr.toString());
            sqlBuilder.mapWith(this.tspTransportGroupPORowMapper);
            return tspTransportGroupRepo.getQueryDao().query(sqlBuilder,parameters, hints);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<TspTransportGroupPO> queryTspTransportByIds(List<Long> transportIds) throws SQLException{
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        builder.in("transport_group_id",transportIds,Types.BIGINT,false);
        return tspTransportGroupRepo.getDao().query(builder,hints);
    }

    @Override
    public List<TspTransportGroupPO> queryAllTransportGroup(QueryAllTransGroupListDO listDO) throws SQLException{
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.select("transport_group_id","transport_group_name","transport_group_mode","group_status");
        if(listDO.getTransportGroupId()!=null && listDO.getTransportGroupId() >= 0 ){
            builder.equal("transport_group_id",listDO.getTransportGroupId(),Types.BIGINT);
        }
        if(!StringUtils.isEmpty(listDO.getTransportGroupName())){
            builder.and();
            builder.like("transport_group_name","%"+listDO.getTransportGroupId()+"%",Types.BIGINT);
        }
        if(listDO.getSupplierId()!=null && listDO.getSupplierId() >= 0){
            builder.and();
            builder.equal("supplier_id",listDO.getSupplierId(),Types.BIGINT);
        }
        return tspTransportGroupRepo.getDao().query(builder,hints);
    }

    @Override
    public List<TspTransportGroupPO> queryTransGByContractAndCity(Long contractId) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.select("transport_group_id","group_status","point_city_id","status_disable","category_synthesize_code");
        builder.equalNullable("contract_id",contractId,Types.BIGINT);
        return tspTransportGroupRepo.getDao().query(builder,hints);
    }

    @Override
    public int updateStatusDisable(List<Long> transportIds, Integer statusDisable) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        builder.setTemplate("update tsp_transport_group set status_disable = ? where transport_group_id in (?)");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.setSensitive(i++, "status_disable", Types.INTEGER, statusDisable);
        parameters.setInParameter(i++, "transport_group_id", Types.BIGINT, transportIds);
        return tspTransportGroupRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public int queryTransportNameCount(String transportName, Long transportGroupId) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<Long> sqlBuilder = new FreeSelectSqlBuilder<>();
            sqlBuilder.simpleType().requireSingle().nullable();
            sqlBuilder.setTemplate("SELECT COUNT(*) FROM tsp_transport_group").where("1 = 1");
            sqlBuilder.and().equal("transport_group_name", transportName, Types.VARCHAR).ignoreNull();
            sqlBuilder.and().notEqual("transport_group_id", transportGroupId, Types.BIGINT).ignoreNull();
            return tspTransportGroupRepo.getQueryDao().query(sqlBuilder, hints).intValue();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<TspTransportGroupPO> queryTransportGroupBySupplierIdAndId(List<Long> transportIds, Long supplierId) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("transport_group_id",transportIds,Types.BIGINT,false);
            if(supplierId !=null && supplierId >0){
                builder.and().equal("supplier_id", supplierId, Types.BIGINT);
            }
            return tspTransportGroupRepo.getDao().query(builder,hints);
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @SneakyThrows
    @Override
    public List<TspTransportGroupPO> queryTspTransportBaseByIds(List<Long> transportIds) {

        if (!tmsTransportQconfig.getInQueryBatchSwitch()) {
            return getTspTransportGroupPOS(transportIds.stream().distinct().collect(
              Collectors.toList()));
        }

        List<List<Long>> partitionedTransportGroupIdList = Lists.partition(transportIds.stream().distinct().collect(
          Collectors.toList()), tmsTransportQconfig.getInQueryBatchSize());
        List<CompletableFuture<List<TspTransportGroupPO>>> futures = partitionedTransportGroupIdList.stream()
          .map(groupIdList -> CompletableFuture.supplyAsync(() -> getTspTransportGroupPOS(groupIdList), threadPoolService.getInQueryTransportPool()))
          .collect(Collectors.toList());

        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[futures.size()]));

        CompletableFuture<List<TspTransportGroupPO>> combinedFuture = allFutures.thenApply(v ->
          futures.stream()
            .map(CompletableFuture::join)
            .flatMap(List::stream)
            .collect(Collectors.toList())
        );

        return combinedFuture.get();

    }

    private List<TspTransportGroupPO> getTspTransportGroupPOS(List<Long> transportIds) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.select("transport_group_id", "transport_group_mode","transport_group_name","group_status","take_order_limit_time","area_group_id","dispatch_only");
            builder.in("transport_group_id", transportIds, Types.BIGINT, false);
            builder.and().equal("group_status",0,Types.SMALLINT,false);
            return tspTransportGroupRepo.getDao().query(builder, hints);
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TspTransportGroupPO> queryTransportGroupBMZByIds(List<Long> transportIds, Integer transportGroupMode, int beginPage, int pageSize) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            if(!CollectionUtils.isEmpty(transportIds)){
                builder.in("transport_group_id",transportIds,Types.BIGINT,false);
            }
            builder.and().equal("transport_group_mode", transportGroupMode, Types.INTEGER);
            builder.and().equal("group_status",0,Types.SMALLINT,false);
            builder.atPage(beginPage,pageSize);
            return tspTransportGroupRepo.getDao().query(builder,hints);
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int countTransportGroupBMZByIds(List<Long> transportIds, Integer transportGroupMode) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            if(!CollectionUtils.isEmpty(transportIds)){
                builder.in("transport_group_id",transportIds,Types.BIGINT,false);
            }
            builder.and().equal("transport_group_mode", transportGroupMode, Types.INTEGER);
            builder.and().equal("group_status",0,Types.SMALLINT,false);
            return tspTransportGroupRepo.getDao().count(builder,hints).intValue();
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TspTransportGroupPO> queryDriverRelatedTransportGroupByModeList(List<Long> transportIds, List<Integer> transportGroupModes) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.select("transport_group_id", "transport_group_name", "transport_group_mode", "category_synthesize_code");
            builder.in("transport_group_id", transportIds, Types.BIGINT, false);
            if (CollectionUtils.isNotEmpty(transportGroupModes)) {
                builder.and().in("transport_group_mode", transportGroupModes, Types.INTEGER);
            }
            return tspTransportGroupRepo.getDao().query(builder, hints);
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TspTransportGroupPO> queryApplyTransportGroupByCityIds(List<Long> cityIds, List<Long> vehicleTypeIds) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("point_city_id",cityIds,Types.BIGINT,false);
            builder.and().in("vehicle_type_id", vehicleTypeIds, Types.BIGINT,false);
            builder.and().equal("group_status", TmsTransportConstant.TransportGroupStatusEnum.ONLINE.getCode(),Types.SMALLINT,false);
            builder.and().equal("transport_group_mode", TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode(),Types.INTEGER,false);
            builder.and().equal("category_synthesize_code", 1,Types.INTEGER,false);
            return tspTransportGroupRepo.getDao().query(builder,hints);
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int queryCountTransportGroupAll(Long id) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            if(id != null){
                builder.equal("transport_group_id",id,Types.BIGINT,false);
            }
            return tspTransportGroupRepo.getDao().count(builder,hints).intValue();
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TspTransportGroupPO> queryTransportGroupAll(Long id, int beginPage, int pageSize) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            if(id != null){
                builder.equal("transport_group_id",id,Types.BIGINT,false);
            }
            builder.atPage(beginPage,pageSize);
            return tspTransportGroupRepo.getDao().query(builder,hints);
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateTransportGroupCountryId(Long transportId, String countryid) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        builder.setTemplate("update tsp_transport_group set country_id = ? where transport_group_id = ?");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.setSensitive(i++, "country_id", Types.VARCHAR, countryid);
        parameters.setSensitive(i++, "transport_group_id", Types.BIGINT, transportId);
        return tspTransportGroupRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public List<TspTransportGroupPO> queryProductLine(List<Long> transportGroupIds) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.select("transport_group_id","category_synthesize_code","supplier_id","take_order_limit_time","dispatch_only");
            builder.in("transport_group_id", transportGroupIds, Types.BIGINT, false);
            builder.and().equal("group_status", TmsTransportConstant.TransportGroupStatusEnum.ONLINE.getCode(),Types.SMALLINT,false);
            return tspTransportGroupRepo.getDao().query(builder, hints);
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TspTransportGroupPO> querySupplierId(List<Long> transportGroupIds) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.select("transport_group_id","supplier_id","inform_email", "point_city_id");
            builder.in("transport_group_id", transportGroupIds, Types.BIGINT, false);
            builder.and().equal("group_status", TmsTransportConstant.TransportGroupStatusEnum.ONLINE.getCode(),Types.SMALLINT,false);
            return tspTransportGroupRepo.getDao().query(builder, hints);
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TspTransportGroupPO> queryGroupBySupplierAndCity(List<Long> supplierIds, List<Long> cityIds) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("supplier_id", supplierIds, Types.BIGINT, false);
            builder.and().in("point_city_id", cityIds, Types.BIGINT, false);
            builder.and().equal("group_status", TmsTransportConstant.TransportGroupStatusEnum.ONLINE.getCode(),Types.SMALLINT,false);
            return tspTransportGroupRepo.getDao().query(builder, hints);
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TspTransportGroupPO> queryTransportGroupByIdFromAndPage(Long transportGroupIdFrom, String beginDate, String endDate, int pageNo, int pageSize) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        if (transportGroupIdFrom != null && transportGroupIdFrom > 0) {
            builder.and().greaterThan("transport_group_id", transportGroupIdFrom, Types.BIGINT);
        }
        if (StringUtils.hasText(beginDate)) {
            builder.and().greaterThanEquals("datachange_lasttime", beginDate, Types.TIMESTAMP);
        }
        if (StringUtils.hasText(endDate)) {
            builder.and().lessThanEquals("datachange_lasttime", endDate, Types.TIMESTAMP);
        }
        builder.atPage(pageNo, pageSize);
        builder.orderBy("transport_group_id", true);
        return tspTransportGroupRepo.getDao().query(builder, hints);
    }
}
