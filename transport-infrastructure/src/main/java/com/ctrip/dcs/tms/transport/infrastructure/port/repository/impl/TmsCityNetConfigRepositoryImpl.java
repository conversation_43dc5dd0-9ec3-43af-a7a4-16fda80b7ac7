package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.ctriposs.baiji.exception.*;
import org.springframework.stereotype.*;

import java.sql.*;
import java.util.*;

/**
 *
 */
@Repository(value = "tmsCityNetConfigRepository")
public class TmsCityNetConfigRepositoryImpl implements TmsCityNetConfigRepository {

    private DalRepository<TmsCityNetConfigPO> tmsCityNetConfigRepo;

    public TmsCityNetConfigRepositoryImpl() throws SQLException {
        tmsCityNetConfigRepo = new DalRepositoryImpl<>(TmsCityNetConfigPO.class);

    }

    @Override
    public DalRepository<TmsCityNetConfigPO> getTmsCityNetConfigRepo() {
        return tmsCityNetConfigRepo;
    }

    @Override
    public TmsCityNetConfigPO queryByPk(Long id) {
        return tmsCityNetConfigRepo.queryByPk(id);
    }

    @Override
    public List<TmsCityNetConfigPO> queryConfigList(Long cityId, Integer configItem, Integer configType) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("city_id", cityId, Types.BIGINT);
            if (configItem != null) {
                builder.and().equal("config_item", configItem, Types.INTEGER);
            }
            if (configType != null) {
                builder.and().equal("config_type", configType, Types.INTEGER);
            }
            return tmsCityNetConfigRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int countConfigByCity(Long cityId, Integer configItem, Integer configType) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            builder.equal("city_id", cityId, Types.BIGINT);
            builder.and().equal("config_item", configItem, Types.INTEGER);
            builder.and().equal("config_type", configType, Types.INTEGER);
            return tmsCityNetConfigRepo.getDao().count(builder, hints).intValue();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

}
