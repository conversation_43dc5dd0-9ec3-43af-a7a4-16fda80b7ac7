package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;

/**
 *  新增司机核验
 */
public class AddDrvCertificateCheckDTO {

    private Long checkId;

    private Integer checkType;

    private Integer certificateType;

    /**
     * 身份证号
     */
    private String drvIdcard;

    /**
     * 姓名
     */
    private String drvName;

    // 初次领证日期
    private String firstDrvLicenseDate;

    //有效结束日期
    private String expiryEndDate;

    //驾驶证图片
    private String newDrvcardImg;

    private String userName;

    //网约车驾驶证
    private String netVehiclePeoImg;

    public static AddDrvCertificateCheckDTO buildDrvDTO(DrvDriverPO newDriverPO, DrvDriverPO orgDriverPO){
        AddDrvCertificateCheckDTO auditDTO = new AddDrvCertificateCheckDTO();
        auditDTO.setCheckId(orgDriverPO.getDrvId());
        auditDTO.setCertificateType(TmsTransportConstant.CertificateCheckTypeEnum.DRV.getCode());
        auditDTO.setDrvName(newDriverPO.getDrvName());
        auditDTO.setDrvIdcard(orgDriverPO.getDrvIdcard());
        auditDTO.setNewDrvcardImg(newDriverPO.getDrvcardImg());
        auditDTO.setUserName(newDriverPO.getModifyUser());
        return auditDTO;
    }

    public Long getCheckId() {
        return checkId;
    }

    public void setCheckId(Long checkId) {
        this.checkId = checkId;
    }

    public Integer getCheckType() {
        return checkType;
    }

    public void setCheckType(Integer checkType) {
        this.checkType = checkType;
    }

    public Integer getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(Integer certificateType) {
        this.certificateType = certificateType;
    }

    public String getDrvIdcard() {
        return drvIdcard;
    }

    public void setDrvIdcard(String drvIdcard) {
        this.drvIdcard = drvIdcard;
    }

    public String getDrvName() {
        return drvName;
    }

    public void setDrvName(String drvName) {
        this.drvName = drvName;
    }

    public String getFirstDrvLicenseDate() {
        return firstDrvLicenseDate;
    }

    public void setFirstDrvLicenseDate(String firstDrvLicenseDate) {
        this.firstDrvLicenseDate = firstDrvLicenseDate;
    }

    public String getExpiryEndDate() {
        return expiryEndDate;
    }

    public void setExpiryEndDate(String expiryEndDate) {
        this.expiryEndDate = expiryEndDate;
    }

    public String getNewDrvcardImg() {
        return newDrvcardImg;
    }

    public void setNewDrvcardImg(String newDrvcardImg) {
        this.newDrvcardImg = newDrvcardImg;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getNetVehiclePeoImg() {
        return netVehiclePeoImg;
    }

    public void setNetVehiclePeoImg(String netVehiclePeoImg) {
        this.netVehiclePeoImg = netVehiclePeoImg;
    }
}
