package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.ctriposs.baiji.exception.*;
import com.google.common.collect.*;
import org.springframework.stereotype.*;

import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

@Repository
public class TspTransportGroupSkuRelationRepositoryImpl implements TspTransportGroupSkuRelationRepository {

    private DalRepository<TspTransportGroupSkuAreaRelationPO> tspTransportGroupDriverRelationRepo;

    private static final String DEFAULT_TABLENAME = "tsp_transport_group_sku_area_relation";
    private static final String DATA_BASE = "dcstransportdb_w";

    public TspTransportGroupSkuRelationRepositoryImpl() throws SQLException {
        tspTransportGroupDriverRelationRepo = new DalRepositoryImpl<>(TspTransportGroupSkuAreaRelationPO.class);
    }

    public DalRepository<TspTransportGroupSkuAreaRelationPO> getTspTransportGroupDriverRelationRepo() {
        return tspTransportGroupDriverRelationRepo;
    }

    @Override
    public int insetBatch(Long transportGroupId, List<Long> skuInfoList, String operator) {
        DalHints hints = DalHints.createIfAbsent(null);
        List<TspTransportGroupSkuAreaRelationPO> relationPOS = Lists.newArrayListWithCapacity(skuInfoList.size());
        for (Long skuId : skuInfoList) {
            TspTransportGroupSkuAreaRelationPO po = new TspTransportGroupSkuAreaRelationPO();
            po.setCreateUser(operator);
            po.setModifyUser(operator);
            po.setTransportGroupId(transportGroupId);
            po.setSkuId(skuId);
            relationPOS.add(po);
        }
        int[] resultList = tspTransportGroupDriverRelationRepo.batchInsert(hints, relationPOS);
        if (resultList == null || resultList.length <= 0) {
            return 0;
        }
        return resultList.length;
    }

    @Override
    public int updateRelationStatus(Long transportGroupId, List<Long> skuInfoList, String operator) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeUpdateSqlBuilder sqlBuilder = new FreeUpdateSqlBuilder();
            sqlBuilder.update(DEFAULT_TABLENAME).set("active", "modify_user", "datachange_lasttime").where("1=1");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.set(i++, "active", Types.BIT, false);
            parameters.set(i++, "modify_user", Types.VARCHAR, operator);
            parameters.set(i++, "datachange_deltime", Types.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
            sqlBuilder.and().equal("transport_group_id");
            parameters.set(i++, "transport_group_id", Types.BIGINT, transportGroupId);
            sqlBuilder.and().in("sku_id");
            parameters.setInParameter(i++, "sku_id", Types.BIGINT, skuInfoList);
            return tspTransportGroupDriverRelationRepo.getQueryDao().update(sqlBuilder, parameters, hints);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<Long> queryBindSkuId(Long transportGroupId,Boolean active) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<Long>> sqlBuilder = new FreeSelectSqlBuilder<>();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("select distinct sku_id from tsp_transport_group_sku_area_relation where 1=1 ");
            if (transportGroupId != null) {
                sqlStr.append(" and transport_group_id = ? ");
                parameters.set(i++,"transport_group_id", Types.BIGINT,transportGroupId);
            }
            if (active != null) {
                sqlStr.append(" and active = ? ");
                parameters.set(i++,"active", Types.BIT,active);
            }
            sqlBuilder.setTemplate(sqlStr.toString());
            sqlBuilder.mapWith(List.class);
            return tspTransportGroupDriverRelationRepo.getQueryDao().query(sqlBuilder,parameters, hints);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<TspTransportGroupSkuAreaRelationPO> querySkuRelationList(List<Long> transportGroupIds, Boolean active) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("transport_group_id", transportGroupIds, Types.BIGINT);
            builder.and().equal("active",active,Types.BIT);
            return tspTransportGroupDriverRelationRepo.getDao().query(builder,hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TspTransportGroupSkuAreaRelationPO> querySkuRelationListBySkuIds(List<Long> skuIds) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("sku_id", skuIds, Types.BIGINT);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return tspTransportGroupDriverRelationRepo.getDao().query(builder,hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

}
