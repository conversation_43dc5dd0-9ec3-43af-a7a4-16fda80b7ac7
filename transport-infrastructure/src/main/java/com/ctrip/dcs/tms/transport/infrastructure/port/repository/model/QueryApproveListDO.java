package com.ctrip.dcs.tms.transport.infrastructure.port.repository.model;

import java.util.*;

public class QueryApproveListDO {

    private Integer eventType;
    private String approveName;
    private Long supplierId;
    private Integer approveStatus;
    private String createUser;
    private Integer oneSelfStatus;
    private String accountId;
    private Integer pageNo;
    private Integer pageSize;
    private Long approveSourceId;
    public Integer verifyStatus;
    public String drvName;
    public String vehLicense;
    public String commitTimeStart;
    public String commitTimeEnd;
    public Integer ocrheadPortraitResult;
    public List<Long> drvIdList;

    public List<Long> getDrvIdList() {
        return drvIdList;
    }

    public void setDrvIdList(List<Long> drvIdList) {
        this.drvIdList = drvIdList;
    }

    public Integer getVerifyStatus() {
        return verifyStatus;
    }

    public void setVerifyStatus(Integer verifyStatus) {
        this.verifyStatus = verifyStatus;
    }

    public String getDrvName() {
        return drvName;
    }

    public void setDrvName(String drvName) {
        this.drvName = drvName;
    }

    public String getVehLicense() {
        return vehLicense;
    }

    public void setVehLicense(String vehLicense) {
        this.vehLicense = vehLicense;
    }

    public String getCommitTimeStart() {
        return commitTimeStart;
    }

    public void setCommitTimeStart(String commitTimeStart) {
        this.commitTimeStart = commitTimeStart;
    }

    public String getCommitTimeEnd() {
        return commitTimeEnd;
    }

    public void setCommitTimeEnd(String commitTimeEnd) {
        this.commitTimeEnd = commitTimeEnd;
    }

    public Integer getOcrheadPortraitResult() {
        return ocrheadPortraitResult;
    }

    public void setOcrheadPortraitResult(Integer ocrheadPortraitResult) {
        this.ocrheadPortraitResult = ocrheadPortraitResult;
    }

    public Long getApproveSourceId() {
        return approveSourceId;
    }

    public void setApproveSourceId(Long approveSourceId) {
        this.approveSourceId = approveSourceId;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public Integer getOneSelfStatus() {
        return oneSelfStatus;
    }

    public void setOneSelfStatus(Integer oneSelfStatus) {
        this.oneSelfStatus = oneSelfStatus;
    }

    public Integer getEventType() {
        return eventType;
    }

    public void setEventType(Integer eventType) {
        this.eventType = eventType;
    }

    public String getApproveName() {
        return approveName;
    }

    public void setApproveName(String approveName) {
        this.approveName = approveName;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Integer getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(Integer approveStatus) {
        this.approveStatus = approveStatus;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
