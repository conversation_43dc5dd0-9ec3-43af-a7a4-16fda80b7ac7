package com.ctrip.dcs.tms.transport.infrastructure.port.repository;


import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.dal.*;

import java.sql.*;
import java.util.*;

public interface TmsCertificateCheckRepository {

    TmsCertificateCheckPO queryByPK(Long id);

    List<TmsCertificateCheckPO> queryCertificateByCheckId(Long checkId, Integer checkType);

    Long  insertTmsCertificateCheck(TmsCertificateCheckPO certificateCheckPO) throws SQLException;

    int batchInsertCheckRecord(List<TmsCertificateCheckPO> certificateCheckList);

    int batchUpdateCheckRecord(List<TmsCertificateCheckPO> certificateCheckList);

    int updateCheckStatus(Long id,Integer checkStatus);

    List<TmsCertificateCheckPO> queryIdCardCheckByCheckIng(List<Long> checkIds,Integer checkType,List<Integer> certificateTypeList,Integer checkStatus,int pageNo,int pageSize);

    int countIdCardCheckByCheckIng(List<Long> checkIds,Integer checkType,List<Integer> certificateTypeList,Integer checkStatus);

    int synchronousCheck(List<Long> ids,Long checkId,Integer checkType);

    List<TmsCertificateCheckPO> queryCerCheckListByCheckIds(Long checkId, Integer checkType);

    List<TmsCertificateCheckPO> queryCerCheckListByKeyWord(String checkKeyword,Integer checkStatus);

    List<TmsCertificateCheckPO> queryCerCheckListByKeyWord(List<String> checkKeyword,Integer checkStatus,Integer checkType,Integer certificateType,int pageNo,int pageSize);


    int countCerCheckListByKeyWord(List<String> checkKeyword,Integer checkStatus,Integer checkType,Integer certificateType);

    List<TmsCertificateCheckPO> queryCerCheckListByCheckIds(List<Long> checkIds, List<Integer> checkType);

    TmsCertificateCheckPO queryCertificateCheckByCondition(Long checkId,int checkType, int certificateType);

    List<TmsCertificateCheckPO> queryCerCheckListByIds(List<Long> ids);

    int batchUpdateCheckStatus(List<Long> ids,Integer checkStatus);

    List<TmsCertificateCheckPO> queryCerCheckListByKeyWordOrderById(Long checkId,String checkKeyword,Integer checkStatus,Integer checkType,Integer certificateType,int pageNo,int pageSize);

    /**
     * 司机缓存查询标签接口
     */
    List<TmsCertificateCheckPO> queryCertificateCheck4DrvCache(Set<Long> checkIdList, int checkType, List<Integer> certificateTypeList);

    List<TmsCertificateCheckPO> queryCertificateByCheckIdOrderBy(Long checkId, Integer checkType);

    int updateCheckStatus(Long id,Integer checkStatus,String modifyUser);

    int updateIdcardCheckStatus(Long id,Integer checkStatus,Boolean isThirdCheck, String modifyUser);

    List<TmsCertificateCheckPO> queryNewCheckListByCheckIds(List<Long> checkIds, Integer checkType,Boolean versionFlag,Integer thirdCheckStatus);

    int updateCertificateActive(Long checkId,Integer checkType,Integer certificateType,Boolean active,String modifyUser);

    int updateCertificateStatus(List<Long> checkIds,Integer checkType,Integer certificateType,Integer checkStatus,String modifyUser);

    int updateCertificateActive(Long checkId,Integer checkType,List<Integer> certificateType,Boolean active,String modifyUser);
}