package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TransportGroupDriverApplicationRecordPO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportGroupDriverApplicationRecordRepository;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.base.DalTableOperations;
import com.ctrip.platform.dal.dao.base.SQLArg;
import com.ctrip.platform.dal.dao.base.SQLArgList;
import com.ctrip.platform.dal.dao.client.DalOperationsFactory;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class TransportGroupDriverApplicationRecordRepositoryImpl implements
  TransportGroupDriverApplicationRecordRepository{
  private static final int ACTIVE = 1;
  private static final int INACTIVE = 0;

  private DalTableOperations<TransportGroupDriverApplicationRecordPO> dalTableOperations;

  public TransportGroupDriverApplicationRecordRepositoryImpl() {
    dalTableOperations = DalOperationsFactory.getDalTableOperations(TransportGroupDriverApplicationRecordPO.class);
  }

  @SneakyThrows
  @Override
  public List<TransportGroupDriverApplicationRecordPO> queryByCondition(
    TransportGroupDriverApplicationRecordPO condition) {
    DalHints dalHints = new DalHints().ignoreAllNullFields();
    return dalTableOperations.queryBy(condition, dalHints);
  }

  @SneakyThrows
  @Override
  public void batchInsert(List<TransportGroupDriverApplicationRecordPO> recordList) {
    if (CollectionUtils.isEmpty(recordList)) {
      return;
    }
    dalTableOperations.batchInsert(new DalHints(),recordList);
  }

  @Override
  public void update(TransportGroupDriverApplicationRecordPO record) {
  }

  @Override
  public void updateHistoryData2Inactive(Long transportGroupId, Long workShiftId) {
    doUpdateHistoryData2Inactive(transportGroupId, null, workShiftId);
  }

  @Override
  public void updateHistoryData2Inactive(Long transportGroupId, List<Long> drvIdList) {
    doUpdateHistoryData2Inactive(transportGroupId, drvIdList, null);
  }

  @SneakyThrows
  public void doUpdateHistoryData2Inactive(Long transportGroupId, List<Long> drvIdList, Long workShiftId) {
    StringBuilder sql = new StringBuilder();
    SQLArgList args = SQLArg.list();
    sql.append("update tsp_transport_group_driver_application_records set active = ? ");
    args.add(INACTIVE);
    sql.append("where transport_group_id = ? ");
    args.add(transportGroupId);
    sql.append(" and active = ? ");
    args.add(ACTIVE);

    if (CollectionUtils.isNotEmpty(drvIdList)){
      sql.append(" and drv_id in (?) ");
      args.add(drvIdList);
    }
    if (workShiftId != null){
      sql.append(" and work_shift_id = ? ");
      args.add(workShiftId);
    }

    dalTableOperations.update(sql.toString(), DalHints.createIfAbsent(null), args);
  }



}
