package com.ctrip.dcs.tms.transport.infrastructure.port.repository.model;

import java.util.*;

public class StepApproveNoPassVO {
    private Long drvRecruitingId;
    private Integer recruitingType;
    private Integer drvFrom;
    /*车证-网约车运输证*/
    private String netTansCtfctImg;
    /*车证-车辆网约车申诉材料*/
    private String vehNetAppealMaterials;

    /*司机其它证件*/
    private String otherCertificateImg;
    /*司机网约车申诉材料*/
    private String drvNetAppealMaterials;
    /*网约车人照*/
    private String netVehiclePeoImg;

    /*核酸报告图片*/
    private String nucleicAcidReportImg;

    /*核酸检查时间 YYYY-MM-DD*/
    private String nucleicAcidTestingTime;

    /*疫苗报告图片*/
    private String vaccineReportImg;

    /*接种时间List*/
    List<String> vaccinationTimeList;

    private Long vehicleId;

    private String modifyUser;

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public Long getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(Long vehicleId) {
        this.vehicleId = vehicleId;
    }

    public Long getDrvRecruitingId() {
        return drvRecruitingId;
    }

    public void setDrvRecruitingId(Long drvRecruitingId) {
        this.drvRecruitingId = drvRecruitingId;
    }

    public Integer getRecruitingType() {
        return recruitingType;
    }

    public void setRecruitingType(Integer recruitingType) {
        this.recruitingType = recruitingType;
    }

    public Integer getDrvFrom() {
        return drvFrom;
    }

    public void setDrvFrom(Integer drvFrom) {
        this.drvFrom = drvFrom;
    }

    public String getNetTansCtfctImg() {
        return netTansCtfctImg;
    }

    public void setNetTansCtfctImg(String netTansCtfctImg) {
        this.netTansCtfctImg = netTansCtfctImg;
    }

    public String getVehNetAppealMaterials() {
        return vehNetAppealMaterials;
    }

    public void setVehNetAppealMaterials(String vehNetAppealMaterials) {
        this.vehNetAppealMaterials = vehNetAppealMaterials;
    }

    public String getOtherCertificateImg() {
        return otherCertificateImg;
    }

    public void setOtherCertificateImg(String otherCertificateImg) {
        this.otherCertificateImg = otherCertificateImg;
    }

    public String getDrvNetAppealMaterials() {
        return drvNetAppealMaterials;
    }

    public void setDrvNetAppealMaterials(String drvNetAppealMaterials) {
        this.drvNetAppealMaterials = drvNetAppealMaterials;
    }

    public String getNetVehiclePeoImg() {
        return netVehiclePeoImg;
    }

    public void setNetVehiclePeoImg(String netVehiclePeoImg) {
        this.netVehiclePeoImg = netVehiclePeoImg;
    }

    public String getNucleicAcidReportImg() {
        return nucleicAcidReportImg;
    }

    public void setNucleicAcidReportImg(String nucleicAcidReportImg) {
        this.nucleicAcidReportImg = nucleicAcidReportImg;
    }

    public String getNucleicAcidTestingTime() {
        return nucleicAcidTestingTime;
    }

    public void setNucleicAcidTestingTime(String nucleicAcidTestingTime) {
        this.nucleicAcidTestingTime = nucleicAcidTestingTime;
    }

    public String getVaccineReportImg() {
        return vaccineReportImg;
    }

    public void setVaccineReportImg(String vaccineReportImg) {
        this.vaccineReportImg = vaccineReportImg;
    }

    public List<String> getVaccinationTimeList() {
        return vaccinationTimeList;
    }

    public void setVaccinationTimeList(List<String> vaccinationTimeList) {
        this.vaccinationTimeList = vaccinationTimeList;
    }
}
