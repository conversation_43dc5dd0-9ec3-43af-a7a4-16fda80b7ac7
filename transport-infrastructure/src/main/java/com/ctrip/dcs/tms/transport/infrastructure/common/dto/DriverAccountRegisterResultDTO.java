package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class DriverAccountRegisterResultDTO {
  private Long drvId;
  private String uid;
  private String ppmAccount;
  private String qunarAccount;
  /**
   * 是否需要注册账号，如果不在灰度或者已经存在了，则为false；否则为true
   */
  private boolean needRegisterAccount;

  /**
   *  如果需要注册账号，且注册成功则为true，否则为false
   */
  private boolean registerSuccess;
  private String errorCode;
  private String errorMsg;


  public DriverAccountRegisterResultDTO() {

  }
  public DriverAccountRegisterResultDTO(Long drvId, String uid, boolean registerSuccess, String errorCode, String errorMsg, boolean needRegisterAccount) {
    this.drvId = drvId;
    this.uid = uid;
    this.registerSuccess = registerSuccess;
    this.errorCode = errorCode;
    this.errorMsg = errorMsg;
    this.needRegisterAccount = needRegisterAccount;
  }

  public DriverAccountRegisterResultDTO(Long drvId, String uid, boolean registerSuccess, String errorCode, String errorMsg, boolean needRegisterAccount, String ppmAccount, String qunarAccount) {
    this.drvId = drvId;
    this.uid = uid;
    this.registerSuccess = registerSuccess;
    this.errorCode = errorCode;
    this.errorMsg = errorMsg;
    this.needRegisterAccount = needRegisterAccount;
    this.ppmAccount = ppmAccount;
    this.qunarAccount = qunarAccount;
  }
}
