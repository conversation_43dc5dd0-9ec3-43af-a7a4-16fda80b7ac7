package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

import java.sql.Timestamp;
import java.util.List;

public class GlobalApplyDriverLeaveDurationVO {

    //司机ID
    private List<Long> drvList;
    //更新赛道前30天的开始时间
    private Timestamp beginBeforeCheckDateTime;
    //更新赛道前30天的结束时间
    private Timestamp endBeforeCheckDateTime;
    //更新赛道后30天的开始时间
    private Timestamp beginAfterCheckDateTime;
    //更新赛道后30天的结束始时间
    private Timestamp endAfterCheckDateTime;
    //当前赛道前的请假判断值
    private Integer durationA;
    //当产赛道后的请假判断值
    private Integer durationB;

    public GlobalApplyDriverLeaveDurationVO(){

    }

    public GlobalApplyDriverLeaveDurationVO(List<Long> drvList,Timestamp beginBeforeCheckDateTime,Timestamp endBeforeCheckDateTime,Timestamp beginAfterCheckDateTime,Timestamp endAfterCheckDateTime,Integer durationA,Integer durationB){
        this.drvList = drvList;
        this.beginBeforeCheckDateTime = beginBeforeCheckDateTime;
        this.endBeforeCheckDateTime = endBeforeCheckDateTime;
        this.beginAfterCheckDateTime = beginAfterCheckDateTime;
        this.endAfterCheckDateTime = endAfterCheckDateTime;
        this.durationA = durationA;
        this.durationB = durationB;
    }

    public List<Long> getDrvList() {
        return drvList;
    }

    public void setDrvList(List<Long> drvList) {
        this.drvList = drvList;
    }

    public Timestamp getBeginBeforeCheckDateTime() {
        return beginBeforeCheckDateTime;
    }

    public void setBeginBeforeCheckDateTime(Timestamp beginBeforeCheckDateTime) {
        this.beginBeforeCheckDateTime = beginBeforeCheckDateTime;
    }

    public Timestamp getEndBeforeCheckDateTime() {
        return endBeforeCheckDateTime;
    }

    public void setEndBeforeCheckDateTime(Timestamp endBeforeCheckDateTime) {
        this.endBeforeCheckDateTime = endBeforeCheckDateTime;
    }

    public Timestamp getBeginAfterCheckDateTime() {
        return beginAfterCheckDateTime;
    }

    public void setBeginAfterCheckDateTime(Timestamp beginAfterCheckDateTime) {
        this.beginAfterCheckDateTime = beginAfterCheckDateTime;
    }

    public Timestamp getEndAfterCheckDateTime() {
        return endAfterCheckDateTime;
    }

    public void setEndAfterCheckDateTime(Timestamp endAfterCheckDateTime) {
        this.endAfterCheckDateTime = endAfterCheckDateTime;
    }

    public Integer getDurationA() {
        return durationA;
    }

    public void setDurationA(Integer durationA) {
        this.durationA = durationA;
    }

    public Integer getDurationB() {
        return durationB;
    }

    public void setDurationB(Integer durationB) {
        this.durationB = durationB;
    }
}
