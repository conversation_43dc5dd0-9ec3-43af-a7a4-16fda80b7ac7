package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehicleGlobalIdRecordPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ErrorCodeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.SharkUtils;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleGlobalIdRecordRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.KeyHolder;
import com.ctrip.platform.dal.dao.base.DalTableOperations;
import com.ctrip.platform.dal.dao.base.SQLResult;
import com.ctrip.platform.dal.dao.client.DalOperationsFactory;
import com.dianping.cat.Cat;
import lombok.SneakyThrows;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EVENT_FAILED;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.GENERATE_VEHICLE_GLOBAL_ID;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.VEHICLE_EVENT;

@Repository
public class VehicleGlobalIdRecordRepositoryImpl implements VehicleGlobalIdRecordRepository {

  private static final Logger logger = LoggerFactory.getLogger(VehicleGlobalIdRecordRepositoryImpl.class);

  private DalTableOperations<VehicleGlobalIdRecordPO> dalRepository;

  public VehicleGlobalIdRecordRepositoryImpl() {
    this.dalRepository = DalOperationsFactory.getDalTableOperations(VehicleGlobalIdRecordPO.class);
  }

  @Override
  public Result<Long> generateGlobalId(String vehicleLicense, String source, Long id) {
    try {
      VehicleGlobalIdRecordPO recordPO = build(vehicleLicense, source, id);
      KeyHolder keyHolder = new KeyHolder();
      DalHints dalHints = new DalHints().ignoreAllNullFields().enableIdentityInsert().setIdentityBack();
      dalRepository.insertDuplicateUpdate(dalHints, recordPO, SQLResult.keyHolder(keyHolder));
      Result.Builder<Long> result = Result.Builder.newResult();
      Cat.logEvent(VEHICLE_EVENT, GENERATE_VEHICLE_GLOBAL_ID);
      Long returnId = keyHolder.getKey() != null ? (Long)keyHolder.getKey() : getId(vehicleLicense);
      return result.success().withCode(TmsTransportConstant.ResultStatusTypeEnum.SUCCESS_CODE.getCode()).withData(returnId).build();
    }catch (SQLException e) {
      logger.error("generateGlobalId", "error, vehicleLicense:{}, source:{}, message: {}", vehicleLicense, source, e.getMessage());
      Result.Builder<Long> result = Result.Builder.newResult();
      Cat.logEvent(VEHICLE_EVENT, GENERATE_VEHICLE_GLOBAL_ID, EVENT_FAILED, vehicleLicense  + "|" + source + "|" + e.getMessage());
      throw new BizException(ErrorCodeEnum.TRANSPORT_GENERATE_VEHICLE_GLOBAL_ID_FAILED.getCode(),
        SharkUtils.getSharkValue(ErrorCodeEnum.TRANSPORT_GENERATE_VEHICLE_GLOBAL_ID_FAILED.getMessage(), e.getMessage()));
    }
  }

  @Override
  public Result<Long> generateGlobalId(String vehicleLicense, String source) {
    return generateGlobalId(vehicleLicense, source, null);
  }

  private VehicleGlobalIdRecordPO build(String vehicleLicense, String source, Long id) {
    VehicleGlobalIdRecordPO po = new VehicleGlobalIdRecordPO();
    po.setVehicleLicense(vehicleLicense);
    po.setSource(source);
    po.setId(id);
    return po;
  }

  @SneakyThrows
  private Long getId(String vehicleLicense) {
    String sql = "select id from tms_vehicle_global_id_record where vehicle_license = ?";
      return dalRepository.queryObject(sql, new DalHints(), vehicleLicense).getId();
  }

}
