package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

/**
 * <AUTHOR>
 * 2021-04-14 16:57:32
 * 疫情防控城市要求
 */
public class EpidemicPreventionControlCityInfoDTO {

    public EpidemicPreventionControlCityInfoDTO() {}

    public EpidemicPreventionControlCityInfoDTO(Integer reportConstraintModel, Integer samplingDateEffectiveDays) {
        this.reportConstraintModel = reportConstraintModel;
        this.samplingDateEffectiveDays = samplingDateEffectiveDays;
    }

    /**
     * 城市id
     */
    private Long cityId;

    /**
     * 要求报告模式
     * 0：无需上传核酸报告 & 疫苗报告
     * 1：必须上传核酸报告
     * 2：必须上传疫苗报告
     * 3：满足上传核酸报告 || 疫苗报告 二选一即可
     * 4：必须上传核酸报告 & 疫苗报告
     *
     * 请参考：EpidemicPreventionControlEnum.AskReportStatusEnum
     */
    private Integer reportConstraintModel;

    /**
     * 核酸报告有效期天数
     */
    private Integer samplingDateEffectiveDays;

    /**
     * 地区性防控文案
     * */
    private String epidemicPreventionControlCopyWriting;

    public String getEpidemicPreventionControlCopyWriting() {
        return epidemicPreventionControlCopyWriting;
    }

    public void setEpidemicPreventionControlCopyWriting(String epidemicPreventionControlCopyWriting) {
        this.epidemicPreventionControlCopyWriting = epidemicPreventionControlCopyWriting;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public Integer getReportConstraintModel() {
        return reportConstraintModel;
    }

    public void setReportConstraintModel(Integer reportConstraintModel) {
        this.reportConstraintModel = reportConstraintModel;
    }

    public Integer getSamplingDateEffectiveDays() {
        return samplingDateEffectiveDays;
    }

    public void setSamplingDateEffectiveDays(Integer samplingDateEffectiveDays) {
        this.samplingDateEffectiveDays = samplingDateEffectiveDays;
    }

}