package com.ctrip.dcs.tms.transport.task.infrastructure.adapter.mysql.po;

import com.ctrip.platform.dal.dao.annotation.*;
import lombok.Data;

import javax.persistence.*;
import java.sql.*;

@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "task_vehicle_dispatch_photo_approval_record")
@Data
public class TaskVehicleDispatchPhotoApprovalRecordPO {

  /**
   * 主键
   */
  @Id
  @Column(name = "id")
  @GeneratedValue(strategy = GenerationType.AUTO)
  @Type(value = Types.BIGINT)
  private Long id;

  /**
   * 车辆主键
   */
  @Column(name = "vehicle_id")
  @Type(value = Types.BIGINT)
  private Long vehicleId;

  /**
   * 车牌号
   */
  @Column(name = "vehicle_license")
  @Type(value = Types.VARCHAR)
  private String vehicleLicense;

  /**
   * 供应商ID
   */
  @Column(name = "supplier_id")
  @Type(value = Types.BIGINT)
  private Long supplierId;

  /**
   * 车型ID
   */
  @Column(name = "vehicle_type_id")
  @Type(value = Types.BIGINT)
  private Long vehicleTypeId;

  /**
   * 司机ID
   */
  @Column(name = "drv_id")
  @Type(value = Types.BIGINT)
  private Long drvId;

  /**
   * 司机姓名
   */
  @Column(name = "drv_name")
  @Type(value = Types.VARCHAR)
  private String drvName;

  /**
   * 审核时间
   */
  @Column(name = "approval_time")
  @Type(value = Types.TIMESTAMP)
  private Timestamp approvalTime;

  /**
   * 审核人
   */
  @Column(name = "approval_user")
  @Type(value = Types.VARCHAR)
  private String approvalUser;

  /**
   * 审核人角色(1.供应商、2.运营)
   */
  @Column(name = "approval_role")
  @Type(value = Types.INTEGER)
  private Integer approvalRole;

  /**
   * 审批状态(0.待审批、1.审批通过、2.审批不通过)
   */
  @Column(name = "approval_status")
  @Type(value = Types.INTEGER)
  private Integer approvalStatus;

  /**
   * 创建人
   */
  @Column(name = "create_user")
  @Type(value = Types.VARCHAR)
  private String createUser;

  /**
   * 变更人
   */
  @Column(name = "modify_user")
  @Type(value = Types.VARCHAR)
  private String modifyUser;

  /**
   * 创建时间
   */
  @Column(name = "datachange_createtime")
  @Type(value = Types.TIMESTAMP)
  private Timestamp datachangeCreateTime;

  /**
   * 更新时间
   */
  @Column(name = "datachange_lasttime")
  @Type(value = Types.TIMESTAMP)
  private Timestamp datachangeLastTime;

  @Column(name = "remark")
  @Type(value = Types.VARCHAR)
  private String remark;

  @Column(name = "business_id")
  @Type(value = Types.VARCHAR)
  private String businessId;

  @Column(name = "audit_progress")
  @Type(value = Types.SMALLINT)
  private Integer auditProgress;

  @Column(name = "city_id")
  @Type(value = Types.BIGINT)
  private Long cityId;

}
