package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

import com.ctrip.dcs.tms.transport.api.model.UpdateTransportGroupApplyStatusSOARequestType;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model.DriverPoint;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.ApplyDriverRelationDetailPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TransportGroupDriverApplicationRecordPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupDriverRelationPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupWorkShiftPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.igt.framework.dal.transaction.Transactional;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Data
@ToString(exclude = {"driverPointMap"})
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransportGroupDriverApplyProcessDTO {

  /**
   * 操作时间
   */
  @Builder.Default
  private LocalDateTime operateTime = LocalDateTime.now();

  /**
   * 参与司机[workshiftId:[drvId]]
   */
  @Builder.Default
  private Map<Long, List<Long>> applyDriverMap = Maps.newHashMap();

  /**
   * 成功司机[workshiftId:[drvId]]
   */
  @Builder.Default
  private Map<Long, List<Long>> workshiftApplySuccessDriverListMap = Maps.newHashMap();

  /**
   * 失败司机[workshiftId:[drvId]]
   */
  @Builder.Default
  private Map<Long, List<Long>> workshiftFailedDriverListMap = Maps.newHashMap();

  /**
   * 司机分
   */
  @Builder.Default

  private Map<Long, DriverPoint> driverPointMap = Maps.newHashMap();

  /**
   * 司机关联关系
   */
  @Builder.Default
  private LinkedHashMap<Long, List<ApplyDriverRelationDetailPO>> workShiftDriverRelationDetailPOMap = Maps.newLinkedHashMap();

  /**
   * 规则（A,B,C,D）
   */
  private TmsTransportConstant.TransportDriverApplyRuleEnum rule;

  /**
   * 报名失败司机+规则（A,B,C,D）原因明细
   */
  @Builder.Default
  private Map<String, TransportGroupDriverApplyFailedReasonDTO> transportGroupDriverApplyFailedReasonDTOMap =
    Maps.newHashMap();

  /**
   * 赛道可报名数量
   */
  @Builder.Default
  private Map<Long, Long> workSiftDriverApplicationMaximumLimitMap = Maps.newHashMap();

  private TmsTransportConstant.ApplyTransPortOperationTypeEnum applyTransPortOperationTypeEnum;

  @Builder.Default
  private Map<Long, String> driverWorkPeriodMap = Maps.newHashMap();

  public void setLeaveHour(Long drvId, Timestamp leaveBeginTime, Timestamp leaveEndTime, long duration) {
    if (duration == 0) {
      return;
    }
    getDrvDTO(drvId).setLeaveHour(drvId, leaveBeginTime, leaveEndTime, duration);
  }

  public void setFreezeHour(Long drvId, Timestamp firstFreezeTime, Timestamp freezeEndDateTime, long freezeHour) {
    if (freezeHour == 0) {
      return;
    }
    getDrvDTO(drvId).setFreezeHour(drvId,firstFreezeTime, freezeEndDateTime, freezeHour);
  }

  public void setDriverPointInfo(Long drvId, Double driverPoint, Long driverRankingInCity, Long driverRankingInBatch, Long workShiftId){
    boolean exists = false;
    for (TmsTransportConstant.TransportDriverApplyRuleEnum rule : TmsTransportConstant.TransportDriverApplyRuleEnum.values()) {
      TransportGroupDriverApplyFailedReasonDTO applyFailedReasonDTO =
        transportGroupDriverApplyFailedReasonDTOMap.get(getKey(rule, drvId));
      if (applyFailedReasonDTO != null) {
        applyFailedReasonDTO.setDriverPointInfo(driverPoint, driverRankingInCity, driverRankingInBatch, workShiftId);
        exists = true;
      }
    }
    // 如果没有则创建
    if (!exists) {
      TransportGroupDriverApplyFailedReasonDTO applyFailedReasonDTO = new TransportGroupDriverApplyFailedReasonDTO();
      applyFailedReasonDTO.setDriverPointInfo(driverPoint, driverRankingInCity, driverRankingInBatch, workShiftId);
      transportGroupDriverApplyFailedReasonDTOMap.put(getKey(drvId), applyFailedReasonDTO);
    }
  }

  /**
   * 处理报名状态
   * @param drvId
   * @param applyStatus
   * @param applyFailedType
   * @param ruleDescription
   */
  public void setApplyResult(Long drvId, TmsTransportConstant.ApplyStatusEnum applyStatus, TmsTransportConstant.ApplyFailedTypeEnum applyFailedType, String ruleDescription, Integer duration) {
    getDrvDTO(drvId).setApplyResult(drvId, applyStatus, applyFailedType, ruleDescription, duration);
  }

  // 处理已存在的司机报名状态
  public void setApplyResultCaseDriverPointIsRankedLow(Long drvId, TmsTransportConstant.ApplyStatusEnum applyStatus, TmsTransportConstant.ApplyFailedTypeEnum applyFailedType, String ruleDescription) {
    for (TmsTransportConstant.TransportDriverApplyRuleEnum rule : TmsTransportConstant.TransportDriverApplyRuleEnum.values()) {
      TransportGroupDriverApplyFailedReasonDTO applyFailedReasonDTO =
        transportGroupDriverApplyFailedReasonDTOMap.get(getKey(rule, drvId));
      if (applyFailedReasonDTO != null && applyFailedReasonDTO.getApplyFailedType() == null) {
        applyFailedReasonDTO.setApplyResult(drvId, applyStatus, applyFailedType, ruleDescription, null);
      }
    }
  }

  public void setCalculateTimeRange(Long drvId, Timestamp calculateTimeFrom, Timestamp calculateTimeTo) {
    getDrvDTO(drvId).setCalculateTimeRange(calculateTimeFrom, calculateTimeTo);
  }

  private TransportGroupDriverApplyFailedReasonDTO getDrvDTO(Long drvId) {
    return transportGroupDriverApplyFailedReasonDTOMap.computeIfAbsent(getKey(drvId), k -> new TransportGroupDriverApplyFailedReasonDTO());
  }

  public TransportGroupDriverApplyFailedReasonDTO getOrCreate(Long drvId) {
    for (TmsTransportConstant.TransportDriverApplyRuleEnum rule : TmsTransportConstant.TransportDriverApplyRuleEnum.values()) {
      TransportGroupDriverApplyFailedReasonDTO applyFailedReasonDTO =
        transportGroupDriverApplyFailedReasonDTOMap.get(getKey(rule, drvId));
      if (applyFailedReasonDTO != null) {
        return applyFailedReasonDTO;
      }
    }
    return new TransportGroupDriverApplyFailedReasonDTO();
  }

  @NotNull
  protected String getKey(Long drvId) {
    return rule.getCode() + drvId;
  }
  protected String getKey(TmsTransportConstant.TransportDriverApplyRuleEnum rule, Long drvId) {
    return rule.getCode() + drvId;
  }

  public String log() {
    return JsonUtil.toJson(this);
  }

  public List<TransportGroupDriverApplicationRecordPO> convert2TransportGroupDriverRelationList(List<TspTransportGroupDriverRelationPO> eliminateDriverRelationList) {
    List<TransportGroupDriverApplicationRecordPO> result = Lists.newArrayList();
    for (TspTransportGroupDriverRelationPO driverRelationPO : eliminateDriverRelationList) {
      TransportGroupDriverApplyFailedReasonDTO drvDTO = this.getDrvDTO(driverRelationPO.getDrvId());
      if (drvDTO.getApplyFailedType() == null || drvDTO.getApplyStatus() == null) {
        continue;
      }
      TransportGroupDriverApplicationRecordPO recordPO = new TransportGroupDriverApplicationRecordPO();
      recordPO.setTransportGroupId(driverRelationPO.getTransportGroupId());
      recordPO.setDrvId(driverRelationPO.getDrvId());
      recordPO.setWorkShiftId(driverRelationPO.getWorkShiftId());
      recordPO.setApplyStatus(drvDTO.getApplyStatus().getCode());
      recordPO.setApplyFailedType(drvDTO.getApplyFailedType().getCode());
      recordPO.setApplyFailedReason(getApplyFailedReason(drvDTO));
      recordPO.setApplyFailedReasonDetail(getApplyFailedReasonDetail(drvDTO, driverRelationPO.getWorkShiftId()));
      recordPO.setCreateUser("system");
      recordPO.setModifyUser("system");
      result.add(recordPO);
    }
    return result;
  }

  public String getApplyFailedReasonDetail(TransportGroupDriverApplyFailedReasonDTO drvDTO, Long workShiftId) {
    return drvDTO.getApplyFailedReasonDetail(this.operateTime, applyTransPortOperationTypeEnum, workSiftDriverApplicationMaximumLimitMap.get(workShiftId), workShiftId);
  }

  public String getApplyFailedReason(TransportGroupDriverApplyFailedReasonDTO drvDTO) {
    return drvDTO.getRuleDescription();
  }

  public List<TransportGroupDriverApplyFailedReasonDTO> getFailedDriverList() {
    Set<Long> successDrvSet =
      this.getWorkshiftApplySuccessDriverListMap().values().stream().flatMap(List::stream).collect(Collectors.toSet());
    return transportGroupDriverApplyFailedReasonDTOMap.values().stream().filter(applyFailedReasonDTO -> applyFailedReasonDTO.getApplyStatus() == TmsTransportConstant.ApplyStatusEnum.APPLY_FAILED).filter(applyFailedReasonDTO -> !successDrvSet.contains(applyFailedReasonDTO.getDrvId())).collect(
      Collectors.toList());
  }

  public void setDriverApplyStatusCaseDriverPoint() {
    try {
      // 过滤出来报名失败的司机
      for (Map.Entry<Long, List<Long>> workShiftApplySuccessEntry : this.getWorkshiftApplySuccessDriverListMap().entrySet()) {
        List<Long> workShiftApplyDrvList = this.getApplyDriverMap().get(workShiftApplySuccessEntry.getKey());
        if (CollectionUtils.isEmpty(workShiftApplyDrvList)) {
          continue;
        }
        this.getWorkshiftFailedDriverListMap().computeIfAbsent(workShiftApplySuccessEntry.getKey(),
          k -> Lists.newArrayList()).addAll(workShiftApplyDrvList.stream().filter(drvId -> !workShiftApplySuccessEntry.getValue().contains(drvId))
          .collect(Collectors.toList()));
      }

      // 设置因为司机分排名靠后导致失败的记录的失败状态
      Set<Object> successDrvSet = this.getWorkshiftApplySuccessDriverListMap().entrySet().stream().flatMap(entry -> entry.getValue()
          .stream())
        .collect(Collectors.toSet());
      for (Map.Entry<Long, List<Long>> workshiftApplyFailedEntry : this.getWorkshiftFailedDriverListMap().entrySet()) {
        if (successDrvSet.contains(workshiftApplyFailedEntry.getKey())) {
          continue;
        }
        for (Long applyFailedDrvId : workshiftApplyFailedEntry.getValue()) {
          this.setApplyResultCaseDriverPointIsRankedLow(applyFailedDrvId, TmsTransportConstant.ApplyStatusEnum.APPLY_FAILED,
            TmsTransportConstant.ApplyFailedTypeEnum.DRIVER_POINT_IS_RANKED_LOW,
            TmsTransportConstant.TransportDriverApplyRuleEnum.DRIVER_POINT.getText());
        }
      }
    }catch (Exception e) {
      Cat.logEvent("driver", "driver-apply-set-driver-apply-status-failed");
    }
  }

  public void setDriverPointAndWorkShiftIntoApplyProcess(Map<Long, List<ApplyDriverRelationDetailPO>> participatingDriversMap, List<Map.Entry<Long, List<DriverPoint>>> cityRankingDriverPointMap) {
    try {
      final Map<Long, Long> driverCityRank = Maps.newHashMap();
      for(Map.Entry<Long, List<DriverPoint>> entry : cityRankingDriverPointMap){
        List<DriverPoint> pointList = entry.getValue();
        pointList.forEach(driverPoint -> {
            driverPointMap.put(driverPoint.getDriverId(), driverPoint);
          driverCityRank.put(driverPoint.getDriverId(), driverPoint.getCityRanking());
          }
        );
      }

      LinkedHashMap<Long, List<ApplyDriverRelationDetailPO>> workShiftDriverRelationMap =
        participatingDriversMap.values().stream().flatMap(List::stream).collect(
          Collectors.groupingBy(ApplyDriverRelationDetailPO::getWorkShiftId, LinkedHashMap::new,
            Collectors.toList()));

      // 存放司机分（原始信息）和司机关联关系
      this.setDriverPointMap(driverPointMap);
      this.setWorkShiftDriverRelationDetailPOMap(workShiftDriverRelationMap);

      // 存放参与报名的司机
      Map<Long, List<Long>> participatingDriverMap = workShiftDriverRelationMap.entrySet().stream()
        .collect(Collectors.toMap(Map.Entry::getKey,
          entry -> entry.getValue().stream()
            .map(ApplyDriverRelationDetailPO::getDrvId)
            .collect(Collectors.toList())
        ));
      this.setApplyDriverMap(participatingDriverMap);

      // 设置司机分信息
      for (Map.Entry<Long, List<ApplyDriverRelationDetailPO>> entry : workShiftDriverRelationMap.entrySet()) {
        Long workShiftId = entry.getKey();
        AtomicLong driverRankingInBatch = new AtomicLong();
        // 按司机分排序
        List<ApplyDriverRelationDetailPO>
          relationDetailPOS = Optional.ofNullable(entry.getValue()).orElse(Lists.newArrayList()).stream().sorted(
          Comparator.comparingLong(o -> driverCityRank.getOrDefault(o.getDrvId(), Long.MAX_VALUE))).collect(
          Collectors.toList());
        for (ApplyDriverRelationDetailPO relationDetail : relationDetailPOS) {
          DriverPoint driverPoint = driverPointMap.get(relationDetail.getDrvId());
          if (driverPoint == null) {
            continue;
          }
          this.setDriverPointInfo(relationDetail.getDrvId(), driverPoint.getPoints(), driverPoint.getCityRanking(), driverRankingInBatch.incrementAndGet(), workShiftId);
        }
      }
    }catch (Exception e) {
      Cat.logEvent("driver", "driver-setDriverPointAndWorkShiftIntoApplyProcess-failed");
    }
  }

  public void setDriverApplicationMaximumLimit(Map<Long, TspTransportGroupWorkShiftPO> workShiftPOSMap, Map<Long, List<Long>> workShiftApplyInfo) {
    for (Map.Entry<Long, TspTransportGroupWorkShiftPO> workShiftPOEntry : workShiftPOSMap.entrySet()) {
      List<Long> applySuccessDriverIdList = workShiftApplyInfo.get(workShiftPOEntry.getKey());
      int applySuccessDriverIdCount = applySuccessDriverIdList == null ? 0 : applySuccessDriverIdList.size();
      workSiftDriverApplicationMaximumLimitMap.put(workShiftPOEntry.getKey(), workShiftPOEntry.getValue().getDriverUpperLimit() - applySuccessDriverIdCount);
    }
  }

  public List<TransportGroupDriverApplicationRecordPO> getTransportGroupDriverApplicationRecordPOList(
    UpdateTransportGroupApplyStatusSOARequestType soaRequestType,
    Map<Long, List<ApplyDriverRelationDetailPO>> participatingDriversMap) {
    List<TransportGroupDriverApplicationRecordPO> applicationRecordPOList = new ArrayList<>();
    Set<Long> drvIds = this.getWorkshiftApplySuccessDriverListMap().values().stream().flatMap(List::stream).collect(Collectors.toSet());
    for(ApplyDriverRelationDetailPO participatingDriver : participatingDriversMap.values().stream().flatMap(
      Collection::stream).collect(Collectors.toList())) {
      if (!drvIds.contains(participatingDriver.getDrvId())) {
        TransportGroupDriverApplyFailedReasonDTO drvDTO = this.getOrCreate(participatingDriver.getDrvId());
        if (drvDTO.getApplyStatus() == null || drvDTO.getApplyFailedType() == null) {
          continue;
        }
        TransportGroupDriverApplicationRecordPO recordPO = new TransportGroupDriverApplicationRecordPO();
        recordPO.setTransportGroupId(soaRequestType.getTransportGroupId());
        recordPO.setDrvId(drvDTO.getDrvId());
        recordPO.setWorkShiftId(participatingDriver.getWorkShiftId());
        recordPO.setApplyStatus(drvDTO.getApplyStatus().getCode());
        recordPO.setApplyFailedType(drvDTO.getApplyFailedType().getCode());
        recordPO.setApplyFailedReason(drvDTO.getRuleDescription());
        recordPO.setApplyFailedReasonDetail(
          this.getApplyFailedReasonDetail(drvDTO, participatingDriver.getWorkShiftId()));
        recordPO.setCreateUser(soaRequestType.getModifyUser());
        recordPO.setModifyUser(soaRequestType.getModifyUser());
        applicationRecordPOList.add(recordPO);
      }
    }
    return applicationRecordPOList;
  }

  /**
   * 剔除请假冻结时长没有触发阈值的记录
   */
  public void removeDriverLeaveAndFreezeDurationNotTriggerThreshold() {
    transportGroupDriverApplyFailedReasonDTOMap.values().removeIf(drvDTO -> drvDTO.getApplyStatus() == null);
  }

  public Map<Long, String> getDriverWorkPeriodMap() {
    return driverWorkPeriodMap;
  }

  public Map<Long, String> getApplySuccessDriverWorkPeriodMap() {
    Set<Long> successDrvSet =
      this.getWorkshiftApplySuccessDriverListMap().values().stream().flatMap(List::stream).collect(Collectors.toSet());
    return this.getDriverWorkPeriodMap().entrySet().stream().filter(entry -> successDrvSet.contains(entry.getKey())).collect(Collectors.toMap(
      Map.Entry::getKey,
      Map.Entry::getValue
    ));
  }
}
