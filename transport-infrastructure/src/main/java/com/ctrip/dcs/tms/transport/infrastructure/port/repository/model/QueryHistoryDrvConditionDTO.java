package com.ctrip.dcs.tms.transport.infrastructure.port.repository.model;

import java.util.*;

public final class QueryHistoryDrvConditionDTO {

    /**
     * 司机id列表
     */
    private List<Long> drvIdList;
    /**
     * 司机电话列表
     */
    private List<String> driverPhoneList;

    /**
     * 请求属性列
     */
    private String[] fields;

    public List<Long> getDrvIdList() {
        return drvIdList;
    }

    public List<String> getDriverPhoneList() {
        return driverPhoneList;
    }

    public String[] getFields() {
        return fields;
    }

    public QueryHistoryDrvConditionDTO withDrvIdList(List<Long> drvIdList) {
        this.drvIdList = drvIdList;
        return this;
    }

    public QueryHistoryDrvConditionDTO withDriverPhoneList(List<String> driverPhoneList) {
        this.driverPhoneList = driverPhoneList;
        return this;
    }

    public QueryHistoryDrvConditionDTO withFields(String[] fields) {
        this.fields = fields;
        return this;
    }

    private QueryHistoryDrvConditionDTO() {
    }

    public static QueryHistoryDrvConditionDTO newCondition() {
        return new QueryHistoryDrvConditionDTO();
    }

    public QueryHistoryDrvConditionDTO build() {
        QueryHistoryDrvConditionDTO condition = new QueryHistoryDrvConditionDTO();
        condition.fields = this.fields;
        condition.drvIdList = this.drvIdList;
        condition.driverPhoneList = this.driverPhoneList;
        return condition;
    }

}