package com.ctrip.dcs.tms.transport.infrastructure.port.repository;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.dal.*;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2020/3/16 17:46
 */
public interface DrvDriverLeaveRepository {

    /**
     * 校验请假时段是否重复(返回时段重复的记录数，返回结果大于0则有重复)
     * @param driverLeavePO
     * @return
     */
    int checkDrvLeaveTime(DrvDriverLeavePO driverLeavePO);

    /**
     * 司机请假
     * @param driverLeavePO
     * @return
     */
    int addDrvLeave(DrvDriverLeavePO driverLeavePO);

    /**
     * 司机销假
     * @param driverLeavePO
     * @return
     */
    int closeDrvLeave(DrvDriverLeavePO driverLeavePO);

    /**
     * 请假详情总条数统计
     * @param sample
     * @return
     */
    int countDrvLeaveDetail(DrvDriverLeavePO sample) ;

    /**
     * 请假详情查询
     * @param sample
     * @param pageInfo
     * @return
     */
    List<DrvLeaveDetailPO> queryDrvLeaveDetail(DrvDriverLeavePO sample, PaginatorDTO pageInfo) ;
    /**
     * 请假详情批量查询（有效且未销假）
     * @param drvIds
     * @return
     */
    List<DrvLeaveDetailPO> queryDrvLeaveDetailForDsp(List<Long> drvIds) ;


    /**
     * 请假详情批量查询（有效且未销假）
     * @param drvIds
     * @return
     */
    List<DrvLeaveDetailPO> queryDrvLeaveIng(List<Long> drvIds) ;

    /**
    　* @description: 查询司机请假记录
    　* <AUTHOR>
    　* @date 2023/1/3 17:03
    */
    List<DrvDriverLeavePO> queryDrvLeaveRecord(List<Long> drvIds) ;

    /**
     * 查询请假信息-运力库存使用
     * @param drvId
     * @param id
     * @return
     */
    public DrvDriverLeavePO queryDriverLeaveById(Long drvId, Long id);

    /**
     * 更新请假信息，目前仅更新udl
     * @param drvPo
     * @return
     */
    int update(DrvDriverLeavePO drvPo);
}
