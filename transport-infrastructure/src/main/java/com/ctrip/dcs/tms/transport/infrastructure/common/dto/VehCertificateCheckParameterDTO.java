package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

/***
 　* @description: 车辆核验参数
 　* <AUTHOR>
 　* @date 2021/10/8 10:08
 */
public class VehCertificateCheckParameterDTO {

    private Long checkId;
    private Integer checkType;
    private String vehicleLicense;//车牌号
    private String newVehicleLicense;//新车牌号
    private String vinCode;//车辆vin
    private String modifyUser;
    private String netTansCtfctImg;//网约车人证
    private String netAppealMaterials;//网约车申诉材料


    public static VehCertificateCheckParameterDTO vehCertificateCheck(Long checkId,Integer checkType,String vehicleLicense,String newVehicleLicense,String vinCode,String modifyUser,String netTansCtfctImg,String netAppealMaterials) {
        VehCertificateCheckParameterDTO auditDTO = new VehCertificateCheckParameterDTO();
        auditDTO.setCheckId(checkId);
        auditDTO.setCheckType(checkType);
        auditDTO.setVehicleLicense(vehicleLicense);
        auditDTO.setNewVehicleLicense(newVehicleLicense);
        auditDTO.setVinCode(vinCode);
        auditDTO.setModifyUser(modifyUser);
        auditDTO.setNetTansCtfctImg(netTansCtfctImg);
        auditDTO.setNetAppealMaterials(netAppealMaterials);
        return auditDTO;
    }

    public Long getCheckId() {
        return checkId;
    }

    public void setCheckId(Long checkId) {
        this.checkId = checkId;
    }

    public Integer getCheckType() {
        return checkType;
    }

    public void setCheckType(Integer checkType) {
        this.checkType = checkType;
    }

    public String getVehicleLicense() {
        return vehicleLicense;
    }

    public void setVehicleLicense(String vehicleLicense) {
        this.vehicleLicense = vehicleLicense;
    }

    public String getNewVehicleLicense() {
        return newVehicleLicense;
    }

    public void setNewVehicleLicense(String newVehicleLicense) {
        this.newVehicleLicense = newVehicleLicense;
    }

    public String getVinCode() {
        return vinCode;
    }

    public void setVinCode(String vinCode) {
        this.vinCode = vinCode;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public String getNetTansCtfctImg() {
        return netTansCtfctImg;
    }

    public void setNetTansCtfctImg(String netTansCtfctImg) {
        this.netTansCtfctImg = netTansCtfctImg;
    }

    public String getNetAppealMaterials() {
        return netAppealMaterials;
    }

    public void setNetAppealMaterials(String netAppealMaterials) {
        this.netAppealMaterials = netAppealMaterials;
    }
}
