package com.ctrip.dcs.tms.transport.infrastructure.port.repository;


import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.OverseasOcrRecordPO;
import com.ctrip.igt.framework.dal.DalRepository;

import java.sql.SQLException;
import java.util.List;

public interface OverseasOcrRecordRepository {

    Long insert(OverseasOcrRecordPO recordPO) throws SQLException;

    OverseasOcrRecordPO queryOverseasOcrRecordList(Long cityId, Integer requestType, String requestImg);

}
