package com.ctrip.dcs.tms.transport.infrastructure.common.qconfig;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.ctrip.dcs.tms.transport.infrastructure.common.dto.RejectReasonDTO;

import qunar.tc.qconfig.client.JsonConfig;

@Component
public class AuditRejectConfig {

    private static JsonConfig.ParameterizedClass parameterString = JsonConfig.ParameterizedClass.of(String.class);// map key的泛型类型
    private static JsonConfig.ParameterizedClass parametervalue = JsonConfig.ParameterizedClass.of(List.class, RejectReasonDTO.class);
    private static JsonConfig.ParameterizedClass parameter = JsonConfig.ParameterizedClass.of(Map.class, parameterString, parametervalue);
    private static JsonConfig<Map<String, List<RejectReasonDTO>>> complexTestJsonConfig = JsonConfig.get("tms.audit.reject.json", parameter);

    public Map<String, List<RejectReasonDTO>> getRejectReasonMap() {
        return complexTestJsonConfig.current();
    }
}
