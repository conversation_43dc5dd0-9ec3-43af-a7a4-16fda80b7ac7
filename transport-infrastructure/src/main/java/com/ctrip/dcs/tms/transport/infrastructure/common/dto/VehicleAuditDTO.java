package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;

import java.util.*;

/**
 * <AUTHOR>
 * @Description  车辆编辑异步审核
 * @Date 10:17 2020/9/28
 * @Param 
 * @return 
 **/
public class VehicleAuditDTO {
    private Long checkId;

    private Integer checkType;

    private Integer certificateType;
    /**
     * 车牌号
     */
    private String vehicleLicense;
    /**
     * 新车牌号
     */
    private String newVehicleLicense;
    /**
     * 车牌种类
     */
    private String vehicleLicenseType;
    /**
     * 原网约车车辆许可证
     */
    private String originNetTansCtfctUrl;
    /**
     * 新网约车车辆许可证
     */
    private String tarNetTansCtfctUrl;
    /**
     * 原行驶证
     */
    private String originVehicleCertiImg;
    /**
     * 行驶证
     */
    private String tarVehicleCertiImg;

    //车架号
    private String vinCode;

    //使用性质
    private String useType;

    private String userName;

    private Long id;

    private Long supplierId;

    private Integer versionFlag;

    private Integer checkStatus;

    private Integer recruitingType;

    private Long recruitingId;

    public static VehicleAuditDTO updateBuildVehicleDTO(VehVehiclePO orgVehPO,VehVehiclePO newVehPO,Integer checkType){
        VehicleAuditDTO carAuditDTO = new VehicleAuditDTO();
        carAuditDTO.setCheckId(orgVehPO.getVehicleId());
        carAuditDTO.setCheckType(checkType);
        carAuditDTO.setVehicleLicense(orgVehPO.getVehicleLicense());
        carAuditDTO.setOriginNetTansCtfctUrl(orgVehPO.getNetTansCtfctImg());
        carAuditDTO.setTarNetTansCtfctUrl(newVehPO.getNetTansCtfctImg());
        carAuditDTO.setVehicleLicenseType(TmsTransportConstant.VEHICLELICENSETYPE_FUEL);
        if(Objects.equals(orgVehPO.getVehicleEnergyType(),2)){
            carAuditDTO.setVehicleLicenseType(TmsTransportConstant.VEHICLELICENSETYPE_ENERGY);
        }
        carAuditDTO.setVinCode(orgVehPO.getVin());
        carAuditDTO.setUserName(orgVehPO.getModifyUser());
        carAuditDTO.setOriginVehicleCertiImg(orgVehPO.getVehicleCertiImg());
        carAuditDTO.setTarVehicleCertiImg(newVehPO.getVehicleCertiImg());
        carAuditDTO.setNewVehicleLicense(newVehPO.getVehicleLicense());
        return carAuditDTO;
    }

    public static VehicleAuditDTO rehBuildVehicleDTO(String vehicleLicense,String vehicleLicenseType,String vin,Long id ,String modifyUser,Integer checkType,Long supplierId,Integer versionFlag,Integer checkStatus,Long recruitingId,Integer recruitingType){
        VehicleAuditDTO carAuditDTO = new VehicleAuditDTO();
        carAuditDTO.setVehicleLicense(vehicleLicense);
        carAuditDTO.setVehicleLicenseType(vehicleLicenseType);
        carAuditDTO.setVinCode(vin);
        carAuditDTO.setId(id);
        carAuditDTO.setUserName(modifyUser);
        carAuditDTO.setCheckType(checkType);
        carAuditDTO.setSupplierId(supplierId);
        carAuditDTO.setVersionFlag(versionFlag);
        carAuditDTO.setCheckStatus(checkStatus);
        carAuditDTO.setRecruitingId(recruitingId);
        carAuditDTO.setRecruitingType(recruitingType);
        return carAuditDTO;
    }

    public static VehicleAuditDTO refreshAllVehicleDTO(VehVehiclePO orgVehPO){
        VehicleAuditDTO carAuditDTO = new VehicleAuditDTO();
        carAuditDTO.setCheckId(orgVehPO.getVehicleId());
        carAuditDTO.setCheckType(TmsTransportConstant.CertificateCheckTypeEnum.VEHICLE.getCode());
        carAuditDTO.setVehicleLicense(orgVehPO.getVehicleLicense());
        carAuditDTO.setVehicleLicenseType(TmsTransportConstant.VEHICLELICENSETYPE_FUEL);
        if(Objects.equals(orgVehPO.getVehicleEnergyType(),2)){
            carAuditDTO.setVehicleLicenseType(TmsTransportConstant.VEHICLELICENSETYPE_ENERGY);
        }
        carAuditDTO.setVinCode(orgVehPO.getVin());
        carAuditDTO.setUserName(TmsTransportConstant.TMS_DEFAULT_USERNAME);
        return carAuditDTO;
    }

    public static VehicleAuditDTO approveVehicleDTO(VehVehiclePO orgVehPO,VehVehiclePO newVehPO){
        VehicleAuditDTO carAuditDTO = new VehicleAuditDTO();
        carAuditDTO.setCheckId(orgVehPO.getVehicleId());
        carAuditDTO.setCheckType(TmsTransportConstant.CertificateCheckTypeEnum.VEHICLE.getCode());
        carAuditDTO.setVehicleLicense(orgVehPO.getVehicleLicense());
        carAuditDTO.setOriginNetTansCtfctUrl(orgVehPO.getNetTansCtfctImg());
        carAuditDTO.setTarNetTansCtfctUrl(newVehPO.getNetTansCtfctImg());
        carAuditDTO.setVehicleLicenseType(TmsTransportConstant.VEHICLELICENSETYPE_FUEL);
        if(Objects.equals(orgVehPO.getVehicleEnergyType(),2)){
            carAuditDTO.setVehicleLicenseType(TmsTransportConstant.VEHICLELICENSETYPE_ENERGY);
        }
        carAuditDTO.setVinCode(orgVehPO.getVin());
        carAuditDTO.setUserName(newVehPO.getModifyUser());
        carAuditDTO.setOriginVehicleCertiImg(orgVehPO.getVehicleCertiImg());
        carAuditDTO.setTarVehicleCertiImg(newVehPO.getVehicleCertiImg());
        carAuditDTO.setNewVehicleLicense(newVehPO.getVehicleLicense());
        return carAuditDTO;
    }

    public static VehicleAuditDTO rehApproveBuildVehicleDTO(String vehicleLicense,String vin){
        VehicleAuditDTO carAuditDTO = new VehicleAuditDTO();
        carAuditDTO.setVehicleLicense(vehicleLicense);
        carAuditDTO.setVinCode(vin);
        return carAuditDTO;
    }

    public String getNewVehicleLicense() {
        return newVehicleLicense;
    }

    public void setNewVehicleLicense(String newVehicleLicense) {
        this.newVehicleLicense = newVehicleLicense;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUseType() {
        return useType;
    }

    public void setUseType(String useType) {
        this.useType = useType;
    }

    public String getVehicleLicense() {
        return vehicleLicense;
    }

    public void setVehicleLicense(String vehicleLicense) {
        this.vehicleLicense = vehicleLicense;
    }

    public String getVehicleLicenseType() {
        return vehicleLicenseType;
    }

    public void setVehicleLicenseType(String vehicleLicenseType) {
        this.vehicleLicenseType = vehicleLicenseType;
    }

    public Long getCheckId() {
        return checkId;
    }

    public void setCheckId(Long checkId) {
        this.checkId = checkId;
    }

    public String getVinCode() {
        return vinCode;
    }

    public void setVinCode(String vinCode) {
        this.vinCode = vinCode;
    }

    public static VehicleAuditDTO builder() {
        return new VehicleAuditDTO();
    }

    public Integer getCheckType() {
        return checkType;
    }

    public void setCheckType(Integer checkType) {
        this.checkType = checkType;
    }

    public Integer getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(Integer certificateType) {
        this.certificateType = certificateType;
    }

    public String getOriginNetTansCtfctUrl() {
        return originNetTansCtfctUrl;
    }

    public void setOriginNetTansCtfctUrl(String originNetTansCtfctUrl) {
        this.originNetTansCtfctUrl = originNetTansCtfctUrl;
    }

    public String getTarNetTansCtfctUrl() {
        return tarNetTansCtfctUrl;
    }

    public void setTarNetTansCtfctUrl(String tarNetTansCtfctUrl) {
        this.tarNetTansCtfctUrl = tarNetTansCtfctUrl;
    }

    public String getOriginVehicleCertiImg() {
        return originVehicleCertiImg;
    }

    public void setOriginVehicleCertiImg(String originVehicleCertiImg) {
        this.originVehicleCertiImg = originVehicleCertiImg;
    }

    public String getTarVehicleCertiImg() {
        return tarVehicleCertiImg;
    }

    public void setTarVehicleCertiImg(String tarVehicleCertiImg) {
        this.tarVehicleCertiImg = tarVehicleCertiImg;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Integer getVersionFlag() {
        return versionFlag;
    }

    public void setVersionFlag(Integer versionFlag) {
        this.versionFlag = versionFlag;
    }

    public Integer getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }

    public Integer getRecruitingType() {
        return recruitingType;
    }

    public void setRecruitingType(Integer recruitingType) {
        this.recruitingType = recruitingType;
    }

    public Long getRecruitingId() {
        return recruitingId;
    }

    public void setRecruitingId(Long recruitingId) {
        this.recruitingId = recruitingId;
    }
}
