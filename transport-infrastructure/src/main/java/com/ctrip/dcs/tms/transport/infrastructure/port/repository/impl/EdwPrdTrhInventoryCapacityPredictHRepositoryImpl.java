package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.api.model.QueryEffCapacitySOARequestVO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.EdwPrdTrhInventoryCapacityPredictHPO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EdwPrdTrhInventoryCapacityPredictHRepository;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.DalRepositoryImpl;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.sqlbuilder.SelectSqlBuilder;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.sql.Types;
import java.util.Collections;
import java.util.List;


@Repository(value = "edwPrdTrhInventoryCapacityPredictHRepository")
public class EdwPrdTrhInventoryCapacityPredictHRepositoryImpl implements EdwPrdTrhInventoryCapacityPredictHRepository {

    private DalRepository<EdwPrdTrhInventoryCapacityPredictHPO> edwPredictRepo;

    public EdwPrdTrhInventoryCapacityPredictHRepositoryImpl() throws SQLException {
        edwPredictRepo = new DalRepositoryImpl<>(EdwPrdTrhInventoryCapacityPredictHPO.class);

    }
    @Override
    public List<EdwPrdTrhInventoryCapacityPredictHPO> queryCapacityPredictH(QueryEffCapacitySOARequestVO requestVO) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("use_date", requestVO.getUseDate(), Types.VARCHAR);
            if (StringUtils.isNotEmpty(requestVO.getUseTime())) {
                builder.and().equal("use_time", requestVO.getUseTime(), Types.VARCHAR);
            }
            if (requestVO.getRegionId() != null && requestVO.getRegionId() > 0) {
                builder.and().equal("region_id", requestVO.getRegionId(), Types.BIGINT);
            }
            if (CollectionUtils.isNotEmpty(requestVO.getCityIds())) {
                builder.and().in("city_id", requestVO.getCityIds(), Types.BIGINT);
            }
            builder.and().equal("car_type_id", requestVO.getCarTypeId(), Types.BIGINT);
            return edwPredictRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            throw  new BaijiRuntimeException(e);
        }
    }
}
