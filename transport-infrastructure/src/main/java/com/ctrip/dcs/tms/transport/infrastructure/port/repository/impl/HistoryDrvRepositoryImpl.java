package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.helper.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.springframework.stereotype.*;

import java.sql.*;
import java.util.*;

@Repository(value = "historyDrvRepository")
public class HistoryDrvRepositoryImpl implements HistoryDrvRepository {

    private static final Logger logger = LoggerFactory.getLogger(HistoryDrvRepositoryImpl.class);

    private DalRepository<DrvHistoryDriverPO> historyDriverPODalRepo;

    public HistoryDrvRepositoryImpl() throws SQLException {
        this.historyDriverPODalRepo = new DalRepositoryImpl<>(DrvHistoryDriverPO.class);
    }

    @Override
    public List<DrvHistoryDriverPO> queryHistoryDrvResourceByCondition(QueryHistoryDrvConditionDTO condition) {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.select(condition.getFields());
        try {
            if (CollectionUtils.isNotEmpty(condition.getDrvIdList())) {
                builder.and();
                builder.in("drv_id", condition.getDrvIdList(), Types.BIGINT);
            }
            if (CollectionUtils.isNotEmpty(condition.getDriverPhoneList())) {
                builder.and();
                builder.in("drv_phone", condition.getDriverPhoneList(), Types.VARCHAR);
            }
            return historyDriverPODalRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            logger.error("queryHistoryDrvResourceByConditionError", "conditionDTO:{} error:{}", JsonUtil.toJson(condition), e);
            return Lists.newArrayList();
        }
    }

}
