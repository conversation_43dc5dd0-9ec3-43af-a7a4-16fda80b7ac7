package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.ctrip.dcs.common.sdk.enums.DataMappingSourceEnum;
import com.ctrip.dcs.common.sdk.enums.DataMappingTableNameEnum;
import com.ctrip.dcs.common.sdk.repository.DataMappingRepository;
import com.ctrip.dcs.common.sdk.repository.DictionaryRepository;
import com.ctrip.dcs.common.sdk.value.Dictionary;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.geo.domain.repository.CountryRepository;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.geo.domain.value.Country;
import com.ctrip.dcs.international.sdk.repository.OralLanguageRepository;
import com.ctrip.dcs.international.sdk.value.OralLanguage;
import com.ctrip.dcs.poi.repository.Limit100Exception;
import com.ctrip.dcs.poi.repository.POIRepositoryManager;
import com.ctrip.dcs.poi.value.Airport;
import com.ctrip.dcs.poi.value.POI;
import com.ctrip.dcs.poi.value.Station;
import com.ctrip.dcs.scm.merchant.interfaces.dto.*;
import com.ctrip.dcs.scm.merchant.interfaces.message.*;
import com.ctrip.dcs.scm.sdk.domain.ContractRepository;
import com.ctrip.dcs.scm.sdk.domain.PrdDictionaryRepository;
import com.ctrip.dcs.scm.sdk.domain.ServiceProviderRepository;
import com.ctrip.dcs.scm.sdk.domain.SupplierRepository;
import com.ctrip.dcs.scm.sdk.domain.contract.Contract;
import com.ctrip.dcs.scm.sdk.domain.prddictionary.PrdDictionary;
import com.ctrip.dcs.scm.sdk.domain.serviceprovider.ServiceProvider;
import com.ctrip.dcs.scm.sdk.domain.supplier.Supplier;
import com.ctrip.dcs.tms.transport.api.model.LocationDTOSOA;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.dcs.vehicle.core.contract.QueryVehicleCategoryRelationsRequest;
import com.ctrip.dcs.vehicle.domain.repository.*;
import com.ctrip.dcs.vehicle.domain.value.*;
import com.ctrip.igt.PaginatorDTO;
import com.ctrip.igt.bizcomponent.basicdata.fixedlocation.FixedLocation;
import com.ctrip.igt.bizcomponent.basicdata.fixedlocation.FixedLocationRepository;
import com.ctrip.igt.bizcomponent.basicdata.fixedlocation.FixedLocationType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.ctrip.igt.open.message.QueryBrandAndVehicleModelRequestType;
import com.ctrip.igt.open.message.QueryBrandAndVehicleModelResponseType;
import com.ctrip.igt.open.message.VehicleModelDTO;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

/**
 * 枚举
 */
@Repository(value = "enumRepository")
public class EnumRepositoryImpl implements EnumRepository {

    private static final Logger LOGGER = LoggerFactory.getLogger(EnumRepositoryImpl.class);

    @Autowired /**城市*/
    private CityRepository cityRepository;
    @Autowired
    private CountryRepository countryRepository;
    @Autowired /*供应商*/
    SupplierRepository supplierRepository;

    @Autowired
    private DataMappingRepository dataMappingRepository;
    @Autowired /**品牌*/
            ServiceProviderRepository serviceProviderRepository;
    @Autowired /**枚举*/
            PmsProductServiceClientProxy pmsProductServiceClientProxy;
    @Autowired /**口语-语言*/
      OralLanguageRepository oralLanguageRepository;
    @Autowired
    FixedLocationRepository fixedLocationRepo;
    @Autowired
    SkuPriceSoaServiceClientProxy skuPriceSoaServiceClientProxy;
    @Autowired
    private DcsScmMerchantServiceClientProxy dcsScmMerchantServiceClientProxy;
    @Autowired
    private SceneVehicleModelRepository vehicleModelRepository;

    @Autowired
    VehicleCoreService vehicleCoreService;

    @Autowired
    private VehicleBrandRepository brandRepository;
    @Autowired
    private VehicleSeriesRepository vehicleSeriesRepository;
    @Autowired
    private DictionaryRepository dictionaryRepository;

    @Autowired
    private IGTOpenPubServiceClientProxy igtOpenPubServiceClientProxy;

    private final String SERVED_SCOPE = "contract.servedscope";

    @Autowired
    private ProductionLineUtil productionLineUtil;
    @Autowired
    private ContractRepository contractRepository;
    @Autowired
    private PrdDictionaryRepository prdDictionaryRepository;
    @Autowired
    private EnumRepositoryHelper helper;

    @Autowired
    StandardVehicleModelRepository standardVehicleModelRepository;

    @Autowired
    VehicleModelGroupRepository vehicleModelGroupRepository;

    @Override
    public String getApplyStatusName(Integer applyStatus) {
        if(applyStatus == null){
            applyStatus = 0;
        }
        Map<Integer, String> map = getNewItemMap(TmsTransportConstant.APPLYSTATUS);
        if (Objects.isNull(applyStatus)) {
            return "";
        }
        return map.get(applyStatus);
    }

    @Override
    public Map<Long, String> getOptionalIntendVehicleType(Long cityId, Long vehicleTypeId,boolean falg) {
        Map<Long, String> result = Maps.newHashMap();
        try {
            if (Objects.nonNull(cityId) && Objects.nonNull(vehicleTypeId)) {

                //查询下接的车型
                QueryVehicleCategoryRelationsRequest relationsRequest = new QueryVehicleCategoryRelationsRequest();
                relationsRequest.setTargetVehicleCategoryCode(String.valueOf(vehicleTypeId));

                //筛选出到当前车型是同级或升级的车型ID
                Set<Long> setVehicleTypeIds = Optional.ofNullable(vehicleCoreService.queryVehicleCategoryRelations(relationsRequest).getData()).orElse(Lists.newArrayList())
                  .stream().filter(item -> (falg && "0".equals(item.getRelationCode())) || "1".equals(item.getRelationCode()))
                  .map(item -> Long.valueOf(item.getSourceVehicleCategoryCode())).collect(
                  Collectors.toSet());

                // 获取车型
                Map<Long, StandardVehicleModel> vehicleModelMap =
                  Optional.ofNullable(standardVehicleModelRepository.findMany(setVehicleTypeIds, helper.getLocaleCode())).orElse(Lists.newArrayList())
                    .stream().collect(Collectors.toMap(StandardVehicleModel::getId, vehicleModel -> vehicleModel, (a, b) -> b));

                // 获取车型分组
                Map<Long, String> groupNameMap = Optional.ofNullable(vehicleModelGroupRepository.findMany(
                  vehicleModelMap.values().stream().map(StandardVehicleModel::getVehicleModelGroupId).collect(Collectors.toList()),
                  helper.getLocaleCode())).orElse(Lists.newArrayList()).stream().collect(
                  Collectors.toMap(VehicleModelGroup::getGroupId, VehicleModelGroup::getGroupName, (a, b) -> b));

                // 返回车型ID和车型名称
                vehicleModelMap.forEach((k, v) -> result.put(k, groupNameMap.get(v.getVehicleModelGroupId()) + "-" + v.getTranslationName()));

            }
        } catch (Exception e) {
            LOGGER.error(e);
        }
        return result;
    }

    @Override
    public String getColorName(Long colorId) {
        if (colorId == null || colorId == 0) {
            return "";
        }
        com.ctrip.dcs.common.sdk.value.Dictionary dictionary = dictionaryRepository.findOne(colorId);
        if (dictionary == null) {
            return "";
        }
        return dictionary.getDictValue();
    }

    @Override
    public String getHasDrvStr(Boolean hasDrv) {
        Map<Integer, String> map = getNewItemMap(TmsTransportConstant.DRIVERBIND);
        return hasDrv ? map.get(1) : (Strings.isNullOrEmpty(map.get(2)) ? map.get(0) : map.get(2));
    }

    @Override
    public Long getVehicleTypeIdBySeries(Long vehicleSeries, Long cityId) {
        if (Objects.isNull(vehicleSeries) || Objects.isNull(cityId)) {
            return null;
        }
        QueryBrandAndVehicleModelRequestType queryBrandAndVehicleModelRequestType = new QueryBrandAndVehicleModelRequestType();
        queryBrandAndVehicleModelRequestType.setCityId(cityId);
        queryBrandAndVehicleModelRequestType.setCarId(String.valueOf(vehicleSeries));
        try {
            QueryBrandAndVehicleModelResponseType queryBrandAndVehicleModelResponseType = igtOpenPubServiceClientProxy.queryBrandAndVehicleModel(queryBrandAndVehicleModelRequestType);
            if (ServiceResponseConstants.ResStatus.SUCCESS_CODE.equals(queryBrandAndVehicleModelResponseType.getResponseResult().getReturnCode())) {
                List<VehicleModelDTO> modelList = queryBrandAndVehicleModelResponseType.getModelList();
                if (CollectionUtils.isNotEmpty(modelList)) {
                    return modelList.get(0).getId();
                }
            }
        } catch (Exception e) {
            LOGGER.error(e);
        }
        return null;
    }

    @Override
    public String getVehicleTypeName(Long vehicleTypeId) {
        if (vehicleTypeId == null || vehicleTypeId <= 0) {
            return "";
        }
        SceneVehicleModel vehicleModel = vehicleModelRepository.findOne(vehicleTypeId, null, helper.getLocaleCode());
        if (vehicleModel == null) {
            return "";
        }
        return vehicleModel.getTranslationName();
    }

    @Override
    public String getUsingNatureValue(Integer usingNatureValue) {
        if (usingNatureValue == null) {
            return "";
        }
        Map<Integer, String> resultMap = getNewItemMap(TmsTransportConstant.VEHICLEOPERATIONTYPE);
        if (MapUtils.isEmpty(resultMap)) {
            return "";
        }
        return resultMap.get(usingNatureValue);
    }

    @Override
    public String getCityName(Long cityId) {
        if (cityId == null || cityId <= 0) {
            return "";
        }
        City city = cityRepository.findOne(cityId,helper.getLocaleCode());
        if(Objects.isNull(city)){
            return "";
        }
        return city.getTranslationName();
    }

    @Override
    public String getCountryName(Long countryId) {
        Country country = countryRepository.findOne(countryId, helper.getLocaleCode());
        return Objects.isNull(country) ? "" : country.getTranslationName();
    }

    @Override
    public City getCityById(Long cityId) {
        if (Objects.isNull(cityId)) {
            return null;
        }
        return cityRepository.findOne(cityId,helper.getLocaleCode());
    }

    @Override
    public String getSupplierName(Long supplierId) {
        if (supplierId == null || supplierId.intValue() <= 0) {
            return "";
        }
        com.ctrip.dcs.scm.sdk.domain.supplier.Supplier supplier = supplierRepository.findOne(supplierId, helper.getLocaleCode());
        if (supplier == null) {
            return "";
        }
        return supplier.getName();
    }

    @Override
    public Map<Long, Supplier> batchGetSupplier(List<Long> supplierIdList) {
        if (CollectionUtils.isEmpty(supplierIdList)) {
            return Maps.newHashMap();
        }
        return supplierRepository.findMany(supplierIdList, helper.getLocaleCode());
    }

    @Override
    public String getBandName(Long bandId) {
        if (bandId == null || bandId.intValue() <= 0) {
            return "";
        }
        VehicleBrand brand = brandRepository.findOne(bandId, helper.getLocaleCode());
        if (brand == null) {
            return "";
        }
        return brand.getBrandName();
    }

    @Override
    public String getVehicleSeriesName(Long vehicleSeriesId) {
        if (vehicleSeriesId == null || vehicleSeriesId.intValue() <= 0) {
            return "";
        }
        VehicleSeries car = vehicleSeriesRepository.findOne(vehicleSeriesId, helper.getLocaleCode());
        if (car == null) {
            return "";
        }
        return car.getSeriesName();
    }

    @Override
    public Long getVehicleSeriesIdByOld(Long vehicleSeriesId) {
        String ctripValue = dataMappingRepository.getCtripValue(String.valueOf(vehicleSeriesId), DataMappingTableNameEnum.BASIC_VEHICLE_CARS, DataMappingSourceEnum.QUNAR);
        return Strings.isNullOrEmpty(ctripValue) ? null : Long.valueOf(ctripValue);
    }

    @Override
    public Long getBandByOld(Long bandId) {
        String ctripValue = dataMappingRepository.getCtripValue(String.valueOf(bandId), DataMappingTableNameEnum.BASIC_VEHICLE_BRANDS, DataMappingSourceEnum.QUNAR);
        return Strings.isNullOrEmpty(ctripValue) ? null : Long.valueOf(ctripValue);
    }

    @Override
    public Map<Integer, String> getDrvStatusName() {
        return getNewItemMap(TmsTransportConstant.DRIVERSTATUS);
    }

    @Override
    public Map<String, String> getDrvLanguageMap() {
        List<OralLanguage> oralLanguageList = oralLanguageRepository.findAll(helper.getLocaleCode());
        if (CollectionUtils.isEmpty(oralLanguageList)) {
            return Collections.emptyMap();
        }
        Map<String, String> resultMap = Maps.newHashMapWithExpectedSize(oralLanguageList.size());
        for (OralLanguage language : oralLanguageList) {
            resultMap.put(language.getCode(), language.getTranslateName());
        }
        return resultMap;
    }

    @Override
    public String getDrvLanguageName(String drvLanguage, Map<String, String> languageMap) {

        StringBuilder stringBuilder = new StringBuilder();
        if (StringUtils.isNotEmpty(drvLanguage) && MapUtils.isNotEmpty(languageMap)) {
            String[] languageCode = drvLanguage.split(TmsTransportConstant.SPLIT);
            for (String code : languageCode) {
                if (StringUtils.isNotEmpty(languageMap.get(code))) {
                    stringBuilder.append(languageMap.get(code)).append(TmsTransportConstant.SPLIT);
                }
            }
            if (stringBuilder.length() > 0) {
                stringBuilder.deleteCharAt(stringBuilder.length() - 1);
            }
        }
        return stringBuilder.toString();
    }

    @Override
    public Map<Integer, String> getDrvFrom() {//1司机自助、2人工录入
        return getNewItemMap(TmsTransportConstant.DRIVERSOURCE);
    }

    @Override
    public String getVehicleEnergyTypeName(Integer vehicleEnergyType) {
        if (vehicleEnergyType == null) {
            return "";
        }
        Map<Long, Dictionary> resultMap = getDictionaryMap(TmsTransportConstant.VEHICLETYPE);
        Dictionary dictionary = resultMap.get(Long.parseLong(vehicleEnergyType + ""));
        return dictionary == null ? "" : dictionary.getDictValue();
    }

    @Override
    public int getAreaScope(Long cityId) {
        //1标识境外 0表示境内
        return cityRepository.isNotChineseMainland(cityId) ? 1 : 0;
    }

    @Override
    public Map<Integer, String> getTakeOrderLimitTimeMap() {
        return getNewItemMap(TmsTransportConstant.MINBOOKINGPERIOD);
    }

    @Override
    public String getIntendVehicleTypeName(String intendVehicleType) {
        if (StringUtils.isEmpty(intendVehicleType)) {
            return "";
        }
        List<Long> vehicleTypeId = Arrays.stream(intendVehicleType.split(TmsTransportConstant.SPLIT))
                .map(s -> Long.parseLong(s.trim()))
                .collect(Collectors.toList());
        List<SceneVehicleModel> vehicleModels = vehicleModelRepository.findMany(vehicleTypeId, null, helper.getLocaleCode());
        if (CollectionUtils.isEmpty(vehicleModels)) {
            return "";
        }
        return vehicleModels.stream().map(SceneVehicleModel::getTranslationName).collect(Collectors.joining(TmsTransportConstant.SPLIT));
    }

    @Override
    public FixedLocation getFixedLocation(Integer locationType, String fixedLocationCode) {
        if (locationType == null || StringUtils.isEmpty(fixedLocationCode)) {
            return null;
        }
        return fixedLocationRepo.findOne(locationType == 2 ? FixedLocationType.STATION : FixedLocationType.AIRPORT, fixedLocationCode);
    }

    @Override
    public ServiceProvider getSkuServiceProvider(Long serviceProviderId) {
        if (serviceProviderId == null || serviceProviderId <= 0) {
            return null;
        }
        return serviceProviderRepository.findOne(serviceProviderId);
    }

    @Override
    public Map<Integer, String> getTransportGroupMode() {
        return getNewItemMap(TmsTransportConstant.TRANSPORTGROUPTYPE);
    }

    @Override
    public List<Long> queryServiceProviderIds(Long supplierId) {
        try {
            if (supplierId == null || supplierId <= 0) {
                return Collections.emptyList();
            }
            QueryServiceProviderListRequestType request = new QueryServiceProviderListRequestType();
            ServiceProviderListQueryFilterDTO inclusionFilter = new ServiceProviderListQueryFilterDTO();
            inclusionFilter.setSupplierIds(Lists.newArrayList(supplierId));
            request.setInclusionFilter(inclusionFilter);
            PaginatorDTO pagingSetting = new PaginatorDTO();
            pagingSetting.setPageSize(100);
            pagingSetting.setPageNo(1);
            request.setPagingSetting(pagingSetting);
            QueryServiceProviderListResponseType queryServiceProviderListResponseType = dcsScmMerchantServiceClientProxy.queryServiceProviderList(request);
            if (CollectionUtils.isEmpty(queryServiceProviderListResponseType.getServiceProviders())) {
                return Collections.emptyList();
            }
            List<Long> collect = queryServiceProviderListResponseType.getServiceProviders()
                    .stream()
                    .map(serviceProviderDTO -> serviceProviderDTO.getBase().getId())
                    .collect(Collectors.toList());
            return collect;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Collections.emptyList();
    }

    @Override
    public Map<Integer, String> getRecruitingApproverStatus() {
        return getNewItemMap(TmsTransportConstant.APPROVERSTATUS);
    }

    @Override
    public List<Long> queryCityListByServiceProviderIds(List<Long> serviceProviderIds, Long supplierId) {
        ContractListQueryFilterDTO filterDTO = new ContractListQueryFilterDTO();
        filterDTO.setServiceProviderIds(serviceProviderIds);
        filterDTO.setSupplierIds(Arrays.asList(supplierId));
        List<ContractDTO> contractDTOList = getContractList(filterDTO);
        if (CollectionUtils.isEmpty(contractDTOList)) {
            return Collections.emptyList();
        }
        Set<Long> citySet = Sets.newHashSet();
        contractDTOList.forEach(contractDTO -> {
            if (CollectionUtils.isNotEmpty(contractDTO.getServedScope())) {
                contractDTO.getServedScope().forEach(servedScopesDTO -> {
                    if (CollectionUtils.isNotEmpty(servedScopesDTO.getCityIds())) {
                        servedScopesDTO.getCityIds().forEach(cityIds -> citySet.add(cityIds));
                    }
                });
            }
        });
        return new ArrayList<>(citySet);
    }

    @Override
    public Map<Integer, String> getDrvCoopMode() {
        return getNewItemMap(TmsTransportConstant.COOPMODE);
    }

    @Override
    public Map<Integer, String> getTransportGroupStatusMap() {
        return getNewItemMap(TmsTransportConstant.TRANSPORTGROUPSTATUS);
    }

    @Override
    public String getSupplierEmail(Long supplied) {
        if(supplied == null || supplied <=0){
            return "";
        }
        try{
            QuerySupplierListRequestType requestType = new QuerySupplierListRequestType();
            SupplierListQueryFilterDTO filterDTO = new SupplierListQueryFilterDTO();
            filterDTO.setSupplierIds(Arrays.asList(supplied));
            requestType.setInclusionFilter(filterDTO);
            requestType.setRetrievalItems(Arrays.asList("supplier.contact"));
            QuerySupplierListResponseType responseType = dcsScmMerchantServiceClientProxy.querySupplierList(requestType);
            if(responseType == null || !Objects.equals(responseType.getResponseResult().getReturnCode(), TmsTransportConstant.SUCCESS_CODE)){
                return "";
            }
            List<SupplierContactDTO> contactDTOS =  responseType.getSupplierContactInfoList();
            if(CollectionUtils.isNotEmpty(contactDTOS)){
                return contactDTOS.get(0).getContactEmail();
            }
            return "";
        }catch (Exception e){
            throw new BaijiRuntimeException("getSupplierEmail error", e);
        }
    }

    @Override
    public Map<String, Object> queryServiceProvider(long contractId) {
        Map<String, Object> result = Maps.newHashMap();
        if (contractId == 0) {
            return result;
        }
        QueryContractListRequestType requestType = new QueryContractListRequestType();
        requestType.setRetrievalItems(ImmutableList.of("serviceprovider.base"));
        ContractListQueryFilterDTO filterDTO = new ContractListQueryFilterDTO();
        filterDTO.setContractIds(ImmutableList.of(contractId));
        requestType.setInclusionFilter(filterDTO);
        try {
            QueryContractListResponseType responseType = dcsScmMerchantServiceClientProxy.queryContractList(requestType);
            if (requestType == null || CollectionUtils.isEmpty(responseType.getServiceProviders()) || CollectionUtils.isEmpty(responseType.getContracts())) {
                return result;
            }
            ContractDTO resultDto = null;
            for (ContractDTO contract : responseType.getContracts()) {
                if (contract.getBase() != null && contract.getBase().getId().longValue() == contractId) {
                    resultDto = contract;
                    break;
                }
            }
            Long resultServiceProviderId = 0L;
            if (resultDto != null) {
                result.put("saleModeId", resultDto.getBase().getSalesMode());
                resultServiceProviderId = resultDto.getBase().getServiceProviderId();
            }
            for (ServiceProviderDTO serviceProviderDTO : responseType.getServiceProviders()) {
                if (serviceProviderDTO.getBase() != null && serviceProviderDTO.getBase().getId().longValue() == resultServiceProviderId.longValue()) {
                    result.put("serviceProviderId", serviceProviderDTO.getBase().getId());
                    result.put("serviceProviderName", serviceProviderDTO.getBase().getBrandLocalName());
                    return result;
                }
            }
            return result;
        } catch (Exception e) {
            LOGGER.error("QueryServiceProviderError", "contractId:{}", contractId);
            return result;
        }
    }

    @Override
    public String getLocationName(String locationCode, Integer locationType) {
        if (StringUtils.isEmpty(locationCode)) {
            return "";
        }
        try {
            String name = "";
            if (locationType == 1) {
                Airport airport = getAirport(locationCode);
                if (airport == null) {
                    return "";
                }
                name = airport.getName();
            } else {
                Station station = getStation(locationCode);
                if (station == null) {
                    return "";
                }
                name = station.getStationName();
            }
            return name;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    @Nullable
    private Station getStation(String locationCode) {
        Station station = POIRepositoryManager.stationRepository.findOne(Integer.parseInt(locationCode), helper.getLocaleCode());
        if (station == null) {
            return null;
        }
        if (!station.isActive()) {
            return null;
        }
        return station;
    }

    @Nullable
    private Airport getAirport(String locationCode) {
        Airport airport = POIRepositoryManager.airportRepository.findOne(locationCode, helper.getLocaleCode());
        if (airport == null) {
            return null;
        }
        boolean active = airport.isActive();
        if (!active) {
            return null;
        }
        return airport;
    }

    @Override
    public List<LocationDTOSOA> queryLocalByCity(Long cityId) {
        List<LocationDTOSOA> locationDTOSOAList = Lists.newArrayList();
        if(cityId == null || cityId <=0){
            return locationDTOSOAList;
        }
        List<Airport> airports = getAirports(cityId);
        if(CollectionUtils.isNotEmpty(airports)){
            airports.forEach(airport -> {
                LocationDTOSOA dtosoa = new LocationDTOSOA();
                dtosoa.setLocationCode(airport.getCode());
                dtosoa.setLocationName(airport.getName());
                locationDTOSOAList.add(dtosoa);
            });
        }
        List<Station> stationList = getStations(Collections.singletonList(cityId));
        if(CollectionUtils.isNotEmpty(stationList)){
            stationList.forEach(station -> {
                LocationDTOSOA dtosoa = new LocationDTOSOA();
                dtosoa.setLocationCode(String.valueOf(station.getStationId()));
                dtosoa.setLocationName(station.getStationName());
                locationDTOSOAList.add(dtosoa);
            });
        }

        return locationDTOSOAList;
    }

    @Nullable
    private List<Airport> getAirports(Long cityId) {
        List<Airport> airports = POIRepositoryManager.airportRepository.findAllOfCity(cityId, helper.getLocaleCode());
        if (CollectionUtils.isNotEmpty(airports)) {
            airports= airports.stream().filter(POI::isActive).collect(Collectors.toList());
        }
        return airports;
    }

    @NotNull
    private List<Station> getStations(List<Long> cityIdList) {
        List<Station> stationList = new ArrayList<>();
        List<List<Long>> partition = Lists.partition(cityIdList, 100);
        for (List<Long> cityIds : partition) {
            try {
                List<Station> stations = POIRepositoryManager.stationRepository.findByCityIdListLimit100(cityIds, helper.getLocaleCode());
                if (CollectionUtils.isNotEmpty(stations)) {
                    stations= stations.stream().filter(POI::isActive).collect(Collectors.toList());
                }
                if (CollectionUtils.isNotEmpty(stations)) {
                    stationList.addAll(stations);
                }
            } catch (Limit100Exception e) {
                LOGGER.error("QueryServiceProviderError", "cityIds:{}", JsonUtil.toJson(cityIds));
            }
        }
        return stationList;
    }

    @Override
    public Map<Integer, String> getVehicleStatus() {
        return getNewItemMap(TmsTransportConstant.VEHICLESTATUS);
    }

    @Override
    public List<Long> queryContractByServiceProviderId(Long serviceProviderId) {
        if (serviceProviderId == null) {
            return Collections.emptyList();
        }
        ContractListQueryFilterDTO filterDTO = new ContractListQueryFilterDTO();
        filterDTO.setServiceProviderIds(ImmutableList.of(serviceProviderId));
        List<ContractDTO> contractDTOList = getContractList(filterDTO);
        if (CollectionUtils.isEmpty(contractDTOList)) {
            return Collections.emptyList();
        }
        Set<Long> contractIdSet = Sets.newHashSet();
        for (ContractDTO contractDTO : contractDTOList) {
            if (contractDTO != null && contractDTO.getBase() != null) {
                contractIdSet.add(contractDTO.getBase().getId());
            }
        }
        return new ArrayList<>(contractIdSet);
    }

    @Override
    public String getCityNameSplt(Collection<Long> cityIds) {
        try {
            if (cityRepository == null || CollectionUtils.isEmpty(cityIds)) {
                return "";
            }
            StringBuilder cityName = new StringBuilder();
            Map<Long,City> cities =  cityRepository.findMany(cityIds,helper.getLocaleCode());
            if(org.springframework.util.CollectionUtils.isEmpty(cities)){
                return "";
            }
            for (Long cityId : cityIds) {
                City city = cities.get(cityId);
                if(city != null){
                    cityName.append(city.getTranslationName()).append(",");
                }
            }
            if(StringUtils.isNotEmpty(cityName.toString())){
                cityName.deleteCharAt(cityName.length() - 1);
            }
            return cityName.toString();
        } catch (Exception e) {
            return "";
        }
    }

    @Override
    public Map<Integer, String> queryCheckStatusMap() {
        return getNewItemMap(TmsTransportConstant.CHECKSTATUS);
    }

    @Override
    public Map<Integer, String> queryTagsStatusMap() {
        return getNewItemMap(TmsTransportConstant.CERTIFICATE_TAG_STATUS);
    }

    @Override
    public SceneVehicleModel getVehicleType(Long vehicleTypeId) {
        if (vehicleTypeId == null || vehicleTypeId <= 0) {
            return null;
        }
        return vehicleModelRepository.findOne(vehicleTypeId, null, helper.getLocaleCode());
    }

    private List<ContractDTO> getContractList(ContractListQueryFilterDTO filterDTO) {
        QueryContractListRequestType requestType = new QueryContractListRequestType();
        requestType.setRetrievalItems(Arrays.asList(SERVED_SCOPE));
        requestType.setInclusionFilter(filterDTO);
        requestType.setPagingSetting(new PaginatorDTO(1, 1000000));
        try {
            QueryContractListResponseType responseType = dcsScmMerchantServiceClientProxy.queryContractList(requestType);
            if (responseType == null || !Objects.equals(responseType.getResponseResult().getReturnCode(), TmsTransportConstant.SUCCESS_CODE)) {
                return Collections.emptyList();
            }
            return responseType.getContracts();
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    @Override
    public Map<Integer, Set<Long>> getServedScopeMapByContractId(long contractId) {
        ContractListQueryFilterDTO filterDTO = new ContractListQueryFilterDTO();
        filterDTO.setContractIds(ImmutableList.of(contractId));
        List<ContractDTO> contractList = getContractList(filterDTO);
        if (CollectionUtils.isEmpty(contractList)) {
            return Collections.EMPTY_MAP;
        }
        Map<Integer, Set<Long>> res = Maps.newHashMap();
        for (ContractDTO contractDTO : contractList) {
            List<ServedScopesDTO> scopesDTOS = contractDTO.getServedScope();
            if (CollectionUtils.isEmpty(scopesDTOS)) {
                continue;
            }
            for (ServedScopesDTO scopesDTO : scopesDTOS) {
                Integer productLine = productionLineUtil.getDBProductLineCode(scopesDTO.getCategoryCode());
                Set<Long> citySet = res.getOrDefault(productLine, Sets.newHashSet());
                citySet.addAll(scopesDTO.getCityIds());
                res.put(productLine, citySet);
            }
        }
        return res;
    }

    @Override
    public Map<Integer, String> getApproveNodeName() {
        return getNewItemMap(TmsTransportConstant.APPROVENODE);
    }

    @Override
    public Map<Integer, String> getApproveEnentType() {
        return getNewItemMap(TmsTransportConstant.APPROVEEVENTTYPE);
    }

    @Override
    public Map<Integer, String> getTransportApproveStatus() {
        return getNewItemMap(TmsTransportConstant.TRANSPORTGROUPSTATUS);
    }

    @Override
    public Map<Long, List<Contract>> getContractRepository() {
        Map<Long, List<Contract>> contranctMap = Maps.newHashMap();
        List<Contract> contractList =  getContractList();
        if(CollectionUtils.isEmpty(contractList)){
            return contranctMap;
        }
        contranctMap = contractList.stream().collect(Collectors.groupingBy(Contract::getSupplierId));
        return contranctMap;
    }

    protected List<Contract> getContractList() {
        int pageNo = 1;
        int pageSize = 200;
        QueryBasicContractResponseType response;
        List<Contract> contractList = Lists.newArrayList();
        do {
            QueryBasicContractRequestType requestType = new QueryBasicContractRequestType();
            requestType.setPagingSetting(new PaginatorDTO(pageNo, pageSize));
            response =
              dcsScmMerchantServiceClientProxy.queryBasicContract(requestType);
            contractList.addAll(convert2contractList(response.getContracts()));
            pageNo++;
        } while (CollectionUtils.isNotEmpty(response.getContracts()) && pageSize == response.getContracts().size());

        return contractList;
    }

    private Collection<Contract> convert2contractList(List<BasicContractDTO> contracts) {
        return Optional.ofNullable(contracts).orElse(Lists.newArrayList()).stream()
          .map(contractDTO -> Contract.newBuilder().withServiceProviderId(contractDTO.getServiceProviderId()).build()).collect(Collectors.toList());
    }

    @Override
    public ServiceProvider getServiceProviderByContractId(Long contractId) {
        ServiceProvider defaultServiceProvider = ServiceProvider.newBuilder().build();
        if (contractId == null) {
            return defaultServiceProvider;
        }
        Contract contract = contractRepository.findOne(contractId);
        if (contract == null) {
            return defaultServiceProvider;
        }
        ServiceProvider serviceProvider = serviceProviderRepository.findOne(contract.getServiceProviderId());
        if (serviceProvider == null) {
            return defaultServiceProvider;
        }
        return serviceProvider;
    }

    @Override
    public String getApproveAgingName(Integer approveAging) {
        return getDicItemV(approveAging,TmsTransportConstant.DicCode.APPROVEAGING);
    }

    @Override
    public String getApproveScheduleName(Integer approveSchedule) {
        return getDicItemV(approveSchedule,TmsTransportConstant.DicCode.APPROVESCHEDULE);
    }

    @Override
    public Map<Integer, String> getSingleApproveItem() {
        return getNewItemMap(TmsTransportConstant.DicCode.SINGLEAPPROVEITEM);
    }

    @Override
    public Long getCountryId(Long cityId) {
        if (cityId == null) {
            return 0L;
        }
        City city = cityRepository.findOne(cityId);
        if (Objects.isNull(city)) {
            return 0L;
        }
        return city.getCountryId() == null ? 0L : city.getCountryId();
    }

    @Override
    public String getColorKey(Long colorId) {
        if (colorId == null || colorId == 0) {
            return "";
        }
        Dictionary dictionary = dictionaryRepository.findOne(colorId);
        if (dictionary == null) {
            return "";
        }
        return dictionary.getDictKey();
    }

    @Override
    public String getQCityIdByCCityId(Long cityId) {
        return cityRepository.findQunarCodeByCityId(cityId);
    }

    @Override
    public boolean isNotChinaMainLand(Long cityId) {
        return cityRepository.isNotChineseMainland(cityId);
    }

    @Override
    public List<City> queryByCityIds(List<Long> cityIds) {
        Map<Long, City> cityMap = cityRepository.findMany(cityIds,helper.getLocaleCode());
        if(org.springframework.util.CollectionUtils.isEmpty(cityMap)){
            return new ArrayList<>();
        }
        return new ArrayList<>(cityMap.values());
    }

    public String getDicItemV(Integer itemKey,String code){
        if (itemKey == null) {
            return "";
        }
        Map<Integer, String> resultMap = this.getNewItemMap(code);
        if (MapUtils.isEmpty(resultMap)) {
            return "";
        }
        return resultMap.get(itemKey);
    }

    private Map<Long,Dictionary> getDictionaryMap(String dicType){
        List<Dictionary> all = dictionaryRepository.findByType(dicType, helper.getLocaleCode());
        if(CollectionUtils.isEmpty(all)){
            return Collections.emptyMap();
        }
        Map<Long,Dictionary> resultMap  = all.stream().collect(Collectors.toMap(Dictionary::getId,dictionary -> dictionary));
        if(MapUtils.isEmpty(resultMap)){
            return Collections.emptyMap();
        }
        return resultMap;
    }

    /***
    　* @description: TODO 新接入枚举缓存
    　* <AUTHOR>
    　* @date 2021/8/17 11:20
    */
    private Map<Integer,String> getNewItemMap(String code){
        List<PrdDictionary> itemDTOList = prdDictionaryRepository.findByCode(code, helper.getLocaleCode());
        if(CollectionUtils.isEmpty(itemDTOList)){
            return Maps.newHashMap();
        }
        Map<Integer,String> itemMap = Maps.newHashMap();
        for(PrdDictionary itemDTO : itemDTOList){
            itemMap.put(Integer.parseInt(itemDTO.getItemKey()),itemDTO.getItemValue());
        }
        return itemMap;
    }
}
