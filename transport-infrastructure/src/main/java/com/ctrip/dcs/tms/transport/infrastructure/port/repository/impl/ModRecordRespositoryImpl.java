package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.udl.UDLHandler;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.common.jackson.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.google.common.base.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.beans.*;
import java.lang.reflect.*;
import java.sql.*;
import java.util.Date;
import java.util.*;

@Repository(value = "modRecordRespository")
public class ModRecordRespositoryImpl<T> implements ModRecordRespository<T> {

    private DalRepository<TmsModRecordPO> tmsModRecordRepo;

    @Autowired
    private TransportGroupRepository transportGroupRepository;

    @Autowired
    private VehicleRepository vehicleRepository;

    @Autowired
    private DrvDrvierRepository drvDrvierRepository;

    @Autowired
    EnumRepository enumRepository;

    @Autowired
    DrvRecruitingRepository drvRecruitingRepository;

    @Autowired
    VehicleRecruitingRepository vehicleRecruitingRepository;

    @Autowired
    UDLHandler udlHandler;

    public ModRecordRespositoryImpl() throws SQLException {
        tmsModRecordRepo = new DalRepositoryImpl<>(TmsModRecordPO.class);
    }

    @Override
    public DalRepository<TmsModRecordPO> getTmsModRecordRepo() {
        return tmsModRecordRepo;
    }

    @Override
    public boolean insetModRecord(Long rrdId, T newMod, CommonEnum.RecordTypeEnum recordTypeEnum, Map<String, String> attributeKeyValue,String modifyUser) {
        TmsModRecordPO modRecordPO = new TmsModRecordPO();
        modRecordPO.setDatachangeCreatetime(new Timestamp(System.currentTimeMillis()));
        modRecordPO.setDatachangeLasttime(modRecordPO.getDatachangeCreatetime());


        modRecordPO.setModifyUser(modifyUser);
        modRecordPO.setCreateUser(modifyUser);
        T mod = getSource(rrdId, recordTypeEnum);
        if (newMod != null) {
            modRecordPO.setModContent(getAssembleJsonList(mod, newMod, attributeKeyValue, recordTypeEnum, getNeedArr(recordTypeEnum, isForeign(recordTypeEnum, newMod))));
        }
        modRecordPO.setModType(newMod == null ? 0 : 1);
        modRecordPO.setRrdType(recordTypeEnum.getCode());
        modRecordPO.setRrdId(rrdId);
        setUdl(rrdId, recordTypeEnum, modRecordPO,  mod);
        return tmsModRecordRepo.insert(modRecordPO) > 0;
    }

    private void setUdl(Long rrdId, CommonEnum.RecordTypeEnum recordTypeEnum, TmsModRecordPO modRecordPO,
      T mod) {
        if (recordTypeEnum == CommonEnum.RecordTypeEnum.DRIVER || recordTypeEnum == CommonEnum.RecordTypeEnum.DrVFREEZE) {
            modRecordPO.setProviderDataLocation(udlHandler.getDrvUdl(rrdId));
        }
        if (recordTypeEnum == CommonEnum.RecordTypeEnum.RECRUIT) {
            modRecordPO.setProviderDataLocation(udlHandler.getDrvUdlByCityId(((DrvRecruitingPO)mod).getCityId()));
        }
    }

    @Override
    public boolean insetModRecord(Long rrdId, List<TmsModContent> contentList, CommonEnum.RecordTypeEnum recordTypeEnum, CommonEnum.ModTypeEnum modTypeEnum,String modifyUser) {
        TmsModRecordPO modRecordPO = new TmsModRecordPO();
        modRecordPO.setDatachangeCreatetime(new Timestamp(System.currentTimeMillis()));
        modRecordPO.setDatachangeLasttime(modRecordPO.getDatachangeCreatetime());
        modRecordPO.setModifyUser(modifyUser);
        modRecordPO.setCreateUser(modifyUser);
        if (CollectionUtils.isNotEmpty(contentList)) {
            modRecordPO.setModContent(JacksonUtil.serialize(contentList));
        }
        modRecordPO.setModType(modTypeEnum.getValue());
        modRecordPO.setRrdType(recordTypeEnum.getCode());
        modRecordPO.setRrdId(rrdId);
        setUdl(rrdId, recordTypeEnum, modRecordPO, getSource(rrdId, recordTypeEnum));
        return tmsModRecordRepo.insert(modRecordPO) > 0;
    }

    @Override
    public List<TmsModRecordPO> queryModRecordList(Long rrdId, Integer rrdType, Long supplierId) {
        if(rrdId == null || rrdType == null){
            return Collections.emptyList();
        }
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("rrd_id", rrdId, Types.BIGINT, false).and().
                    equal("rrd_type", rrdType, Types.TINYINT, false).and().
                    equalNullable("supplier_Id", supplierId, Types.BIGINT, false);
            builder.orderBy("id", false);
            return tmsModRecordRepo.getDao().query(builder, hints);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<TmsModRecordPO> queryModRecordList(List<Long> rrdIdList, Integer rrdType) {
        if(CollectionUtils.isEmpty(rrdIdList)){
            return Collections.emptyList();
        }
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("rrd_id", rrdIdList, Types.BIGINT, false).and().
              equal("rrd_type", rrdType, Types.TINYINT, false);
            builder.orderBy("id", false);
            return tmsModRecordRepo.getDao().query(builder, hints);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }


    @Override
    public Long insetRecruitingRecord(Long rrdId, List<TmsModContent> contentList, CommonEnum.RecordTypeEnum recordTypeEnum, CommonEnum.ModTypeEnum modTypeEnum, String modifyUser) throws SQLException {
        TmsModRecordPO modRecordPO = new TmsModRecordPO();
        modRecordPO.setDatachangeCreatetime(new Timestamp(System.currentTimeMillis()));
        modRecordPO.setDatachangeLasttime(modRecordPO.getDatachangeCreatetime());
        modRecordPO.setModifyUser(modifyUser);
        modRecordPO.setCreateUser(modifyUser);
        if (CollectionUtils.isNotEmpty(contentList)) {
            modRecordPO.setModContent(JacksonUtil.serialize(contentList));
        }
        modRecordPO.setModType(modTypeEnum.getValue());
        modRecordPO.setRrdType(recordTypeEnum.getCode());
        modRecordPO.setRrdId(rrdId);
        KeyHolder keyHolder = new KeyHolder();
        tmsModRecordRepo.insert(new DalHints(), keyHolder, modRecordPO);
        return keyHolder.getKey().longValue();
    }

    @Override
    public List<TmsModRecordPO> queryModRecordListPage(Long rrdId, Integer rrdType, int pageNo, int pageSize) {
        if(rrdId == null || rrdType == null){
            return Collections.emptyList();
        }
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("rrd_id", rrdId, Types.BIGINT, false).and().
                    equal("rrd_type", rrdType, Types.TINYINT, false);
            builder.orderBy("id", false);
            builder.atPage(pageNo,pageSize);
            return tmsModRecordRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int queryModRecordListCount(Long rrdId, Integer rrdType) {
        if(rrdId == null || rrdType == null){
            return 0;
        }
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            builder.equal("rrd_id", rrdId, Types.BIGINT, false).and().
                    equal("rrd_type", rrdType, Types.TINYINT, false);
            builder.orderBy("id", false);
            return tmsModRecordRepo.getDao().count(builder, hints).intValue();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    private Boolean isForeign(CommonEnum.RecordTypeEnum recordTypeEnum, T newMod) {
        switch (recordTypeEnum) {
            case VEHICLE:
                return enumRepository.getAreaScope(((VehVehiclePO) newMod).getCityId()) == 1;
            default:
                return false;
        }
    }

    private T getSource(Long rrdId, CommonEnum.RecordTypeEnum recordTypeEnum) {
        if (rrdId == null) {
            return null;
        }
        switch (recordTypeEnum) {
            case DRIVER:
                return (T) drvDrvierRepository.getDrvDriverRepo().queryByPk(rrdId);
            case VEHICLE:
                return (T) vehicleRepository.getVehVehicleRepo().queryByPk(rrdId);
            case TRANSPORT_GROUP:
                return (T) transportGroupRepository.getTspTransportGroupRepo().queryByPk(rrdId);
            case RECRUIT:
                return (T) drvRecruitingRepository.queryByPK(rrdId);
            case VEHRECRUIT:
                return (T) vehicleRecruitingRepository.queryByPK(rrdId);
            default:
                return null;
        }
    }

    private String[] getNeedArr(CommonEnum.RecordTypeEnum recordTypeEnum, boolean isForeign) {
        switch (recordTypeEnum) {
            case DRIVER:
                return Constant.DRV_NEED_KEY;
            case VEHICLE:
                return isForeign ? Constant.VEHICLE_FOREIGN_NEED_KEY : Constant.VEHICLE_NEED_KEY;
            case TRANSPORT_GROUP:
                return Constant.TRANSPORTGROUP_NEED_KEY;
            case RECRUIT:
                return Constant.DRV_RECRUITING_NEED_KEY;
            case VEHRECRUIT:
                return Constant.VEHICLE_RECRUITING_NEED_KEY;
            default:
                return null;
        }
    }

    private String[] getSpecialArr(CommonEnum.RecordTypeEnum recordTypeEnum) {
        switch (recordTypeEnum) {
            case VEHICLE:
                return Constant.VEHICLE_SPECIAL_KEY;
            default:
                return null;
        }
    }

    private String getAssembleJsonList(T sourceMod, T newMod, Map<String, String> attributeKeyValue, CommonEnum.RecordTypeEnum recordTypeEnum, String[] needArr) {
        Map<String, String[]> fields = compareFields(sourceMod, newMod, needArr);
        if (fields == null || fields.isEmpty()) {
            return null;
        }
        List<Map<String, String>> jsonStrList = Lists.newArrayListWithCapacity(fields.size());
        String[] specialArr;
        List<String> specialArrList = (specialArr = getSpecialArr(recordTypeEnum)) == null ? Lists.newLinkedList() : Arrays.asList(specialArr);
        for (Map.Entry<String, String[]> entry : fields.entrySet()) {
            Map<String, String> jsonMap = Maps.newHashMapWithExpectedSize(4);
            if (specialArrList.contains(entry.getKey())) {
                jsonMap = getAttributejsonMap(entry.getKey(), entry.getValue()[0], entry.getValue()[1]);
            } else {
                jsonMap.put("originalValue", entry.getValue()[0]);
                jsonMap.put("changeValue", entry.getValue()[1]);
            }
            jsonMap.put("attributeName", attributeKeyValue.get(entry.getKey()));
            jsonMap.put("attributeKey", entry.getKey());
            jsonStrList.add(jsonMap);
        }
        fields.clear();
        return JacksonUtil.serialize(jsonStrList);
    }

    private Map<String, String> getAttributejsonMap(String keyAttributeName, String oldId, String newId) {
        Map<String, String> jsonMap = Maps.newHashMapWithExpectedSize(4);
        switch (keyAttributeName) {
            case "supplierId":
                jsonMap.put("originalValue", Strings.isNullOrEmpty(oldId) ? "" : enumRepository.getSupplierName(Long.valueOf(oldId)));
                jsonMap.put("changeValue", Strings.isNullOrEmpty(newId) ? "" : enumRepository.getSupplierName(Long.valueOf(newId)));
                break;
            case "cityId":
                jsonMap.put("originalValue", Strings.isNullOrEmpty(oldId) ? "" : enumRepository.getCityName(Long.valueOf(oldId)));
                jsonMap.put("changeValue", Strings.isNullOrEmpty(newId) ? "" : enumRepository.getCityName(Long.valueOf(newId)));
                break;
            case "vehicleTypeId":
                jsonMap.put("originalValue", Strings.isNullOrEmpty(oldId) ? "" : enumRepository.getVehicleTypeName(Long.valueOf(oldId)));
                jsonMap.put("changeValue", Strings.isNullOrEmpty(newId) ? "" : enumRepository.getVehicleTypeName(Long.valueOf(newId)));
                break;
            case "vehicleBrandId":
                jsonMap.put("originalValue", Strings.isNullOrEmpty(oldId) ? "" : enumRepository.getBandName(Long.valueOf(oldId)));
                jsonMap.put("changeValue", Strings.isNullOrEmpty(newId) ? "" : enumRepository.getBandName(Long.valueOf(newId)));
                break;
            case "vehicleSeries":
                jsonMap.put("originalValue", Strings.isNullOrEmpty(oldId) ? "" : enumRepository.getVehicleSeriesName(Long.valueOf(oldId)));
                jsonMap.put("changeValue", Strings.isNullOrEmpty(newId) ? "" : enumRepository.getVehicleSeriesName(Long.valueOf(newId)));
                break;
            case "vehicleColorId":
                jsonMap.put("originalValue", Strings.isNullOrEmpty(oldId) ? "" : enumRepository.getColorName(Long.valueOf(oldId)));
                jsonMap.put("changeValue", Strings.isNullOrEmpty(newId) ? "" : enumRepository.getColorName(Long.valueOf(newId)));
                break;
            case "vehicleEnergyType":
                jsonMap.put("originalValue", Strings.isNullOrEmpty(oldId) ? "" : enumRepository.getVehicleEnergyTypeName(Integer.valueOf(oldId)));
                jsonMap.put("changeValue", Strings.isNullOrEmpty(newId) ? "" : enumRepository.getVehicleEnergyTypeName(Integer.valueOf(newId)));
                break;
            case "usingNature":
                jsonMap.put("originalValue", Strings.isNullOrEmpty(oldId) ? "" : enumRepository.getUsingNatureValue(Integer.valueOf(oldId)));
                jsonMap.put("changeValue", Strings.isNullOrEmpty(newId) ? "" : enumRepository.getUsingNatureValue(Integer.valueOf(newId)));
                break;
        }
        return jsonMap;
    }

    @SuppressWarnings("rawtypes")
    private Map<String, String[]> compareFields(T sourceMod, T newMod, String[] needArr) {
        try {
            Map<String, String[]> map = Maps.newHashMap();
            List<String> needList;
            if (needArr == null || needArr.length <= 0) {
                return Maps.newHashMap();
            }
            needList = Arrays.asList(needArr);
            Class clazz = sourceMod.getClass();
            PropertyDescriptor[] pds = Introspector.getBeanInfo(clazz, Object.class).getPropertyDescriptors();
            for (PropertyDescriptor pd : pds) {
                String fileName = pd.getName();
                if (!needList.contains(fileName)) {
                    continue;
                }
                Method readMethod = pd.getReadMethod();
                Object o1 = readMethod.invoke(sourceMod);
                Object o2 = readMethod.invoke(newMod);
                if (o1 instanceof Timestamp) {
                    o1 = new Date(((Timestamp) o1).getTime());
                }
                if (o2 instanceof Timestamp) {
                    o2 = new Date(((Timestamp) o2).getTime());
                }
                if (o1 == null && o2 == null) {
                    continue;
                } else if (o1 == null && o2 != null) {
                    String[] str = new String[2];
                    str[0] = null;
                    str[1] = o2.toString();
                    map.put(fileName, str);
                    continue;
                } else if (o1 != null && o2 == null) {
                    String[] str = new String[2];
                    str[0] = o1.toString();
                    str[1] = null;
                    map.put(fileName, str);
                    continue;
                }
                if (!o1.equals(o2)) {
                    if (o1.toString().equals(o2.toString())) {
                        continue;
                    }
                    String[] str = new String[2];
                    str[0] = o1.toString();
                    str[1] = o2.toString();
                    map.put(fileName, str);
                }
            }
            return map;
        } catch (Exception e) {
            return null;
        }
    }

}
