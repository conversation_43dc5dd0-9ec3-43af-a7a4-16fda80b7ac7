package com.ctrip.dcs.tms.transport.infrastructure.port.repository;


import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.dal.*;

import java.util.*;

public interface TmsCityNetConfigRepository {

    DalRepository<TmsCityNetConfigPO> getTmsCityNetConfigRepo();

    TmsCityNetConfigPO queryByPk(Long id);

    List<TmsCityNetConfigPO> queryConfigList(Long cityId,Integer configItem,Integer configType);

    int countConfigByCity(Long cityId,Integer configItem,Integer configType);

}
