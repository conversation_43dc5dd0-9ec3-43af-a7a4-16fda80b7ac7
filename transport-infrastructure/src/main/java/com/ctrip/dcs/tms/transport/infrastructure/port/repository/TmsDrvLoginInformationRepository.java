package com.ctrip.dcs.tms.transport.infrastructure.port.repository;


import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.dal.*;

import java.util.*;

public interface TmsDrvLoginInformationRepository {

    TmsDrvLoginInformationPO queryByPk(Long id);

    int insert(TmsDrvLoginInformationPO po);

    List<TmsDrvLoginInformationPO> queryDrvLoginInfoLimit(Long drvId,int pageNo,int pageSize);
}