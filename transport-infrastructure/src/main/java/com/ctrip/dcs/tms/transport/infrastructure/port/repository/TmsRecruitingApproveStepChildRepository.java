package com.ctrip.dcs.tms.transport.infrastructure.port.repository;


import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.dal.*;

import java.sql.*;
import java.util.*;

public interface TmsRecruitingApproveStepChildRepository {
    TmsRecruitingApproveStepChildPO queryByPk(Long id);

    int update (TmsRecruitingApproveStepChildPO childPO);

    Long insert(TmsRecruitingApproveStepChildPO stepChildPO) throws SQLException;

    List<TmsRecruitingApproveStepChildPO> queryChildByStepIdList(List<Long> stepIdList);

    int updateChildCheckStatus(Long id,Integer checkStatus,String modifyUser);

    List<TmsRecruitingApproveStepChildPO> queryChildByIds(List<Long> ids);
}
