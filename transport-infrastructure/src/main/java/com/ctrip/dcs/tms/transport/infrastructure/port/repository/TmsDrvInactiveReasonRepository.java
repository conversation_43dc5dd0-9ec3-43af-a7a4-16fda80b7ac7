package com.ctrip.dcs.tms.transport.infrastructure.port.repository;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TmsDrvInactiveReasonPO;

import java.sql.SQLException;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface TmsDrvInactiveReasonRepository {

  int insert(TmsDrvInactiveReasonPO po);

  List<TmsDrvInactiveReasonPO> query(List<Long> drvIdList);

  int deleteReason(Long drvId, String modifyUser);

  /**
   * 根据司机ID和原因代码删除未激活原因
   *
   * @param drvId      司机ID
   * @param reasonCode 原因代码
   * @param modifyUser 修改人
   * @return 影响的行数
   */
  int deleteReasonByCode(Long drvId, List<Integer> reasonCode, String modifyUser);
}
