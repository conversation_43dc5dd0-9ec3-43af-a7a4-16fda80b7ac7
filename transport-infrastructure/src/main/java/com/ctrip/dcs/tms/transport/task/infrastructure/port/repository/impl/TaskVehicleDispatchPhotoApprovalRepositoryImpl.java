package com.ctrip.dcs.tms.transport.task.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.task.infrastructure.port.repository.impl.TaskVehicleDispatchPhotoApprovalRepository;
import com.ctrip.dcs.tms.transport.task.infrastructure.adapter.mysql.po.TaskVehicleDispatchPhotoApprovalDetailPO;
import com.ctrip.dcs.tms.transport.task.infrastructure.adapter.mysql.po.TaskVehicleDispatchPhotoApprovalRecordPO;
import com.ctrip.dcs.tms.transport.task.infrastructure.value.UploadVehicleDispatchPhotoList;
import com.ctrip.dcs.tms.transport.task.infrastructure.value.VehicleDispatchPhoto;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.annotation.DalTransactional;
import com.ctrip.platform.dal.dao.base.DalTableOperations;
import com.ctrip.platform.dal.dao.client.DalOperationsFactory;
import lombok.SneakyThrows;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant.TMS_DEFAULT_USERNAME;

/**
 * <AUTHOR>
 */
@Repository
public class TaskVehicleDispatchPhotoApprovalRepositoryImpl implements TaskVehicleDispatchPhotoApprovalRepository {

  private DalTableOperations<TaskVehicleDispatchPhotoApprovalRecordPO> taskVehicleDispatchPhotoDalOperations;
  private DalTableOperations<TaskVehicleDispatchPhotoApprovalDetailPO> taskVehicleDispatchPhotoDetailDalOperations;

  public TaskVehicleDispatchPhotoApprovalRepositoryImpl() {
    taskVehicleDispatchPhotoDalOperations = DalOperationsFactory.getDalTableOperations(TaskVehicleDispatchPhotoApprovalRecordPO.class);
    taskVehicleDispatchPhotoDetailDalOperations = DalOperationsFactory.getDalTableOperations(TaskVehicleDispatchPhotoApprovalDetailPO.class);
  }

  @SneakyThrows
  @Override
  @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
  public Long insert(UploadVehicleDispatchPhotoList dto) {
    TaskVehicleDispatchPhotoApprovalRecordPO recordPO = convert2VehicleDispatchPhotoRecordPO(dto);
    taskVehicleDispatchPhotoDalOperations.insert(new DalHints().setIdentityBack(), recordPO);
    taskVehicleDispatchPhotoDetailDalOperations.batchInsert(new DalHints(), convert2DetailList(dto, recordPO));
      return recordPO.getId();
  }

  protected List<TaskVehicleDispatchPhotoApprovalDetailPO> convert2DetailList(UploadVehicleDispatchPhotoList dto, TaskVehicleDispatchPhotoApprovalRecordPO recordPO) {
      return dto.getPhotoList().stream().map(photo -> convert2DetailPO(photo, recordPO)).collect(
        Collectors.toList());
  }

  protected TaskVehicleDispatchPhotoApprovalDetailPO convert2DetailPO(VehicleDispatchPhoto photo, TaskVehicleDispatchPhotoApprovalRecordPO recordPO) {
    TaskVehicleDispatchPhotoApprovalDetailPO po = new TaskVehicleDispatchPhotoApprovalDetailPO();
    po.setVehicleDispatchPhotoApprovalId(recordPO.getId());
    po.setPhotoType(photo.getPhotoType());
    po.setPhotoUrl(photo.getPhotoUrl());
    po.setCreateUser(TMS_DEFAULT_USERNAME);
    po.setModifyUser(TMS_DEFAULT_USERNAME);
    return po;
  }

  protected TaskVehicleDispatchPhotoApprovalRecordPO convert2VehicleDispatchPhotoRecordPO(
    UploadVehicleDispatchPhotoList dto) {
      TaskVehicleDispatchPhotoApprovalRecordPO po = new TaskVehicleDispatchPhotoApprovalRecordPO();
      po.setVehicleId(dto.getVehicleId());
      po.setVehicleLicense(dto.getVehicleLicense());
      po.setSupplierId(dto.getSupplierId());
      po.setVehicleTypeId(dto.getVehicleTypeId());
      po.setDrvId(dto.getDrvId());
      po.setDrvName(dto.getDrvName());
      po.setCreateUser(TMS_DEFAULT_USERNAME);
      po.setModifyUser(TMS_DEFAULT_USERNAME);
      po.setCityId(dto.getCityId());
      return po;
  }
}
