package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.google.common.collect.Maps;
import lombok.Data;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Map;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant.JS_SPLIT;

@Data
public class TransportGroupDriverApplyFailedReasonDTO {

  private Long drvId;

  /**
   * 操作时间
   */
  private LocalDateTime operateTime = LocalDateTime.now();

  /**
   * 统计时间左值
   */
  private Timestamp calculateTimeFrom;

  /**
   * 统计时间右值
   */
  private Timestamp calculateTimeTo;

  /**
   * 规则解释
   */
  private String ruleDescription;

  /**
   * 报名状态
   */
  private TmsTransportConstant.ApplyStatusEnum applyStatus;

  /**
   * 失败分类: 触发请假冻结时长限制规则；触发司机分限制规则
   */
  private TmsTransportConstant.ApplyFailedTypeEnum applyFailedType;

  /**
   * 司机分
   */
  private Double driverPoint;

  /**
   * 司机分城市排名
   */
  private Long driverRankingInCity;

  /**
   * 同一批次内司机排名
   */
  private Map<Long,Long> driverRankingInBatchMap = Maps.newHashMap();

  /**
   * 请假冻结时长明细
   */
  private LeaveAndFreezeDTO leaveAndFreeze = new LeaveAndFreezeDTO();

  public void setDriverPointInfo(Double driverPoint, Long driverRankingInCity, Long driverRankingInBatch, Long workShiftId){
    this.driverPoint = driverPoint;
    this.driverRankingInCity = driverRankingInCity;
    driverRankingInBatchMap.put(workShiftId, driverRankingInBatch);
  }

  public void setApplyResult(Long drvId, TmsTransportConstant.ApplyStatusEnum applyStatus, TmsTransportConstant.ApplyFailedTypeEnum applyFailedType, String ruleDescription, Integer duration) {
    this.applyStatus = applyStatus;
    this.applyFailedType = applyFailedType;
    if (applyFailedType == TmsTransportConstant.ApplyFailedTypeEnum.LEAVE_AND_FREEZE_TOTAL_HOUR_OVER_LIMIT) {
      this.ruleDescription = String.format(ruleDescription, leaveAndFreeze.getTotalHour(), duration);
    }
    if (applyFailedType == TmsTransportConstant.ApplyFailedTypeEnum.DRIVER_POINT_IS_RANKED_LOW) {
      this.ruleDescription = String.format(ruleDescription, this.getDriverPoint());
    }
    this.drvId = drvId;
  }

  public void setCalculateTimeRange(Timestamp calculateTimeFrom, Timestamp calculateTimeTo) {
    this.calculateTimeFrom = calculateTimeFrom;
    this.calculateTimeTo = calculateTimeTo;
  }

  public void setLeaveHour(Long drvId, Timestamp leaveBeginTime, Timestamp leaveEndTime, long duration) {
    LeaveAndFreezeDTO leaveAndFreezeDTO = new LeaveAndFreezeDTO(leaveBeginTime, leaveEndTime, duration,
      TmsTransportConstant.LeaveAndFreezeTypeEmue.LEAVE);
    leaveAndFreeze.getLeavItemList().add(leaveAndFreezeDTO);
    leaveAndFreeze.sumTotalHour(duration);
    this.drvId = drvId;
  }

  public void setFreezeHour(Long drvId, Timestamp firstFreezeTime, Timestamp freezeEndDateTime, long freezeHour) {
    leaveAndFreeze.getFreezeItemList().add(new LeaveAndFreezeDTO(firstFreezeTime, freezeEndDateTime, freezeHour,
      TmsTransportConstant.LeaveAndFreezeTypeEmue.FREEZE));
    leaveAndFreeze.sumTotalHour(freezeHour);
    this.drvId = drvId;
  }

  public String getApplyFailedReasonDetail(LocalDateTime operateTime,
    TmsTransportConstant.ApplyTransPortOperationTypeEnum applyTransPortOperationTypeEnum, Long driverApplicationMaximumLimit, Long workShiftId) {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append(TmsTransportConstant.UNICODE.OPERATE_TIME).append(DateUtil.getTimeStr(operateTime)).append("  ").append(JS_SPLIT);
    stringBuilder.append(ruleDescription).append(",");
    stringBuilder.append(applyFailedType.getText()).append(JS_SPLIT);

    if (this.applyFailedType == TmsTransportConstant.ApplyFailedTypeEnum.DRIVER_POINT_IS_RANKED_LOW) {
      if (applyTransPortOperationTypeEnum == TmsTransportConstant.ApplyTransPortOperationTypeEnum.GLOBAL_TRACK) {
        stringBuilder.append(TmsTransportConstant.UNICODE.DRIVER_UP_LIMIT).append(driverApplicationMaximumLimit).append(",").append(
          TmsTransportConstant.UNICODE.GLOBAL_REFRESH_DRIVER_RANKING).append(driverRankingInBatchMap.get(workShiftId));
      }else {
        stringBuilder.append(TmsTransportConstant.UNICODE.CAPACITY_REPLACEMENT_LIMIT).append(driverApplicationMaximumLimit).append(",").append(
          TmsTransportConstant.UNICODE.CAPACITY_REPLACEMENT_DEMAND_RANKING).append(driverRankingInBatchMap.get(workShiftId));
      }
      return stringBuilder.toString();
    }

    if (this.applyFailedType == TmsTransportConstant.ApplyFailedTypeEnum.LEAVE_AND_FREEZE_TOTAL_HOUR_OVER_LIMIT) {
      getLeaveAndFreezeOverview(stringBuilder);
      getLeaveDetail(stringBuilder);
      getFreezeDetail(stringBuilder);
    }

    return stringBuilder.toString();
  }

  private void getFreezeDetail(StringBuilder stringBuilder) {
    stringBuilder.append(this.leaveAndFreeze.getFreezeDetail()).append(JS_SPLIT);
  }

  private void getLeaveDetail(StringBuilder stringBuilder) {
    stringBuilder.append(this.leaveAndFreeze.getLeaveDetail()).append(JS_SPLIT);
  }

  protected void getLeaveAndFreezeOverview(StringBuilder stringBuilder) {
    stringBuilder.append(DateUtil.getDateStr(calculateTimeFrom)).append("--").append(DateUtil.getDateStr(calculateTimeTo)).append(TmsTransportConstant.UNICODE.LEAVE_AND_FREEZE_TOTAL_HOUR_OVER_LIMIT).append(this.leaveAndFreeze.getTotalHour()).append(
      TmsTransportConstant.UNICODE.HOUR).append(JS_SPLIT);
  }
}
