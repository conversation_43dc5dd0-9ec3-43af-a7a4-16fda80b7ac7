package com.ctrip.dcs.tms.transport.infrastructure.port.repository.model;

public class DrvFreezeReasonDTO {
    private String freezeTime;
    private String freezeReason;
    private String remark;

    public String getFreezeTime() {
        return freezeTime;
    }

    public void setFreezeTime(String freezeTime) {
        this.freezeTime = freezeTime;
    }

    public String getFreezeReason() {
        return freezeReason;
    }

    public void setFreezeReason(String freezeReason) {
        this.freezeReason = freezeReason;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public static DrvFreezeReasonDTO buildDTO(String now,String freezeReason,String remark){
        DrvFreezeReasonDTO reasonDTO = new DrvFreezeReasonDTO();
        reasonDTO.setFreezeTime(now);
        reasonDTO.setFreezeReason(freezeReason);
        reasonDTO.setRemark(remark);
        return reasonDTO;
    }
}
