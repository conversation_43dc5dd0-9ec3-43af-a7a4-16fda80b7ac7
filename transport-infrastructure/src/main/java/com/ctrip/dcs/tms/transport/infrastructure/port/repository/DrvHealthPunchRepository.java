package com.ctrip.dcs.tms.transport.infrastructure.port.repository;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.dal.*;

import java.sql.*;
import java.util.*;

/***
　* @description: 司机健康打卡
　* <AUTHOR>
　* @date 2021/9/9 11:39
*/
public interface DrvHealthPunchRepository {

    long insert(DrvHealthPunchPO drvHealthPunchPO) throws SQLException;

}
