package com.ctrip.dcs.tms.transport.infrastructure.port.repository;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;

import java.sql.*;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2020/3/17 12:24
 */
public interface DriverGroupRelationRepository {

    /**
     * 通过运力组id查询有效司机id
     * */
    List<Long> queryActiveDrvIdByTransportIdListNew(List<Long> drvIdList,List<Long> groupIdList);

}
