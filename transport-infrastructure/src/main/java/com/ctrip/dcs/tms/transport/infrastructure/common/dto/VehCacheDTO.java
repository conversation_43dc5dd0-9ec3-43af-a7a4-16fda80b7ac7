package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

import java.io.*;
import java.util.*;

public class VehCacheDTO implements Serializable {
    //序列化版本号
    private static final long serialVersionUID = 110000L;

    public Long carId;

    public String carLicense;

    public Long carBrandId;

    public String carBrandName;

    public Integer carTypeId;

    public String carTypeName;

    public Long carColorId;

    public String carColor;

    public Integer isEnergy;

    public Long carSeriesId;

    public String carSeriesName;

    public Integer maxLuggages;

    public Integer maxPassengers;

    public List<Integer> vehProductionLineCodeList;

    public Integer vehicleStatus;

    public String vin;

    public String vehRegstDate;

    public String vehCreateTime;

    public Boolean rideHailingVehCertValid;

    public String vehicleFullImg;
    //网约车运输证号
    public String vehicleNetCertNo;

    public Integer temporaryDispatchMark;

    public String temporaryDispatchEndDatetime;

    public Long supplierId;

    public Long cityId;

    public String overAgeTime;

    /**
     * 燃油车类型
     */
    public Integer vehicleEnergyType;

    public Integer getVehicleEnergyType() {
        return vehicleEnergyType;
    }

    public void setVehicleEnergyType(Integer vehicleEnergyType) {
        this.vehicleEnergyType = vehicleEnergyType;
    }

    public Integer getTemporaryDispatchMark() {
        return temporaryDispatchMark;
    }

    public void setTemporaryDispatchMark(Integer temporaryDispatchMark) {
        this.temporaryDispatchMark = temporaryDispatchMark;
    }

    public String getTemporaryDispatchEndDatetime() {
        return temporaryDispatchEndDatetime;
    }

    public void setTemporaryDispatchEndDatetime(String temporaryDispatchEndDatetime) {
        this.temporaryDispatchEndDatetime = temporaryDispatchEndDatetime;
    }

    public String getVehicleFullImg() {
        return vehicleFullImg;
    }

    public void setVehicleFullImg(String vehicleFullImg) {
        this.vehicleFullImg = vehicleFullImg;
    }

    public String getVehCreateTime() {
        return vehCreateTime;
    }

    public void setVehCreateTime(String vehCreateTime) {
        this.vehCreateTime = vehCreateTime;
    }

    public Long getCarId() {
        return carId;
    }

    public void setCarId(Long carId) {
        this.carId = carId;
    }

    public String getCarLicense() {
        return carLicense;
    }

    public void setCarLicense(String carLicense) {
        this.carLicense = carLicense;
    }

    public Long getCarBrandId() {
        return carBrandId;
    }

    public void setCarBrandId(Long carBrandId) {
        this.carBrandId = carBrandId;
    }

    public String getCarBrandName() {
        return carBrandName;
    }

    public void setCarBrandName(String carBrandName) {
        this.carBrandName = carBrandName;
    }

    public Integer getCarTypeId() {
        return carTypeId;
    }

    public void setCarTypeId(Integer carTypeId) {
        this.carTypeId = carTypeId;
    }

    public String getCarTypeName() {
        return carTypeName;
    }

    public void setCarTypeName(String carTypeName) {
        this.carTypeName = carTypeName;
    }

    public Long getCarColorId() {
        return carColorId;
    }

    public void setCarColorId(Long carColorId) {
        this.carColorId = carColorId;
    }

    public String getCarColor() {
        return carColor;
    }

    public void setCarColor(String carColor) {
        this.carColor = carColor;
    }

    public Integer getIsEnergy() {
        return isEnergy;
    }

    public void setIsEnergy(Integer isEnergy) {
        this.isEnergy = isEnergy;
    }

    public Long getCarSeriesId() {
        return carSeriesId;
    }

    public void setCarSeriesId(Long carSeriesId) {
        this.carSeriesId = carSeriesId;
    }

    public String getCarSeriesName() {
        return carSeriesName;
    }

    public void setCarSeriesName(String carSeriesName) {
        this.carSeriesName = carSeriesName;
    }

    public Integer getMaxLuggages() {
        return maxLuggages;
    }

    public void setMaxLuggages(Integer maxLuggages) {
        this.maxLuggages = maxLuggages;
    }

    public Integer getMaxPassengers() {
        return maxPassengers;
    }

    public void setMaxPassengers(Integer maxPassengers) {
        this.maxPassengers = maxPassengers;
    }

    public List<Integer> getVehProductionLineCodeList() {
        return vehProductionLineCodeList;
    }

    public void setVehProductionLineCodeList(List<Integer> vehProductionLineCodeList) {
        this.vehProductionLineCodeList = vehProductionLineCodeList;
    }

    public Integer getVehicleStatus() {
        return vehicleStatus;
    }

    public void setVehicleStatus(Integer vehicleStatus) {
        this.vehicleStatus = vehicleStatus;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getVehRegstDate() {
        return vehRegstDate;
    }

    public void setVehRegstDate(String vehRegstDate) {
        this.vehRegstDate = vehRegstDate;
    }

    public Boolean getRideHailingVehCertValid() {
        return rideHailingVehCertValid;
    }

    public void setRideHailingVehCertValid(Boolean rideHailingVehCertValid) {
        this.rideHailingVehCertValid = rideHailingVehCertValid;
    }

    public String getVehicleNetCertNo() {
        return vehicleNetCertNo;
    }

    public void setVehicleNetCertNo(String vehicleNetCertNo) {
        this.vehicleNetCertNo = vehicleNetCertNo;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public String getOverAgeTime() {
        return overAgeTime;
    }

    public void setOverAgeTime(String overAgeTime) {
        this.overAgeTime = overAgeTime;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }
}
