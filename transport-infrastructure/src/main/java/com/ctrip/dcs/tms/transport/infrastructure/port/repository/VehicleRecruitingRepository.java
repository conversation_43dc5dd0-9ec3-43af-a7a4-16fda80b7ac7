package com.ctrip.dcs.tms.transport.infrastructure.port.repository;


import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.dal.*;

import java.sql.*;
import java.util.*;

public interface VehicleRecruitingRepository {

    DalRepository<VehicleRecruitingPO> getVehicleRecruitingRepo();

    VehicleRecruitingPO queryByPK(Long vehicleRecruitingId);

    /**
     * 新增车辆审批
     *
     * @param po
     * @return
     */
    Long addVehicleRecruiting(VehicleRecruitingPO po) throws SQLException;

    /**
     * 编辑车辆审批
     *
     * @param po
     * @return
     */
    int update(VehicleRecruitingPO po) throws SQLException;

    /**
     * 更新车辆审批状态
     */
    Integer updateVehicleRecApproverStatus(List<Long> vehicleRecruitingIds, Integer approverStatus, String remark, String modifyUser,Integer checkStatus,Boolean approveTimeFlag,Integer bdTurnDownCount) throws SQLException;

    /**
     * 获取车辆审批列表
     */
    List<VehicleRecruitingPO> getVehicleRecruitingList(Set<Long> idList) throws SQLException;

    /**
     * 招募列表数
     * @param soaRequestDTO
     * @return
     */
    int countVehicleRecruitingList(RecruitingSOARequestDTO soaRequestDTO,List<Integer> jurisdictionList, List<Integer> productionLineList,Integer accountType,Boolean newOldSwitch) throws SQLException;


    /**
     * 招募列表
     * @param soaRequestDTO
     * @return
     */
    List<VehicleRecruitingPO> queryVehicleRecruitingList(RecruitingSOARequestDTO soaRequestDTO, List<Integer> jurisdictionList, PaginatorDTO paginator, List<Integer> productionLineList,Integer accountType,Boolean newOldSwitch) throws SQLException;


    List<VehicleRecruitingPO> queryVehicleRecruitingBySupplierIdAndId(List<Long> idList, Long supplierId);

    int updateCheckStatus(List<Long> vehicleRecruitingIds,int checkStatus) throws SQLException;

    List<TodoListCountPO> todoListCount(Long supplierId);

    int countApproveIngVehicleRecruiting(List<Long> vehicleIds,Integer approveStatus,Integer checkStatus);

    List<VehicleRecruitingPO> queryApproveIngVehicleRecruiting(List<Long> vehicleIds,Integer approveStatus,Integer checkStatus);

    int updateApproveAging(List<Long> vehicleRecruitingIds,int approveAging) throws SQLException;

    int updateVehCheckStatus(Long vehRecruitingId,int checkStatus,String modifyUser) throws SQLException;


    /**
     * 校验车辆唯一性
     * @param str
     * @param type
     * @return
     */
    Boolean checkRecruitingVehOnly(String str, Integer type) throws SQLException;

    List<VehicleRecruitingPO> queryvWaitApproveRecruitingByPage(int pageNo,int pageSize);

    int queryvCountWaitApproveRecruiting();

    int updateDrvApproveSchedule(Long recruitingId,Integer accountType,Integer approveSchedule) throws SQLException;

    int updateApproveAging(Long recruitingId) throws SQLException;

    int discardRecruitingVeh(Long recruitingId,Boolean active,String modifyUser);

    int updateDrvImgQToC(VehicleRecruitingPO vehVehiclePO);
}
