package com.ctrip.dcs.tms.transport.infrastructure.port.repository.model;

/**
 * 司机基本信息查询参数
 * <AUTHOR>
 * @Date 2020/4/24 14:52
 */
public class DrvInfoParam {

    private Long drvId;
    private Long igtCode;
    private String drvPhone;
    private String loginAccount;
    private String qunarAccout;
    private String email;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Long getDrvId() {
        return drvId;
    }

    public void setDrvId(Long drvId) {
        this.drvId = drvId;
    }

    public Long getIgtCode() {
        return igtCode;
    }

    public void setIgtCode(Long igtCode) {
        this.igtCode = igtCode;
    }

    public String getDrvPhone() {
        return drvPhone;
    }

    public void setDrvPhone(String drvPhone) {
        this.drvPhone = drvPhone;
    }

    public String getLoginAccount() {
        return loginAccount;
    }

    public void setLoginAccount(String loginAccount) {
        this.loginAccount = loginAccount;
    }

    public String getQunarAccout() {
        return qunarAccout;
    }

    public void setQunarAccout(String qunarAccout) {
        this.qunarAccout = qunarAccout;
    }
}
