package com.ctrip.dcs.tms.transport.infrastructure.port.repository.model;

import java.sql.*;
import java.util.*;

public class QueryDrvDO {

    private Long drvId;
    private String drvIdcard;
    private String drvName;
    private String drvPhone;
    private String vehicleLicense;
    private Long cityId;
    private Integer vehicleTypeId;
    private Integer drvFrom;
    private Integer status;
    private String drvLanguage;
    private Integer supplierId;
    private Long transportGroupId;
    private Long vehicleId;
    private Integer coopMode;
    private Timestamp registStartDate;
    private Timestamp registEndDate;
    private Timestamp onlineStartDate;
    private Timestamp onlineEndDate;
    private Integer page;
    private Integer size;
    private List<Long> drvIdList;
    private List<String> drvPhoneList;
    private String igtCode;
    private List<Integer> proLineIdList;
    private Boolean raisingPickUp;
    private Boolean childSeat;
    private Boolean active;
    private Integer relationType;
    private Integer temporaryDispatchMark;

    public Integer getTemporaryDispatchMark() {
        return temporaryDispatchMark;
    }

    public void setTemporaryDispatchMark(Integer temporaryDispatchMark) {
        this.temporaryDispatchMark = temporaryDispatchMark;
    }

    public Integer getRelationType() {
        return relationType;
    }

    public void setRelationType(Integer relationType) {
        this.relationType = relationType;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public Boolean getRaisingPickUp() {
        return raisingPickUp;
    }

    public void setRaisingPickUp(Boolean raisingPickUp) {
        this.raisingPickUp = raisingPickUp;
    }

    public Boolean getChildSeat() {
        return childSeat;
    }

    public void setChildSeat(Boolean childSeat) {
        this.childSeat = childSeat;
    }

    public List<Integer> getProLineIdList() {
        return proLineIdList;
    }

    public void setProLineIdList(List<Integer> proLineIdList) {
        this.proLineIdList = proLineIdList;
    }

    public List<String> getDrvPhoneList() {
        return drvPhoneList;
    }

    public void setDrvPhoneList(List<String> drvPhoneList) {
        this.drvPhoneList = drvPhoneList;
    }

    public List<Long> getDrvIdList() {
        return drvIdList;
    }

    public void setDrvIdList(List<Long> drvIdList) {
        this.drvIdList = drvIdList;
    }

    public Integer getCoopMode() {
        return coopMode;
    }

    public void setCoopMode(Integer coopMode) {
        this.coopMode = coopMode;
    }

    public Long getDrvId() {
        return drvId;
    }

    public void setDrvId(Long drvId) {
        this.drvId = drvId;
    }

    public String getDrvIdcard() {
        return drvIdcard;
    }

    public void setDrvIdcard(String drvIdcard) {
        this.drvIdcard = drvIdcard;
    }

    public String getDrvName() {
        return drvName;
    }

    public void setDrvName(String drvName) {
        this.drvName = drvName;
    }

    public String getDrvPhone() {
        return drvPhone;
    }

    public void setDrvPhone(String drvPhone) {
        this.drvPhone = drvPhone;
    }

    public String getVehicleLicense() {
        return vehicleLicense;
    }

    public void setVehicleLicense(String vehicleLicense) {
        this.vehicleLicense = vehicleLicense;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public Integer getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(Integer vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public Integer getDrvFrom() {
        return drvFrom;
    }

    public void setDrvFrom(Integer drvFrom) {
        this.drvFrom = drvFrom;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDrvLanguage() {
        return drvLanguage;
    }

    public void setDrvLanguage(String drvLanguage) {
        this.drvLanguage = drvLanguage;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public Long getTransportGroupId() {
        return transportGroupId;
    }

    public void setTransportGroupId(Long transportGroupId) {
        this.transportGroupId = transportGroupId;
    }

    public Long getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(Long vehicleId) {
        this.vehicleId = vehicleId;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public Timestamp getRegistStartDate() {
        return registStartDate;
    }

    public void setRegistStartDate(Timestamp registStartDate) {
        this.registStartDate = registStartDate;
    }

    public Timestamp getRegistEndDate() {
        return registEndDate;
    }

    public void setRegistEndDate(Timestamp registEndDate) {
        this.registEndDate = registEndDate;
    }

    public Timestamp getOnlineStartDate() {
        return onlineStartDate;
    }

    public void setOnlineStartDate(Timestamp onlineStartDate) {
        this.onlineStartDate = onlineStartDate;
    }

    public Timestamp getOnlineEndDate() {
        return onlineEndDate;
    }

    public void setOnlineEndDate(Timestamp onlineEndDate) {
        this.onlineEndDate = onlineEndDate;
    }

    public String getIgtCode() {
      return igtCode;
    }

    public void setIgtCode(String igtCode) {
      this.igtCode = igtCode;
    }
}
