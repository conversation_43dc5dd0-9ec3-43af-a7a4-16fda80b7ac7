package com.ctrip.dcs.tms.transport.task.infrastructure.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */


@Getter
public enum TaskTypeEnum {
  VEHICLE_DISPATCH_PHOTO("vehicle_dispatch_photo"),
  ;
  private final String code;

  TaskTypeEnum(String code) {
    this.code = code;
  }

  public static TaskTypeEnum getByCode(String code) {
    for (TaskTypeEnum taskTypeEnum : values()) {
      if (taskTypeEnum.getCode().equals(code)) {
        return taskTypeEnum;
      }
    }
    return null;
  }

}
