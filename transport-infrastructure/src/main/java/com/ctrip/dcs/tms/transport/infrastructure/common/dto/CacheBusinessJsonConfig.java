package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

import lombok.Data;

@Data
public class CacheBusinessJsonConfig {
  /**
   * 缓存的比率
   */
  private int transportGroupCacheRate;

  private boolean transportGroupCacheDiff;

  /**
   * 运力和sku关系缓存比率
   */
  private int transportGroupSkuRelationCacheRate;

  private boolean transportGroupSkuRelationCacheDiff;

  /**
   * 运力的进单配置系缓存比率
   */
  private int transportGroupIntoOrderConfigCacheRate;

  private boolean transportGroupIntoOrderConfigCacheDiff;

  /**
   * 司机所属供应商缓存比率
   */
  private int drvDispatchSupplierRate;

  private boolean drvDispatchSupplierDiff;

  /**
   * 司机运力关系缓存比率
   */
  private int drvTransportGroupRelationCacheRate;

  private boolean drvTransportGroupRelationCacheDiff;

  /**
   * 司机请假缓存比
   */
  private int drvLeaveCacheRate;

}
