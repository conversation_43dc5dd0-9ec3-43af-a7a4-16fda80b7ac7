package com.ctrip.dcs.tms.transport.infrastructure.port.repository;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;

import java.sql.*;
import java.util.*;

/**
 * <AUTHOR>
 * 2021-04-15 疫情防控信息dao层
 */
public interface DrvEpidemicPreventionControlInfoRepository {

    DrvEpidemicPreventionControlInfoPO queryByDrvId(Long drvId);

    List<DrvEpidemicPreventionControlInfoPO> queryByDrvIdList(List<Long> drvIdList);

    Long addEpidemicPreventionControlInfo(DrvEpidemicPreventionControlInfoPO infoPO) throws SQLException;

    Integer updateEpidemicPreventionControlInfo(DrvEpidemicPreventionControlInfoPO infoPO) throws SQLException;

}