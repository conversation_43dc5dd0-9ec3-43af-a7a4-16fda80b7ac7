package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;


import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.ctriposs.baiji.exception.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.springframework.stereotype.*;

import java.sql.*;
import java.util.*;

@Repository(value = "tmsCertificateCheckRepository")
public class TmsCertificateCheckRepositoryImpl implements TmsCertificateCheckRepository {

    private static final Logger logger = LoggerFactory.getLogger(TmsCertificateCheckRepositoryImpl.class);

    private DalRepository<TmsCertificateCheckPO> tmsCertificateCheckPORepo;

    public TmsCertificateCheckRepositoryImpl() throws SQLException {
        tmsCertificateCheckPORepo = new DalRepositoryImpl<>(TmsCertificateCheckPO.class);
    }


    @Override
    public TmsCertificateCheckPO queryByPK(Long id) {
        return tmsCertificateCheckPORepo.queryByPk(id);
    }

    @Override
    public List<TmsCertificateCheckPO> queryCertificateByCheckId(Long checkId, Integer checkType) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append(" select c.id, c.check_id,c.check_type,c.certificate_type,c.check_status,c.datachange_createtime,c.check_result,c.check_keyword,c.check_content,c.datachange_lasttime,c.create_user" +
                    " from (select id, check_id,check_type,certificate_type,check_status,datachange_createtime,check_result,check_keyword,check_content,datachange_lasttime,create_user from tms_certificate_check where check_id = ? and check_type = ? and check_status in (?)  order by datachange_createtime desc limit 100) c" +
                    "     group by c.certificate_type ");
            parameters.set(i++, "check_id", Types.BIGINT, checkId);
            parameters.set(i++, "check_type", Types.INTEGER, checkType);
            parameters.setInParameter(i++, "check_status", Types.INTEGER, Arrays.asList(1,2,3,4));
            return tmsCertificateCheckPORepo.getQueryDao().query(sqlStr.toString(), parameters, hints, TmsCertificateCheckPO.class);
        } catch (Exception e) {
            logger.error("queryCertificateByCheckIdError", e);
        }
        return Collections.emptyList();
    }

    @Override
    public Long insertTmsCertificateCheck(TmsCertificateCheckPO certificateCheckPO) throws SQLException {
        KeyHolder keyHolder = new KeyHolder();
        tmsCertificateCheckPORepo.insert(new DalHints(), keyHolder, certificateCheckPO);
        return keyHolder.getKey().longValue();
    }

    @Override
    public int batchInsertCheckRecord(List<TmsCertificateCheckPO> certificateCheckList) {
        DalHints hints = DalHints.createIfAbsent(null);
        int[] resultV = tmsCertificateCheckPORepo.batchInsert(hints,certificateCheckList);
        if(resultV.length > 0){
            return resultV.length;
        }
        return 0;
    }

    @Override
    public int batchUpdateCheckRecord(List<TmsCertificateCheckPO> certificateCheckList) {
        DalHints hints = DalHints.createIfAbsent(null);
        int[] resultV = tmsCertificateCheckPORepo.batchUpdate(hints,certificateCheckList);
        if(resultV.length > 0){
            return resultV.length;
        }
        return 0;
    }

    @Override
    public int updateCheckStatus(Long id,Integer checkStatus) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            builder.setTemplate("update tms_certificate_check set check_status = ? where id = ? ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "check_status", Types.INTEGER, checkStatus);
            parameters.setSensitive(i++, "id", Types.BIGINT, id);
            return tmsCertificateCheckPORepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TmsCertificateCheckPO> queryIdCardCheckByCheckIng(List<Long> checkIds,Integer checkType,List<Integer> certificateTypeList,Integer checkStatus,int pageNo,int pageSize) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            if(CollectionUtils.isNotEmpty(checkIds)){
                builder.in("check_id", checkIds, Types.BIGINT);
            }
            if(checkType!=null){
                builder.and().equal("check_type", checkType, Types.INTEGER);
            }
            if(CollectionUtils.isNotEmpty(certificateTypeList)){
                builder.and().in("certificate_type", certificateTypeList, Types.INTEGER);
            }
            builder.and().equal("check_status",checkStatus,Types.INTEGER);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.atPage(pageNo,pageSize);
            return tmsCertificateCheckPORepo.getDao().query(builder,hints);
        } catch (Exception e) {
            logger.error("queryCertificateByCheckId error", e);
        }
        return Collections.emptyList();
    }

    @Override
    public int countIdCardCheckByCheckIng(List<Long> checkIds,Integer checkType,List<Integer> certificateTypeList,Integer checkStatus) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            if(CollectionUtils.isNotEmpty(checkIds)){
                builder.in("check_id", checkIds, Types.BIGINT);
            }
            if(checkType!=null){
                builder.and().equal("check_type", checkType, Types.INTEGER);
            }
            if(CollectionUtils.isNotEmpty(certificateTypeList)){
                builder.and().in("certificate_type", certificateTypeList, Types.INTEGER);
            }

            builder.and().equal("check_status",checkStatus,Types.INTEGER);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return tmsCertificateCheckPORepo.getDao().count(builder,hints).intValue();
        } catch (Exception e) {
            logger.error("queryCertificateByCheckId error", e);
        }
        return 0;
    }

    @Override
    public int synchronousCheck(List<Long> ids, Long checkId, Integer checkType) {
        if(CollectionUtils.isEmpty(ids)){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            builder.setTemplate("update tms_certificate_check set check_id = ?,check_type = ? where id in (?) ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "check_id", Types.BIGINT, checkId);
            parameters.setSensitive(i++, "check_type", Types.INTEGER, checkType);
            parameters.setInParameter(i++, "id", Types.BIGINT, ids);
            return tmsCertificateCheckPORepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TmsCertificateCheckPO> queryCerCheckListByCheckIds(Long checkId, Integer checkType) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append(" select c.id, c.check_id,c.check_type,c.certificate_type,c.check_status,c.datachange_createtime,c.check_result,c.check_keyword,c.check_content,c.datachange_lasttime,c.create_user" +
                    " from (select id, check_id,check_type,certificate_type,check_status,datachange_createtime,check_result,check_keyword,check_content,datachange_lasttime,create_user from tms_certificate_check where check_id = ? and check_type = ?  order by datachange_createtime desc limit 100) c" +
                    "     group by c.certificate_type ");
            parameters.set(i++, "check_id", Types.BIGINT, checkId);
            parameters.set(i++, "check_type", Types.INTEGER, checkType);
            return tmsCertificateCheckPORepo.getQueryDao().query(sqlStr.toString(), parameters, hints, TmsCertificateCheckPO.class);
        } catch (Exception e) {
            logger.error("queryCertificateByCheckId error", e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<TmsCertificateCheckPO> queryCerCheckListByKeyWord(String checkKeyword,Integer checkStatus) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("check_keyword",checkKeyword,Types.VARCHAR);
            builder.and().equal("check_status",checkStatus,Types.INTEGER);
            return tmsCertificateCheckPORepo.getDao().query(builder,hints);
        } catch (Exception e) {
            logger.error("queryCertificateByCheckId error", e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<TmsCertificateCheckPO> queryCerCheckListByKeyWord(List<String> checkKeyword,Integer checkStatus,Integer checkType,Integer certificateType,int pageNo,int pageSize) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("check_status",checkStatus,Types.INTEGER);
            if(CollectionUtils.isNotEmpty(checkKeyword)){
                builder.and().in("check_keyword",checkKeyword,Types.VARCHAR);
            }
            if(certificateType!=null){
                builder.and().equal("certificate_type",certificateType,Types.INTEGER);
            }
            if(checkType!=null){
                builder.and().equal("check_type",checkType,Types.INTEGER);
            }
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.atPage(pageNo,pageSize);
            return tmsCertificateCheckPORepo.getDao().query(builder,hints);
        } catch (Exception e) {
            logger.error("queryCertificateByCheckId error", e);
        }
        return Collections.emptyList();
    }

    @Override
    public int countCerCheckListByKeyWord(List<String> checkKeyword, Integer checkStatus, Integer checkType, Integer certificateType) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            builder.equal("check_status",checkStatus,Types.INTEGER);
            if(CollectionUtils.isNotEmpty(checkKeyword)){
                builder.and().in("check_keyword",checkKeyword,Types.VARCHAR);
            }
            if(certificateType!=null){
                builder.and().equal("certificate_type",certificateType,Types.INTEGER);
            }
            if(checkType!=null){
                builder.and().equal("check_type",checkType,Types.INTEGER);
            }
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return tmsCertificateCheckPORepo.getDao().count(builder,hints).intValue();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TmsCertificateCheckPO> queryCerCheckListByCheckIds(List<Long> checkIds, List<Integer> checkType) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("check_id",checkIds,Types.BIGINT);
            builder.and().in("check_type",checkType,Types.INTEGER);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return tmsCertificateCheckPORepo.getDao().query(builder,hints);
        } catch (Exception e) {
            logger.error("queryCertificateByCheckId error", e);
        }
        return Collections.emptyList();
    }

    @Override
    public TmsCertificateCheckPO queryCertificateCheckByCondition(Long checkId, int checkType, int certificateType) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.select("id","check_status");
            builder.equal("check_id", checkId, Types.BIGINT, false);
            builder.and().equal("check_type", checkType, Types.INTEGER, false);
            builder.and().equal("certificate_type", certificateType, Types.INTEGER, false);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.orderBy("datachange_lasttime", false);
            builder.atPage(1, 1);
            List<TmsCertificateCheckPO> checkPOS = tmsCertificateCheckPORepo.getDao().query(builder, hints);
            return CollectionUtils.isEmpty(checkPOS) ? null : checkPOS.get(0);
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TmsCertificateCheckPO> queryCerCheckListByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("id",ids,Types.BIGINT);
            return tmsCertificateCheckPORepo.getDao().query(builder,hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int batchUpdateCheckStatus(List<Long> ids, Integer checkStatus) {
        if(CollectionUtils.isEmpty(ids)){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            builder.setTemplate("update tms_certificate_check set check_status = ?,third_check_status = ?,datachange_lasttime=now() where id in (?) ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "check_status", Types.INTEGER, checkStatus);
            parameters.setSensitive(i++, "third_check_status", Types.INTEGER, checkStatus);
            parameters.setInParameter(i++, "id", Types.BIGINT, ids);
            return tmsCertificateCheckPORepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TmsCertificateCheckPO> queryCerCheckListByKeyWordOrderById(Long checkId,String checkKeyword, Integer checkStatus, Integer checkType, Integer certificateType, int pageNo, int pageSize) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("check_id",checkId,Types.BIGINT);
            builder.and().equal("check_status",checkStatus,Types.INTEGER);
            builder.and().equal("check_keyword",checkKeyword,Types.VARCHAR);
            if(certificateType!=null){
                builder.and().equal("certificate_type",certificateType,Types.INTEGER);
            }
            if(checkType!=null){
                builder.and().equal("check_type",checkType,Types.INTEGER);
            }
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.orderBy("id", false);
            builder.atPage(pageNo,pageSize);
            return tmsCertificateCheckPORepo.getDao().query(builder,hints);
        } catch (Exception e) {
            logger.error("queryCertificateByCheckId error", e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<TmsCertificateCheckPO> queryCertificateCheck4DrvCache(Set<Long> checkIdList, int checkType, List<Integer> certificateTypeList) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.select("check_id", "certificate_type", "check_status");
            builder.equal("check_type", checkType, Types.TINYINT);
            builder.and().in("check_id", Lists.newArrayList(checkIdList), Types.BIGINT);
            builder.and().in("certificate_type", certificateTypeList, Types.TINYINT);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.orderBy("id", false);
            return tmsCertificateCheckPORepo.getDao().query(builder, hints);
        } catch (Exception e) {
            logger.error("queryCertificateCheck4DrvCacheError", "Error:{}", e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<TmsCertificateCheckPO> queryCertificateByCheckIdOrderBy(Long checkId, Integer checkType) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("check_id",checkId,Types.BIGINT);
            builder.and().equal("check_type",checkType,Types.INTEGER);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.orderBy( "id",false);
            return tmsCertificateCheckPORepo.getDao().query(builder,hints);
        } catch (Exception e) {
            logger.error("queryCertificateByCheckId error", e);
        }
        return Collections.emptyList();
    }

    @Override
    public int updateCheckStatus(Long id, Integer checkStatus, String modifyUser) {

        DalHints hints = DalHints.createIfAbsent(null);
        try {
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            builder.setTemplate("update tms_certificate_check set check_status = ?,modify_user = ? where id = ? ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "check_status", Types.INTEGER, checkStatus);
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
            parameters.setSensitive(i++, "id", Types.BIGINT, id);
            return tmsCertificateCheckPORepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateIdcardCheckStatus(Long id, Integer checkStatus, Boolean isThirdCheck, String modifyUser) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("update tms_certificate_check set modify_user = ?,third_check_status = ?,check_status = ? where id = ? ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
            parameters.setSensitive(i++, "third_check_status", Types.INTEGER, checkStatus);
            parameters.setSensitive(i++, "check_status", Types.INTEGER, checkStatus);
            parameters.setSensitive(i++, "id", Types.BIGINT, id);
            builder.setTemplate(stringBuilder.toString());
            return tmsCertificateCheckPORepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TmsCertificateCheckPO> queryNewCheckListByCheckIds(List<Long> checkIds, Integer checkType,Boolean versionFlag, Integer thirdCheckStatus) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("check_id",checkIds,Types.BIGINT);
            builder.and().equal("check_type",checkType,Types.INTEGER);
            if(versionFlag){
                builder.and().equal("third_check_status",thirdCheckStatus,Types.INTEGER);
            }else {
                builder.and().equal("check_status",thirdCheckStatus,Types.INTEGER);
            }
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.orderBy( "id",false);
            return tmsCertificateCheckPORepo.getDao().query(builder,hints);
        } catch (Exception e) {
            logger.error("queryCertificateByCheckId error", e);
        }
        return Collections.emptyList();
    }

    @Override
    public int updateCertificateActive(Long checkId, Integer checkType, Integer certificateType, Boolean active, String modifyUser) {
        if(checkId == null || checkId <= 0){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("update tms_certificate_check set modify_user = ?,active = ? where check_id = ? and check_type = ? and certificate_type = ? ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
            parameters.setSensitive(i++, "active", Types.BIT, active);
            parameters.setSensitive(i++, "check_id", Types.BIGINT, checkId);
            parameters.setSensitive(i++, "check_type", Types.INTEGER, checkType);
            parameters.setSensitive(i++, "certificate_type", Types.INTEGER, certificateType);
            builder.setTemplate(stringBuilder.toString());
            return tmsCertificateCheckPORepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateCertificateStatus(List<Long> checkIds, Integer checkType, Integer certificateType, Integer checkStatus, String modifyUser) {
        if(CollectionUtils.isEmpty(checkIds)){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("update tms_certificate_check set modify_user = ?,third_check_status = ?,check_status = ? where check_id in (?) and check_type = ? and certificate_type = ? ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
            parameters.setSensitive(i++, "third_check_status", Types.INTEGER, checkStatus);
            parameters.setSensitive(i++, "check_status", Types.INTEGER, checkStatus);
            parameters.setInParameter(i++, "check_id", Types.BIGINT, checkIds);
            parameters.setSensitive(i++, "check_type", Types.INTEGER, checkType);
            parameters.setSensitive(i++, "certificate_type", Types.INTEGER, certificateType);
            builder.setTemplate(stringBuilder.toString());
            return tmsCertificateCheckPORepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateCertificateActive(Long checkId, Integer checkType, List<Integer> certificateType, Boolean active, String modifyUser) {
        if(checkId == null || checkId <= 0){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("update tms_certificate_check set modify_user = ?,active = ? where check_id = ? and check_type = ? and certificate_type in (?) ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
            parameters.setSensitive(i++, "active", Types.BIT, active);
            parameters.setSensitive(i++, "check_id", Types.BIGINT, checkId);
            parameters.setSensitive(i++, "check_type", Types.INTEGER, checkType);
            parameters.setInParameter(i++, "certificate_type", Types.INTEGER, certificateType);
            builder.setTemplate(stringBuilder.toString());
            return tmsCertificateCheckPORepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }
}
