package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.helper.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import org.apache.commons.collections.*;
import org.springframework.stereotype.*;
import org.springframework.util.StringUtils;

import java.sql.*;
import java.util.*;

/**
 * 进单配置
 * <AUTHOR>
 * @Date 2020/3/3 17:36
 */
@Repository
public class InOrderConfigRepositoryImpl implements InOrderConfigRepository {

    private DalRepository<TspIntoOrderConfigPO> tspIntoOrderConfigRepo;

    private DalRowMapper<TspIntoOrderConfigPO> tspTransportGroupPORowMapper;

    public InOrderConfigRepositoryImpl() throws SQLException {
        tspIntoOrderConfigRepo = new DalRepositoryImpl<>(TspIntoOrderConfigPO.class);
        this.tspTransportGroupPORowMapper = new DalDefaultJpaMapper<>(TspIntoOrderConfigPO.class);
    }

    public DalRepository<TspIntoOrderConfigPO> getTspIntoOrderConfigRepo() {
        return tspIntoOrderConfigRepo;
    }

    /**
     * 新增进单配置
     * @param intoOrderConfigPOList
     * @return
     */
    public int addInOrderConfig(List<TspIntoOrderConfigPO> intoOrderConfigPOList){
        try {
            if (CollectionUtils.isEmpty(intoOrderConfigPOList)) {
                return 0;
            }
            DalHints hints = DalHints.createIfAbsent(null);
            return tspIntoOrderConfigRepo.getDao().combinedInsert(hints, intoOrderConfigPOList);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 更新进单配置
     * @param intoOrderConfigPOList
     * @return
     */
    public void updateInOrderConfig(List<TspIntoOrderConfigPO> intoOrderConfigPOList){
        try {
            if (CollectionUtils.isEmpty(intoOrderConfigPOList)) {
                return ;
            }
            DalHints hints = DalHints.createIfAbsent(null);
            tspIntoOrderConfigRepo.getDao().batchUpdate(hints, intoOrderConfigPOList);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 查询进单配置
     * @param transportGroupId
     * @param active
     * @return
     */
    public List<TspIntoOrderConfigPO> queryInOrderConfigs(Long transportGroupId,Integer active){
        try {
            DalHints hints = DalHints.createIfAbsent(null);

            SelectSqlBuilder sqlBuilder = new SelectSqlBuilder();
            sqlBuilder.selectAll();
            sqlBuilder.equal("transport_group_id",transportGroupId, Types.BIGINT,false);
            if (active != null) {
                sqlBuilder.and().equal("active", active,Types.BIT,false);
            }
            return tspIntoOrderConfigRepo.getDao().query(sqlBuilder,hints);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 查询进单配置
     * @param transportGroupIds
     * @param active
     * @return
     */
    public List<TspIntoOrderConfigPO> queryInOrderConfigs(List<Long> transportGroupIds,Integer active){
        try {
            DalHints hints = DalHints.createIfAbsent(null);

            SelectSqlBuilder sqlBuilder = new SelectSqlBuilder();
            sqlBuilder.selectAll();
            if (CollectionUtils.isNotEmpty(transportGroupIds)) {
                sqlBuilder.and().in("transport_group_id",transportGroupIds, Types.BIGINT,false);
            }
            if (active != null) {
                sqlBuilder.and().equal("active", active,Types.BIT,false);
            }
            return tspIntoOrderConfigRepo.getDao().query(sqlBuilder,hints);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    @Override
    public int queryCountTspIntoOrderConfigAll(Long id) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            if(id != null){
                builder.equal("id",id,Types.BIGINT,false);
            }
            return tspIntoOrderConfigRepo.getDao().count(builder,hints).intValue();
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TspIntoOrderConfigPO> queryTspIntoOrderConfigAll(Long id, Integer pageNo, Integer pageSize) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);

            SelectSqlBuilder sqlBuilder = new SelectSqlBuilder();
            sqlBuilder.selectAll();
            if (id != null) {
                sqlBuilder.equal("id",id, Types.BIGINT,false);
            }
            sqlBuilder.atPage(pageNo,pageSize);
            return tspIntoOrderConfigRepo.getDao().query(sqlBuilder,hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateIntoOrderCounryId(Long id, Long countryId) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        builder.setTemplate("update tsp_into_order_config set country_id = ? where id = ?");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.setSensitive(i++, "country_id", Types.VARCHAR, countryId);
        parameters.setSensitive(i++, "id", Types.BIGINT, id);
        return tspIntoOrderConfigRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public List<TspIntoOrderConfigPO> queryInOrderConfigs(List<Long> transportGroupIds, Integer active, Long cityId, String locationCode)throws SQLException{
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        builder.in("transport_group_id",transportGroupIds,Types.BIGINT);
        builder.and();
        builder.equal("active",active,Types.BIT);
        //忽略城市id 条件接送机站 根据车站和航站楼 查询  包车根据运力组id查
        if(!StringUtils.isEmpty(locationCode)){
            builder.and();
            builder.equal("location_code",locationCode,Types.VARCHAR);
        }
        return tspIntoOrderConfigRepo.getDao().query(builder,hints);
    }
}
