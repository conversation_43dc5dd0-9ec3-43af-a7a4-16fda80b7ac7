package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import org.apache.commons.lang3.*;

/**
 * 驾驶证核验DTO
 */
public class DrvAuditDTO {


    private Long checkId;

    private Integer checkType;

    private Integer certificateType;

    /**
     * 原身份证号
     */
    private String drvIdCard;

    /**
     * 暂定OCR识别的驾驶证号
     */
    private String newDrvIdCard;

    /**
     * 姓名
     */
    private String drvName;

    // 初次领证日期
    private String firstDrvLicenseDate;

    //有效结束日期
    private String expiryEndDate;

    //原驾驶证图片
    private String orgDrvcardImg;

    //新驾驶证图片
    private String newDrvcardImg;

    //原网约车驾驶证图片
    private String orgNetVehiclePeoImg;

    //新网约车驾驶证图片
    private String newNetVehiclePeoImg;

    private String userName;

    //原身份证图片
    private String orgIdcardImg;
    //新身份证图片
    private String newIdcardImg;
    private String drvPhone;
    private Long id;
    private Long supplierId;
    private Integer versionFlag;
    private Integer checkStatus;

    public static DrvAuditDTO buildNewAddRecruitingDrvDTO(DrvDriverPO  orgDriverPO,Integer checkType){
        DrvAuditDTO auditDTO = new DrvAuditDTO();
        auditDTO.setCheckId(orgDriverPO.getDrvId());
        auditDTO.setCertificateType(TmsTransportConstant.CertificateTypeEnum.DRIVERLICENSE.getCode());
        auditDTO.setDrvName(orgDriverPO.getDrvName());
        auditDTO.setCheckType(checkType);
        auditDTO.setDrvIdCard(TmsTransUtil.decrypt(orgDriverPO.getDrvIdcard(), KeyType.Identity_Card));
        auditDTO.setOrgNetVehiclePeoImg(orgDriverPO.getNetVehiclePeoImg());
        auditDTO.setOrgDrvcardImg(orgDriverPO.getDrvcardImg());
        auditDTO.setUserName(orgDriverPO.getModifyUser());
        auditDTO.setOrgIdcardImg(orgDriverPO.getIdcardImg());
        auditDTO.setDrvPhone(orgDriverPO.getDrvPhone());
        return auditDTO;
    }

    public static DrvAuditDTO refreshbuildDrvDTO(Long id,String drvName,String drvIdCard,Integer checkType,Long supplierId,Integer versionFlag,Integer checkStatus,Long checkId){
        DrvAuditDTO auditDTO = new DrvAuditDTO();
        auditDTO.setDrvName(drvName);
        auditDTO.setDrvIdCard(TmsTransUtil.decrypt(drvIdCard, KeyType.Identity_Card));
        auditDTO.setId(id);
        auditDTO.setCheckType(checkType);
        auditDTO.setSupplierId(supplierId);
        auditDTO.setVersionFlag(versionFlag);
        auditDTO.setCheckStatus(checkStatus);
        auditDTO.setCheckId(checkId);
        return auditDTO;
    }

    public static DrvAuditDTO refreshAllDrvDTO(DrvDriverPO drvDriverPO){
        DrvAuditDTO auditDTO = new DrvAuditDTO();
        auditDTO.setCheckId(drvDriverPO.getDrvId());
        auditDTO.setDrvName(drvDriverPO.getDrvName());
        auditDTO.setCheckType(TmsTransportConstant.CertificateCheckTypeEnum.DRV.getCode());
        auditDTO.setDrvIdCard(TmsTransUtil.decrypt(drvDriverPO.getDrvIdcard(), KeyType.Identity_Card));
        auditDTO.setOrgNetVehiclePeoImg(drvDriverPO.getNetVehiclePeoImg());
        auditDTO.setOrgDrvcardImg(drvDriverPO.getDrvcardImg());
        auditDTO.setUserName(drvDriverPO.getModifyUser());
        auditDTO.setOrgIdcardImg(drvDriverPO.getIdcardImg());
        auditDTO.setDrvPhone(drvDriverPO.getDrvPhone());
        return auditDTO;
    }

    public static DrvAuditDTO approveDrvDTO(DrvDriverPO drvDriverPO,DrvDriverPO newDrvDriverPO,Long approveId){
        DrvAuditDTO auditDTO = new DrvAuditDTO();
        auditDTO.setCheckId(drvDriverPO.getDrvId());
        auditDTO.setDrvName(StringUtils.isEmpty(newDrvDriverPO.getDrvLicenseName())?newDrvDriverPO.getDrvName():newDrvDriverPO.getDrvLicenseName());
        auditDTO.setCheckType(TmsTransportConstant.CertificateCheckTypeEnum.DRV.getCode());
        auditDTO.setDrvIdCard(TmsTransUtil.decrypt(drvDriverPO.getDrvIdcard(), KeyType.Identity_Card));
        auditDTO.setNewDrvIdCard(TmsTransUtil.decrypt(StringUtils.isEmpty(newDrvDriverPO.getDrvLicenseNumber())?newDrvDriverPO.getDrvIdcard():newDrvDriverPO.getDrvLicenseNumber(), KeyType.Identity_Card));
        auditDTO.setOrgNetVehiclePeoImg(drvDriverPO.getNetVehiclePeoImg());
        auditDTO.setNewNetVehiclePeoImg(newDrvDriverPO.getNetVehiclePeoImg());
        auditDTO.setOrgDrvcardImg(drvDriverPO.getDrvcardImg());
        auditDTO.setNewDrvcardImg(newDrvDriverPO.getDrvcardImg());
        auditDTO.setUserName(newDrvDriverPO.getModifyUser());
        auditDTO.setOrgIdcardImg(drvDriverPO.getIdcardImg());
        auditDTO.setNewIdcardImg(newDrvDriverPO.getIdcardImg());
        auditDTO.setDrvPhone(drvDriverPO.getDrvPhone());
        auditDTO.setId(approveId);
        return auditDTO;
    }

    public static DrvAuditDTO refreshApprovebuildDrvDTO(String drvName,String drvIdCard){
        DrvAuditDTO auditDTO = new DrvAuditDTO();
        auditDTO.setDrvName(drvName);
        auditDTO.setDrvIdCard(TmsTransUtil.decrypt(drvIdCard, KeyType.Identity_Card));
        return auditDTO;
    }

    public Integer getVersionFlag() {
        return versionFlag;
    }

    public void setVersionFlag(Integer versionFlag) {
        this.versionFlag = versionFlag;
    }

    public String getNewDrvIdCard() {
        return newDrvIdCard;
    }

    public void setNewDrvIdCard(String newDrvIdCard) {
        this.newDrvIdCard = newDrvIdCard;
    }

    public Long getCheckId() {
        return checkId;
    }

    public void setCheckId(Long checkId) {
        this.checkId = checkId;
    }

    public Integer getCheckType() {
        return checkType;
    }

    public void setCheckType(Integer checkType) {
        this.checkType = checkType;
    }

    public Integer getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(Integer certificateType) {
        this.certificateType = certificateType;
    }

    public String getDrvIdCard() {
        return drvIdCard;
    }

    public void setDrvIdCard(String drvIdCard) {
        this.drvIdCard = drvIdCard;
    }

    public String getDrvName() {
        return drvName;
    }

    public void setDrvName(String drvName) {
        this.drvName = drvName;
    }

    public String getFirstDrvLicenseDate() {
        return firstDrvLicenseDate;
    }

    public void setFirstDrvLicenseDate(String firstDrvLicenseDate) {
        this.firstDrvLicenseDate = firstDrvLicenseDate;
    }

    public String getExpiryEndDate() {
        return expiryEndDate;
    }

    public void setExpiryEndDate(String expiryEndDate) {
        this.expiryEndDate = expiryEndDate;
    }

    public String getOrgDrvcardImg() {
        return orgDrvcardImg;
    }

    public void setOrgDrvcardImg(String orgDrvcardImg) {
        this.orgDrvcardImg = orgDrvcardImg;
    }

    public String getNewDrvcardImg() {
        return newDrvcardImg;
    }

    public void setNewDrvcardImg(String newDrvcardImg) {
        this.newDrvcardImg = newDrvcardImg;
    }

    public String getOrgNetVehiclePeoImg() {
        return orgNetVehiclePeoImg;
    }

    public void setOrgNetVehiclePeoImg(String orgNetVehiclePeoImg) {
        this.orgNetVehiclePeoImg = orgNetVehiclePeoImg;
    }

    public String getNewNetVehiclePeoImg() {
        return newNetVehiclePeoImg;
    }

    public void setNewNetVehiclePeoImg(String newNetVehiclePeoImg) {
        this.newNetVehiclePeoImg = newNetVehiclePeoImg;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getOrgIdcardImg() {
        return orgIdcardImg;
    }

    public void setOrgIdcardImg(String orgIdcardImg) {
        this.orgIdcardImg = orgIdcardImg;
    }

    public String getNewIdcardImg() {
        return newIdcardImg;
    }

    public void setNewIdcardImg(String newIdcardImg) {
        this.newIdcardImg = newIdcardImg;
    }

    public String getDrvPhone() {
        return drvPhone;
    }

    public void setDrvPhone(String drvPhone) {
        this.drvPhone = drvPhone;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Integer getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }
}
