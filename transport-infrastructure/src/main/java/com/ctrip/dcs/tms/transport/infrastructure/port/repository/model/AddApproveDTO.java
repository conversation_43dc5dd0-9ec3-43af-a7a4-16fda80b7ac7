package com.ctrip.dcs.tms.transport.infrastructure.port.repository.model;

import com.ctrip.dcs.tms.transport.api.model.OcrPassStatusModelSOA;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;

import java.util.*;

public class AddApproveDTO<T> {

    private Long rrdId;
    private T newMod;
    private TmsTransportConstant.ApproveSourceTypeEnum recordTypeEnum;
    private Map<String, String> attributeKeyValue;
    private String modifyUser;
    private String approveName;
    private Long supplierId;
    private String toDoJson;
    //境内标识 true:境内,false:境外
    private Boolean domesticFlag;
    private Integer eventType;
    List<OcrPassStatusModelSOA> ocrPassStatusList;
    private Boolean temporaryDispatchMarkFlag;
    private Boolean incomplianceRuleFlag;
    private Integer auditStatus;
    private Long cityId;
    private List<String> editFieldList;

    public static <T> AddApproveDTO<T> buildAddDTO(Long rrdId,Long supplierId, T newMod,String approveName,
                                                   TmsTransportConstant.ApproveSourceTypeEnum recordTypeEnum,
                                                   Map<String, String> attributeKeyValue, String modifyUser,String toDoJson,Boolean domesticFlag,
                                                   List<OcrPassStatusModelSOA> ocrPassStatusList,Boolean temporaryDispatchMarkFlag,Boolean incomplianceRuleFlag,Integer auditStatus, Long cityId, List<String> editFieldList){
        AddApproveDTO<T> addApproveDTO = new AddApproveDTO<T>();
        addApproveDTO.setRrdId(rrdId);
        addApproveDTO.setNewMod(newMod);
        addApproveDTO.setAttributeKeyValue(attributeKeyValue);
        addApproveDTO.setRecordTypeEnum(recordTypeEnum);
        addApproveDTO.setModifyUser(modifyUser);
        addApproveDTO.setApproveName(approveName);
        addApproveDTO.setSupplierId(supplierId);
        addApproveDTO.setToDoJson(toDoJson);
        addApproveDTO.setDomesticFlag(domesticFlag);
        //如果是境外+车辆，审核名称为车辆ID
        if(!domesticFlag && recordTypeEnum.getCode().intValue() == TmsTransportConstant.ApproveSourceTypeEnum.VEHICLE.getCode().intValue()){
            addApproveDTO.setApproveName(String.valueOf(rrdId));
        }
        if(!domesticFlag){
            addApproveDTO.setOcrPassStatusList(ocrPassStatusList);
        }
        addApproveDTO.setTemporaryDispatchMarkFlag(temporaryDispatchMarkFlag);
        addApproveDTO.setIncomplianceRuleFlag(incomplianceRuleFlag);
        addApproveDTO.setAuditStatus(auditStatus);
        addApproveDTO.setCityId(cityId);
        addApproveDTO.setEditFieldList(editFieldList);
        return addApproveDTO;
    }

    public static <T> AddApproveDTO<T> buildcheckColumnApproveIngDTO(Long rrdId, T newMod, TmsTransportConstant.ApproveSourceTypeEnum recordTypeEnum, Boolean domesticFlag, Integer eventType,
        List<OcrPassStatusModelSOA> ocrPassStatusList, Boolean temporaryDispatchMarkFlag, Boolean incomplianceRuleFlag, Integer auditStatus, Long cityId, List<String> editFieldList) {
        AddApproveDTO<T> addApproveDTO = new AddApproveDTO<T>();
        addApproveDTO.setRrdId(rrdId);
        addApproveDTO.setNewMod(newMod);
        addApproveDTO.setRecordTypeEnum(recordTypeEnum);
        addApproveDTO.setDomesticFlag(domesticFlag);
        addApproveDTO.setEventType(eventType);
        addApproveDTO.setTemporaryDispatchMarkFlag(temporaryDispatchMarkFlag);
        if(!domesticFlag){
            addApproveDTO.setEventType(TmsTransportConstant.EnentTypeEnum.OVERSEASVEHMOD.getCode());
            if(temporaryDispatchMarkFlag){
                addApproveDTO.setEventType(TmsTransportConstant.EnentTypeEnum.TEMPORARYDISPATCH.getCode());
            }
            addApproveDTO.setOcrPassStatusList(ocrPassStatusList);
        }
        addApproveDTO.setIncomplianceRuleFlag(incomplianceRuleFlag);
        addApproveDTO.setAuditStatus(auditStatus);
        addApproveDTO.setCityId(cityId);
        addApproveDTO.setEditFieldList(editFieldList);
        return addApproveDTO;
    }

    public Boolean getTemporaryDispatchMarkFlag() {
        return temporaryDispatchMarkFlag;
    }

    public void setTemporaryDispatchMarkFlag(Boolean temporaryDispatchMarkFlag) {
        this.temporaryDispatchMarkFlag = temporaryDispatchMarkFlag;
    }

    public String getToDoJson() {
        return toDoJson;
    }

    public void setToDoJson(String toDoJson) {
        this.toDoJson = toDoJson;
    }

    public Long getRrdId() {
        return rrdId;
    }

    public void setRrdId(Long rrdId) {
        this.rrdId = rrdId;
    }

    public T getNewMod() {
        return newMod;
    }

    public void setNewMod(T newMod) {
        this.newMod = newMod;
    }

    public TmsTransportConstant.ApproveSourceTypeEnum getRecordTypeEnum() {
        return recordTypeEnum;
    }

    public void setRecordTypeEnum(TmsTransportConstant.ApproveSourceTypeEnum recordTypeEnum) {
        this.recordTypeEnum = recordTypeEnum;
    }

    public Map<String, String> getAttributeKeyValue() {
        return attributeKeyValue;
    }

    public void setAttributeKeyValue(Map<String, String> attributeKeyValue) {
        this.attributeKeyValue = attributeKeyValue;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public String getApproveName() {
        return approveName;
    }

    public void setApproveName(String approveName) {
        this.approveName = approveName;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Boolean getDomesticFlag() {
        return domesticFlag;
    }

    public void setDomesticFlag(Boolean domesticFlag) {
        this.domesticFlag = domesticFlag;
    }

    public Integer getEventType() {
        return eventType;
    }

    public void setEventType(Integer eventType) {
        this.eventType = eventType;
    }

    public List<OcrPassStatusModelSOA> getOcrPassStatusList() {
        return ocrPassStatusList;
    }

    public void setOcrPassStatusList(List<OcrPassStatusModelSOA> ocrPassStatusList) {
        this.ocrPassStatusList = ocrPassStatusList;
    }

    public Boolean getIncomplianceRuleFlag() {
        return incomplianceRuleFlag;
    }

    public void setIncomplianceRuleFlag(Boolean incomplianceRuleFlag) {
        this.incomplianceRuleFlag = incomplianceRuleFlag;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public List<String> getEditFieldList() {
        return editFieldList;
    }

    public void setEditFieldList(List<String> editFieldList) {
        this.editFieldList = editFieldList;
    }
}
