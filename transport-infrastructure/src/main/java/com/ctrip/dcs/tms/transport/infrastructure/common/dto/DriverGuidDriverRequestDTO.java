package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

@Data
public class DriverGuidDriverRequestDTO {

  /**
   * 多个，用逗号分割
   */
  List<Long> driverIdList;
  String driverName;
  List<String> driverPhoneList;
  Integer status;
  Long supplierId;
  private Long cityId;

  public void setDriverIdListByString(String driverIds) {
    if (StringUtils.isBlank(driverIds)) {
      return;
    }
    driverIdList = Lists.newArrayList();
    Arrays.stream(driverIds.split(",")).forEach(id -> {
      driverIdList.add(Long.valueOf(id));
    });
  }

  public void setDriverPhoneListByString(String driverPhones) {
    if (StringUtils.isBlank(driverPhones)) {
      return;
    }
    driverPhoneList = Lists.newArrayList();
    Arrays.stream(driverPhones.split(",")).forEach(phone -> {
      driverPhoneList.add(phone);
    });
  }
}
