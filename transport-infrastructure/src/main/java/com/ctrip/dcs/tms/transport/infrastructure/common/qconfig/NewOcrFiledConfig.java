package com.ctrip.dcs.tms.transport.infrastructure.common.qconfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.ctrip.dcs.tms.transport.infrastructure.common.dto.NewOCRDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OcrComplianceDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OcrRecognitionResultDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.RequiredFieldDTO;

import qunar.tc.qconfig.client.JsonConfig;

@Component
public class NewOcrFiledConfig {

    private static JsonConfig.ParameterizedClass parameterString1 = JsonConfig.ParameterizedClass.of(String.class);// map key的泛型类型
    private static JsonConfig.ParameterizedClass parametervalue1 = JsonConfig.ParameterizedClass.of(List.class, RequiredFieldDTO.class);
    private static JsonConfig.ParameterizedClass parameter1 = JsonConfig.ParameterizedClass.of(Map.class, parameterString1, parametervalue1);// Map<String, Set<String>>
    private static JsonConfig<Map<String, List<RequiredFieldDTO>>> complexTestJsonConfig1 = JsonConfig.get("new.ocr.back.field.json", parameter1);

    public List<RequiredFieldDTO> getFieldList(Long locationId, String locationType, List<String> sceneList) {
        Map<String, List<RequiredFieldDTO>> current = complexTestJsonConfig1.current();

        List<RequiredFieldDTO> list = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(sceneList)) {
            sceneList.forEach(scene -> {
                List<RequiredFieldDTO> requiredFieldDTOList = current.get(scene);
                if (CollectionUtils.isNotEmpty(requiredFieldDTOList)) {
                    List<RequiredFieldDTO> collect = requiredFieldDTOList.stream().filter(item -> item.getLocationId().contains(locationId) && item.getLocationType().equals(locationType)).collect(Collectors.toList());
                    collect.forEach(item -> item.setScene(scene));
                    if (CollectionUtils.isNotEmpty(collect)) {
                        list.addAll(collect);
                    }

                }
            });
        }
        return list;
    }

    JsonConfig<List<OcrRecognitionResultDTO>> config2 = JsonConfig.get("new.ocr.recognition.result.json", JsonConfig.ParameterizedClass.of(List.class, OcrRecognitionResultDTO.class));
    public OcrRecognitionResultDTO getOcrRecognitionResultList(Long locationId, String locationType, String imgType) {
        List<OcrRecognitionResultDTO> current = config2.current();
        return current.stream().filter(a -> a.getLocationId().equals(locationId) && a.getLocationType().equals(locationType) && a.getImgType().equals(imgType)).findFirst().orElse(null);
    }

    JsonConfig<List<OcrComplianceDTO>> ocrComplianceConfig = JsonConfig.get("new.ocr.compliance.type.json", JsonConfig.ParameterizedClass.of(List.class, OcrComplianceDTO.class));
    public OcrComplianceDTO getOcrComplianceList(Long locationId, String locationType) {
        List<OcrComplianceDTO> current = ocrComplianceConfig.current();
        return current.stream().filter(a -> a.getLocationId().equals(locationId) && a.getLocationType().equals(locationType)).findFirst().orElse(null);
    }


    JsonConfig<List<NewOCRDTO>> newOCrConfig = JsonConfig.get("new.ocr.use.json", JsonConfig.ParameterizedClass.of(List.class, NewOCRDTO.class));
    public NewOCRDTO getNewOcr(Long locationId, String locationType) {
        List<NewOCRDTO> current = newOCrConfig.current();
        return current.stream().filter(a -> a.getLocationId().equals(locationId) && a.getLocationType().equals(locationType)).findFirst().orElse(null);
    }
}
