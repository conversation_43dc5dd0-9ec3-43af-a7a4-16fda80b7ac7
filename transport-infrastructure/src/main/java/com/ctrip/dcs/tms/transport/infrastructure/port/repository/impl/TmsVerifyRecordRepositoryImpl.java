package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.helper.*;
import org.springframework.stereotype.*;

import java.sql.*;

/**
 *
 */
@Repository(value = "tmsVerifyRecordRepository")
public class TmsVerifyRecordRepositoryImpl implements TmsVerifyRecordRepository {

    private DalRepository<TmsVerifyRecordPO> tmsVerifyRecordRepo;

    private DalRowMapper<TmsVerifyRecordPO> tmsVerifyRecordPODalRowMapper;

    public TmsVerifyRecordRepositoryImpl() throws SQLException {
        tmsVerifyRecordRepo = new DalRepositoryImpl<>(TmsVerifyRecordPO.class);
        this.tmsVerifyRecordPODalRowMapper = new DalDefaultJpaMapper<>(TmsVerifyRecordPO.class);

    }

    @Override
    public TmsVerifyRecordPO queryByPk(Long id) {
        return tmsVerifyRecordRepo.queryByPk(id);
    }

    @Override
    public long insert(TmsVerifyRecordPO verifyRecordPO) throws SQLException {
        KeyHolder keyHolder = new KeyHolder();
        tmsVerifyRecordRepo.insert(new DalHints(), keyHolder, verifyRecordPO);
        return keyHolder.getKey().longValue();
    }
}
