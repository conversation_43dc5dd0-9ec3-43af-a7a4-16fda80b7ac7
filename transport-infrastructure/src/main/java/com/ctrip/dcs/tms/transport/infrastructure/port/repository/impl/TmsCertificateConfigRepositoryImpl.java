package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.helper.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.ctriposs.baiji.exception.*;
import org.apache.commons.collections.*;
import org.springframework.stereotype.*;

import java.sql.*;
import java.util.*;

/**
 * <AUTHOR>
 * @Description  证件
 * @Date 14:21 2020/9/11
 * @Param
 * @return
 **/
@Repository(value = "tmsCertificateConfigRepository")
public class TmsCertificateConfigRepositoryImpl implements TmsCertificateConfigRepository {

    private DalRepository<TmsCertificateConfigPO> tmsCertificateConfigRepo;

    private DalRowMapper<TmsCertificateConfigPO> tmsCertificateConfigPORowMapper;

    public TmsCertificateConfigRepositoryImpl() throws SQLException {
        tmsCertificateConfigRepo = new DalRepositoryImpl<>(TmsCertificateConfigPO.class);
        this.tmsCertificateConfigPORowMapper = new DalDefaultJpaMapper<>(TmsCertificateConfigPO.class);
    }
    @Override
    public TmsCertificateConfigPO queryByPK(Long id) {
        return tmsCertificateConfigRepo.queryByPk(id);
    }

    @Override
    public int countTmsCertificateConfig(List<Long> cityIds, List<Integer> productLines) {
        DalHints hints = DalHints.createIfAbsent(null);
        try{
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            if(CollectionUtils.isNotEmpty(cityIds)){
                builder.in("city_id",cityIds, Types.BIGINT);
            }
            if(CollectionUtils.isNotEmpty(productLines)){
                builder.and().in("product_line",productLines, Types.INTEGER);
            }
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return tmsCertificateConfigRepo.getDao().count(builder, hints).intValue();
        }catch (Exception e){
            e.printStackTrace();
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TmsCertificateConfigPO> queryTmsCertificateConfiList(List<Long> cityIds, List<Integer> productLines, int beginPage, int pageSize) {
        DalHints hints = DalHints.createIfAbsent(null);
        try{
            SelectSqlBuilder builder = new SelectSqlBuilder();
            if(CollectionUtils.isNotEmpty(cityIds)){
                builder.in("city_id",cityIds, Types.BIGINT);
            }
            if(CollectionUtils.isNotEmpty(productLines)){
                builder.and().in("product_line",productLines, Types.INTEGER);
            }
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.orderBy("datachange_lasttime", false);
            builder.atPage(beginPage, pageSize);
            return tmsCertificateConfigRepo.getDao().query(builder, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateCertificateConfig(Long id, String certificateConfig, String modifyUser,String vehicleTypeIds) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            builder.setTemplate("update tms_certificate_config set certificate_config = ?,modify_user = ?,vehicle_type_id = ? where id = ?");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "certificate_config", Types.VARCHAR, certificateConfig);
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
            parameters.setSensitive(i++, "vehicle_type_id", Types.VARCHAR, vehicleTypeIds);
            parameters.setSensitive(i++, "id", Types.BIGINT, id);
            return tmsCertificateConfigRepo.getQueryDao().update(builder, parameters, hints);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<TmsCertificateConfigPO> queryConfigByCityAndProLine(List<Long> cityIds, List<Integer> productLines) {
        DalHints hints = DalHints.createIfAbsent(null);
        try{
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.in("city_id",cityIds,Types.BIGINT);
            builder.and().in("product_line",productLines, Types.INTEGER);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.orderBy("datachange_lasttime", false);
            return tmsCertificateConfigRepo.getDao().query(builder, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int batchAddCertificateConfig(List<TmsCertificateConfigPO> configPO) throws SQLException {
        int[] resultList = tmsCertificateConfigRepo.batchInsert(configPO);
        return resultList.length;
    }

}
