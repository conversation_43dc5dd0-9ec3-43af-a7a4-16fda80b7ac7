package com.ctrip.dcs.tms.transport.infrastructure.port.repository;


import com.ctrip.dcs.tms.transport.api.model.DriverRelationListRequestSOAType;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.CommunicationsDrvInfoPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DriverCacheInfoPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvInfoPO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.dal.DalRepository;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface DrvDrvierRepository {

  /**
   * 查询司机的报名状态(针对某种运力组模式)
   * @param drvId
   * @return
   */
    int queryDrvApplyStatus(Long drvId,Integer transportGroupMode);

    DalRepository<DrvDriverPO> getDrvDriverRepo();

    DrvDriverPO queryByPk(Long drvId);

    int batchUpdateDrv(List<DrvDriverPO> drvDriverPOList);

    Long addDrv(DrvDriverPO doo) throws SQLException;

    Integer updateDrv(DrvDriverPO po);

    List<DrvDriverPO> queryDrvList(QueryDrvDO drvDO);

    DrvInfoPO queryDrvInfo(DrvInfoParam drvInfoParam) throws SQLException;

    List<DrvInfoPO> queryDrvInfoList(List<Long> drvIds) throws SQLException;

    Integer countDrvList(QueryDrvDO drvDO);

    int updateDrvStatus(List<Long> drvIds, Integer status, String modifyUser);

    //司机解绑车辆
    int unbindCarforDrv(List<Long> drvIds, String modifyUser);

    List<DrvDriverPO> queryDrvList(List<Long> drvIds);

    int freezeDrv(List<Long> drvIds, Integer status, Integer freezeHour, String freezeReason, String modifyUser);

    /**
     * 查询绑定状态司机list
     * */
    List<DrvDriverPO> queryBundleStatusDrvList(List<Long> drvIds, Long supplierId, DriverRelationListRequestSOAType requestSOAType);

    /**
     * 查询绑定解绑状态司机条数
     * */
    int queryBundleStatusDrvCount(List<Long> drvIds, Long supplierId, DriverRelationListRequestSOAType requestSOAType);

    /**
     * 校验司机唯一性
     * @param str
     * @param type
     * @return
     */
    Boolean checkDrvOnly(String str, Integer type) throws SQLException;

    /**
     * 修改司机地址
     * @param drvId
     * @param drvAddr
     * @return
     */
    int updateDrvAddr(Long drvId, String drvAddr,String addrModCount,String modifyUser) throws SQLException;

    /**
     * 更新司机意愿车型
     * @param drvId
     * @param intendVehicleTypeId
     * @param modifyUser
     * @return
     * @throws SQLException
     */
    int updateDrvIntendVehicleType(Long drvId, String intendVehicleTypeId,String modifyUser) throws SQLException;

    /**
     * 修改司机车型
     * @param vehicleId
     * @param vehicleTypeId
     * @return
     */
    int updateDrvVehicleType(Long vehicleId, Long vehicleTypeId) throws SQLException;

    /**
     * 通过运力组id查询已关联司机
     * @return
     */
    List<DrvDriverPO> queryDrvListByTgId(Long transportGroupId);

    /**
     * 司机端查询司机
     * @return
     */
    List<DrvDriverPO> queryDrvListByApp(QueryDrvListByAppDO appDO) throws SQLException;

    /**
     * 查询已绑定车辆的司机id
     * */
    Long queryDriverByVehicleId(Long vehicleId);

    /**
     * 查询司机列表(id,name)
     * @param supplierId
     * @return
     */
    List<DrvDriverPO> queryDrvBySupplierIdList(Long supplierId) throws SQLException;

    /**
     * 登录账号查询司机
     * @param loginAccount
     * @return
     */
    List<DrvDriverPO> queryDrvByLoginAccount(String loginAccount);


    int updateDrvAccount(Long drvId, String qunarAccout, String ppmAccout, String ctripAccount);

    /**
     *
     * 刷新司机上线时间
     * @param drvIds
     * @return
     */
    int updateDrvOnlineTime(List<Long> drvIds) throws SQLException;

    /**
     * 多选条件查询司机列表
     * @return
     */
    List<DrvDriverPO> queryDrvByMuSelConditions(QueryDrvByMuSelDO muSelDO);

    /**
     * 多选条件查询司机数据
     * @return
     */
    int countDrvByMuSelConditions(QueryDrvByMuSelDO muSelDO);

    /**
     * 查询已上线和已冻结的司机数量
     * @return
     */
    int countDrvByOnlineAndfreeze(List<Integer> drvStatus,List<Long> drvList);

    List<DrvDriverPO> queryDrvByOnlineAndfreeze(List<Long> paramsDrvList,List<Integer> drvStatus,Integer page,Integer pageSize);

    /**
     * 查询司机电话号码是0开头的数量
     * @return
     */
    int countDrvDirtyPhone();

    /**
     * 查询司机电话号码是0开头的数据
     * @return
     */
    List<DrvDriverPO> queryDrvDirtyPhone(int pageNo, int pageSize);

    DrvDriverPO queryOneDrvByPhone(String phone);
      /**
     * 根据车辆ID查询关联司机
     * @param vehicleIds
     * @return
     */
    List<DrvDriverPO> queryDrvByVehicleIds(List<Long> vehicleIds);

    List<DrvDriverPO> queryByHybridAccount(String account);

    List<DrvDriverPO> queryByNewHybridAccount(String account);

    List<DrvDriverPO> queryDrvBySupplierIdAndId(List<Long> drvId,Long supplierId);

    /**
     * 缓存使用
     * */
    List<Long> queryDrvIdByCondition(String name, String phone);

    /**
     * 可缓存数据
     * */
    List<Long> queryDrvId4Cache(int pageNo, int size);

  /**
   * 批量修改司机工作时段
   * @param drvIds
   * @param workPeriod
   * @param modifyUser
   * @return
   */
    int updateDrvWorkPeriod(List<Long> drvIds,String workPeriod,String modifyUser);

    /**
     * 查询判罚司机id
     * */
    List<Long> queryPenaltyOfflineDrvIdList(List<Long> drvIds);

    /**
     * 查询司机名
     * */
    List<String> queryDrvNameByIdList(List<Long> drvIds);

    /**
     * 更新司机状态
     * */
    int updateDrvStatus(List<Long> drvIds,Integer opFrom, Integer status, String modifyUser);
    /**
     * 根据城市查询推送给交通部数据
     * */
    List<CommunicationsDrvInfoPO> queryDriverInfo4TrafficAgency(Long cityId, List<Long> drvIds, Integer status, int pageNo, int size);

    /**
     * 批量修改司机审批状态
     * @param drvIds
     * @param approveStatus
     * @param modifyUser
     * @return
     */
    int updateDrvApproveStatus(List<Long> drvIds,Integer approveStatus,String modifyUser) throws SQLException;

  /**
   * 更新司机手机号
   * @param driverId
   * @param phone
   * @return
   * @throws SQLException
   */
    int updateDrvPhone(Long driverId,String phone,String modifyUser)throws SQLException;


    List<DrvDriverPO> queryOnlineDrvByCityAndVehicleType(Long cityId,Long vehicleTypeId,Integer drvStatus);
    /**
     *  查询英文名字为空的司机的数量
     * @return
     */
    int countDriverEnglishNameIsEmpty();
    /**
     *  查询英文名字为空的司机
     * @param pageNo
     * @param pageSize
     * @return
     */
    List<DrvDriverPO> queryDriverEnglishNameIsEmpty(int pageNo, int pageSize);

    /**
     * 查询司机数据
     * */
    List<DrvDriverPO> queryDrvByPage(Long drvId) throws SQLException;

    /**
     * 司机主缓存使用 - 查询司机id及车辆id
     */
    List<DrvDriverPO> queryDrvIdAndVehicleIdByCondition(QueryDriverConditionDTO req);

    /**
     * 司机缓存改版查询
     */
    List<DrvDriverPO> queryCacheDrvList(Set<Long> drvIdSet);

    /**
     　* @description: 城市查询司机
     　* <AUTHOR>
     　* @date 2022/6/30 16:44
     */
   List<Long>  queryDrvListByCity(List<Long> cityIds,List<Integer> drvStatus);

   /**
    * 查询司机资源组
    * */
   List<DrvDriverPO> queryDrvResourceByCondition(QueryDrvResourceConditionDTO conditionDTO);

  /**
   * 查询司机资源组数量
   * */
  int queryDrvResourceCountByCondition(QueryDrvResourceConditionDTO conditionDTO);

   /**
   　* @description: 废弃司机
   　* <AUTHOR>
   　* @date 2022/8/17 14:20
   */
   int discardDrv(Long drvId,Boolean active,String modifyUser);

  /**
   * 校验司机唯一性
   * @param str
   * @param type
   * @return
   */
  DrvDriverPO drvDispatchcheckDrvOnly(String str, Integer type) throws SQLException;

  /**
   * 新增司机，校验司机手机号唯一性（和库里带0/00校验重复）
   *
   * @param phone     手机号
   * @return
   */
  DrvDriverPO drvDispatchcheckDrvPhoneOnly(String phone);

  /**
   * 编辑司机，校验司机手机号唯一性（和库里带0/00校验重复）
   *
   * @param phone
   * @param originDrvId
   * @return
   */
  Boolean checkDrvPhoneOnly(String phone, Long originDrvId);

  /**
   　* @description: 司机派遣
   　* <AUTHOR>
   　* @date 2023/2/16 11:08
   */
  Integer countDrvDispatchList(QueryDrvDO drvDO);

  /**
   　* @description: 司机派遣
   　* <AUTHOR>
   　* @date 2023/2/16 11:09
   */
  List<DrvDriverPO> queryDrvDispatchList(QueryDrvDO drvDO);

  /**
  　* @description: 境外车辆修改车牌，同步司机的车牌
  　* <AUTHOR>
  　* @date 2023/7/7 10:46
  */
  int syncDrvVehicleLicense(Long drvId,String vehicleLicense,String modifyUser);

  /**
  　* @description: 只限于图片转换功能使用，其它功能勿用
  　* <AUTHOR>
  　* @date 2023/8/23 14:59
  */
  List<DrvDriverPO> queryQunarImgList(List<Long> drvId,int pageNo,int pageSize);

  /**
   　* @description: 只限于图片转换功能使用，其它功能勿用
   　* <AUTHOR>
   　* @date 2023/8/23 14:59
   */
  int queryCountQunarImg(List<Long> drvId);

  int updateDrvImgQToC(DrvDriverPO drvDriverPO);

  /**
   * 已废弃的司机数
   * @param drvDO
   * @return
   */
  Integer countDiscardDrvList(QueryDrvDO drvDO);

  List<DrvDriverPO> queryDiscardDrvList(QueryDrvDO drvDO);

  List<Long> queryDrvIdByMarkPage(List<Long> drvIds,Integer temporaryDispatchMark);


  int updateTemporaryDispatchMark(List<Long> drvIds,Boolean active,String modifyUser);

  /**
  　* @description: 查询废弃的临派司机ID
  　* <AUTHOR>
  　* @date 2023/10/24 9:53
  */
  List<Long> queryDiscardTemDrv(String drvPhone);

  /**
   * 查询未废弃的临时派遣司机
   * @return
   */
  List<Long> queryTemporaryDispatchDriver(Timestamp nowDate);

  /**
   * 查询司机id
   * @param drvPhone
   * @return DrvDriverPO
   */
  List<DrvDriverPO> queryDriverId(List<String> drvPhone);

  int updatedDvAddPaiayAccount(Long drvId,String paiayAccount,String paiayEmail,String modifyUser);

  int countDrvAddrModCountNoEmpty();

  List<Long> queryDrvAddrModCountNoEmptyList(int pageNo,int pageSize);

  int initDrvAddrModCount(List<Long> drvIds,String addrModCount);

  int queryDrvUidEmptyCount(List<Long> drvIds,List<Long> cityids);

  List<DrvDriverPO> queryDrvUidEmptyList(List<Long> drvIds,List<Long> cityids,int pageNo,int pageSize);

  int updateDrvUid(Long drvId,String uid,String modifyUser, String ppmAccount, String qunarAccount);

  List<DrvDriverPO> queryDrvListByDriverIdFromAndPage(Long drvId, int pageNo, int pageSize);

  List<DrvDriverPO> queryDrvListByDriverIdFromAndPage(Long drvId,String beginDate, String endDate, int pageNo,int pageSize) throws SQLException;

  List<DrvDriverPO> queryRecentDateDrvListByDriverIdFromAndPage(int pageNo,int pageSize, Date date) throws SQLException;

  Map<Long, DrvDriverPO> getDrvDriverPoMap(List<Long> drvIds);

  /**
   * by供应商Id列表查询有效的正式司机ID， 这样做的好处是基于辅助索引就查到数据，不需要回表，提升性能
   * @param supplierIds 供应商ID列表
   * @return 有效的正式司机ID列表
   */
  List<Long> queryOfficialDriverIdBySupplierIds(List<Long> supplierIds);

  List<DrvDriverPO> getDrvDriverPoList(List<Long> drvIds);
  
  List<DrvDriverPO> queryFromByPage(long lastDriverId, int pageSize);
  
  long countAll();
}
