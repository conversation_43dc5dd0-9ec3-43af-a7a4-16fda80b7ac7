package com.ctrip.dcs.tms.transport.infrastructure.port.repository;


import java.sql.SQLException;
import java.util.List;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvFreezeRecordPO;

public interface DrvFreezeRecordRepository {

    Long insert(DrvFreezeRecordPO po) throws SQLException;

    int update(DrvFreezeRecordPO po) throws SQLException;

    List<DrvFreezeRecordPO> queryDrvFreezeRecordList(List<Long> drvList);
}
