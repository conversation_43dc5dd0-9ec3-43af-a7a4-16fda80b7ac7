package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.OverseasOcrRecordPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.OverseasOcrRecordRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.DalRepositoryImpl;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.KeyHolder;
import com.ctrip.platform.dal.dao.sqlbuilder.SelectSqlBuilder;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.sql.Types;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 　* @description: 境外OCR存储
 * 　* <AUTHOR>
 * 　* @date 2023/6/8 14:53
 */
@Repository
public class OverseasOcrRecordRepositoryImpl implements OverseasOcrRecordRepository {

    private static final Logger logger = LoggerFactory.getLogger(OverseasOcrRecordRepositoryImpl.class);

    private DalRepository<OverseasOcrRecordPO> overseasOcrRecordRepo;

    public OverseasOcrRecordRepositoryImpl() throws SQLException {
        overseasOcrRecordRepo = new DalRepositoryImpl<>(OverseasOcrRecordPO.class);
    }

    @Override
    public Long insert(OverseasOcrRecordPO recordPO) throws SQLException {
        KeyHolder keyHolder = new KeyHolder();
        overseasOcrRecordRepo.insert(new DalHints(), keyHolder, recordPO);
        return keyHolder.getKey().longValue();
    }

    @Override
    public OverseasOcrRecordPO queryOverseasOcrRecordList(Long cityId, Integer requestType, String requestImg) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.equal("request_city_id", cityId, Types.BIGINT);
            builder.and().equal("request_type", requestType, Types.INTEGER);
            builder.and().equal("request_img", requestImg, Types.VARCHAR);
            builder.and().greaterThanEquals("datachange_createtime", DateUtil.dateToDate(new Date(), DateUtil.YYYYMMDD), Types.TIMESTAMP);
            builder.orderBy("datachange_lasttime", Boolean.FALSE);
            return overseasOcrRecordRepo.getDao().queryFirst(builder, hints);
        } catch (Exception e) {
            logger.error("queryOverseasOcrRecordListError","Params - cityId:{} requestType:{} requestImg:{}", cityId, requestType, requestImg, e);
            return null;
        }
    }

}