package com.ctrip.dcs.tms.transport.infrastructure.port.repository;


import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.dal.*;

import java.sql.*;
import java.util.*;


public interface TmsTransportApproveRepository {
    TmsTransportApprovePO queryByPk(Long id);

    Long insertTmsTransportApprovePO(TmsTransportApprovePO approvePO) throws SQLException;

    int countQueryApproveList(QueryApproveListDO approveListDO);

    List<TmsTransportApprovePO> queryApproveList(QueryApproveListDO approveListDO);

    int updateApproveStatus(UpdateApproveStatusDO statusDO);

    Boolean checkIsApproveIng(Long approveSourceId,Integer approveSourceType);

    List<TmsTransportApprovePO> queryApproveListByIds(List<Long> ids);

    int updateCheckResult(Long id,String certificateCheckResult,String modifyUser);

    List<TmsTransportApprovePO> queryApproveBySourceId(Long approveSourceId,Integer approveSourceType,Integer approveStatus,Integer eventType);

    List<TmsTransportApprovePO> queryApproveIngBySourceId(List<Long> approveSourceIds,Integer approveSourceType,Integer approveStatus);

    TmsTransportApprovePO queryApproveRemarks(Long approveSourceId, Integer approveSourceType, Integer approveStatus, Integer eventType);

    int updateCheckResultAndFinishStatus(Long id,String certificateCheckResult,Integer approveSchedule,String modifyUser);

    int queryApproveCountByParams(Long approveSourceId, Integer approveSourceType, Integer approveStatus, Integer eventType);

    int countQueryApproveListV2(QueryApproveListDO approveListDO);

    List<TmsTransportApprovePO> queryApproveListV2(QueryApproveListDO approveListDO);

    /**
     * 查询临时派遣编辑审核记录数
     * @param approveSourceId
     * @param approveSourceType
     * @param eventType
     * @return
     */
    int queryTemporaryDispatchApproveCount(Long approveSourceId, Integer approveSourceType,Integer eventType);

    void update(TmsTransportApprovePO po);
}
