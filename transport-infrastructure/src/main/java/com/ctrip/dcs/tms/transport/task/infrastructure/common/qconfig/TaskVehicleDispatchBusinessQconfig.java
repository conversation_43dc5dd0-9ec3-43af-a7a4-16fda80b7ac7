package com.ctrip.dcs.tms.transport.task.infrastructure.common.qconfig;

import com.ctrip.dcs.tms.transport.task.infrastructure.value.VehicleDispatchPhotoTaskQConfigData;
import com.ctrip.dcs.tms.transport.task.infrastructure.value.VehicleDispatchPhotoTaskQConfigJSON;
import com.google.common.collect.Lists;
import lombok.Data;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.Feature;
import qunar.tc.qconfig.client.JsonConfig;
import qunar.tc.qconfig.client.spring.QMapConfig;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class TaskVehicleDispatchBusinessQconfig {

  private JsonConfig<VehicleDispatchPhotoTaskQConfigJSON> config = JsonConfig.get("task.vehicle.dispatch.photo.business.json", Feature.create().setFailOnNotExists(false).build(), VehicleDispatchPhotoTaskQConfigJSON.class);

  public Map<String, VehicleDispatchPhotoTaskQConfigData> getVehicleTaskConfig() {
    return config.current().getVehicle_dispatch_photo_config();
  }

  public List<Long> getVehicleDispatchGrayCityList() {
    return Optional.ofNullable(config.current().getVehicleDispatchGrayCityList()).orElse(Lists.newArrayList(-2L));
  }

}
