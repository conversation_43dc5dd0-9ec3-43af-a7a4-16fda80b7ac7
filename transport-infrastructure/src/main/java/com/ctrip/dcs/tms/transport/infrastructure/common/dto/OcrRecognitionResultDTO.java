package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

import java.util.List;

import lombok.Data;

@Data
public class OcrRecognitionResultDTO {
    /**
     * 位置id
     */
    private Long locationId;
    /**
     * 位置类型
     */
    private String locationType;
    /**
     * 图像类型
     */
    private String imgType;
    /**
     * 请求类型
     */
    private String reqType;
    /**
     * 响应
     */
    private List<OcrRespDTO> response;
}
