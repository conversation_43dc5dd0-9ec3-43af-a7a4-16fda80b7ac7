package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.CtripCommonUtils;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DriverGroupRelationRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.QueryDrvByMuSelDO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.QueryDrvDO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.DalRepositoryImpl;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.DalRowMapper;
import com.ctrip.platform.dal.dao.StatementParameters;
import com.ctrip.platform.dal.dao.helper.DalDefaultJpaMapper;
import com.ctrip.platform.dal.dao.sqlbuilder.FreeSelectSqlBuilder;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.sql.Types;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/3/17 12:25
 */
@Repository(value = "driverGroupRelationRepository")
public class DriverGroupRelationRepositoryImpl implements DriverGroupRelationRepository {

    private static final Logger logger = LoggerFactory.getLogger(DriverGroupRelationRepositoryImpl.class);

    private DalRepository<DriverGroupRelationPO> driverGroupRelationRepo;

    private DalRepository<TransportGroupBasePO> transportGroupBasePODalRepo;

    private DalRowMapper<DriverGroupRelationPO> DriverGroupRelationPORowMapper;

    private DalRowMapper<TransportGroupBasePO> transportGroupBasePODalRowMapper;

    public DriverGroupRelationRepositoryImpl() throws SQLException {
        this.DriverGroupRelationPORowMapper = new DalDefaultJpaMapper<>(DriverGroupRelationPO.class);
        this.transportGroupBasePODalRowMapper = new DalDefaultJpaMapper<>(TransportGroupBasePO.class);
        driverGroupRelationRepo = new DalRepositoryImpl<>(DriverGroupRelationPO.class);
        transportGroupBasePODalRepo = new DalRepositoryImpl<>(TransportGroupBasePO.class);
    }

    @Override
    public List<Long> queryActiveDrvIdByTransportIdListNew(List<Long> drvIdList,List<Long> groupIdList) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<Long>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("SELECT drv_id FROM tsp_transport_group_driver_relation WHERE transport_group_id IN (?) AND active = 1  ");
            StatementParameters parameters = new StatementParameters();
            parameters.setInParameter(1, "transport_group_id", Types.BIGINT, groupIdList);
            if (CollectionUtils.isNotEmpty(drvIdList)) {
                sqlStr.append("AND drv_id IN (?) ");
                parameters.setInParameter(2, "drv_id", Types.BIGINT, drvIdList);
            }
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(DriverGroupRelationPORowMapper);
            builder.simpleType().nullable();
            return driverGroupRelationRepo.getQueryDao().query(builder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryActiveDrvIdByTransportIdList error", e);
        }
    }

}
