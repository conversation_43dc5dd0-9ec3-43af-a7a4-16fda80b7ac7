package com.ctrip.dcs.tms.transport.infrastructure.port.repository.model;

import com.ctrip.igt.*;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2020/11/30 17:00
 */
public class QueryDriverRelationDetailParam {

    /**
     * 分页
     */
    public PaginatorDTO paginator;

    /**
     * 运力组id
     */
    public Long transportGroupId;

    /**
     * 司机姓名 like
     */
    public String drvName;

    /**
     * 司机手机号 like
     */
    public String drvPhone;

    /**
     * 城市Id
     */
    public List<Long> cityId;

    /**
     * 车型Id
     */
    public List<Long> vehicleTypeId;

    /**
     * 司机语言(多种语言逗号分隔)
     */
    public List<String> drvLanguage;

    /**
     * *是否包含关系 0 关联司机  1未关联司机 默认0
     */
    public Integer isHasRelation;

    /**
     * 合作模式(1.全职,2.兼职)
     */
    public Integer coopMode;

    /**
     * 状态(0未激活、1上线、2冻结、3下线)
     */
    public Integer drvStatus;

    /**
     * 供应商id列表
     */
    public List<Long> supplierIdList;

    /**
     * 报名状态（0：未报名，1：已报名，2：报名成功）
     */
    public Integer applyStatus;

    /**
     * 班次id
     */
    public Long workShiftId;

    /**
     * 产品逻辑：http://conf.ctripcorp.com/pages/viewpage.action?pageId=477552583
     * 全职（全职指派模式及全职指派-报名制模式）的接送机运力组，和包车运力组互斥。任何司机不可同时绑定二者
     * 如司机已绑定全职接送机运力组，包车运力组关联司机时，无法搜到该司机；
     * 如司机已绑定包车运力组，全职接送机运力组关联司机时，无法搜到该司机；
     */
    private List<Long> fullTimeRuleOutDrvIdList;

    /**
     * 运力组模式
     */
    private List<Integer> transportGroupModeList;

    /**
     * 产线
     */
    private List<Integer> productLines;

    /**
     * 判断是接送机产线
     */
    private boolean isJNTProductLine;
    private boolean ispTpProductLine;

    /**
     * 是否人工调度
     * */
    private boolean isHumanDispatch;

    //派遣供应商-当前运力组的所属供应商
    private Long dispatchSupplierId;

    public Long getDispatchSupplierId() {
        return dispatchSupplierId;
    }

    public void setDispatchSupplierId(Long dispatchSupplierId) {
        this.dispatchSupplierId = dispatchSupplierId;
    }

    public boolean isHumanDispatch() {
        return isHumanDispatch;
    }

    public void setHumanDispatch(boolean humanDispatch) {
        isHumanDispatch = humanDispatch;
    }

    public boolean isJNTProductLine() {
        return isJNTProductLine;
    }

    public void setJNTProductLine(boolean JNTProductLine) {
        isJNTProductLine = JNTProductLine;
    }

    public List<Integer> getTransportGroupModeList() {
        return transportGroupModeList;
    }

    public void setTransportGroupModeList(List<Integer> transportGroupModeList) {
        this.transportGroupModeList = transportGroupModeList;
    }

    public List<Integer> getProductLines() {
        return productLines;
    }

    public void setProductLines(List<Integer> productLines) {
        this.productLines = productLines;
    }

    public List<Long> getFullTimeRuleOutDrvIdList() {
        return fullTimeRuleOutDrvIdList;
    }

    public void setFullTimeRuleOutDrvIdList(List<Long> fullTimeRuleOutDrvIdList) {
        this.fullTimeRuleOutDrvIdList = fullTimeRuleOutDrvIdList;
    }

    public PaginatorDTO getPaginator() {
        return paginator;
    }

    public void setPaginator(PaginatorDTO paginator) {
        this.paginator = paginator;
    }

    public Long getTransportGroupId() {
        return transportGroupId;
    }

    public void setTransportGroupId(Long transportGroupId) {
        this.transportGroupId = transportGroupId;
    }

    public String getDrvName() {
        return drvName;
    }

    public void setDrvName(String drvName) {
        this.drvName = drvName;
    }

    public String getDrvPhone() {
        return drvPhone;
    }

    public void setDrvPhone(String drvPhone) {
        this.drvPhone = drvPhone;
    }

    public List<Long> getCityId() {
        return cityId;
    }

    public void setCityId(List<Long> cityId) {
        this.cityId = cityId;
    }

    public List<Long> getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(List<Long> vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public List<String> getDrvLanguage() {
        return drvLanguage;
    }

    public void setDrvLanguage(List<String> drvLanguage) {
        this.drvLanguage = drvLanguage;
    }

    public Integer getIsHasRelation() {
        return isHasRelation;
    }

    public void setIsHasRelation(Integer isHasRelation) {
        this.isHasRelation = isHasRelation;
    }

    public Integer getCoopMode() {
        return coopMode;
    }

    public void setCoopMode(Integer coopMode) {
        this.coopMode = coopMode;
    }

    public Integer getDrvStatus() {
        return drvStatus;
    }

    public void setDrvStatus(Integer drvStatus) {
        this.drvStatus = drvStatus;
    }

    public List<Long> getSupplierIdList() {
        return supplierIdList;
    }

    public void setSupplierIdList(List<Long> supplierIdList) {
        this.supplierIdList = supplierIdList;
    }

    public Integer getApplyStatus() {
        return applyStatus;
    }

    public void setApplyStatus(Integer applyStatus) {
        this.applyStatus = applyStatus;
    }

    public Long getWorkShiftId() {
        return workShiftId;
    }

    public void setWorkShiftId(Long workShiftId) {
        this.workShiftId = workShiftId;
    }

    public boolean isIspTpProductLine() {
        return ispTpProductLine;
    }

    public void setPTpProductLine(boolean ispTpProductLine) {
        this.ispTpProductLine = ispTpProductLine;
    }
}
