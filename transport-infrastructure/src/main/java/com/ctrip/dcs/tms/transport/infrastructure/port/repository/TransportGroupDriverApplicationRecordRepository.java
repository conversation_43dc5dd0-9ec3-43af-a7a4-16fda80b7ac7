package com.ctrip.dcs.tms.transport.infrastructure.port.repository;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TransportGroupDriverApplicationRecordPO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TransportGroupDriverApplicationRecordRepository {

  List<TransportGroupDriverApplicationRecordPO> queryByCondition(TransportGroupDriverApplicationRecordPO condition);

  void batchInsert(List<TransportGroupDriverApplicationRecordPO> recordList);

  void update(TransportGroupDriverApplicationRecordPO record);

  void updateHistoryData2Inactive(Long transportGroupId, Long workShiftId);

  void updateHistoryData2Inactive(Long transportGroupId, List<Long> drvIdList);
}
