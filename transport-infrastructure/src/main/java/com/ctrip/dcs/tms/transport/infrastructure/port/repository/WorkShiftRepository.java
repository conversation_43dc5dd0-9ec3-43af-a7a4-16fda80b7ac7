package com.ctrip.dcs.tms.transport.infrastructure.port.repository;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.dal.*;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2020/11/26 15:33
 */
public interface WorkShiftRepository {

    DalRepository<TspTransportGroupWorkShiftPO> getVehVehicleRepo();

    /**
     * 更新工作班次
     * @param workShiftPOList
     * @return
     */
    int updateWorkShift(List<TspTransportGroupWorkShiftPO> workShiftPOList);

    /**
     * 新增工作班次
     * @param workShiftPOList
     * @return
     */
    int addWorkShift(List<TspTransportGroupWorkShiftPO> workShiftPOList);

    /**
     * 查询工作班次信息
     * @param transportGroupId
     * @param active
     * @return
     */
    List<TspTransportGroupWorkShiftPO> queryWorkShifts(Long transportGroupId,Integer active);

    /**
     * 查询所有工作班次信息
     * @param supplierId
     * @param pointCityId
     * @param vehicleTypeId
     * @param transportGroupMode
     * @return
     */
    List<TspTransportGroupWorkShiftPO> queryWorkShifts(Long supplierId, Long pointCityId, Long vehicleTypeId, Integer transportGroupMode,Long workShiftId);

    /**
     * 查询运力组工作班次报名信息
     * @param transportGroupId
     * @return
     */
    List<WorkShiftApplyInfoPO> queryWorkShiftApplyInfo(Long transportGroupId);

    List<TspTransportGroupWorkShiftPO> queryWorkShiftList(List<Long> workShiftIds);

}
