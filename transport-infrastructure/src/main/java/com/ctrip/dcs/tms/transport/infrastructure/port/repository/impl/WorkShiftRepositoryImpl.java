package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.helper.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.ctriposs.baiji.exception.*;
import org.apache.commons.collections.*;
import org.springframework.stereotype.*;

import java.sql.*;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2020/11/26 15:35
 */
@Repository(value = "workShiftRepository")
public class WorkShiftRepositoryImpl implements WorkShiftRepository {

    private DalRepository<TspTransportGroupWorkShiftPO> workShiftRepo;
    private DalRowMapper<WorkShiftApplyInfoPO> workShiftApplyInfoPORowMapper;
    private DalRowMapper<TspTransportGroupWorkShiftPO> workShiftPORowMapper;

    public WorkShiftRepositoryImpl() throws SQLException {
        workShiftRepo = new DalRepositoryImpl<>(TspTransportGroupWorkShiftPO.class);
        this.workShiftApplyInfoPORowMapper = new DalDefaultJpaMapper<>(WorkShiftApplyInfoPO.class);
        this.workShiftPORowMapper = new DalDefaultJpaMapper<>(TspTransportGroupWorkShiftPO.class);

    }

    @Override
    public DalRepository<TspTransportGroupWorkShiftPO> getVehVehicleRepo() {
        return workShiftRepo;
    }

    @Override
    public int updateWorkShift(List<TspTransportGroupWorkShiftPO> workShiftPOList) {
        try {
            if (CollectionUtils.isEmpty(workShiftPOList)) {
                return 0;
            }
            DalHints hints = DalHints.createIfAbsent(null);
            return workShiftRepo.getDao().batchUpdate(hints, workShiftPOList).length;
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int addWorkShift(List<TspTransportGroupWorkShiftPO> workShiftPOList) {
        try {
            if (CollectionUtils.isEmpty(workShiftPOList)) {
                return 0;
            }
            DalHints hints = DalHints.createIfAbsent(null);
            return workShiftRepo.getDao().combinedInsert(hints, workShiftPOList);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TspTransportGroupWorkShiftPO> queryWorkShifts(Long transportGroupId, Integer active) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);

            SelectSqlBuilder sqlBuilder = new SelectSqlBuilder();
            sqlBuilder.selectAll();
            sqlBuilder.equal("transport_group_id",transportGroupId, Types.BIGINT,false);
            if (active != null) {
                sqlBuilder.and().equal("active", active,Types.INTEGER,false);
            }
            return workShiftRepo.getDao().query(sqlBuilder,hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TspTransportGroupWorkShiftPO> queryWorkShifts(Long supplierId, Long pointCityId, Long vehicleTypeId, Integer transportGroupMode,Long workShiftId) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<TspTransportGroupWorkShiftPO>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            sqlStr.append(" select ws.* from tsp_transport_group_work_shift ws " +
                    "inner join tsp_transport_group tg on ws.transport_group_id = tg.transport_group_id " +
                    "and tg.group_status = 0 " +
                    "and tg.supplier_id = ? " +
                    "and tg.point_city_id = ? " +
                    "and tg.vehicle_type_id = ? " +
                    "and tg.transport_group_mode = ? " +
                    "where ws.active = 1 ");
            parameters.set(i++, "tg.supplier_id", Types.BIGINT, supplierId);
            parameters.set(i++, "tg.point_city_id", Types.BIGINT, pointCityId);
            parameters.set(i++, "tg.vehicle_type_id", Types.BIGINT, vehicleTypeId);
            parameters.set(i++, "tg.transport_group_mode", Types.INTEGER, transportGroupMode);
            if (workShiftId!=null && workShiftId > 0) {
                sqlStr.append(" and ws.id = ? ");
                parameters.set(i++, "ws.id", Types.BIGINT, workShiftId);
            }
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(workShiftPORowMapper);
            return workShiftRepo.getQueryDao().query(builder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryWorkShifts error", e);
        }
    }

    @Override
    public List<WorkShiftApplyInfoPO> queryWorkShiftApplyInfo(Long transportGroupId) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<WorkShiftApplyInfoPO>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            sqlStr.append("select ws.id as work_shift_id,ws.transport_group_id,ws.work_shift_order,ws.start_time,ws.end_time,ws.driver_upper_limit,count(1) as driverBindedNum,sum(if(dr.apply_status=2,1,0)) as driverAppliedSuccessNum,sum(if(dr.apply_status=2,if(d.drv_status=2 or dl.drv_id is not null,0,1),0)) as driverAppliedSuccessValidNum from  tsp_transport_group_work_shift ws " +
                    "inner join tsp_transport_group_driver_relation dr on ws.id  = dr.work_shift_id and ws.transport_group_id = dr.transport_group_id and dr.active = 1 "+
                    "inner join drv_driver d on dr.drv_id = d.drv_id and d.active = 1 " +
                    "left join drv_driver_leave dl on d.drv_id = dl.drv_id and dl.leave_begin_time < now() and dl.leave_end_time > now() and dl.active=1 "+
                    "where ws.active = 1 and ws.transport_group_id = ? group by ws.id");
            parameters.set(i++, "ws.transport_group_id", Types.BIGINT, transportGroupId);
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(workShiftApplyInfoPORowMapper);
            return workShiftRepo.getQueryDao().query(builder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryWorkShiftApplyInfo error", e);
        }
    }

    @Override
    public List<TspTransportGroupWorkShiftPO> queryWorkShiftList(List<Long> workShiftIds) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder sqlBuilder = new SelectSqlBuilder();
            sqlBuilder.selectAll();
            sqlBuilder.in("id",workShiftIds, Types.BIGINT,false);
            sqlBuilder.and().equal("active", 1,Types.INTEGER,false);
            return workShiftRepo.getDao().query(sqlBuilder,hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }
}
