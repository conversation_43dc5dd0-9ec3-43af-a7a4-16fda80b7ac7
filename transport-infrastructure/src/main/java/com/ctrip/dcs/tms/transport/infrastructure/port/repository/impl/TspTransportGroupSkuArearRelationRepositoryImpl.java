package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.helper.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.ctriposs.baiji.exception.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.springframework.stereotype.*;

import java.sql.*;
import java.util.*;

@Repository
public class TspTransportGroupSkuArearRelationRepositoryImpl implements TspTransportGroupSkuArearRelationRepository {

    private DalQueryDao queryDao;
    private static final String DEFAULT_TABLENAME = "tsp_transport_group_sku_area_relation";
    private static final String DATA_BASE = "dcstransportdb_w";
    private DalTableDao<TspTransportGroupSkuAreaRelationPO> client;

    public TspTransportGroupSkuArearRelationRepositoryImpl() throws SQLException {
        this.queryDao = new DalQueryDao(DATA_BASE);
        this.client = new DalTableDao(new DalDefaultJpaParser<>(TspTransportGroupSkuAreaRelationPO.class));
    }

    @Override
    public List<TspTransportGroupSkuAreaRelationPO> queryTransportGroupSkuInfoList(Long transportGroupId) {
        try{
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("transport_group_id",transportGroupId,Types.BIGINT);
            builder.and();
            builder.equal("active",Boolean.TRUE,Types.BIT);
            return client.query(builder, hints);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<TspTransportGroupSkuAreaRelationPO> queryTransportGroupSkuInfos(QueryTransportGroupSkuInfoModel queryModel) {
        try{
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            if (queryModel.getActive() != null) {
                builder.and().equal("active",queryModel.getActive(),Types.BIT);
            }
            if (queryModel.getServiceAreaType() != null) {
                builder.and().equal("service_area_type",queryModel.getServiceAreaType(),Types.TINYINT);
            }
            if (CollectionUtils.isNotEmpty(queryModel.getTransportGroupIds())) {
                builder.and().in("transport_group_id",queryModel.getTransportGroupIds(),Types.BIGINT);
            }
            if (queryModel.getSkuId() != null) {
                builder.and().equal("sku_id",queryModel.getSkuId(),Types.BIGINT);
            }
            if (CollectionUtils.isNotEmpty(queryModel.getServiceAreaIds())) {
                builder.and().in("service_area_id",queryModel.getServiceAreaIds(),Types.BIGINT);
            }
            return client.query(builder, hints);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<Long> querySkuBindInfo(List<Long> skuIds,Integer groupStatus) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<Long>> sqlBuilder = new FreeSelectSqlBuilder<>();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("select r.sku_id from tsp_transport_group_sku_area_relation r inner join tsp_transport_group g on r.transport_group_id = g.transport_group_id");
            if (groupStatus != null) {
                sqlStr.append(" and  g.group_status = ? ");
                parameters.set(i++,"g.group_status", Types.SMALLINT,groupStatus);
            }

            sqlStr.append(" where r.active = ? ");
            parameters.set(i++,"r.active", Types.BIT,true);

            if (CollectionUtils.isNotEmpty(skuIds)) {
                sqlStr.append(" and r.sku_id in (?)");
                parameters.setInParameter(i++,"r.sku_id", Types.BIGINT,skuIds);
            }
            sqlStr.append(" group by r.sku_id");
            sqlBuilder.setTemplate(sqlStr.toString());
            sqlBuilder.mapWith(List.class);
            return queryDao.query(sqlBuilder,parameters, hints);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<TspTransportGroupSkuAreaRelationPO> queryTransportGroupSkuInfoListBySkuIdList(List<Long> skuIdList) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("select sar.* from tsp_transport_group_sku_area_relation sar inner join  tsp_transport_group tg  on sar.transport_group_id = tg.transport_group_id and tg.group_status = 0 where sar.active = 1 and sar.sku_id in (?)");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setInParameter(i++,"sar.sku_id",Types.BIGINT, skuIdList);
            return queryDao.query(sqlBuilder.toString(), parameters, hints, TspTransportGroupSkuAreaRelationPO.class);
        }catch (Exception e){
            throw new RuntimeException(e);
        }

    }

    @Override
    public List<TspTransportGroupSkuAreaRelationPO> queryTransportGroupSkuIds(QueryTransportGroupSkuInfoModel queryModel) {
        try{
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            if (queryModel.getActive() != null) {
                builder.and().equal("active",queryModel.getActive(),Types.BIT);
            }
            if (queryModel.getServiceAreaType() != null) {
                builder.and().equal("service_area_type",queryModel.getServiceAreaType(),Types.TINYINT);
            }
            if (CollectionUtils.isNotEmpty(queryModel.getTransportGroupIds())) {
                builder.and().in("transport_group_id",queryModel.getTransportGroupIds(),Types.BIGINT);
            }
            if (CollectionUtils.isNotEmpty(queryModel.getSkuIds())) {
                builder.and().in("sku_id",queryModel.getSkuIds(),Types.BIGINT);
            }
            if (CollectionUtils.isNotEmpty(queryModel.getServiceAreaIds())) {
                builder.and().in("service_area_id",queryModel.getServiceAreaIds(),Types.BIGINT);
            }
            return client.query(builder, hints);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

}