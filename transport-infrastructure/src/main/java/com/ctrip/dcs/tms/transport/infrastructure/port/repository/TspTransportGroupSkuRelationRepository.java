package com.ctrip.dcs.tms.transport.infrastructure.port.repository;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.dal.*;

import java.util.*;

public interface TspTransportGroupSkuRelationRepository {

    DalRepository<TspTransportGroupSkuAreaRelationPO> getTspTransportGroupDriverRelationRepo();

    /**
     * 批量绑定关系
     * */
    int insetBatch(Long transportGroupId, List<Long> skuInfoList, String operator);

    /**
     * 批量解绑关系
     * */
    int updateRelationStatus(Long transportGroupId, List<Long> skuInfoList, String operator);

    /**
     * 查询已绑定skuid
     * @param transportGroupId
     * @return
     */
    List<Long> queryBindSkuId(Long transportGroupId,Boolean active);

    List<TspTransportGroupSkuAreaRelationPO> querySkuRelationList(List<Long> transportGroupIds,Boolean active);

    List<TspTransportGroupSkuAreaRelationPO> querySkuRelationListBySkuIds(List<Long> skuIds);

}