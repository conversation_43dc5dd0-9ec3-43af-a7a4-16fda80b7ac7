package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.ctriposs.baiji.exception.*;
import org.apache.commons.collections.*;
import org.springframework.stereotype.*;

import java.sql.*;
import java.util.*;


@Repository(value = "drvHealthPunchRepository")
public class DrvHealthPunchRepositoryImpl implements DrvHealthPunchRepository {

    private DalRepository<DrvHealthPunchPO> drvHealthPunchRepo;

    public DrvHealthPunchRepositoryImpl() throws SQLException {
        drvHealthPunchRepo = new DalRepositoryImpl<>(DrvHealthPunchPO.class);

    }

    @Override
    public long insert(DrvHealthPunchPO drvHealthPunchPO) throws SQLException {
        KeyHolder keyHolder = new KeyHolder();
        drvHealthPunchRepo.insert(new DalHints(), keyHolder, drvHealthPunchPO);
        return keyHolder.getKey().longValue();
    }
}
