package com.ctrip.dcs.tms.transport.infrastructure.port.repository.model;

import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;

/**
*
* <AUTHOR>
* @date 2020/4/21 14:51
*/
public class DrvVehRecruitingInsertModRrdDTO {

    private Long drvRecruitingId;
    private Integer approverStatus;
    private String remark;
    private String modifyUser;
    private Integer recruitingType;
    private Integer approverAperation;

    public DrvVehRecruitingInsertModRrdDTO(){}

    public DrvVehRecruitingInsertModRrdDTO(Long drvRecruitingId, Integer approverStatus, String remark, String modifyUser) {
        this.drvRecruitingId = drvRecruitingId;
        this.approverStatus = approverStatus;
        this.remark = remark;
        this.modifyUser = modifyUser;
    }

    public Long getDrvRecruitingId() {
        return drvRecruitingId;
    }

    public void setDrvRecruitingId(Long drvRecruitingId) {
        this.drvRecruitingId = drvRecruitingId;
    }

    public Integer getApproverStatus() {
        return approverStatus;
    }

    public void setApproverStatus(Integer approverStatus) {
        this.approverStatus = approverStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public Integer getRecruitingType() {
        return recruitingType;
    }

    public void setRecruitingType(Integer recruitingType) {
        this.recruitingType = recruitingType;
    }

    public Integer getApproverAperation() {
        return approverAperation;
    }

    public void setApproverAperation(Integer approverAperation) {
        this.approverAperation = approverAperation;
    }

    public static DrvVehRecruitingInsertModRrdDTO buildDTO(Long drvRecruitingId, Integer approverStatus, String remark, String modifyUser, Integer recruitingType){
        DrvVehRecruitingInsertModRrdDTO recruitingInsertModRrdDTO = new DrvVehRecruitingInsertModRrdDTO();
        recruitingInsertModRrdDTO.setDrvRecruitingId(drvRecruitingId);
        recruitingInsertModRrdDTO.setApproverStatus(approverStatus);
        recruitingInsertModRrdDTO.setRemark(remark);
        recruitingInsertModRrdDTO.setModifyUser(modifyUser);
        recruitingInsertModRrdDTO.setRecruitingType(recruitingType);
        recruitingInsertModRrdDTO.setApproverAperation(TmsTransportConstant.ApproverAperationEnum.H5_register_submit.getCode());
        return recruitingInsertModRrdDTO;
    }
}
