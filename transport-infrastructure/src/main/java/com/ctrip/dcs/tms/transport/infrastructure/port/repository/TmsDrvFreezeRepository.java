package com.ctrip.dcs.tms.transport.infrastructure.port.repository;


import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.dal.*;

import java.util.*;

public interface TmsDrvFreezeRepository {

    DalRepository<TmsDrvFreezePO> getTmsDrvFreezeRepo();

    TmsDrvFreezePO queryByPk(Long drvId);

    Integer addDrvFreeze(TmsDrvFreezePO doo);

    Integer updateDrvFreeze(TmsDrvFreezePO freezePO);

    List<TmsDrvFreezePO> queryDrvFreezeByDrvIds(Set<Long> drvIds);

    Integer drvUnFreeze(Long drvId ,String unfreezeReason,String modifyUser);

    Integer batchInsertDrvFreeze(List<TmsDrvFreezePO> list);

    Integer batchUpdateDrvFreeze(List<TmsDrvFreezePO> list);

    List<TmsDrvFreezePO> queryDrvFreezeAll();

    Integer batchUnFreezeByDrvIds(List<Long> drvids,String modifyUser);

    List<TmsDrvFreezePO> queryPenaltyFreezeDrvIds(List<Long> drvIds);

    Integer drvUnfreezeConfirmOnline(List<Long> drvids,String modifyUser,Boolean confirmOnlineStatus);

}