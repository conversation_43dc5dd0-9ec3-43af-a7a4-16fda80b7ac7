package com.ctrip.dcs.tms.transport.infrastructure.port.repository;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.dal.*;

import java.sql.*;
import java.util.*;

public interface VehicleRepository {

    DalRepository<VehVehiclePO> getVehVehicleRepo();

    VehVehiclePO  queryByPk(Long vehicleId);

    /***
     * 添加车辆
     * */
    Long addVehicle(VehVehiclePO vehVehiclePO);

    /***
     * 修改车辆
     * */
    int updateVehicle(VehVehiclePO vehVehiclePO);

    /***
     * 批量修改车辆
     * */
    int batchUpdateVehicle(List<VehVehiclePO> vehVehicleList);

    /***
     * 车辆详情
     * */
    VehicleDetailDTO queryVehicleDetail(Long vehicleId);

    /***
     * 车辆列表
     * */
    List<VehicleListSOADTO> queryVehicleList(QueryVehicleSOARequestType queryVehicleRequestType, List<Integer> productionLineIdList);

    /***
     * 车辆列表条数
     * */
    int queryVehicleCount(QueryVehicleSOARequestType queryVehicleRequestType, List<Integer> productionLineIdList);

    /**
     * 车牌号校验
     */
    boolean isVehicleLicenseUniqueness(Long vehicleId, String vehicleLicense);

    /**
     * 车辆是否可用
     *
     * @param vehicleId
     * @return
     * @desc 如果车辆不存在、已绑定状态、供应商不同  标识为车辆不可用
     */
    Boolean checkVehAvailAndHasNoDrv(Long vehicleId, Long supplierId);

    /**
     * 修改车辆绑定状态
     *
     * @param Vehicle
     * @return
     */
    int updateVehicleHasDrv(Long Vehicle, Integer hasDrv, String modifyUser) throws SQLException;


    /**
     * 通过车辆ID查询车辆信息
     *
     * @param vehVehicleIds
     * @return
     */
    List<VehVehiclePO> queryVehVehicleByIds(List<Long> vehVehicleIds) throws SQLException;

    /**
     * 通过供应商id搜索
     */
    List<VehVehiclePO> queryVehVehicleBySupplierId(Long supplierId, Integer hasDrv, Long cityId, List<Integer> productionLineIdList) throws SQLException;

    /**
     * 车辆状态变更
     *
     * @param vehVehicleIds
     * @param vehicleStatus
     * @return
     */
    int updateVehicleStatus(List<Long> vehVehicleIds, Integer vehicleStatus, String modifyUser) throws SQLException;

    /**
     * 校验车辆唯一性
     *
     * @param str
     * @param type
     * @return
     */
    Boolean checkVehOnly(String str, Integer type) throws SQLException;


    List<VehVehiclePO> queryVehVehicleBySupplierIdAndId(List<Long> vehVehicleIds, Long supplierId);

    int countAllVehVehicleByIds(List<Long> vehVehicleIds,List<Long> cityIds);

    List<VehVehiclePO> queryAllVehVehicleByIds(List<Long> vehVehicleIds,List<Long> cityIds,int beginNo,int pageSize) throws SQLException;

    List<VehVehiclePO> queryAllVehVehicleByIds(List<Long> vehVehicleIds,Integer vehicleStatus,int beginNo,int pageSize);

    int countAllVehVehicleByIds(List<Long> vehVehicleIds,Integer vehicleStatus);
    /**
     * 查询驾驶证初次领证日期不合法的车辆
     * @return
     */
    List<VehVehiclePO> queryIllegalCertiDateVehicle(List<Long> vehVehicleIds, int page, int pageSize);

    /**
     * 查询驾驶证初次领证日期不合法的车辆
     * @return
     */
    int countIllegalCertiDateVehicle(List<Long> vehVehicleIds);

    /**
     * 查询司机缓存车辆信息部分
     */
    List<VehVehiclePO> queryVehInfo4Cache(Set<Long> vehIdSet);

    /**
     * 根据车牌号查询车辆id
     * @param vehicleNo
     * @return
     */
    Long queryOnlineVehicleId(String vehicleNo);

    /**
     * 查询1000个车辆数据
     * @param supplierId
     * @return
     */
    List<VehVehiclePO> queryRandomVehicleId(Long supplierId,Long cityId,int page,int pageSize);

    List<VehVehiclePO> queryVehicleByPage(Long supplierId, List<Long> cityId, long id);
    /**
     * 根据条件查询车辆数量（用于分页）
     * @param supplierId
     * @param cityId
     * @return
     */
    int queryRandomVehicleIdCount(Long supplierId,Long cityId);

    /**
    　* @description: 车牌号查询车辆信息
    　* <AUTHOR>
    　* @date 2023/9/1 9:56
    */
    List<VehVehiclePO> queryVehByVehicleLicense(String vehicleLicense);

    int  queryCountQunarImgList(List<Long> vehicleIds);

    List<VehVehiclePO> queryQunarImgList(List<Long> vehicleIds,int pageNo,int pagesise);

    int updateDrvImgQToC(VehVehiclePO vehVehiclePO);

    int countDiscardVeh(QueryDiscardVehicleListSOARequestType requestType);

    List<VehVehiclePO> queryDiscardVeh(QueryDiscardVehicleListSOARequestType requestType);

    /**
    　* @description: 临派标识变更
    　* <AUTHOR>
    　* @date 2023/10/18 16:37
    */
    int updateTemporaryDispatchMark(List<Long> vehicleIds,Boolean active,String modifyUser);

    List<Long> queryDiscardTemVeh(String vehicleLicense);

    /**
     * 查询需要废弃的临时派遣车辆
     * @param nowDate
     * @return
     */
    List<Long> queryTemporaryDispatchVehicle(Timestamp nowDate);

    /**
     * 查询车辆id
     * @param vehicleLicense
     * @param supplierId
     * @return
     */
    List<Long> queryVehicleId(List<String> vehicleLicense, Long supplierId);
    /**
     * 通过车辆ID查询车辆信息
     *
     * @param vehicleIds
     * @return
     */
    List<VehVehiclePO> queryVehicleByIds(List<Long> vehicleIds);

    List<VehVehiclePO> queryVehicleListByVehicleIdFromAndPage(Long vehicleId,int pageNo,int pageSize) throws SQLException;

    int updateVehicleAgeType(Long vehicleId, String code, String system);

    int initVehicleAgeType(List<Long> vehicleIdList);

    List<VehVehiclePO> queryVehicleById(long id);

    /**
     * 车辆ID更新下线状态的车辆车型ID
     * @param vehVehiclePO
     * @return
     */
    boolean updateOfflineVehicleById(VehVehiclePO vehVehiclePO);

}
