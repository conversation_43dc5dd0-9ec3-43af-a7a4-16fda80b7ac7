package com.ctrip.dcs.tms.transport.infrastructure.port.repository;


import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;

import java.util.*;

public interface TmsBackgroundChecksRepository {

    int batchInsertCheckRecord(List<TmsBackgroundChecksPO> checksPOList);

    List<TmsBackgroundChecksPO> queryBackgroundByPerson(String personId);

    List<TmsBackgroundChecksPO> queryBackgroundByPersons(List<String> personId);


}