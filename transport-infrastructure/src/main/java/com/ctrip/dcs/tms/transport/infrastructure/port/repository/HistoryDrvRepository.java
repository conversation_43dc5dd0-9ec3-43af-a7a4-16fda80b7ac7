package com.ctrip.dcs.tms.transport.infrastructure.port.repository;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;

import java.sql.*;
import java.util.*;

public interface HistoryDrvRepository {
    /**
     * 对外提供历史数据
     */
    List<DrvHistoryDriverPO> queryHistoryDrvResourceByCondition(QueryHistoryDrvConditionDTO condition);

}
