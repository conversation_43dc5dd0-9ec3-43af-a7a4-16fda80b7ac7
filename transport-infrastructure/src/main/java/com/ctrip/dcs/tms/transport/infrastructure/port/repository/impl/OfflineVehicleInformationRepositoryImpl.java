package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.time.*;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.OfflineVehicleInformationPO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.OfflineVehicleInformationRepository;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.DalRepositoryImpl;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.sqlbuilder.SelectSqlBuilder;

@Repository(value = "offlineVehicleInformationRepositoryImpl")
public class OfflineVehicleInformationRepositoryImpl implements OfflineVehicleInformationRepository {

    private DalRepository<OfflineVehicleInformationPO> repository;

    public OfflineVehicleInformationRepositoryImpl() {
        this.repository = new DalRepositoryImpl<>(OfflineVehicleInformationPO.class);;
    }

    @Override
    public void saveOfflineVehicleInformation(List<OfflineVehicleInformationPO> List) {
        if (CollectionUtils.isEmpty(List)) {
            return;
        }
        repository.batchInsert(List);
    }

    @Override
    public List<OfflineVehicleInformationPO> queryofflineVehicleInformation(long id, LocalDate localDate) {
        try {
            LocalDateTime startTime = localDate.atStartOfDay();
            LocalDateTime endTime = localDate.atTime(LocalTime.MAX);
            ZonedDateTime startTimeDateTime = startTime.atZone(ZoneId.systemDefault());
            ZonedDateTime endTimeDateTime = endTime.atZone(ZoneId.systemDefault());
            Instant startInstant = startTimeDateTime.toInstant();
            Instant andInstant = endTimeDateTime.toInstant();

            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.greaterThan("id", id, Types.VARCHAR);
            builder.and();
            builder.greaterThan("send_date", Timestamp.from(startInstant), Types.TIMESTAMP);
            builder.and();
            builder.lessThan("send_date", Timestamp.from(andInstant), Types.TIMESTAMP);
            builder.orderBy("id", true);
            builder.atPage(1, 100);
            return repository.getDao().query(builder, hints);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }
}
