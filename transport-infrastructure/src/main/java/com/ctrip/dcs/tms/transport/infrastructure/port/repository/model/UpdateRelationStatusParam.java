package com.ctrip.dcs.tms.transport.infrastructure.port.repository.model;

import java.util.*;

/**
 * 解绑运力组司机关联
 * <AUTHOR>
 * @Date 2020/12/1 14:31
 */
public class UpdateRelationStatusParam {

    /**
     * 运力组id
     */
    private Long transportGroupId;
    /**
     * 司机id
     */
    private List<Long> drvIds;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 班次id
     */
    private Long workShiftId;

    public UpdateRelationStatusParam() {
    }

    public UpdateRelationStatusParam(Long transportGroupId, List<Long> drvIds, String operator, Long workShiftId) {
        this.transportGroupId = transportGroupId;
        this.drvIds = drvIds;
        this.operator = operator;
        this.workShiftId = workShiftId;
    }

    public Long getTransportGroupId() {
        return transportGroupId;
    }

    public void setTransportGroupId(Long transportGroupId) {
        this.transportGroupId = transportGroupId;
    }

    public List<Long> getDrvIds() {
        return drvIds;
    }

    public void setDrvIds(List<Long> drvIds) {
        this.drvIds = drvIds;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Long getWorkShiftId() {
        return workShiftId;
    }

    public void setWorkShiftId(Long workShiftId) {
        this.workShiftId = workShiftId;
    }
}
