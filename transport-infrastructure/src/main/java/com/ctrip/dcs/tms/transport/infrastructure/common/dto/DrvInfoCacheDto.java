package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

public class DrvInfoCacheDto {

    private Long drvId;

    private Integer coopMode;

    private Integer broadcast;

    private Integer isSendWorkPeriod;

    private Integer compatibleCoopMode;

    public DrvInfoCacheDto(){

    }

    public DrvInfoCacheDto(Long drvId,Integer coopMode,Integer broadcast,Integer isSendWorkPeriod,Integer compatibleCoopMode){
        this.drvId = drvId;
        this.coopMode = coopMode;
        this.broadcast = broadcast;
        this.isSendWorkPeriod = isSendWorkPeriod;
        this.compatibleCoopMode = compatibleCoopMode;
    }

    public static final DrvInfoCacheDto defaultDTO(){
        return new DrvInfoCacheDto(0L,0,0,0,2);
    }

    public Long getDrvId() {
        return drvId;
    }

    public void setDrvId(Long drvId) {
        this.drvId = drvId;
    }

    public Integer getCoopMode() {
        return coopMode;
    }

    public void setCoopMode(Integer coopMode) {
        this.coopMode = coopMode;
    }

    public Integer getBroadcast() {
        return broadcast;
    }

    public void setBroadcast(Integer broadcast) {
        this.broadcast = broadcast;
    }

    public Integer getIsSendWorkPeriod() {
        return isSendWorkPeriod;
    }

    public void setIsSendWorkPeriod(Integer isSendWorkPeriod) {
        this.isSendWorkPeriod = isSendWorkPeriod;
    }

    public Integer getCompatibleCoopMode() {
        return compatibleCoopMode;
    }

    public void setCompatibleCoopMode(Integer compatibleCoopMode) {
        this.compatibleCoopMode = compatibleCoopMode;
    }
}