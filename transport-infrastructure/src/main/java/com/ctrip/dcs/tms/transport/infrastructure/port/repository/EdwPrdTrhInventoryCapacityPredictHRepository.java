package com.ctrip.dcs.tms.transport.infrastructure.port.repository;


import com.ctrip.dcs.tms.transport.api.model.QueryEffCapacitySOARequestVO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.EdwPrdTrhInventoryCapacityPredictHPO;
import com.ctrip.igt.framework.dal.DalRepository;

import java.util.List;

public interface EdwPrdTrhInventoryCapacityPredictHRepository {

    List<EdwPrdTrhInventoryCapacityPredictHPO> queryCapacityPredictH(QueryEffCapacitySOARequestVO requestVO);

}