package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @create 2023/7/25 11:21
 */
public class DrvCacheConditionQuery {

    /**
     * 司机id列表
     */
    private List<Long> drvIdList;

    /**
     * 司机合作模式列表
     */
    private List<Integer> coopModeList;

    /**
     * 司机状态
     */
    private Integer status;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 司机电话
     */
    private String driverPhone;

    public List<Long> getDrvIdList() {
        return drvIdList;
    }

    public void setDrvIdList(List<Long> drvIdList) {
        this.drvIdList = drvIdList;
    }

    public List<Integer> getCoopModeList() {
        return coopModeList;
    }

    public void setCoopModeList(List<Integer> coopModeList) {
        this.coopModeList = coopModeList;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getDriverPhone() {
        return driverPhone;
    }

    public void setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone;
    }

}