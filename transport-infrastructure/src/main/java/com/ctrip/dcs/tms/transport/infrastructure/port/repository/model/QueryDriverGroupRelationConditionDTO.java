package com.ctrip.dcs.tms.transport.infrastructure.port.repository.model;

import java.util.List;

/**
 * 查询司机与运力组关系条件类
 */
public class QueryDriverGroupRelationConditionDTO {

    /**
     * 查询参数
     * */
    private final String[] RESOURCE_BASE = new String[]{"drv_id", "apply_status", "transport_group_id"};

    /**
     * 默认查询有效关系
     */
    private Boolean active = Boolean.TRUE;

    private List<Long> drvIdList;

    private List<Long> transportGroupIdList;

    public String[] getRESOURCE_BASE() {
        return RESOURCE_BASE;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public List<Long> getDrvIdList() {
        return drvIdList;
    }

    public void setDrvIdList(List<Long> drvIdList) {
        this.drvIdList = drvIdList;
    }

    public List<Long> getTransportGroupIdList() {
        return transportGroupIdList;
    }

    public void setTransportGroupIdList(List<Long> transportGroupIdList) {
        this.transportGroupIdList = transportGroupIdList;
    }

}