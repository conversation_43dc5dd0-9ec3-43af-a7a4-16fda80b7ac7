package com.ctrip.dcs.tms.transport.task.infrastructure.value;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class UploadVehicleDispatchPhotoList {
  public Long vehicleId;
  public String vehicleLicense;
  public Long vehicleTypeId;
  public Long drvId;
  public String drvName;
  public String businessId;
  private Long supplierId;
  private Long cityId;
  public List<VehicleDispatchPhoto> photoList;
}
