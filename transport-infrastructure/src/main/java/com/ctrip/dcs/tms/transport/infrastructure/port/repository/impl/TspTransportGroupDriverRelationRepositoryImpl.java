package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.monitoring.TransportMetric;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.thread.IThreadPoolService;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.wrapper.BatchRequestGatewayWrapper;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.helper.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.ctriposs.baiji.exception.*;
import com.google.common.base.*;
import com.google.common.collect.*;
import io.dropwizard.metrics5.ResetTimer;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.*;
import org.springframework.util.*;

import javax.annotation.Resource;
import java.sql.*;
import java.util.Date;
import java.util.Objects;
import java.util.*;

@Repository
public class TspTransportGroupDriverRelationRepositoryImpl implements TspTransportGroupDriverRelationRepository {

    private static final Logger logger = LoggerFactory.getLogger(TspTransportGroupDriverRelationRepositoryImpl.class);

    private DalRepository<TspTransportGroupDriverRelationPO> tspTransportGroupDriverRelationRepo;
    private DalRowMapper<TspTransportGroupDriverRelationPO> tspTransportGroupDriverRelationPORowMapper;
    private DalRowMapper<DriverRelationDetailPO> driverRelationDetailPORowMapper;
    private DalRowMapper<ApplyDriverRelationDetailPO> applyDriverRelationDetailPORowMapper;

    @Autowired
    private TmsTransportQconfig tmsTransportQconfig;

    @Resource
    private DrvDrvierRepository drvDrvierRepository;

    @Resource
    IThreadPoolService threadPoolService;

    @Autowired
    private TransportGroupRepository transportGroupRepository;

    private static final String DEFAULT_TABLENAME = "tsp_transport_group_driver_relation";
    private static final String DATA_BASE = "dcstransportdb_w";

    public TspTransportGroupDriverRelationRepositoryImpl() throws SQLException {
        tspTransportGroupDriverRelationRepo = new DalRepositoryImpl<>(TspTransportGroupDriverRelationPO.class);
        this.tspTransportGroupDriverRelationPORowMapper = new DalDefaultJpaMapper<>(TspTransportGroupDriverRelationPO.class);
        this.driverRelationDetailPORowMapper = new DalDefaultJpaMapper<>(DriverRelationDetailPO.class);
        this.applyDriverRelationDetailPORowMapper = new DalDefaultJpaMapper<>(ApplyDriverRelationDetailPO.class);

    }

    @Override
    public int updateApplyStatus(List<Long> workShiftIds, Integer applyStatus, String modifyUser) {
        if (CollectionUtils.isEmpty(workShiftIds)) {
            return 0;
        }
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeUpdateSqlBuilder sqlBuilder = new FreeUpdateSqlBuilder();
            sqlBuilder.update(DEFAULT_TABLENAME)
                    .set("apply_status", "modify_user")
                    .where()
                    .equal("active")
                    .and()
                    .in("work_shift_id");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.set(i++, "apply_status", Types.INTEGER, applyStatus);
            parameters.set(i++, "modify_user", Types.VARCHAR, modifyUser);
            parameters.set(i++, "active", Types.BIT, true);
            parameters.setInParameter(i++, "work_shift_id", Types.BIGINT, workShiftIds);
            return tspTransportGroupDriverRelationRepo.getQueryDao().update(sqlBuilder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateApplyStatus(Long workShiftId, List<Long> drvIds, Integer applyStatus, String modifyUser) {
        if (CollectionUtils.isEmpty(drvIds)) {
            return 0;
        }
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeUpdateSqlBuilder sqlBuilder = new FreeUpdateSqlBuilder();
            sqlBuilder.update(DEFAULT_TABLENAME)
                    .set("apply_status", "modify_user")
                    .where()
                    .equal("active")
                    .and()
                    .equal("work_shift_id")
                    .and()
                    .in("drv_id");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.set(i++, "apply_status", Types.INTEGER, applyStatus);
            parameters.set(i++, "modify_user", Types.VARCHAR, modifyUser);
            parameters.set(i++, "active", Types.BIT, true);
            parameters.set(i++, "work_shift_id", Types.BIGINT, workShiftId);
            parameters.setInParameter(i++,"drv_id",Types.BIGINT,drvIds);
            return tspTransportGroupDriverRelationRepo.getQueryDao().update(sqlBuilder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<ApplyDriverRelationDetailPO> queryRelationDrvList(List<Long> workShiftIds,List<Integer> applyStatus) {
        try {
            if (CollectionUtils.isEmpty(workShiftIds)) {
                return Lists.newArrayList();
            }
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<ApplyDriverRelationDetailPO>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            sqlStr.append("select dr.id,dr.drv_id,dr.work_shift_id,dr.apply_status,dd.city_id,dd.vehicle_id,dd.drv_status,dd.supplier_id from tsp_transport_group_driver_relation dr " +
                    "left join drv_driver dd on dr.drv_id = dd.drv_id where dr.work_shift_id in ( ? ) ");
            parameters.setInParameter(i++, "dr.work_shift_id", Types.BIGINT, workShiftIds);
            if (CollectionUtils.isNotEmpty(applyStatus)) {
                sqlStr.append("and dr.apply_status in (?) ");
                parameters.setInParameter(i++, "dr.apply_status", Types.INTEGER, applyStatus);
            }
            sqlStr.append(" and dr.active = 1 and dd.active = 1 order by dr.datachange_createtime asc");
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(applyDriverRelationDetailPORowMapper);
            return tspTransportGroupDriverRelationRepo.getQueryDao().query(builder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryRelationDrvList error", e);
        }
    }

    @Override
    public List<DriverRelationDetailPO> queryRelationDrvList(QueryDriverRelationDetailParam param) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);

            FreeSelectSqlBuilder<List<DriverRelationDetailPO>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            sqlStr.append("SELECT driver.drv_id, driver.drv_name, driver.supplier_id, driver.drv_language, driver.city_id, driver.drv_phone, driver.vehicle_type_id, " +
                    "driver.drv_status, driver.drv_from, driver.coop_mode, tsp_group_drv.apply_status, tsp_group_drv.work_shift_id, driver.category_synthesize_code,driver.internal_scope " +
                    "FROM tsp_transport_group_driver_relation tsp_group_drv INNER JOIN drv_driver driver ON driver.drv_id = tsp_group_drv.drv_id ");
            i = commonPullAway(i, parameters, sqlStr, param);

            sqlStr.append("LEFT JOIN veh_vehicle veh ON driver.vehicle_id = veh.vehicle_id where 1=1 and tsp_group_drv.active = 1 and driver.active= 1 ");

            if (Objects.nonNull(param.getTransportGroupId())) {
                sqlStr.append("and tsp_group_drv.transport_group_id=? ");
                parameters.set(i++, "tsp_group_drv.transport_group_id", Types.BIGINT, param.getTransportGroupId());
            }
            if (Objects.nonNull(param.getApplyStatus())) {
                sqlStr.append("and tsp_group_drv.apply_status= ? ");
                parameters.set(i++, "tsp_group_drv.apply_status", Types.INTEGER, param.getApplyStatus());
            }
            if (Objects.nonNull(param.getWorkShiftId())) {
                sqlStr.append("and tsp_group_drv.work_shift_id = ? ");
                parameters.set(i++, "tsp_group_drv.work_shift_id", Types.BIGINT, param.getWorkShiftId());
            }
            sqlStr.append(productionLineLimitSQLStr(param.isJNTProductLine(), param.isIspTpProductLine(),param.isHumanDispatch(),param.getIsHasRelation()));
            sqlStr.append(" order by tsp_group_drv.datachange_lasttime desc ");
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(driverRelationDetailPORowMapper);
            if (Objects.nonNull(param.getPaginator())) {
                builder.atPage(param.getPaginator().getPageNo(),param.getPaginator().getPageSize());
            }
            return tspTransportGroupDriverRelationRepo.getQueryDao().query(builder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryRelationDrvList error", e);
        }
    }

    @Override
    public int queryRelationDrvCount(QueryDriverRelationDetailParam param) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);

            FreeSelectSqlBuilder<Long> builder = new FreeSelectSqlBuilder<>();
            builder.simpleType().requireSingle().nullable();
            StringBuilder sqlStr = new StringBuilder();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            sqlStr.append("select count(*) from tsp_transport_group_driver_relation tsp_group_drv inner join drv_driver driver on driver.drv_id = tsp_group_drv.drv_id ");
            i = commonPullAway(i, parameters, sqlStr, param);
            sqlStr.append(" LEFT JOIN veh_vehicle veh ON driver.vehicle_id = veh.vehicle_id ");

            sqlStr.append(" where 1=1 and tsp_group_drv.active = 1 and driver.active= 1 ");

            if (Objects.nonNull(param.getTransportGroupId())) {
                sqlStr.append("and tsp_group_drv.transport_group_id=? ");
                parameters.set(i++, "tsp_group_drv.transport_group_id", Types.BIGINT, param.getTransportGroupId());
            }
            if (Objects.nonNull(param.getApplyStatus())) {
                sqlStr.append("and tsp_group_drv.apply_status= ? ");
                parameters.set(i++, "tsp_group_drv.apply_status", Types.INTEGER, param.getApplyStatus());
            }
            if (Objects.nonNull(param.getWorkShiftId())) {
                sqlStr.append("and tsp_group_drv.work_shift_id = ? ");
                parameters.set(i++, "tsp_group_drv.work_shift_id", Types.BIGINT, param.getWorkShiftId());
            }
            sqlStr.append(productionLineLimitSQLStr(param.isJNTProductLine(),param.isIspTpProductLine(), param.isHumanDispatch(), param.getIsHasRelation()));
            builder.setTemplate(sqlStr.toString());
            return tspTransportGroupDriverRelationRepo.getQueryDao().query(builder, parameters, hints).intValue();
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryRelationDrvCount error", e);
        }
    }

    @Override
    public List<DriverRelationDetailPO> queryNotRelationDrvList(QueryDriverRelationDetailParam param) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);

            FreeSelectSqlBuilder<List<DriverRelationDetailPO>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            sqlStr.append("select * from ( select driver.drv_id,driver.drv_name,driver.supplier_id,driver.drv_language,driver.city_id ,driver.drv_phone,driver.vehicle_type_id,driver.drv_status,driver.drv_from,driver.coop_mode," +
                    "tsp_group_drv.transport_group_id,tsp_group_drv.apply_status,tsp_group_drv.work_shift_id,driver.category_synthesize_code,driver.internal_scope  " +
                    "from drv_driver driver left join tsp_transport_group_driver_relation tsp_group_drv  on driver.drv_id = tsp_group_drv.drv_id and tsp_group_drv.active = 1 ");

            if (Objects.nonNull(param.getTransportGroupId())) {
                sqlStr.append("and tsp_group_drv.transport_group_id=? ");
                parameters.set(i++, "tsp_group_drv.transport_group_id", Types.BIGINT, param.getTransportGroupId());
            }
            if (Objects.nonNull(param.getWorkShiftId())) {
                sqlStr.append("and tsp_group_drv.work_shift_id = ? ");
                parameters.set(i++, "tsp_group_drv.work_shift_id", Types.BIGINT, param.getWorkShiftId());
            }
            sqlStr.append(" LEFT JOIN veh_vehicle veh ON driver.vehicle_id = veh.vehicle_id ");
            sqlStr.append(" where 1=1  and driver.active = 1 ");
            i = commonPullAway(i, parameters, sqlStr, param);
            sqlStr.append(productionLineLimitSQLStr(param.isJNTProductLine(),param.isIspTpProductLine(), param.isHumanDispatch(), param.getIsHasRelation()));
            sqlStr.append(" and (tsp_group_drv.apply_status=0 or tsp_group_drv.apply_status is null) order by driver.datachange_lasttime desc ) temp where temp.transport_group_id is null");
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(driverRelationDetailPORowMapper);
            if (Objects.nonNull(param.getPaginator())) {
                builder.atPage(param.getPaginator().getPageNo(),param.getPaginator().getPageSize());
            }
            return tspTransportGroupDriverRelationRepo.getQueryDao().query(builder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryNotRelationDrvList error", e);
        }
    }

    @Override
    public int queryNotRelationDrvCount(QueryDriverRelationDetailParam param) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);

            FreeSelectSqlBuilder<Long> builder = new FreeSelectSqlBuilder<>();
            builder.simpleType().requireSingle().nullable();
            StringBuilder sqlStr = new StringBuilder();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            sqlStr.append("select count(1) from ( select driver.drv_id,driver.drv_name,driver.supplier_id,driver.drv_language,driver.city_id ,driver.drv_phone,driver.vehicle_type_id,driver.drv_status,driver.drv_from,driver.coop_mode," +
                    "tsp_group_drv.transport_group_id,tsp_group_drv.apply_status,tsp_group_drv.work_shift_id " +
                    "from drv_driver driver left join tsp_transport_group_driver_relation tsp_group_drv  on driver.drv_id = tsp_group_drv.drv_id and tsp_group_drv.active = 1 ");
            if (Objects.nonNull(param.getTransportGroupId())) {
                sqlStr.append("and tsp_group_drv.transport_group_id=? ");
                parameters.set(i++, "tsp_group_drv.transport_group_id", Types.BIGINT, param.getTransportGroupId());
            }
            if (Objects.nonNull(param.getWorkShiftId())) {
                sqlStr.append("and tsp_group_drv.work_shift_id = ? ");
                parameters.set(i++, "tsp_group_drv.work_shift_id", Types.BIGINT, param.getWorkShiftId());
            }
            sqlStr.append(" LEFT JOIN veh_vehicle veh ON driver.vehicle_id = veh.vehicle_id ");
            sqlStr.append(" where 1=1 and driver.active = 1 ");
            i = commonPullAway(i, parameters, sqlStr, param);
            sqlStr.append(productionLineLimitSQLStr(param.isJNTProductLine(),param.isIspTpProductLine(), param.isHumanDispatch(), param.getIsHasRelation()));
            sqlStr.append(" and (tsp_group_drv.apply_status=0 or tsp_group_drv.apply_status is null)) temp where temp.transport_group_id is null");
            builder.setTemplate(sqlStr.toString());
            return tspTransportGroupDriverRelationRepo.getQueryDao().query(builder, parameters, hints).intValue();
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryNotRelationDrvCount error", e);
        }
    }

    @Override
    public int insetBatch(InsertBatchRelationParam param) {
        DalHints hints = DalHints.createIfAbsent(null);
        List<TspTransportGroupDriverRelationPO> relationPOS = Lists.newArrayListWithCapacity(param.getDrvIds().size());
        for (Long drvId : param.getDrvIds()) {
            TspTransportGroupDriverRelationPO po = new TspTransportGroupDriverRelationPO();
            po.setDrvId(drvId);
            po.setModifyUser(param.getOperator());
            po.setCreateUser(param.getOperator());
            po.setTransportGroupId(param.getTransportGroupId());
            if (Objects.nonNull(param.getWorkShiftId()) && 0 != param.getWorkShiftId()) {
                po.setWorkShiftId(param.getWorkShiftId());
                //默认已报名
                po.setApplyStatus(TmsTransportConstant.ApplyStatusEnum.APPLIED.getCode());
            }
            relationPOS.add(po);
        }
        int[] resultList = tspTransportGroupDriverRelationRepo.batchInsert(hints, relationPOS);
        if (resultList == null || resultList.length <= 0) {
            return 0;
        }
        return resultList.length;
    }

    @Override
    public int updateRelationStatus(UpdateRelationStatusParam param) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeUpdateSqlBuilder sqlBuilder = new FreeUpdateSqlBuilder();
            sqlBuilder.update(DEFAULT_TABLENAME).set("active","modify_user").where("1=1");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.set(i++, "active", Types.BIT, false);
            parameters.set(i++, "modify_user", Types.VARCHAR, param.getOperator());
            sqlBuilder.and().equal("transport_group_id");
            parameters.set(i++, "transport_group_id", Types.BIGINT, param.getTransportGroupId());
            if (Objects.nonNull(param.getWorkShiftId())) {
                sqlBuilder.and().equal("work_shift_id");
                parameters.set(i++, "work_shift_id", Types.BIGINT, param.getWorkShiftId());
            }
            sqlBuilder.and().in("drv_id");
            parameters.setInParameter(i++, "drv_id", Types.BIGINT, param.getDrvIds());
            return tspTransportGroupDriverRelationRepo.getQueryDao().update(sqlBuilder,parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<Long> queryRelationDrvIdListByTransportGroupId(Long transportGroupId) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("transport_group_id", transportGroupId, Types.BIGINT);
            builder.and();
            builder.equal("active", true, Types.BIT);
            List<TspTransportGroupDriverRelationPO> relationPOList = tspTransportGroupDriverRelationRepo.getDao().query(builder, hints);
            return getDrvIdList(relationPOList);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryRelationDrvIdListByTransportGroupId error", e);
        }
    }

    @Override
    public List<TspTransportGroupDriverRelationPO> queryTransportGroupIdByDrvIds(List<Long> drvIds) {
        try{
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("drv_id", drvIds, Types.BIGINT);
            builder.and();
            builder.equal("active", true, Types.BIT);
            return tspTransportGroupDriverRelationRepo.getDao().query(builder, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException("queryTransportGroupIdByDrvIds error", e);
        }
    }

    @Override
    public List<TspTransportGroupDriverRelationPO> queryTransportGroupDriverRelation(List<Long> drvIds) {
        try{
            DalHints hints = DalHints.createIfAbsent(null);

            FreeSelectSqlBuilder<List<TspTransportGroupDriverRelationPO>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            sqlStr.append("select r.* from tsp_transport_group_driver_relation r inner join tsp_transport_group g on g.transport_group_id = r.transport_group_id and g.group_status = 0 where r.drv_id in (?) and r.active = 1");
            parameters.setInParameter(i++,"driver.drv_id", Types.BIGINT,drvIds);
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(tspTransportGroupDriverRelationPORowMapper);

            return tspTransportGroupDriverRelationRepo.getQueryDao().query(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException("queryTransportGroupDriverRelation error", e);
        }
    }


    @Override
    public int unFreezeTransportGroup(List<Long> drvId, List<Long> transportGroupIds, String operator) {
        if(CollectionUtils.isEmpty(transportGroupIds)){
            return 0;
        }
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeUpdateSqlBuilder sqlBuilder = new FreeUpdateSqlBuilder();
            sqlBuilder.update(DEFAULT_TABLENAME).set("active","modify_user","datachange_lasttime","apply_status").where("1=1");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.set(i++, "active", Types.BIT, false);
            parameters.set(i++, "modify_user", Types.VARCHAR, operator);
            parameters.set(i++, "datachange_deltime", Types.TIMESTAMP, new Timestamp(new Date().getTime()));
            parameters.set(i++, "apply_status", Types.INTEGER, 0);
            sqlBuilder.and().in("drv_id");
            parameters.setInParameter(i++, "drv_id", Types.BIGINT, drvId);
            sqlBuilder.and().in("transport_group_id");
            parameters.setInParameter(i++, "transport_group_id", Types.BIGINT, transportGroupIds);
            return tspTransportGroupDriverRelationRepo.getQueryDao().update(sqlBuilder,parameters, hints);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<TspTransportGroupDriverRelationPO> queryRelationListByTransportGroupId(Long transportGroupId) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("transport_group_id", transportGroupId, Types.BIGINT);
            builder.and();
            builder.equal("active", true, Types.BIT);
            return tspTransportGroupDriverRelationRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryRelationDrvIdListByTransportGroupId error", e);
        }
    }

    @Override
    public int updateDriverRelationApplyStatus(Long transportGroupId,String modifyUser,Integer applyStatus) {
        if(transportGroupId == null || transportGroupId <=0 || applyStatus==null){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        try {
            builder.setTemplate("update tsp_transport_group_driver_relation set apply_status = ?,modify_user = ? where transport_group_id = ? and active = 1");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "apply_status", Types.INTEGER, applyStatus);
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, StringUtils.isEmpty(modifyUser)?TmsTransportConstant.TMS_DEFAULT_USERNAME:modifyUser);
            parameters.setSensitive(i++, "transport_group_id", Types.BIGINT, transportGroupId);
            return tspTransportGroupDriverRelationRepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException("queryRelationDrvIdListByTransportGroupId error", e);
        }
    }

    @Override
    public List<TspTransportGroupDriverRelationPO> queryRelationListByWorkShiftIds(List<Long> workIds) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("work_shift_id", workIds, Types.BIGINT);
            builder.and();
            builder.equal("active", true, Types.BIT);
            builder.and().equal("apply_status", TmsTransportConstant.ApplyStatusEnum.SUCCESS.getCode(),Types.INTEGER);
            return tspTransportGroupDriverRelationRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryRelationListByWorkShiftIds error", e);
        }
    }

    @Override
    public List<Long> queryRelationDrvListByTransportGroupIdAndDrvIdList(Long transportGroupId, List<Long> drvIdList, Long workShiftId) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<Long>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("SELECT drv_id FROM tsp_transport_group_driver_relation WHERE ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            sqlStr.append("transport_group_id = ? ");
            parameters.set(i++, "transport_group_id", Types.BIGINT,transportGroupId);
            sqlStr.append("AND drv_id IN (?) ");
            parameters.setInParameter(i++, "drv_id", Types.BIGINT,drvIdList);
            sqlStr.append("AND active = 1 AND work_shift_id = ?");
            parameters.set(i++, "work_shift_id", Types.BIGINT, (workShiftId == null) ? 0 : workShiftId);
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(tspTransportGroupDriverRelationPORowMapper);
            builder.simpleType().nullable();
            return tspTransportGroupDriverRelationRepo.getQueryDao().query(builder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryRelationDrvListByTransportGroupIdAndDrvIdList error", e);
        }
    }

    @Override
    public List<TspTransportGroupDriverRelationPO> queryRelationListByTransportGroupIds(List<Long> transportGroupIds,Long workId) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("transport_group_id", transportGroupIds, Types.BIGINT);
            builder.and();
            builder.equal("active", true, Types.BIT);
            if(workId!=null && workId >0 ){
                builder.and();
                builder.equal("work_shift_id", workId, Types.BIGINT);
            }
            return tspTransportGroupDriverRelationRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryRelationDrvIdListByTransportGroupId error", e);
        }
    }

    @Override
    public List<Long> queryValidRelationTransportGroupId() {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<Long>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("SELECT DISTINCT tsp_group_drv.transport_group_id FROM tsp_transport_group_driver_relation driver LEFT JOIN tsp_transport_group tsp_group_drv ON driver.transport_group_id = tsp_group_drv.transport_group_id " +
                    "WHERE tsp_group_drv.group_status = 0 AND driver.active = 1");
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(driverRelationDetailPORowMapper);
            builder.simpleType().nullable();
            return tspTransportGroupDriverRelationRepo.getQueryDao().query(builder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryValidRelationTransportGroupId error", e);
        }
    }

    @Override
    public List<Long> queryRelationDrvIdList(QueryDriverRelationDetailParam param) {
        try {
            ////sql优化， in 条件分批查询。。同时减少此sql回表次数
            if (CollectionUtils.isNotEmpty(param.getSupplierIdList())) {
                return batchQueryRelationDrvIdList(param);
            }
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<Long>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder("SELECT driver.drv_id FROM drv_driver driver WHERE driver.active = 1 ");
            StatementParameters parameters = new StatementParameters();
            commonPullAway(1, parameters, sqlStr, param);
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(tspTransportGroupDriverRelationPORowMapper);
            builder.simpleType().nullable();
            return tspTransportGroupDriverRelationRepo.getQueryDao().query(builder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryDrvIdList error", e);
        }
    }

    private List<Long> batchQueryRelationDrvIdList(QueryDriverRelationDetailParam param) throws Exception {
        /*
         * step1： 先用辅助联合索引列作为条件，查出司机Id（避免回表，导致慢sql）
         */
        List<Long> driverIds = new BatchRequestGatewayWrapper<Long, Long>(threadPoolService.getInQueryDriverPool(), tmsTransportQconfig.getInQueryBatchSize()) {
            @Override
            protected List<Long> doRequest(List<Long> supplierIds, Object... param) {
                return drvDrvierRepository.queryOfficialDriverIdBySupplierIds(supplierIds);
            }
        }.batchRequest(param.getSupplierIdList());

        /*
         * step2: 用司机Id作为条件，加其他业务条件再次查询过滤，此时也不会回表
         */
        return new BatchRequestGatewayWrapper<Long, Long>(threadPoolService.getInQueryDriverPool(), tmsTransportQconfig.getInQueryBatchSize()) {
            @Override
            protected List<Long> doRequest(List<Long> ids, Object... param) {
                return queryRelationDrvIdByDrvIds(ids, (QueryDriverRelationDetailParam) param[0]);
            }
        }.batchRequest(driverIds, param);
    }

    private List<Long> queryRelationDrvIdByDrvIds(List<Long> driverIds, QueryDriverRelationDetailParam param) {
        if (CollectionUtils.isEmpty(driverIds)) {
            return Lists.newArrayList();
        }
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<Long>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder("SELECT driver.drv_id FROM drv_driver driver WHERE driver.active = 1 AND driver.drv_id IN (?) ");
            StatementParameters parameters = new StatementParameters();
            parameters.setInParameter(1, "driver.drv_id", Types.BIGINT, driverIds);
            commonDispatchPullAway(2, parameters, sqlStr, param);
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(tspTransportGroupDriverRelationPORowMapper);
            builder.simpleType().nullable();
            return tspTransportGroupDriverRelationRepo.getQueryDao().query(builder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryRelationDrvIdByDrvIds error", e);
        }
    }

    @Override
    public int queryApplySuccessTransportCount(List<Long> drvIds) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            builder.equal("apply_status", TmsTransportConstant.ApplyStatusEnum.SUCCESS.getCode(), Types.TINYINT);
            if(CollectionUtils.isNotEmpty(drvIds)){
                builder.and();
                builder.in("drv_id", drvIds, Types.BIGINT);
            }
            builder.and();
            builder.equal("active", true, Types.BIT);
            return tspTransportGroupDriverRelationRepo.getDao().count(builder, hints).intValue();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TspTransportGroupDriverRelationPO> queryApplySuccessTransportList(List<Long> drvIds, Integer pageNo, Integer pageSize) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("apply_status", TmsTransportConstant.ApplyStatusEnum.SUCCESS.getCode(), Types.TINYINT);
            if(CollectionUtils.isNotEmpty(drvIds)){
                builder.and();
                builder.in("drv_id", drvIds, Types.BIGINT);
            }
            builder.and();
            builder.equal("active", true, Types.BIT);
            builder.atPage(pageNo,pageSize);
            return tspTransportGroupDriverRelationRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int eliminateApplySuccessDrv(List<Long> ids, String modifyUser, Integer applyStatus) {
        if(CollectionUtils.isEmpty(ids)){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        try {
            builder.setTemplate("update tsp_transport_group_driver_relation set apply_status = ?,modify_user = ? where id in (?) and active = 1");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "apply_status", Types.INTEGER, applyStatus);
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, StringUtils.isEmpty(modifyUser)?TmsTransportConstant.TMS_DEFAULT_USERNAME:modifyUser);
            parameters.setInParameter(i++, "id", Types.BIGINT, ids);
            return tspTransportGroupDriverRelationRepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updatEeliminateStatusByDrvId(List<Long> drvIds, String modifyUser) {
        if(CollectionUtils.isEmpty(drvIds)){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        try {
            builder.setTemplate("update tsp_transport_group_driver_relation set apply_status = ?,modify_user = ? where drv_id in (?) and apply_status = 3 and  active = 1");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "apply_status", Types.INTEGER, TmsTransportConstant.ApplyStatusEnum.APPLIED.getCode());
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, StringUtils.isEmpty(modifyUser)?TmsTransportConstant.TMS_DEFAULT_USERNAME:modifyUser);
            parameters.setInParameter(i++, "drv_id", Types.BIGINT, drvIds);
            return tspTransportGroupDriverRelationRepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    private int commonPullAway(int i, StatementParameters parameters, StringBuilder sqlStr, QueryDriverRelationDetailParam param) {
        if (CollectionUtils.isNotEmpty(param.getSupplierIdList())) {
            sqlStr.append(" AND driver.supplier_id IN (?) ");
            parameters.setInParameter(i++, "driver.supplier_id", Types.BIGINT, param.getSupplierIdList());
        }
        if (CollectionUtils.isNotEmpty(param.getCityId())) {
            sqlStr.append("AND driver.city_id IN (?) ");
            parameters.setInParameter(i++, "driver.city_id", Types.BIGINT, param.getCityId());
        }
        if (CollectionUtils.isNotEmpty(param.getDrvLanguage())) {
            sqlStr.append("AND ( ");
            List<String> likes = Lists.newArrayList();
            for (String drvLanguage : param.getDrvLanguage()) {
                likes.add("driver.drv_language LIKE ? ");
                parameters.set(i++, "driver.drv_language", Types.VARCHAR, BaseUtil.getLikeSQLStr(drvLanguage));
            }
            sqlStr.append(StringUtils.collectionToDelimitedString(likes, " OR "));
            sqlStr.append(" ) ");
        }
        if (CollectionUtils.isNotEmpty(param.getVehicleTypeId())) {
            sqlStr.append("AND driver.vehicle_type_id IN (?) ");
            parameters.setInParameter(i++, "driver.vehicle_type_id", Types.BIGINT, param.getVehicleTypeId());
        }
        if (!Strings.isNullOrEmpty(param.getDrvName())) {
            sqlStr.append("AND driver.drv_name LIKE ? ");
            parameters.set(i++, "driver.drv_name", Types.VARCHAR, param.getDrvName()+"%");
        }
        if (!Strings.isNullOrEmpty(param.getDrvPhone())) {
            sqlStr.append("AND driver.drv_phone = ? ");
            parameters.set(i++, "driver.drv_phone", Types.VARCHAR, param.getDrvPhone());
        }
        if (Objects.nonNull(param.getCoopMode())) {
            sqlStr.append("AND driver.coop_mode = ? ");
            parameters.set(i++, "driver.coop_mode", Types.INTEGER, param.getCoopMode());
        }
        if (Objects.nonNull(param.getDrvStatus())) {
            sqlStr.append("AND driver.drv_status = ? ");
            parameters.set(i++, "driver.drv_status", Types.INTEGER, param.getDrvStatus());
        }
        if (CollectionUtils.isNotEmpty(param.getFullTimeRuleOutDrvIdList())) {
            sqlStr.append("AND driver.drv_id NOT IN (?) ");
            parameters.setInParameter(i++, "driver.drv_id", Types.BIGINT, param.getFullTimeRuleOutDrvIdList());
        }
        //关联里只查询正式司机，去除临派司机
        sqlStr.append(" AND driver.temporary_dispatch_mark = ? ");
        parameters.set(i++, "driver.temporary_dispatch_mark", Types.INTEGER, TmsTransportConstant.TemporaryDispatchMarkEnum.OFFICIAL.getCode());
        return i;
    }

    public int commonDispatchPullAway(int i, StatementParameters parameters, StringBuilder sqlStr, QueryDriverRelationDetailParam param) {
        if (CollectionUtils.isNotEmpty(param.getCityId())) {
            sqlStr.append("AND driver.city_id IN (?) ");
            parameters.setInParameter(i++, "driver.city_id", Types.BIGINT, param.getCityId());
        }
        if (CollectionUtils.isNotEmpty(param.getDrvLanguage())) {
            sqlStr.append("AND ( ");
            List<String> likes = Lists.newArrayList();
            for (String drvLanguage : param.getDrvLanguage()) {
                likes.add("driver.drv_language LIKE ? ");
                parameters.set(i++, "driver.drv_language", Types.VARCHAR, BaseUtil.getLikeSQLStr(drvLanguage));
            }
            sqlStr.append(StringUtils.collectionToDelimitedString(likes, " OR "));
            sqlStr.append(" ) ");
        }
        if (CollectionUtils.isNotEmpty(param.getVehicleTypeId())) {
            sqlStr.append("AND driver.vehicle_type_id IN (?) ");
            parameters.setInParameter(i++, "driver.vehicle_type_id", Types.BIGINT, param.getVehicleTypeId());
        }
        if (!Strings.isNullOrEmpty(param.getDrvName())) {
            sqlStr.append("AND driver.drv_name LIKE ? ");
            parameters.set(i++, "driver.drv_name", Types.VARCHAR, param.getDrvName()+"%");
        }
        if (!Strings.isNullOrEmpty(param.getDrvPhone())) {
            sqlStr.append("AND driver.drv_phone = ? ");
            parameters.set(i++, "driver.drv_phone", Types.VARCHAR, param.getDrvPhone());
        }
        if (Objects.nonNull(param.getCoopMode())) {
            sqlStr.append("AND driver.coop_mode = ? ");
            parameters.set(i++, "driver.coop_mode", Types.INTEGER, param.getCoopMode());
        }
        if (Objects.nonNull(param.getDrvStatus())) {
            sqlStr.append("AND driver.drv_status = ? ");
            parameters.set(i++, "driver.drv_status", Types.INTEGER, param.getDrvStatus());
        }
        if (CollectionUtils.isNotEmpty(param.getFullTimeRuleOutDrvIdList())) {
            sqlStr.append("AND driver.drv_id NOT IN (?) ");
            parameters.setInParameter(i++, "driver.drv_id", Types.BIGINT, param.getFullTimeRuleOutDrvIdList());
        }
        //关联里只查询正式司机，去除临派司机
        sqlStr.append(" AND driver.temporary_dispatch_mark = ? ");
        parameters.set(i++, "driver.temporary_dispatch_mark", Types.INTEGER, TmsTransportConstant.TemporaryDispatchMarkEnum.OFFICIAL.getCode());
        return i;
    }

    private List<Long> getDrvIdList(List<TspTransportGroupDriverRelationPO> relationPOList) {
        if (CollectionUtils.isEmpty(relationPOList)) {
            return Lists.newArrayList();
        }
        List<Long> idList = Lists.newArrayListWithCapacity(relationPOList.size());
        for (TspTransportGroupDriverRelationPO po : relationPOList) {
            idList.add(po.getDrvId());
        }
        return idList;
    }

    /**
     * 生产线限制sqlstr http://conf.ctripcorp.com/pages/viewpage.action?pageId=2130556912
     *
     * @param isJNTProductLine 输入方法
     * @param isHumanDispatch  是人力派遣
     * @param isHasRelation
     * @return {@link String }
     */
    public String productionLineLimitSQLStr(Boolean isJNTProductLine, Boolean ispTpProductLine , Boolean isHumanDispatch, Integer isHasRelation) {
        if (!Objects.equals(isJNTProductLine, Boolean.TRUE) && !Objects.equals(ispTpProductLine, Boolean.TRUE)) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder(" AND (");
        /*
        * 接送机运力组情况下，司机产线一定要包含接送机，且 车辆产线不能为纯包车产线
        * 点对点运力组下，司机产下一定要含点对点，且 车辆不能包含纯包车
        * */
        if (Objects.equals(isJNTProductLine, Boolean.TRUE)) {
            stringBuilder.append(" ((driver.category_synthesize_code & 1 = 1) and (veh.category_synthesize_code != 4");
            if (Objects.equals(isHumanDispatch, Boolean.TRUE)){
                stringBuilder.append(" or driver.vehicle_id = 0 ))");
                if (isHasRelation == 0){ // 已关联 可以查询出 人工调度接送机的情况下，司机和车辆都是纯包车的情况
                    stringBuilder.append(" or (driver.category_synthesize_code = 4 and veh.category_synthesize_code = 4)");
                }
                stringBuilder.append(")");
            }else {
                stringBuilder.append(")))");
            }
        } else if (Objects.equals(ispTpProductLine, Boolean.TRUE)) {
            stringBuilder.append(" ((driver.category_synthesize_code & 8 = 8) and (veh.category_synthesize_code != 4");
            if (Objects.equals(isHumanDispatch, Boolean.TRUE)){
                stringBuilder.append(" or driver.vehicle_id = 0");
            }
            stringBuilder.append(" )))");
        }
        return stringBuilder.toString();
    }

    @Override
    public List<TspTransportGroupDriverRelationPO> queryDriverGroupRelation(QueryDriverGroupRelationConditionDTO conditionDTO) {
        ResetTimer.Context allContext = TransportMetric.queryDrvTransportRelationAllResetTimer().time();
        try {
            if (CollectionUtils.isEmpty(conditionDTO.getDrvIdList())) {
                return doQueryDriverGroupRelation(conditionDTO);
            }
            List<List<Long>> drvIdListArr = Lists.partition(conditionDTO.getDrvIdList(), Constant.SQL_REQUEST_ID_LIMIT_ROW_COUNT);
            List<TspTransportGroupDriverRelationPO> res = Lists.newArrayList();
            for (List<Long> drvIdList : drvIdListArr) {
                conditionDTO.setDrvIdList(drvIdList);
                ResetTimer.Context oneContext = TransportMetric.queryDrvTransportRelationOneResetTimer().time();
                try {
                    res.addAll(doQueryDriverGroupRelation(conditionDTO));
                } finally {
                    if (oneContext != null) {
                        oneContext.stop();
                    }
                }
            }
            return res;
        } finally {
            if (allContext != null) {
                allContext.stop();
            }
        }
    }

    @Override
    public List<TspTransportGroupDriverRelationPO> queryDriverGroupRelation(List<Long> drvIds) {
        QueryDriverGroupRelationConditionDTO conditionDTO = new QueryDriverGroupRelationConditionDTO();
        conditionDTO.setDrvIdList(drvIds);
        return queryDriverGroupRelation(conditionDTO);
    }

    private List<TspTransportGroupDriverRelationPO> doQueryDriverGroupRelation(QueryDriverGroupRelationConditionDTO conditionDTO) {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.select(conditionDTO.getRESOURCE_BASE());
        try {
            if (CollectionUtils.isNotEmpty(conditionDTO.getDrvIdList())) {
                builder.and();
                builder.in("drv_id", conditionDTO.getDrvIdList(), Types.BIGINT);
            }
            if (CollectionUtils.isNotEmpty(conditionDTO.getTransportGroupIdList())) {
                builder.and();
                builder.in("transport_group_id", conditionDTO.getTransportGroupIdList(), Types.BIGINT);
            }
            builder.and().equal("active",conditionDTO.getActive(),Types.BIT);
            return tspTransportGroupDriverRelationRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            logger.error("queryDriverGroupRelationByConditionError", "conditionDTO:{} error:{}", JsonUtil.toJson(conditionDTO), e);
            return Lists.newArrayList();
        }
    }

    @Override
    public List<Long> queryDispatchRelationDrvIdList(QueryDriverRelationDetailParam param) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<Long>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            sqlStr.append("SELECT driver.drv_id FROM drv_driver driver  ");
            sqlStr.append("INNER JOIN tms_drv_dispatch_relation ddr ON driver.drv_id = ddr.drv_id  where driver.active = 1 and ddr.active = 1 ");
            sqlStr.append("AND ddr.supplier_id = ? ");
            parameters.set(i++, "ddr.supplier_id", Types.BIGINT, param.getDispatchSupplierId());
            commonDispatchPullAway(i, parameters, sqlStr, param);
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(tspTransportGroupDriverRelationPORowMapper);
            builder.simpleType().nullable();
            return tspTransportGroupDriverRelationRepo.getQueryDao().query(builder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryDrvIdList error", e);
        }
    }

    @Override
    public List<Long> queryDrvIdListByTransportGroups(List<Long> transportGroupIdList) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<Long>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            sqlStr.append("SELECT drv_id FROM tsp_transport_group_driver_relation WHERE active = ? AND transport_group_id IN (?) ");
            parameters.set(i++, "active", Types.BIT, Boolean.TRUE);
            parameters.setInParameter(i++, "transport_group_id", Types.BIGINT, transportGroupIdList);
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(tspTransportGroupDriverRelationPORowMapper);
            builder.simpleType().nullable();
            return tspTransportGroupDriverRelationRepo.getQueryDao().query(builder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryDrvIdListByTransportGroups error", e);
        }
    }

    @Override
    public int batchRelationGroup(List<Long> transportIds, Long drvId) {
        if(CollectionUtils.isEmpty(transportIds) || drvId == null || drvId <= 0 ){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            List<TspTransportGroupDriverRelationPO> relationPOS = Lists.newArrayList();
            for(Long transportId : transportIds){
                TspTransportGroupDriverRelationPO po = new TspTransportGroupDriverRelationPO();
                po.setDrvId(drvId);
                po.setModifyUser(SessionHolder.getRestSessionAccountName());
                po.setCreateUser(SessionHolder.getRestSessionAccountName());
                po.setTransportGroupId(transportId);
                relationPOS.add(po);
            }
            int[] resultList = tspTransportGroupDriverRelationRepo.batchInsert(hints, relationPOS);
            if (resultList == null || resultList.length <= 0) {
                return 0;
            }
            return resultList.length;
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<DriverRelationDetailPO> queryDispatchRelationDrvList(QueryDriverRelationDetailParam param) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);

            FreeSelectSqlBuilder<List<DriverRelationDetailPO>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            sqlStr.append("SELECT driver.drv_id, driver.drv_name, driver.supplier_id, driver.drv_language, driver.city_id, driver.drv_phone, driver.vehicle_type_id, " +
                    "driver.drv_status, driver.drv_from, driver.coop_mode, tsp_group_drv.apply_status, tsp_group_drv.work_shift_id, driver.category_synthesize_code,driver.internal_scope " +
                    "FROM tsp_transport_group_driver_relation tsp_group_drv INNER JOIN drv_driver driver ON driver.drv_id = tsp_group_drv.drv_id ");
            sqlStr.append("INNER JOIN tms_drv_dispatch_relation ddr ON ddr.drv_id = tsp_group_drv.drv_id ");
            i = commonDispatchPullAway(i, parameters, sqlStr, param);

            sqlStr.append("AND ddr.supplier_id = ? ");
            parameters.set(i++, "ddr.supplier_id", Types.BIGINT, param.getDispatchSupplierId());

            sqlStr.append("LEFT JOIN veh_vehicle veh ON driver.vehicle_id = veh.vehicle_id where 1=1 and tsp_group_drv.active = 1 and driver.active= 1 and ddr.active = 1 ");

            if (Objects.nonNull(param.getTransportGroupId())) {
                sqlStr.append("and tsp_group_drv.transport_group_id=? ");
                parameters.set(i++, "tsp_group_drv.transport_group_id", Types.BIGINT, param.getTransportGroupId());
            }
            if (Objects.nonNull(param.getApplyStatus())) {
                sqlStr.append("and tsp_group_drv.apply_status= ? ");
                parameters.set(i++, "tsp_group_drv.apply_status", Types.INTEGER, param.getApplyStatus());
            }
            if (Objects.nonNull(param.getWorkShiftId())) {
                sqlStr.append("and tsp_group_drv.work_shift_id = ? ");
                parameters.set(i++, "tsp_group_drv.work_shift_id", Types.BIGINT, param.getWorkShiftId());
            }
            sqlStr.append(productionLineLimitSQLStr(param.isJNTProductLine(),param.isIspTpProductLine(), param.isHumanDispatch(), param.getIsHasRelation()));
            sqlStr.append(" order by tsp_group_drv.datachange_lasttime desc ");
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(driverRelationDetailPORowMapper);
            if (Objects.nonNull(param.getPaginator())) {
                builder.atPage(param.getPaginator().getPageNo(),param.getPaginator().getPageSize());
            }
            return tspTransportGroupDriverRelationRepo.getQueryDao().query(builder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryRelationDrvList error", e);
        }
    }

    @Override
    public int queryDispatchRelationDrvCount(QueryDriverRelationDetailParam param) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);

            FreeSelectSqlBuilder<Long> builder = new FreeSelectSqlBuilder<>();
            builder.simpleType().requireSingle().nullable();
            StringBuilder sqlStr = new StringBuilder();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            sqlStr.append("select count(*) from tsp_transport_group_driver_relation tsp_group_drv inner join drv_driver driver on driver.drv_id = tsp_group_drv.drv_id ");
            sqlStr.append(" INNER JOIN tms_drv_dispatch_relation ddr ON ddr.drv_id = tsp_group_drv.drv_id ");
            i = commonDispatchPullAway(i, parameters, sqlStr, param);
            sqlStr.append(" LEFT JOIN veh_vehicle veh ON driver.vehicle_id = veh.vehicle_id ");

            sqlStr.append(" where 1=1 and tsp_group_drv.active = 1 and driver.active= 1 and ddr.active = 1 ");

            if (Objects.nonNull(param.getTransportGroupId())) {
                sqlStr.append("and tsp_group_drv.transport_group_id=? ");
                parameters.set(i++, "tsp_group_drv.transport_group_id", Types.BIGINT, param.getTransportGroupId());
            }
            if (Objects.nonNull(param.getApplyStatus())) {
                sqlStr.append("and tsp_group_drv.apply_status= ? ");
                parameters.set(i++, "tsp_group_drv.apply_status", Types.INTEGER, param.getApplyStatus());
            }
            if (Objects.nonNull(param.getWorkShiftId())) {
                sqlStr.append("and tsp_group_drv.work_shift_id = ? ");
                parameters.set(i++, "tsp_group_drv.work_shift_id", Types.BIGINT, param.getWorkShiftId());
            }
            sqlStr.append(" AND ddr.supplier_id = ? ");
            parameters.set(i++, "ddr.supplier_id", Types.BIGINT, param.getDispatchSupplierId());
            sqlStr.append(productionLineLimitSQLStr(param.isJNTProductLine(),param.isIspTpProductLine(), param.isHumanDispatch(), param.getIsHasRelation()));
            builder.setTemplate(sqlStr.toString());
            return tspTransportGroupDriverRelationRepo.getQueryDao().query(builder, parameters, hints).intValue();
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryRelationDrvCount error", e);
        }
    }

    @Override
    public List<DriverRelationDetailPO> queryDispatchNotRelationDrvList(QueryDriverRelationDetailParam param) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);

            FreeSelectSqlBuilder<List<DriverRelationDetailPO>> builder = new FreeSelectSqlBuilder<>();
            StringBuilder sqlStr = new StringBuilder();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            sqlStr.append("select * from ( select driver.drv_id,driver.drv_name,driver.supplier_id,driver.drv_language,driver.city_id ,driver.drv_phone,driver.vehicle_type_id,driver.drv_status,driver.drv_from,driver.coop_mode," +
                    "tsp_group_drv.transport_group_id,tsp_group_drv.apply_status,tsp_group_drv.work_shift_id,driver.category_synthesize_code,driver.internal_scope  " +
                    "from drv_driver driver ");
            sqlStr.append(" INNER JOIN tms_drv_dispatch_relation ddr ON ddr.drv_id = driver.drv_id ");
            sqlStr.append("left join tsp_transport_group_driver_relation tsp_group_drv  on driver.drv_id = tsp_group_drv.drv_id and tsp_group_drv.active = 1 ");
            if (Objects.nonNull(param.getTransportGroupId())) {
                sqlStr.append("and tsp_group_drv.transport_group_id=? ");
                parameters.set(i++, "tsp_group_drv.transport_group_id", Types.BIGINT, param.getTransportGroupId());
            }
            if (Objects.nonNull(param.getWorkShiftId())) {
                sqlStr.append("and tsp_group_drv.work_shift_id = ? ");
                parameters.set(i++, "tsp_group_drv.work_shift_id", Types.BIGINT, param.getWorkShiftId());
            }
            sqlStr.append(" LEFT JOIN veh_vehicle veh ON driver.vehicle_id = veh.vehicle_id ");
            sqlStr.append(" where 1=1  and driver.active = 1 and ddr.active = 1 ");
            sqlStr.append(" AND ddr.supplier_id = ? ");
            parameters.set(i++, "ddr.supplier_id", Types.BIGINT, param.getDispatchSupplierId());
            i = commonDispatchPullAway(i, parameters, sqlStr, param);
            sqlStr.append(productionLineLimitSQLStr(param.isJNTProductLine(),param.isIspTpProductLine(), param.isHumanDispatch(), param.getIsHasRelation()));
            sqlStr.append(" and (tsp_group_drv.apply_status=0 or tsp_group_drv.apply_status is null) order by driver.datachange_lasttime desc ) temp where temp.transport_group_id is null");
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(driverRelationDetailPORowMapper);
            if (Objects.nonNull(param.getPaginator())) {
                builder.atPage(param.getPaginator().getPageNo(),param.getPaginator().getPageSize());
            }
            return tspTransportGroupDriverRelationRepo.getQueryDao().query(builder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryNotRelationDrvList error", e);
        }
    }

    @Override
    public int queryDispatchNotRelationDrvCount(QueryDriverRelationDetailParam param) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);

            FreeSelectSqlBuilder<Long> builder = new FreeSelectSqlBuilder<>();
            builder.simpleType().requireSingle().nullable();
            StringBuilder sqlStr = new StringBuilder();
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            sqlStr.append("select count(1) from ( select driver.drv_id,driver.drv_name,driver.supplier_id,driver.drv_language,driver.city_id ,driver.drv_phone,driver.vehicle_type_id,driver.drv_status,driver.drv_from,driver.coop_mode," +
                    "tsp_group_drv.transport_group_id,tsp_group_drv.apply_status,tsp_group_drv.work_shift_id " +
                    "from drv_driver driver ");
            sqlStr.append(" INNER JOIN tms_drv_dispatch_relation ddr ON ddr.drv_id = driver.drv_id ");
            sqlStr.append(" left join tsp_transport_group_driver_relation tsp_group_drv  on driver.drv_id = tsp_group_drv.drv_id and tsp_group_drv.active = 1 ");
            if (Objects.nonNull(param.getTransportGroupId())) {
                sqlStr.append("and tsp_group_drv.transport_group_id=? ");
                parameters.set(i++, "tsp_group_drv.transport_group_id", Types.BIGINT, param.getTransportGroupId());
            }
            if (Objects.nonNull(param.getWorkShiftId())) {
                sqlStr.append("and tsp_group_drv.work_shift_id = ? ");
                parameters.set(i++, "tsp_group_drv.work_shift_id", Types.BIGINT, param.getWorkShiftId());
            }
            sqlStr.append(" LEFT JOIN veh_vehicle veh ON driver.vehicle_id = veh.vehicle_id ");
            sqlStr.append(" where 1=1 and driver.active = 1 and ddr.active = 1 ");
            sqlStr.append(" AND ddr.supplier_id = ? ");
            parameters.set(i++, "ddr.supplier_id", Types.BIGINT, param.getDispatchSupplierId());
            i = commonDispatchPullAway(i, parameters, sqlStr, param);
            sqlStr.append(productionLineLimitSQLStr(param.isJNTProductLine(),param.isIspTpProductLine(), param.isHumanDispatch(), param.getIsHasRelation()));
            sqlStr.append(" and (tsp_group_drv.apply_status=0 or tsp_group_drv.apply_status is null)) temp where temp.transport_group_id is null");
            builder.setTemplate(sqlStr.toString());
            return tspTransportGroupDriverRelationRepo.getQueryDao().query(builder, parameters, hints).intValue();
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryNotRelationDrvCount error", e);
        }
    }

}
