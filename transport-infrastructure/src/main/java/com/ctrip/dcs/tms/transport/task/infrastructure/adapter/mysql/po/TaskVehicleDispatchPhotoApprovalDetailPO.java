package com.ctrip.dcs.tms.transport.task.infrastructure.adapter.mysql.po;

import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;
import java.sql.Types;

@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "task_vehicle_dispatch_photo_approval_detail")
@Data
public class TaskVehicleDispatchPhotoApprovalDetailPO {

  /**
   * 主键
   */
  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  @Column(name = "id")
  @Type(value = Types.BIGINT)
  private Long id;

  /**
   * 车照片审核记录ID
   */
  @Column(name = "vehicle_dispatch_photo_approval_id")
  @Type(value = Types.BIGINT)
  private Long vehicleDispatchPhotoApprovalId;

  /**
   * 照片类型(1.服务形象、2.车辆外观照、3.车内前排照、4.车内后排照、5.车后备箱照)
   */
  @Column(name = "photo_type")
  @Type(value = Types.INTEGER)
  private int photoType;

  /**
   * 图片url
   */
  @Column(name = "photo_url")
  @Type(value = Types.VARCHAR)
  private String photoUrl;

  /**
   * 审核时间
   */
  @Column(name = "approval_time")
  @Type(value = Types.TIMESTAMP)
  private Timestamp approvalTime;

  /**
   * 审核人
   */
  @Column(name = "approval_user")
  @Type(value = Types.TIMESTAMP)
  private String approvalUser;

  /**
   * 审批状态(0.待审批、1.审批通过、2.审批不通过)
   */
  @Column(name = "approval_status")
  @Type(value = Types.INTEGER)
  private Integer approvalStatus;

  /**
   * 创建人
   */
  @Column(name = "create_user")
  @Type(value = Types.VARCHAR)
  private String createUser;

  /**
   * 变更人
   */
  @Column(name = "modify_user")
  @Type(value = Types.VARCHAR)
  private String modifyUser;

  /**
   * 创建时间
   */
  @Column(name = "datachange_createtime")
  @Type(value = Types.TIMESTAMP)
  private Timestamp datachangeCreatetime;

  /**
   * 更新时间
   */
  @Column(name = "datachange_lasttime")
  @Type(value = Types.TIMESTAMP)
  private Timestamp datachangeLasttime;

  @Column(name = "remark")
  @Type(value = Types.VARCHAR)
  private String remark;
}

