package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

import java.util.List;

public class NetCertCheckRuleDTO {
    private List<NetCertNoCheckRuleDetailDTO> vehicleNetCertNoCheckRule;
    private List<NetCertNoCheckRuleDetailDTO> driverNetCertNoCheckRule;
    private String vehicleNetCertNoExample;
    private String driverNetCertNoExample;
    private String equalsDriverIdCardFlag;

    public List<NetCertNoCheckRuleDetailDTO> getVehicleNetCertNoCheckRule() {
        return vehicleNetCertNoCheckRule;
    }

    public void setVehicleNetCertNoCheckRule(List<NetCertNoCheckRuleDetailDTO> vehicleNetCertNoCheckRule) {
        this.vehicleNetCertNoCheckRule = vehicleNetCertNoCheckRule;
    }

    public List<NetCertNoCheckRuleDetailDTO> getDriverNetCertNoCheckRule() {
        return driverNetCertNoCheckRule;
    }

    public void setDriverNetCertNoCheckRule(List<NetCertNoCheckRuleDetailDTO> driverNetCertNoCheckRule) {
        this.driverNetCertNoCheckRule = driverNetCertNoCheckRule;
    }

    public String getVehicleNetCertNoExample() {
        return vehicleNetCertNoExample;
    }

    public void setVehicleNetCertNoExample(String vehicleNetCertNoExample) {
        this.vehicleNetCertNoExample = vehicleNetCertNoExample;
    }

    public String getDriverNetCertNoExample() {
        return driverNetCertNoExample;
    }

    public void setDriverNetCertNoExample(String driverNetCertNoExample) {
        this.driverNetCertNoExample = driverNetCertNoExample;
    }

    public String getEqualsDriverIdCardFlag() {
        return equalsDriverIdCardFlag;
    }

    public void setEqualsDriverIdCardFlag(String equalsDriverIdCardFlag) {
        this.equalsDriverIdCardFlag = equalsDriverIdCardFlag;
    }
}
