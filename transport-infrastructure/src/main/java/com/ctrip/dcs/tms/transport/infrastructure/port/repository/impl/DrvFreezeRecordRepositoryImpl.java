package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;


import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvFreezeRecordPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.udl.UDLHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvFreezeRecordRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.DalRepositoryImpl;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.KeyHolder;
import com.ctrip.platform.dal.dao.sqlbuilder.SelectSqlBuilder;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.util.Date;
import java.util.List;

@Repository(value = "drvFreezeRecordRepository")
public class DrvFreezeRecordRepositoryImpl implements DrvFreezeRecordRepository {

    private DalRepository<DrvFreezeRecordPO> drvFreezeRecordRepo;

    @Autowired
    UDLHandler udlHandler;

    public DrvFreezeRecordRepositoryImpl() throws SQLException {
        drvFreezeRecordRepo = new DalRepositoryImpl<>(DrvFreezeRecordPO.class);
    }

    @Override
    public Long insert(DrvFreezeRecordPO po) throws SQLException {
        KeyHolder keyHolder = new KeyHolder();
        po.setProviderDataLocation(udlHandler.getDrvUdl(po.getDrvId()));
        drvFreezeRecordRepo.insert(new DalHints(), keyHolder, po);
        return keyHolder.getKey().longValue();
    }

    @Override
    public int update(DrvFreezeRecordPO po) throws SQLException {
        return drvFreezeRecordRepo.update(po);
    }

    @Override
    public List<DrvFreezeRecordPO> queryDrvFreezeRecordList(List<Long> drvList) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("drv_id", drvList, Types.BIGINT);
            builder.and();
            builder.greaterThanEquals("first_freeze_time", DateUtil.dayDisplacement(new Date(),-90), Types.TIMESTAMP);
            builder.orderBy("id", false);
            return drvFreezeRecordRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }
}
