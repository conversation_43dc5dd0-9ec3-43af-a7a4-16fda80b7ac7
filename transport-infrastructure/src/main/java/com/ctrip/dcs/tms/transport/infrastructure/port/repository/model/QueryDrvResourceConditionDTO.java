package com.ctrip.dcs.tms.transport.infrastructure.port.repository.model;


import com.ctrip.igt.*;

import java.util.*;

/**
 * <AUTHOR>
 * 查询司机资源条件对象
 */
public class QueryDrvResourceConditionDTO {

    /**
     * 分页对象
     */
    private PaginatorDTO paginator;
    /**
     * 司机id列表
     */
    private List<Long> drvIdList;
    /**
     * 城市id列表
     */
    private List<Long> cityIdList;
    /**
     * 供应商id列表
     */
    private List<Long> supplierIdList;
    /**
     * 司机姓名列表
     */
    private List<String> drvNameList;
    /**
     * 司机电话列表
     */
    private List<String> driverPhoneList;
    /**
     * 司机状态列表
     */
    private List<Integer> drvStatusList;
    /**
     * 司机合作模式列表
     */
    private List<Integer> drvCoopModeList;

    /**
     * 司机产线列表
     */
    private List<Integer> proLineIdList;

    /**
     * 国家id列表
     */
    private List<Long> countryIdList;

    /**
     * 车型id列表
     */
    private List<Long> vehicleTypeIdList;

    /**
     * 分界司机id
     */
    private Long boundaryDrvId;

    /**
     * 请求属性列
     */
    private String[] fields;

    public List<Integer> getProLineIdList() {
        return proLineIdList;
    }

    public String[] getFields() {
        return fields;
    }

    public PaginatorDTO getPaginator() {
        return paginator;
    }

    public List<Long> getDrvIdList() {
        return drvIdList;
    }

    public List<Long> getCityIdList() {
        return cityIdList;
    }

    public List<Long> getSupplierIdList() {
        return supplierIdList;
    }

    public List<String> getDrvNameList() {
        return drvNameList;
    }

    public List<String> getDriverPhoneList() {
        return driverPhoneList;
    }

    public List<Integer> getDrvStatusList() {
        return drvStatusList;
    }

    public List<Integer> getDrvCoopModeList() {
        return drvCoopModeList;
    }

    public List<Long> getCountryIdList() {
        return countryIdList;
    }

    public List<Long> getVehicleTypeIdList() {
        return vehicleTypeIdList;
    }

    public Long getBoundaryDrvId() {
        return boundaryDrvId;
    }

    public QueryDrvResourceConditionDTO withPaginator(PaginatorDTO paginator) {
        this.paginator = paginator;
        return this;
    }

    public QueryDrvResourceConditionDTO withDrvIdList(List<Long> drvIdList) {
        this.drvIdList = drvIdList;
        return this;
    }

    public QueryDrvResourceConditionDTO withCityIdList(List<Long> cityIdList) {
        this.cityIdList = cityIdList;
        return this;
    }

    public QueryDrvResourceConditionDTO withSupplierIdList(List<Long> supplierIdList) {
        this.supplierIdList = supplierIdList;
        return this;
    }

    public QueryDrvResourceConditionDTO withDrvNameList(List<String> drvNameList) {
        this.drvNameList = drvNameList;
        return this;
    }

    public QueryDrvResourceConditionDTO withDriverPhoneList(List<String> driverPhoneList) {
        this.driverPhoneList = driverPhoneList;
        return this;
    }

    public QueryDrvResourceConditionDTO withDrvStatusList(List<Integer> drvStatusList) {
        this.drvStatusList = drvStatusList;
        return this;
    }

    public QueryDrvResourceConditionDTO withDrvCoopModeList(List<Integer> drvCoopModeList) {
        this.drvCoopModeList = drvCoopModeList;
        return this;
    }

    public QueryDrvResourceConditionDTO withFields(String[] fields) {
        this.fields = fields;
        return this;
    }

    public QueryDrvResourceConditionDTO withProLineIdList(List<Integer> proLineIdList) {
        this.proLineIdList = proLineIdList;
        return this;
    }

    public QueryDrvResourceConditionDTO withCountryIdList(List<Long> countryIdList) {
        this.countryIdList = countryIdList;
        return this;
    }

    public QueryDrvResourceConditionDTO withVehicleTypeIdList(List<Long> vehicleTypeIdList) {
        this.vehicleTypeIdList = vehicleTypeIdList;
        return this;
    }

    public QueryDrvResourceConditionDTO withBoundaryDrvId(Long boundaryDrvId) {
        this.boundaryDrvId = boundaryDrvId;
        return this;
    }

    private QueryDrvResourceConditionDTO() {
    }

    public static QueryDrvResourceConditionDTO newCondition() {
        return new QueryDrvResourceConditionDTO();
    }

    public QueryDrvResourceConditionDTO build() {
        QueryDrvResourceConditionDTO condition = new QueryDrvResourceConditionDTO();
        condition.fields = this.fields;
        condition.paginator = this.paginator;
        condition.drvIdList = this.drvIdList;
        condition.cityIdList = this.cityIdList;
        condition.supplierIdList = this.supplierIdList;
        condition.drvNameList = this.drvNameList;
        condition.driverPhoneList = this.driverPhoneList;
        condition.drvStatusList = this.drvStatusList;
        condition.drvCoopModeList = this.drvCoopModeList;
        condition.proLineIdList = this.proLineIdList;
        condition.countryIdList = this.countryIdList;
        condition.vehicleTypeIdList = this.vehicleTypeIdList;
        condition.boundaryDrvId = this.boundaryDrvId;
        return condition;
    }

}