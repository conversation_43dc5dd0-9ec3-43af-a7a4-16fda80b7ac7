package com.ctrip.dcs.tms.transport.task.infrastructure.value;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Data
public class VehicleDispatchPhotoTaskQConfigJSON {
  private Map<String, VehicleDispatchPhotoTaskQConfigData> vehicle_dispatch_photo_config = Maps.newHashMap();
  private List<Long> vehicleDispatchGrayCityList = Lists.newArrayList();
}
