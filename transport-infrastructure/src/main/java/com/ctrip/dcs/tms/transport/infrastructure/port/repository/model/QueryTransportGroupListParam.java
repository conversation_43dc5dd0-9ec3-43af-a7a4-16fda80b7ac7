package com.ctrip.dcs.tms.transport.infrastructure.port.repository.model;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.*;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2020/11/27 16:32
 */
public class QueryTransportGroupListParam {

    TspTransportGroupPO po;
    PaginatorDTO pageInfo;
    List<Long> contractIdList;
    List<Long> transportGroupIdList;
    List<Long> supplierIdList;
    List<Long> salfSupplierCityList;
    List<Long> vehicleTypeIdList;

    public QueryTransportGroupListParam(TspTransportGroupPO po, PaginatorDTO pageInfo, List<Long> contractIdList, List<Long> transportGroupIdList, List<Long> supplierIdList,List<Long> salfSupplierCityList,List<Long> vehicleTypeIdList) {
        this.po = po;
        this.pageInfo = pageInfo;
        this.contractIdList = contractIdList;
        this.transportGroupIdList = transportGroupIdList;
        this.supplierIdList = supplierIdList;
        this.salfSupplierCityList = salfSupplierCityList;
        this.vehicleTypeIdList = vehicleTypeIdList;
    }

    public TspTransportGroupPO getPo() {
        return po;
    }

    public void setPo(TspTransportGroupPO po) {
        this.po = po;
    }

    public PaginatorDTO getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PaginatorDTO pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<Long> getContractIdList() {
        return contractIdList;
    }

    public void setContractIdList(List<Long> contractIdList) {
        this.contractIdList = contractIdList;
    }

    public List<Long> getTransportGroupIdList() {
        return transportGroupIdList;
    }

    public void setTransportGroupIdList(List<Long> transportGroupIdList) {
        this.transportGroupIdList = transportGroupIdList;
    }

    public List<Long> getSupplierIdList() {
        return supplierIdList;
    }

    public void setSupplierIdList(List<Long> supplierIdList) {
        this.supplierIdList = supplierIdList;
    }

    public List<Long> getSalfSupplierCityList() {
        return salfSupplierCityList;
    }

    public void setSalfSupplierCityList(List<Long> salfSupplierCityList) {
        this.salfSupplierCityList = salfSupplierCityList;
    }

    public List<Long> getVehicleTypeIdList() {
        return vehicleTypeIdList;
    }

    public void setVehicleTypeIdList(List<Long> vehicleTypeIdList) {
        this.vehicleTypeIdList = vehicleTypeIdList;
    }
}
