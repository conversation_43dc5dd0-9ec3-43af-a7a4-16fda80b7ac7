package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.ctriposs.baiji.exception.*;
import org.springframework.stereotype.*;

import java.sql.*;
import java.util.*;

/**
 * hecai
 */
@Repository(value = "tmsDrvLoginInformationRepository")
public class TmsDrvLoginInformationRepositoryImpl implements TmsDrvLoginInformationRepository {

    private DalRepository<TmsDrvLoginInformationPO> tmsVerifyEventRepo;

    public TmsDrvLoginInformationRepositoryImpl() throws SQLException {
        tmsVerifyEventRepo = new DalRepositoryImpl<>(TmsDrvLoginInformationPO.class);

    }


    @Override
    public TmsDrvLoginInformationPO queryByPk(Long id) {
        return tmsVerifyEventRepo.queryByPk(id);
    }

    @Override
    public int insert(TmsDrvLoginInformationPO po) {
        return tmsVerifyEventRepo.insert(po);
    }

    @Override
    public List<TmsDrvLoginInformationPO> queryDrvLoginInfoLimit(Long drvId,int pageNo,int pageSize) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("drv_id",drvId, Types.BIGINT);
            builder.orderBy("id", false);
            builder.atPage(pageNo,pageSize);
            return tmsVerifyEventRepo.getDao().query(builder,hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }
}
