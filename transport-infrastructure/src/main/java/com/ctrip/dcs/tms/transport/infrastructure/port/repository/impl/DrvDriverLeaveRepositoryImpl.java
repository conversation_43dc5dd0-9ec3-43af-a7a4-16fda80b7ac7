package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.thread.ThreadPoolService;
import com.ctrip.dcs.tms.transport.infrastructure.common.udl.UDLHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.helper.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.ctriposs.baiji.exception.*;
import com.google.common.collect.*;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import org.springframework.util.*;

import java.sql.*;
import java.util.Date;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/2/25 11:15
 */
@Repository
public class DrvDriverLeaveRepositoryImpl implements DrvDriverLeaveRepository {

    private static final Logger logger = LoggerFactory.getLogger(DrvDriverLeaveRepositoryImpl.class);

    private DalRepository<DrvDriverLeavePO> drvDriverLeaveRepo;

    private static final String DEFAULT_TABLENAME = "drv_driver_leave";

    private DalRowMapper<DrvLeaveDetailPO> drvLeaveDetailPORowMapper;

    @Autowired
    ThreadPoolService threadPoolService;

    @Autowired
    TmsTransportQconfig tmsTransportQconfig;

    @Autowired
    UDLHandler udlHandler;

    public DrvDriverLeaveRepositoryImpl() throws SQLException {
        drvDriverLeaveRepo = new DalRepositoryImpl<>(DrvDriverLeavePO.class);
        this.drvLeaveDetailPORowMapper = new DalDefaultJpaMapper<>(DrvLeaveDetailPO.class);
    }

    /**
     * 校验请假时段是否重复(返回时段重复的记录数，返回结果大于0则有重复)
     * @param driverLeavePO
     * @return
     */
    public int checkDrvLeaveTime(DrvDriverLeavePO driverLeavePO){
        try {
            DalHints hints = DalHints.createIfAbsent(null);

            SelectSqlBuilder sqlBuilder = new SelectSqlBuilder();
            sqlBuilder.selectCount();
            sqlBuilder.equal("drv_id",driverLeavePO.getDrvId(),Types.BIGINT,false)
                    .and()
                    .equal("leave_status",driverLeavePO.getLeaveStatus(),Types.TINYINT,false)
                    .and()
                    .equal("active",driverLeavePO.getActive(),Types.BIT,false);
            sqlBuilder.and()
                    .leftBracket()
                    .between("leave_begin_time",driverLeavePO.getLeaveBeginTime(),driverLeavePO.getLeaveEndTime(),Types.TIMESTAMP,false)
                    .or()
                    .between("leave_end_time",driverLeavePO.getLeaveBeginTime(),driverLeavePO.getLeaveEndTime(),Types.TIMESTAMP,false)
                    .or()
                    .leftBracket()
                    .lessThan("leave_begin_time",driverLeavePO.getLeaveBeginTime(),Types.TIMESTAMP,false)
                    .and()
                    .greaterThan("leave_end_time",driverLeavePO.getLeaveEndTime(),Types.TIMESTAMP,false)
                    .rightBracket()
                    .rightBracket();

            return drvDriverLeaveRepo.getDao().count(sqlBuilder,hints).intValue();
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 司机请假
     * @param driverLeavePO
     * @return
     */
    public int addDrvLeave(DrvDriverLeavePO driverLeavePO){
        try {
            if (driverLeavePO == null) {
                return 0;
            }
            DalHints hints = DalHints.createIfAbsent(null);
            //拼接SQL
            FreeUpdateSqlBuilder sqlBuilder = new FreeUpdateSqlBuilder();
            List<String> columnList = Lists.newArrayList();
            columnList.add("drv_id");
            columnList.add("leave_begin_time");
            columnList.add("leave_end_time");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.set(i++, "drv_id", Types.BIGINT, driverLeavePO.getDrvId());
            parameters.set(i++, "leave_begin_time", Types.TIMESTAMP, driverLeavePO.getLeaveBeginTime());
            parameters.set(i++, "leave_end_time", Types.TIMESTAMP, driverLeavePO.getLeaveEndTime());
            Timestamp currentTimestamp = new Timestamp(new Date().getTime());
            if (driverLeavePO.getDrvName() != null) {
                columnList.add("drv_name");
                parameters.set(i++, "drv_name", Types.VARCHAR, driverLeavePO.getDrvName());
            }
            if (driverLeavePO.getLeaveReason() != null) {
                columnList.add("leave_reason");
                parameters.set(i++, "leave_reason", Types.VARCHAR, driverLeavePO.getLeaveReason());
            }
            if (driverLeavePO.getLeaveStatus() != null) {
                columnList.add("leave_status");
                parameters.set(i++, "leave_status", Types.TINYINT, driverLeavePO.getLeaveStatus());
            }
            if (driverLeavePO.getActive() != null) {
                columnList.add("active");
                parameters.set(i++, "active", Types.BIT, driverLeavePO.getActive());
            }
            if (driverLeavePO.getOperateType() != null) {
                columnList.add("operate_type");
                parameters.set(i++, "operate_type", Types.TINYINT, driverLeavePO.getOperateType());
            }
            if (driverLeavePO.getCreateUser() != null) {
                columnList.add("create_user");
                columnList.add("modify_user");
                parameters.set(i++, "create_user", Types.VARCHAR, driverLeavePO.getCreateUser());
                parameters.set(i++, "modify_user", Types.VARCHAR, driverLeavePO.getCreateUser());
            }
            columnList.add("provider_data_location");
            parameters.set(i++, "provider_data_location", Types.VARCHAR, udlHandler.getDrvUdl(driverLeavePO.getDrvId()));
            String[] columnNames = new String[columnList.size()];
            sqlBuilder.insertInto(DEFAULT_TABLENAME).values(columnList.toArray(columnNames));
            return drvDriverLeaveRepo.getQueryDao().update(sqlBuilder, parameters, hints);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 司机销假
     * @param driverLeavePO
     * @return
     */
    public int closeDrvLeave(DrvDriverLeavePO driverLeavePO){
        try {
            if (driverLeavePO == null) {
                return 0;
            }
            DalHints hints = DalHints.createIfAbsent(null);
            FreeUpdateSqlBuilder sqlBuilder = new FreeUpdateSqlBuilder();
            sqlBuilder.update(DEFAULT_TABLENAME).set("active","modify_user","datachange_deltime").where("1=1");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.set(i++, "active", Types.BIT, driverLeavePO.getActive());
            parameters.set(i++, "modify_user", Types.VARCHAR, driverLeavePO.getModifyUser());
            parameters.set(i++, "datachange_deltime", Types.TIMESTAMP, driverLeavePO.getDatachangeDeltime());
            if (driverLeavePO.getId() != null) {
                sqlBuilder.and().equal("id");
                parameters.set(i++, "id", Types.BIGINT, driverLeavePO.getId());
            }
            if (driverLeavePO.getDrvId() != null) {
                sqlBuilder.and().equal("drv_id");
                parameters.set(i++, "drv_id", Types.BIGINT, driverLeavePO.getDrvId());
            }
            return drvDriverLeaveRepo.getQueryDao().update(sqlBuilder,parameters, hints);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 请假详情总条数统计
     * @param sample
     * @return
     */
    @Override
    public int countDrvLeaveDetail(DrvDriverLeavePO sample) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<Long> sqlBuilder = new FreeSelectSqlBuilder<>();
            sqlBuilder.simpleType().requireSingle().nullable();
            sqlBuilder.setTemplate("select count(1) from drv_driver_leave").where("1=1");
            if (sample.getDrvId() != null) {
                sqlBuilder.and().equal("drv_id",sample.getDrvId(),Types.BIGINT);
            }
            return drvDriverLeaveRepo.getQueryDao().query(sqlBuilder, hints).intValue();
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    /**
     * 请假详情查询
     * @param sample
     * @param pageInfo
     * @return
     */
    @Override
    public List<DrvLeaveDetailPO> queryDrvLeaveDetail(DrvDriverLeavePO sample, PaginatorDTO pageInfo) {
        logger.info("queryDrvLeaveDetail ", JsonUtil.toJson(sample));
        try {
            DalHints hints = DalHints.createIfAbsent(null);

            FreeSelectSqlBuilder<List<DrvLeaveDetailPO>> sqlBuilder = new FreeSelectSqlBuilder<>();
            sqlBuilder.mapWith(this.drvLeaveDetailPORowMapper)
                    .selectAll().from(DEFAULT_TABLENAME)
                    .where("1=1");
            if (sample.getDrvId() != null) {
                sqlBuilder.and().equal("drv_id",sample.getDrvId(),Types.BIGINT);
            }
            sqlBuilder.orderBy("datachange_lasttime", false);
            if (pageInfo != null) {
                sqlBuilder.atPage(pageInfo.getPageNo(), pageInfo.getPageSize());
            }
            return drvDriverLeaveRepo.getQueryDao().query(sqlBuilder, hints);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }


    @SneakyThrows
    @Override
    public List<DrvLeaveDetailPO> queryDrvLeaveDetailForDsp(List<Long> drvIdList) {
        if (CollectionUtils.isEmpty(drvIdList)) {
            return Lists.newArrayList();
        }

        drvIdList = drvIdList.stream().distinct().collect(Collectors.toList());

        if (!tmsTransportQconfig.getInQueryBatchSwitch()) {
            return getDrvLeaveDetailPOS(drvIdList);
        }

        List<List<Long>> drvIdListPage = Lists.partition(drvIdList, tmsTransportQconfig.getInQueryBatchSize());
        List<CompletableFuture<List<DrvLeaveDetailPO>>> futures =
          drvIdListPage.stream().map(singlePartition -> CompletableFuture.supplyAsync(() -> getDrvLeaveDetailPOS(singlePartition), threadPoolService.getInQueryLeavelPool())).collect(
            Collectors.toList());
        // 获取结果
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[futures.size()]));

        return allFutures.thenApply(v ->
          futures.stream()
            .map(CompletableFuture::join)
            .flatMap(List::stream)
            .collect(Collectors.toList())
        ).get();
    }

    protected List<DrvLeaveDetailPO> getDrvLeaveDetailPOS(List<Long> drvIds) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);

            FreeSelectSqlBuilder<List<DrvLeaveDetailPO>> sqlBuilder = new FreeSelectSqlBuilder<>();
            sqlBuilder.mapWith(this.drvLeaveDetailPORowMapper)
                    .selectAll().from(DEFAULT_TABLENAME)
                    .where("1=1");
            //未销假
            sqlBuilder.and().equal("active",Boolean.TRUE,Types.BIGINT);
            //有效
            sqlBuilder.and().equal("leave_status", TmsTransportConstant.DrvLeaveStatusEnum.VALID.getCode(),Types.TINYINT);
            //请假生效中
            Timestamp currentTimestamp = new Timestamp(new Date().getTime());
//            sqlBuilder.and().lessThanEquals("leave_begin_time",currentTimestamp,Types.TIMESTAMP);
            sqlBuilder.and().greaterThanEquals("leave_end_time",currentTimestamp,Types.TIMESTAMP);
            if (!CollectionUtils.isEmpty(drvIds)) {
                sqlBuilder.and().in("drv_id", drvIds,Types.BIGINT);
            }
            return drvDriverLeaveRepo.getQueryDao().query(sqlBuilder, hints);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }


    @Override
    public List<DrvLeaveDetailPO> queryDrvLeaveIng(List<Long> drvIds) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);

            FreeSelectSqlBuilder<List<DrvLeaveDetailPO>> sqlBuilder = new FreeSelectSqlBuilder<>();
            sqlBuilder.mapWith(this.drvLeaveDetailPORowMapper)
                    .selectAll().from(DEFAULT_TABLENAME)
                    .where("1=1");
            //未销假
            sqlBuilder.and().equal("active",Boolean.TRUE,Types.BIGINT);
            //有效
            sqlBuilder.and().equal("leave_status", TmsTransportConstant.DrvLeaveStatusEnum.VALID.getCode(),Types.TINYINT);
            //请假生效中
            Timestamp currentTimestamp = new Timestamp(new Date().getTime());
            sqlBuilder.and().lessThanEquals("leave_begin_time",currentTimestamp,Types.TIMESTAMP);
            sqlBuilder.and().greaterThanEquals("leave_end_time",currentTimestamp,Types.TIMESTAMP);
            if (!CollectionUtils.isEmpty(drvIds)) {
                sqlBuilder.and().in("drv_id",drvIds,Types.BIGINT);
            }
            return drvDriverLeaveRepo.getQueryDao().query(sqlBuilder, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<DrvDriverLeavePO> queryDrvLeaveRecord(List<Long> drvIds) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("drv_id", drvIds, Types.BIGINT);
            builder.and().greaterThanEquals("leave_begin_time",DateUtil.dayDisplacement(new Date(),-60),Types.TIMESTAMP);
            builder.orderBy("datachange_lasttime", false);
            return drvDriverLeaveRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    @Override
    public DrvDriverLeavePO queryDriverLeaveById(Long drvId, Long id) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("drv_id", drvId, Types.BIGINT);
            builder.and();
            builder.equal("id",id,Types.BIGINT);
            List<DrvDriverLeavePO> result = drvDriverLeaveRepo.getDao().query(builder, hints);
            if(CollectionUtils.isEmpty(result)){
                return null;
            }
            return result.get(0);
        } catch (Exception e) {
            Map<String,String> tag = new HashMap<>();
            tag.put("driverId",drvId.toString());
            tag.put("driverLeaveId",id.toString());
            logger.error("DrvDriverLeaveRepositoryImpl_queryDriverLeaveById_ex",e,tag);
            throw new BizException("DrvDriverLeaveRepositoryImpl_queryDriverLeaveById_ex");
        }
    }

    @Override
    public int update(DrvDriverLeavePO drvPo) {
        return drvDriverLeaveRepo.update(drvPo);
    }
}
