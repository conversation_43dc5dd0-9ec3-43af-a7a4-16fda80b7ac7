package com.ctrip.dcs.tms.transport.infrastructure.port.repository;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.dal.*;

import java.sql.*;
import java.util.*;

/**
 * 运力组信息
 * <AUTHOR>
 * @Date 2020/3/3 16:28
 */
public interface TransportGroupRepository {

    DalRepository<TspTransportGroupPO> getTspTransportGroupRepo();

    /**
     * 新增运力组
     * @param transportGroupPO
     * @return
     */
    long addTransportGroup(TspTransportGroupPO transportGroupPO);

    /**
     * 更新运力组
     * @param transportGroupPO
     * @return
     */
    void updateTransportGroup(TspTransportGroupPO transportGroupPO);

    /**
     * 查询运力组详情
     * @param transportGroupId
     * @return
     */
    TspTransportGroupPO queryTransportGroupDetail(Long transportGroupId);

    /**
     * 统计运力组条数
     * @param param
     * @return
     */
    Long countTransportGroupList(QueryTransportGroupListParam param);

    /**
     * 查询运力组列表
     * @param param
     * @return
     */
    List<TspTransportGroupPO> queryTransportGroupList(QueryTransportGroupListParam param);

    /**
     * 城市+车型+模式查询
     * @param cityId
     * @param vehicleTypeId
     * @param transportGroupMode
     * @return
     */
    List<TspTransportGroupPO> queryTransportGroupList(Long cityId,Long vehicleTypeId,Integer transportGroupMode);

    /**
     * 查询运力组信息
     * @param queryModel
     * @return
     */
    List<TspTransportGroupPO> queryTransportGroups(QueryTransportGroupModel queryModel);

    /**
     * 通过运力组Id 获取
     * @param transportIds
     * @return
     * @throws SQLException
     */
     List<TspTransportGroupPO> queryTspTransportByIds(List<Long> transportIds) throws SQLException;

    /**
     * 全量运力组
     * @param listDO
     * @return
     */
     List<TspTransportGroupPO> queryAllTransportGroup(QueryAllTransGroupListDO listDO) throws SQLException;

    /**
     * 根据合同和城市查询有效运力组
     * @param contractId
     * @return
     */
    List<TspTransportGroupPO> queryTransGByContractAndCity(Long contractId) throws SQLException;

    /**
     * 批量更新状态禁用标识
     * @param transportIds
     * @param statusDisable
     * @return
     */
    int updateStatusDisable(List<Long> transportIds,Integer statusDisable) throws SQLException;

    /**
     * 查询运力组名称条数
     * */
    int queryTransportNameCount(String transportName, Long transportGroupId);

    List<TspTransportGroupPO> queryTransportGroupBySupplierIdAndId(List<Long> transportIds, Long supplierId);

    /**
     * 通过运力组Id 获取
     */
    List<TspTransportGroupPO> queryTspTransportBaseByIds(List<Long> transportIds);

    /**
     * 查询报名制运力组
     */
    List<TspTransportGroupPO> queryTransportGroupBMZByIds(List<Long> transportIds,Integer transportGroupMode,int beginPage,int pageSize);

    int countTransportGroupBMZByIds(List<Long> transportIds,Integer transportGroupMode);

    /**
     * 查询司机关联的指定合作模式的运力组
     */
    List<TspTransportGroupPO> queryDriverRelatedTransportGroupByModeList(List<Long> transportIds, List<Integer> transportGroupModes);

    List<TspTransportGroupPO> queryApplyTransportGroupByCityIds(List<Long> cityIds,List<Long> vehicleTypeIds);

    int  queryCountTransportGroupAll(Long id);

    List<TspTransportGroupPO>  queryTransportGroupAll(Long id,int beginPage,int pageSize);

    int updateTransportGroupCountryId(Long transportId,String countryid) throws SQLException;

    List<TspTransportGroupPO>  queryProductLine(List<Long> transportGroupIds);

    List<TspTransportGroupPO>  querySupplierId(List<Long> transportGroupIds);

    List<TspTransportGroupPO> queryGroupBySupplierAndCity(List<Long> supplierIds,List<Long> cityIds);

    /**
     * 根据运力组ID分页查询运力组信息
     * @param transportGroupIdFrom 起始运力组ID
     * @param beginDate 开始时间
     * @param endDate 结束时间
     * @param pageNo 页码
     * @param pageSize 每页大小
     * @return 运力组信息列表
     * @throws SQLException SQL异常
     */
    List<TspTransportGroupPO> queryTransportGroupByIdFromAndPage(Long transportGroupIdFrom, String beginDate, String endDate, int pageNo, int pageSize) throws SQLException;
}
