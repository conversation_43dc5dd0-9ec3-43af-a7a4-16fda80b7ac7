package com.ctrip.dcs.tms.transport.infrastructure.port.repository.model;


public class StepChildModRecordParams {

    //子标签ID
    private Long stepChildId;

    //父ID
    private Long parentStepId;

    //子项原值
    private Integer orgCheckStatus;

    //子项目标值
    private Integer tarCheckStatus;

    //子项类型
    private Integer childItem;

    private String modifyUser;

    private Integer certificateType;

    public Long getParentStepId() {
        return parentStepId;
    }

    public void setParentStepId(Long parentStepId) {
        this.parentStepId = parentStepId;
    }

    public Integer getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(Integer certificateType) {
        this.certificateType = certificateType;
    }

    public Long getStepChildId() {
        return stepChildId;
    }

    public void setStepChildId(Long stepChildId) {
        this.stepChildId = stepChildId;
    }

    public Integer getOrgCheckStatus() {
        return orgCheckStatus;
    }

    public void setOrgCheckStatus(Integer orgCheckStatus) {
        this.orgCheckStatus = orgCheckStatus;
    }

    public Integer getTarCheckStatus() {
        return tarCheckStatus;
    }

    public void setTarCheckStatus(Integer tarCheckStatus) {
        this.tarCheckStatus = tarCheckStatus;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public Integer getChildItem() {
        return childItem;
    }

    public void setChildItem(Integer childItem) {
        this.childItem = childItem;
    }
}
