package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc;

import com.ctrip.basebiz.ai.aiplatform.contract.api.AiPlatformServiceClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.ctrip.basebiz.ai.aiplatform.contract.common.AiPlatformRequest;

@Configuration
public class AiPlatformProxy {

    @Bean("aiPlatformRequest")
    public AiPlatformServiceClient aiPlatformServiceClient() {
        return new AiPlatformRequest();
    }

}
