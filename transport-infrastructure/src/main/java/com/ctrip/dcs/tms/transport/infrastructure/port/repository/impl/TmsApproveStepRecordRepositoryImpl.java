package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;


import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.ctriposs.baiji.exception.*;
import org.springframework.stereotype.*;

import java.sql.*;
import java.util.*;

@Repository(value = "tmsApproveStepRecordRespository")
public class TmsApproveStepRecordRepositoryImpl implements TmsApproveStepRecordRespository {

    private DalRepository<ApproveStepRecordPO> stepRecordRepo;

    public TmsApproveStepRecordRepositoryImpl() throws SQLException {
        stepRecordRepo = new DalRepositoryImpl<>(ApproveStepRecordPO.class);
    }


    @Override
    public Long insert(ApproveStepRecordPO approveStepRecordPO) throws SQLException {
        KeyHolder keyHolder = new KeyHolder();
        stepRecordRepo.insert(new DalHints(), keyHolder, approveStepRecordPO);
        return keyHolder.getKey().longValue();
    }

    @Override
    public List<ApproveStepRecordPO> queryList(Long recruitingId,Integer recruitingType,List<Long> totalRecruitingIds) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("recruiting_id", recruitingId, Types.BIGINT, false);
            builder.and().equal("recruiting_type", recruitingType, Types.INTEGER, false);
            builder.and().in("recruiting_record_id", totalRecruitingIds, Types.BIGINT, false);
            builder.orderBy("id", false);
            return stepRecordRepo.getDao().query(builder, hints);
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

}
