package com.ctrip.dcs.tms.transport.infrastructure.port.repository;


import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TransportTrackRecordPO;
import com.ctrip.igt.framework.dal.DalRepository;

import java.sql.SQLException;
import java.util.List;

public interface TransportTrackRecordRepository {


    DalRepository<TransportTrackRecordPO> getTransportTrackRecordRepo();

    TransportTrackRecordPO queryByPk(Long id);

    Long insert(TransportTrackRecordPO po) throws SQLException;

    List<TransportTrackRecordPO> queryTrackByTransportGroupId(Long transportGroupId,Integer operationType);
}