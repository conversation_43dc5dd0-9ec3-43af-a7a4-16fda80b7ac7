package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.common.result.*;
import com.google.common.base.*;
import com.google.common.collect.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.time.format.*;
import java.util.*;

/**
 * 进单配置操作
 * <AUTHOR>
 * @Date 2020/3/6 14:04
 */
@Service("inOrderConfigQueryService")
public class InOrderConfigQueryServiceImpl implements InOrderConfigQueryService {

    @Autowired
    private InOrderConfigRepository inOrderConfigRepository;

    private static DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm");

    /**
     * 检查简单配置是否合法（返回true表示通过合法）
     * @param configSOATypes
     * @return
     */
    @Override
    public boolean checkConfig(List<InOrderConfigSOAType> configSOATypes) {
        Map<Integer,Set<String>> locationMap = Maps.newHashMap();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm");
        boolean anyMatch = configSOATypes.stream().anyMatch(inOrderConfigSOAType -> {
            //检查点位是否重复
            Integer locationType = inOrderConfigSOAType.getLocationType();
            Set<String> locationSet = locationMap.get(locationType);
            if (locationSet == null) {
                locationSet = Sets.newHashSet();
                locationMap.put(locationType,locationSet);
            }
            boolean contains = locationSet.contains(inOrderConfigSOAType.getLocationCode());
            if (!contains) {
                locationSet.add(inOrderConfigSOAType.getLocationCode());
                //检查时段是否重复
                Set<TimeFrame> timeSet = Sets.newHashSet();
                return inOrderConfigSOAType.getConfigItems().stream().anyMatch(configItemSOAType -> {
                    if (Strings.isNullOrEmpty(configItemSOAType.getTime())) {
                        return false;
                    }
                    String[] split = configItemSOAType.getTime().split("-");
                    TimeFrame timeFrame = new TimeFrame(split[0],split[1],dateTimeFormatter);
                    boolean flag = timeSet.contains(timeFrame);
                    if (!flag) {
                        timeSet.add(timeFrame);
                    }
                    return flag;
                });
            }
            return contains;
        });
        return !anyMatch;
    }

    @Override
    public Result<List<TspIntoOrderConfigPO>> queryInOrderConfigs(Long transportGroupId,Integer active) {
        List<TspIntoOrderConfigPO> tspIntoOrderConfigPOS = inOrderConfigRepository.queryInOrderConfigs(transportGroupId, active);
        return Result.Builder.<List<TspIntoOrderConfigPO>>newResult()
                .success()
                .withData(tspIntoOrderConfigPOS)
                .build();
    }
}
