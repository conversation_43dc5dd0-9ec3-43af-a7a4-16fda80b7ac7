package com.ctrip.dcs.tms.transport.application.query;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.dto.QueryDriverFromCacheParamDTO;

import java.util.*;

/**
 * <AUTHOR>
 * 2021-10-28 19:32
 * 司机缓存改版
 */
public interface DriverCacheAgent {

    String clearLevel = "clear";
    String DRV_CACHE_ERROR_TITLE = "DrvCacheError";
    String DRV_CACHE_INFO_TITLE = "DrvCacheInfo";
    String DRV_CACHE_WARN_TITLE = "DrvCacheWarn";

    /**
     * 查询司机综合信息
     * @param req 请求参数
     * @return List<DriverInfo> 缓存实体列表
     */
    List<DriverInfo> queryDriver(QueryDriverFromCacheParamDTO req);

}