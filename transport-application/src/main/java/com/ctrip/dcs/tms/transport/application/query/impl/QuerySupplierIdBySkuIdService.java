package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.application.dto.SkuTransportGroupDTO;
import com.ctrip.dcs.tms.transport.application.query.IQuerySupplierIdBySkuIdService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupSkuAreaRelationPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.thread.IThreadPoolService;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportGroupRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TspTransportGroupSkuArearRelationRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.QueryTransportGroupSkuInfoModel;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

@Component
public class QuerySupplierIdBySkuIdService implements IQuerySupplierIdBySkuIdService {
    private static final Logger logger = LoggerFactory.getLogger(QuerySupplierIdBySkuIdService.class);
    @Autowired
    private TransportGroupRepository transportGroupRepository;
    @Autowired
    private TspTransportGroupSkuArearRelationRepository relationRepository;
    @Autowired
    private IThreadPoolService poolService;

    @Override
    public Map<Long,SkuTransportGroupDTO> querySupplierIdBySkuId(List<Long> skuIds) {
        try{
            //查询skuId关联的运力组id
            Map<Long,Set<Long>> relationMap = queryTransportId(skuIds);
            if(CollectionUtils.isEmpty(relationMap)){
                logger.info("querySupplierIdBySkuId_relationMap_null", JsonUtil.toJson(skuIds));
                return new HashMap<>();
            }
            //解析获取运力组id和供应商id的关系
            Map<Long,TspTransportGroupPO> supplierIdMap = queryTransportGroup(relationMap);
            if(CollectionUtils.isEmpty(supplierIdMap)){
                logger.info("querySupplierIdBySkuId_supplierIdMap_null", JsonUtil.toJson(skuIds));
                return new HashMap<>();
            }
            //解析获取skuId和供应商id的关系
            return querySkuSupplierMap(relationMap,supplierIdMap);
        }catch (Exception e){
            logger.error("querySupplierIdBySkuId_ex", JsonUtil.toJson(skuIds),e,new HashMap<>());
            throw new BizException("querySupplierIdBySkuId_ex");
        }
    }

    /**
     * 解析获取skuId和供应商id的关系
     * @param relationMap
     * @param supplierIdMap
     * @return
     */
    private Map<Long,SkuTransportGroupDTO> querySkuSupplierMap( Map<Long,Set<Long>> relationMap, Map<Long,TspTransportGroupPO> supplierIdMap){
        Map<Long,SkuTransportGroupDTO> skuSupplierMap = new HashMap<>();
        for (Map.Entry<Long, Set<Long>> en : relationMap.entrySet()) {
            for (Long transportGroupId : en.getValue()) {
                if(!supplierIdMap.containsKey(transportGroupId)){
                    continue;
                }
                if(!skuSupplierMap.containsKey(en.getKey())){
                    skuSupplierMap.put(en.getKey(),new SkuTransportGroupDTO());
                }
                //供应商id
                skuSupplierMap.get(en.getKey()).getSupplierIdList().add(supplierIdMap.get(transportGroupId).getSupplierId());
                //邮箱
                String email = supplierIdMap.get(transportGroupId).getInformEmail();
                if(StringUtils.isEmpty(email)){
                    continue;
                }
                skuSupplierMap.get(en.getKey()).getEmailList().add(email);
            }
        }
        return skuSupplierMap;
    }
    /**
     * 查询skuId和运力组id的关系
     * @param skuIds
     * @return
     */
    private Map<Long,Set<Long>> queryTransportId(List<Long> skuIds){
        QueryTransportGroupSkuInfoModel queryModel = new QueryTransportGroupSkuInfoModel();
        queryModel.setSkuIds(skuIds);
        queryModel.setActive(Boolean.TRUE);
        List<TspTransportGroupSkuAreaRelationPO> relationPOS = relationRepository.queryTransportGroupSkuIds(queryModel);
        if(CollectionUtils.isEmpty(relationPOS)){
            return new HashMap<>();
        }
        //解析获取skuId和运力组id的关系
        Map<Long,Set<Long>> relationMap = new HashMap<>();
        for (TspTransportGroupSkuAreaRelationPO relationPO : relationPOS) {
            if(!relationMap.containsKey(relationPO.getSkuId())){
                relationMap.put(relationPO.getSkuId(),new HashSet<>());
            }
            relationMap.get(relationPO.getSkuId()).add(relationPO.getTransportGroupId());
        }
        return relationMap;
    }
    /**
     * 异步多线程查询运力组信息
     * @param relationMap
     * @return
     */
    private  Map<Long, TspTransportGroupPO> queryTransportGroup(Map<Long,Set<Long>> relationMap){
        //汇总所有的运力组id
        Set<Long> transportGroupIds = new HashSet<>();
        for (Set<Long> value : relationMap.values()) {
            transportGroupIds.addAll(value);
        }
        //按照100进行分割
        List<List<Long>> partitionList = Lists.partition(new ArrayList<>(transportGroupIds),100);
        //获取线程池
        ExecutorService service = poolService.getPool(this.getClass().getSimpleName());
        if(service == null){
            logger.info("CThreadPool_notExist","QuerySupplierIdBySkuIdService");
            return new HashMap<>();
        }
        //根据运力组id查询供应商id 异步查询
        List<Future<List<TspTransportGroupPO>>> futureList = new ArrayList<>();
        for (List<Long> transportIds : partitionList) {
            futureList.add(service.submit(()->transportGroupRepository.querySupplierId(transportIds)));
        }
        //同步获取结果
        List<TspTransportGroupPO> tspTransportGroupPOS = new ArrayList<>();
        for (Future<List<TspTransportGroupPO>> listFuture : futureList) {
            tspTransportGroupPOS.addAll(getFromFuture(listFuture));
        }
        //无结果返回空map
        if(CollectionUtils.isEmpty(tspTransportGroupPOS)){
            return new HashMap<>();
        }
        //封装运力组id和供应商id的关系 key=运力组id  value=供应商id 和 email
        Map<Long,TspTransportGroupPO> supplierIdMap = new HashMap<>();
        for (TspTransportGroupPO tspTransportGroupPO : tspTransportGroupPOS) {
            supplierIdMap.put(tspTransportGroupPO.getTransportGroupId(),tspTransportGroupPO);
        }
        return supplierIdMap;
    }
    /**
     * 读取异步查询结果
     * @param listFuture
     * @return
     */
    private List<TspTransportGroupPO> getFromFuture(Future<List<TspTransportGroupPO>> listFuture){
        try{
            //500毫秒超时
            List<TspTransportGroupPO> result = listFuture.get(500, TimeUnit.MILLISECONDS);
            if(CollectionUtils.isEmpty(result)){
                return new ArrayList<>();
            }
            return result;
        }catch (Exception e){
            logger.warn("QuerySupplierIdBySkuIdService_getFromFuture_timeout","");
            return new ArrayList<>();
        }
    }
}
