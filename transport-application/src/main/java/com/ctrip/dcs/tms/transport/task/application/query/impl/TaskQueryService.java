package com.ctrip.dcs.tms.transport.task.application.query.impl;

import com.ctrip.dcs.tms.transport.api.model.IsNeedCreateTaskRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryTaskConfigRequestType;
import com.ctrip.dcs.tms.transport.task.infrastructure.common.enums.NeedCreateTaskEnum;
import com.ctrip.igt.framework.common.result.Result;

public interface TaskQueryService {
  Result<String> queryTaskConfig(QueryTaskConfigRequestType requestType);

  Result<NeedCreateTaskEnum> isNeedCreateTask(IsNeedCreateTaskRequestType requestType);
}
