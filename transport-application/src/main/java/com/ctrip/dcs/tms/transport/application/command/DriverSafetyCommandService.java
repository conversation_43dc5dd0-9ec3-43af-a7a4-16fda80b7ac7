package com.ctrip.dcs.tms.transport.application.command;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.igt.framework.common.result.*;

import java.util.*;

/**
 * <AUTHOR>
 * 2021-04-14 14：26
 * 核酸及疫苗报告检验
 */
public interface DriverSafetyCommandService {

    /**
     * 1.先判断OCR识别结果的个数是否只有1个或者都没有，如果没有直接不通过
     * 2.判断是否为同一个人
     * */

    /**
     * 模式匹配司机是否为同一个人依据 司机姓名,身份证号
     *
     * @param driverName    司机姓名
     * @param idNumber      身份证号
     * @param ocrDriverName ocr识别提取的司机姓名 (存在掩码)
     * @param ocrIdNumber   ocr识别提取的身份证号 (存在掩码)
     */
    Result<EpidemicPreventionControlEnum.ReportResultEnum> baseVerifySameDriver(String driverName, String idNumber, String ocrDriverName, String ocrIdNumber);


    /**
     * 核酸报告检查是否通过
     *
     * @param cityId                      核酸报告有效期不同城市时间不同
     * @param samplingDate                核酸报告采样日期
     * @param nucleicAcidTestingResult    核酸报告采样结果
     * @param ocrSamplingDate             ocr识别提取的核酸报告采样日期
     * @param ocrNucleicAcidTestingResult ocr识别提取的核酸报告采样结果
     */
    Result<EpidemicPreventionControlEnum.ReportResultEnum> checkNucleicAcidTestingReport(Long cityId, Date samplingDate, String nucleicAcidTestingResult, Date ocrSamplingDate, String ocrNucleicAcidTestingResult);

    /**
     * 获取对外文案描述
     *
     * @param detectionType    检测类型
     * @param reportResultEnum 结果枚举
     */
    String getExternalDescribe(int detectionType, Long drvId, Long cityId, int days, EpidemicPreventionControlEnum.ReportResultEnum reportResultEnum);

    /**
     * OCR 结果量检查
     *
     * @param isNucleicAcidDetectionType 检验类型
     * @param ocrRequest                 ocr识别对象
     */
    Boolean checkOcrInfoComplete(SaveDriverSafetyInfoSOARequestType ocrRequest, Boolean isNucleicAcidDetectionType);
}