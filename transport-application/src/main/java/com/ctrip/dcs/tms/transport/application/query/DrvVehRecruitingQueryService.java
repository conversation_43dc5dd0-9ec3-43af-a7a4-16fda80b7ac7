package com.ctrip.dcs.tms.transport.application.query;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.igt.framework.common.base.*;
import com.ctrip.igt.framework.common.result.*;

import java.util.*;

/**
 * 司机查询类接口
 * <AUTHOR>
 * @Date 2020/3/17 14:58
 */
public interface DrvVehRecruitingQueryService {

    /**
     * 司机车辆审批详情
     * @param drvRecruitingId
     * @return
     */
    Result<DrvVehRecruitingDetailSOADTO> queryDrvVehRecruitingDetail(Long drvRecruitingId,Integer recruitingType);

    /**
     * 司机车辆审批操作记录
     * @param drvRecruitingId
     * @return
     */
    Result<List<QueryDrvVehRecruitingModRrdSOADTO>> queryDrvVehRecruitingModRrd(Long drvRecruitingId,Integer recruitingType);

  Result<List<QueryDrvVehRecruitingModRrdSOADTO>> queryDrvVehRecruitingModRrd(List<Long> drvRecruitingIdList,
    Integer recruitingType);

  /**
     * 查询供应商是否有资质生成二维码
     * @param supplierId
     * @return
     */
    Result<String> checkSupplierQualification(Long supplierId);


    /**
     * 首页代办事项
     * @param requestType
     * @return
     */
    Result<List<TodoListTypeSOA>> todoListCount(TodoListCountSOARequestType requestType);

    /**
    　* @description: 检验境内司机的唯一性(手机号,身份证号)
    　* <AUTHOR>
    　* @date 2021/11/1 11:02
    */
    Result<Boolean> checkRecruitingDrvOnly(String drvPhone,String drvIdCard);

    /**
    　* @description: 检验境内车辆的唯一性(车牌号,vin码)
    　* <AUTHOR>
    　* @date 2021/11/1 11:02
    */
    Result<Boolean> checkRecruitingVehicleOnly(String vehicleLicense,String vin);

    Result<PageHolder<QueryRecruitingDrvForCrawlerDTO>> queryRecruitingDrvForCrawler(QueryRecruitingDrvForCrawlerRequestType request);

    Result<PageHolder<QueryRecruitingVehForCrawlerDTO>> queryRecruitingVehForCrawler(QueryRecruitingVehForCrawlerRequestType request);

    /**
     * 司机车辆审批详情-H5
     * @param drvRecruitingId
     * @return
     */
    Result<DrvVehRecruitingDetailSOADTO> queryDrvVehRecruitingDetailFromH5(DrvVehRecruitingDetailFromH5RequestType requestType);

    Boolean h5VerificationPermissions(String drvPhone,String verificationCode);



}
