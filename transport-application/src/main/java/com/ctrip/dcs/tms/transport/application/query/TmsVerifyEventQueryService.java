package com.ctrip.dcs.tms.transport.application.query;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.igt.framework.common.result.*;

import java.util.*;

/**
 * 人脸验证事件
 */
public interface TmsVerifyEventQueryService {

    Result<DrvVerifyParamsSOADTO> queryDrvVerifyParams(DrvVerifyParamsSOARequestType requestType);

    Result<DrvVerifyResultSOADTO> queryDrvVerifyResult(DrvVerifyResultSOARequestType requestType);

    Result<VehicleVerifyResultSOADTO> queryVehicleVerifyResult(VehicleVerifyResultSOARequestType requestType);

    Result<DrvIsNeedVerifySOADTO> drvIsNeedVerify(DrvIsNeedVerifySOARequestType requestType);

    Result<List<QueryDrvVerifyEventListSOADTO>> queryDrvVerifyEventList(QueryDrvVerifyEventListSOARequestType requestType);

    Boolean judgeVerifyEventTimes(Long verifySourceId, TmsTransportConstant.VerifyTypeEnum verifyTypeEnum, Integer verifyEventType);

    Result<Boolean> drvFaceVerifyCountTransfinite(Long drvId);


}
