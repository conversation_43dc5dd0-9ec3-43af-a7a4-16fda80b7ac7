package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.arch.distlock.*;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.config.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.platform.dal.dao.annotation.*;
import com.ctriposs.baiji.exception.*;
import com.google.common.collect.*;
import credis.java.client.*;
import credis.java.client.pipeline.*;
import credis.java.client.util.CacheFactory;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.*;

@Service
public class TmsVerifyEventCommandServiceImpl implements TmsVerifyEventCommandService {

    private static final String FACE_VERIFY_FAIL_COUNT_KEY = "face_verify_fail_count_key_";

    @Autowired
    private TmsVerifyEventRepository eventRepository;
    @Autowired
    TmsDrvLoginInformationRepository informationRepository;
    @Autowired
    TmsTransportQconfig qconfig;
    @Autowired
    TmsVerifyEventQueryService eventQueryService;
    @Autowired
    TmsModRecordCommandService tmsModRecordCommandService;
    @Autowired
    private DistributedLockConfig distributedLockConfig;
    @Autowired
    TmsVerifyRecordRepository tmsVerifyRecordRepository;
    private CacheProvider provider = CacheFactory.getProvider("dcs_tms");

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> pushDrvVerifyTimes(DrvVerifyTimesSOARequestType requestType) {
        try {
            List<Long> ids = Arrays.stream(requestType.getVerifyEventId().split("_")).mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
            if(CollectionUtils.isEmpty(ids)){
                return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
            }
            List<TmsVerifyEventPO> eventPOList = eventRepository.queryVerifyEventByIds(ids);
            if(CollectionUtils.isEmpty(eventPOList)){
                return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
            }

            List<TmsVerifyEventPO> updateList = Lists.newArrayList();
            for(TmsVerifyEventPO tmsVerifyEventPO : eventPOList){
                int noticeTimes = tmsVerifyEventPO.getNoticeTimes();
                //首次提醒,变更验证超时时间
                if(noticeTimes == 0){
                    tmsVerifyEventPO.setVerifyEndTime(DateUtil.getTimestampDisMinu(new Date(), qconfig.getVerifyTimeOut()));
                }
                tmsVerifyEventPO.setNoticeTimes(noticeTimes + 1);
                tmsVerifyEventPO.setDatachangeLasttime(DateUtil.getNow());
                updateList.add(tmsVerifyEventPO);
            }
            eventRepository.batchUpdate(updateList);
            return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> updateVerifyEvent(UpdateDrvVerifyEventSOARequestType requestType) {
        try {
            TmsVerifyEventPO tmsVerifyEventPO = eventRepository.queryByPk(requestType.getVerifyEventId());
            if(Objects.isNull(tmsVerifyEventPO)){
                return Result.Builder.<Boolean>newResult().fail().withMsg("VerifyEvent is empty").build();
            }
            eventRepository.updateVerifyEventCheckTime(requestType.getVerifyEventId(), requestType.getVerifyCheckTime(), requestType.getModifyUser());
            //存储变更记录
            tmsModRecordCommandService.tmsUpdateVerifyEventModRrd(requestType.getVerifyEventId(),DateUtil.timestampToString(tmsVerifyEventPO.getVerifyStartTime(),DateUtil.YYYYMMDDHHMMSS),requestType.getVerifyCheckTime(),requestType.getModifyUser());
            return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> insertConventionalEvent(CheckDrvLoginRequestType requestType, DrvDriverPO drvDriverPO) {
        Map<String, Long> sourceIdMap = Maps.newHashMap();
        sourceIdMap.put(TmsTransportConstant.DRV_SOURCE_ID_KEY, drvDriverPO.getDrvId());
//        sourceIdMap.put(TmsTransportConstant.VEHICLE_SOURCE_ID_KEY, drvDriverPO.getVehicleId());
        try {
            if(!qconfig.getDrvVerifySwitch() || Objects.equals(drvDriverPO.getInternalScope(), AreaScopeTypeEnum.OVERSEAS.getCode()) || BaseUtil.getLongSet(qconfig.getExemptVerifyDrvIdList()).contains(drvDriverPO.getDrvId())){
                return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
            }
            List<Long> verifyCityIds = qconfig.getDrvVerifyCitySwitchList();
            //-1 代表开全量
            if(!verifyCityIds.contains(drvDriverPO.getCityId()) && !verifyCityIds.contains(-1L)){
                return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
            }
            //查询司机是否登录过
            List<TmsDrvLoginInformationPO> informationPOList = informationRepository.queryDrvLoginInfoLimit(drvDriverPO.getDrvId(), 1, 1);
            if (CollectionUtils.isEmpty(informationPOList)) {
                return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
            }

            TmsDrvLoginInformationPO informationPO = informationPOList.get(0);
            String nowDateStr = DateUtil.dateToString(new Date(),DateUtil.YYYYMMDDHHMMSS);
            //司机更新设备，需要新增一个常规事件
            if (StringUtils.isNotEmpty(informationPO.getDrvImei()) && !Objects.equals(requestType.getDriverImei(), informationPO.getDrvImei())) {
                List<Long> sourceIds = Lists.newArrayList();
                for (Map.Entry<String, Long> entry : sourceIdMap.entrySet()) {
                    if(entry.getValue() > 0){
                        sourceIds.add(entry.getValue());
                    }
                }
                Map<Long,List<TmsVerifyEventPO>> eventMap = Maps.newHashMap();
                List<TmsVerifyEventPO> eventPOList = eventRepository.queryWaitVerifyEvent(sourceIds, null, TmsTransportConstant.VerifyStatusEnum.NO_VERIFY.getCode(), TmsTransportConstant.VerifyEventTypeEnum.CONVENTIONAL_EVENT.getCode(), false);
                if(CollectionUtils.isNotEmpty(eventPOList)){
                    eventMap = eventPOList.stream().collect(Collectors.groupingBy(TmsVerifyEventPO::getVerifySourceId));
                }
                for (Map.Entry<String, Long> entry : sourceIdMap.entrySet()) {
                    String key = entry.getKey();
                    if(MapUtils.isEmpty(eventMap) || CollectionUtils.isEmpty(eventMap.get(entry.getValue()))){
                        if (Objects.equals(TmsTransportConstant.DRV_SOURCE_ID_KEY, key)) {
                            eventRepository.insert(buildBatchInsertEventPO(requestType.getDriverImei(), TmsTransportConstant.VerifyEventTypeEnum.CONVENTIONAL_EVENT.getCode(), entry.getValue(), TmsTransportConstant.VerifySourceTypeEnum.DRV.getCode(), TmsTransportConstant.VerifyTypeEnum.FACE.getCode(),nowDateStr, drvDriverPO.getDrvName()));
                        }
//                        if (Objects.equals(TmsTransportConstant.VEHICLE_SOURCE_ID_KEY, key) && entry.getValue() > 0) {
//                            eventRepository.insert(buildBatchInsertEventPO(requestType.getDriverImei(), TmsTransportConstant.VerifyEventTypeEnum.CONVENTIONAL_EVENT.getCode(), entry.getValue(), TmsTransportConstant.VerifySourceTypeEnum.VEHICLE.getCode(), TmsTransportConstant.VerifyTypeEnum.VEHICLE.getCode(),nowDateStr, drvDriverPO.getDrvName()));
//                        }
                    }
                }
            }
            return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Boolean insertFaceResultedEvent(String drvImei, Long drvId, String verifyResult, String verifyResultDesc, String modifyUser) {
        try {
            insertResultedEvent(drvImei,drvId, TmsTransportConstant.VerifySourceTypeEnum.DRV,verifyResult,verifyResultDesc, TmsTransportConstant.VerifyTypeEnum.FACE,modifyUser);
            return Boolean.TRUE;
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Boolean insertVehicleResultedEvent(String drvImei, Long vehicleId, String verifyResult, String verifyResultDesc, String modifyUser) {
        //公共返回结果并将事件标注为已验证同事新生成一个人脸事件
        try {
            insertResultedEvent(drvImei,vehicleId, TmsTransportConstant.VerifySourceTypeEnum.VEHICLE,verifyResult,verifyResultDesc, TmsTransportConstant.VerifyTypeEnum.VEHICLE,modifyUser);
            return Boolean.TRUE;
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    public Boolean insertResultedEvent(String drvImei,Long eventSourceId,TmsTransportConstant.VerifySourceTypeEnum verifySourceTypeEnum, String verifyResult,String verifyResultDesc, TmsTransportConstant.VerifyTypeEnum verifyTypeEnum,String modifyUser){
        //公共返回结果并将事件标注为已验证同事新生成一个人脸事件
        DLock lock = distributedLockConfig.getDistributedLockInsertVerify(eventSourceId,verifySourceTypeEnum.getCode());
        boolean locked = false;
        try {
            if (lock != null && (locked = lock.tryLock(DistributedLockConfig.PROCESSING_TIME, TimeUnit.SECONDS))) {
                //车辆验证没有事件，代表主动认证，生成一个主动认证事件，
                List<TmsVerifyEventPO> eventPOList = eventRepository.queryWaitVerifyEvent(Arrays.asList(eventSourceId), verifyTypeEnum.getCode(),TmsTransportConstant.VerifyStatusEnum.NO_VERIFY.getCode(), null, false);
                //必须在提醒司机后，司机认证通过 才算是完成本次认证
                //若抽查事件已生成，但未到提醒时间 未提醒司机， 司机主动来认证 不能算抽查认证完成
                List<Long> verifyIds = this.judgeIsIndependent(eventPOList);
                if (CollectionUtils.isEmpty(verifyIds)) {
                    eventRepository.insert(buildInsertIndependentEvent(drvImei,eventSourceId, verifyResult,verifyResultDesc,
                            verifySourceTypeEnum.getCode(),verifyTypeEnum.getCode(),modifyUser));
                    return Boolean.TRUE;
                }

                Boolean spotCheckFlag = Boolean.FALSE;
                String verifyStartTime = "";
                //检查是否有抽查事件
                for (TmsVerifyEventPO verifyEventPO : eventPOList) {
                    if (verifyEventPO.getNoticeTimes()!=null && verifyEventPO.getNoticeTimes() > 0 && Objects.equals(verifyEventPO.getVerifyReasonStatus(), TmsTransportConstant.VerifyEventTypeEnum.SPOT_CHECK_EVENT.getCode())) {
                        spotCheckFlag = Boolean.TRUE;
                        verifyStartTime = DateUtil.timestampToString(verifyEventPO.getVerifyStartTime(), DateUtil.YYYYMMDDHHMMSS);
                        break;
                    }
                }
                //只有验证通过了，才会变更
                if(StringUtils.isNotEmpty(verifyResult) && StringUtils.equals(verifyResult,"0")){
                    //修改事件状态
//                eventRepository.updatemsVerifyEventBydriverId(eventSourceId, verifyTypeEnum.getCode(), verifyResult, verifyResultDesc, modifyUser);
                    eventRepository.updateVerifyEventByIds(verifyIds,verifyResult, verifyResultDesc, modifyUser);
                    if (spotCheckFlag && !eventQueryService.judgeVerifyEventTimes(eventSourceId, verifyTypeEnum, TmsTransportConstant.VerifyEventTypeEnum.SPOT_CHECK_EVENT.getCode())) {
                        String secondVerifyStartTime = calculateSecondStartTime(verifyStartTime);
                        if(StringUtils.isNotEmpty(secondVerifyStartTime)){
                            eventRepository.insert(buildBatchInsertEventPO(drvImei, TmsTransportConstant.VerifyEventTypeEnum.SPOT_CHECK_EVENT.getCode(), eventSourceId,
                                    verifySourceTypeEnum.getCode(), verifyTypeEnum.getCode(), secondVerifyStartTime,modifyUser));
                        }
                    }
                }else{
                    eventRepository.updatemsVerifyEventResult(eventSourceId,verifyTypeEnum.getCode(),verifyResult,verifyResultDesc,modifyUser);
                }
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }finally {
            if (locked) {
                lock.unlock();
            }
        }
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public String insertCheckVerifyEvent(DrvIsNeedVerifySOARequestType requestType, Map<String, Long> sourceIdMap, String verifyStartTime) {
        try {
            StringBuilder eventId = new StringBuilder();
            for (Map.Entry<String, Long> entry : sourceIdMap.entrySet()) {
                String key = entry.getKey();
                if (Objects.equals(TmsTransportConstant.DRV_SOURCE_ID_KEY, key)) {
                    //判断人脸事件是否已达当天上限
                    if (!eventQueryService.judgeVerifyEventTimes(entry.getValue(), TmsTransportConstant.VerifyTypeEnum.FACE, requestType.getVerifyEventType())) {
                        Long faceEventId = eventRepository.insert(buildBatchInsertEventPO(requestType.getDriverImei(), requestType.getVerifyEventType(), entry.getValue(), TmsTransportConstant.VerifySourceTypeEnum.DRV.getCode(), TmsTransportConstant.VerifyTypeEnum.FACE.getCode(), verifyStartTime,""));
                        eventId.append(faceEventId);
                    }
                }
                if (Objects.equals(TmsTransportConstant.VEHICLE_SOURCE_ID_KEY, key)) {
                    if (!eventQueryService.judgeVerifyEventTimes(entry.getValue(), TmsTransportConstant.VerifyTypeEnum.VEHICLE, requestType.getVerifyEventType())) {
                        Long vehicleEventId = eventRepository.insert(buildBatchInsertEventPO(requestType.getDriverImei(), requestType.getVerifyEventType(), entry.getValue(), TmsTransportConstant.VerifySourceTypeEnum.VEHICLE.getCode(), TmsTransportConstant.VerifyTypeEnum.VEHICLE.getCode(), verifyStartTime,""));
                        if(sourceIdMap.size() == 1){
                            eventId.append(vehicleEventId);
                        }else {
                            eventId.append("_").append(vehicleEventId);
                        }
                    }
                }
            }
            return eventId.toString();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    public TmsVerifyEventPO buildBatchInsertEventPO(String drvImei, Integer verifyEventType, Long verifySourceId, Integer verifySourceType, Integer verifyType, String verifyStartTime,String modifyUser) {
        TmsVerifyEventPO tmsVerifyEventPO = new TmsVerifyEventPO();
        tmsVerifyEventPO.setVerifySourceId(verifySourceId);
        tmsVerifyEventPO.setVerifySourceType(verifySourceType);
        tmsVerifyEventPO.setVerifyFlag(2);
        tmsVerifyEventPO.setVerifyResultCode("");
        tmsVerifyEventPO.setDrvImei(drvImei);
        tmsVerifyEventPO.setVerifyReasonStatus(verifyEventType);
        tmsVerifyEventPO.setVerifyStartTime(DateUtil.string2Timestamp(verifyStartTime, DateUtil.YYYYMMDDHHMMSS));
        tmsVerifyEventPO.setVerifyNoticeTime(DateUtil.getNow());
        tmsVerifyEventPO.setVerifyEndTime(DateUtil.string2Timestamp(verifyStartTime, DateUtil.YYYYMMDDHHMMSS));
        tmsVerifyEventPO.setVerifyType(verifyType);
        tmsVerifyEventPO.setVerifyStatus(TmsTransportConstant.VerifyStatusEnum.NO_VERIFY.getCode());
        tmsVerifyEventPO.setCreateUser(modifyUser);
        tmsVerifyEventPO.setModifyUser(modifyUser);
        return tmsVerifyEventPO;
    }

    //插入主动事件PO
    public TmsVerifyEventPO buildInsertIndependentEvent(String drvImei, Long verifySourceId,String resultStatus,String resultDesc, Integer verifySourceType, Integer verifyType,String modifyUser) {
        TmsVerifyEventPO tmsVerifyEventPO = new TmsVerifyEventPO();
        tmsVerifyEventPO.setVerifySourceId(verifySourceId);
        tmsVerifyEventPO.setVerifySourceType(verifySourceType);
        tmsVerifyEventPO.setVerifyFlag(1);
        tmsVerifyEventPO.setVerifyResultCode(resultStatus);
        tmsVerifyEventPO.setFailReason(resultDesc);
        tmsVerifyEventPO.setDrvImei(drvImei);
        tmsVerifyEventPO.setVerifyReasonStatus(TmsTransportConstant.VerifyEventTypeEnum.INDEPENDENT_EVENT.getCode());
        tmsVerifyEventPO.setVerifyStartTime(DateUtil.getNow());
        tmsVerifyEventPO.setVerifyNoticeTime(DateUtil.getNow());
        tmsVerifyEventPO.setVerifyEndTime(DateUtil.getNow());
        tmsVerifyEventPO.setVerifyType(verifyType);
        tmsVerifyEventPO.setVerifyStatus(TmsTransportConstant.VerifyStatusEnum.VERIFYED.getCode());
        tmsVerifyEventPO.setCreateUser(modifyUser);
        tmsVerifyEventPO.setModifyUser(modifyUser);
        tmsVerifyEventPO.setVerifyTime(DateUtil.getNow());
        return tmsVerifyEventPO;
    }

    public String calculateSecondStartTime(String firstVerifyStartTime) {
        if (StringUtils.isEmpty(firstVerifyStartTime)) {
            return "";
        }
        String secondVerifyStartTime = DateUtil.calculateVerifyStartTime(qconfig.getVerifyStartTimeRadom());
        Date afterOneDay = DateUtil.dayDisplacement(new Date(), 1);
        String afterOneDayStr = DateUtil.dateToString(afterOneDay, DateUtil.YYYYMMDD);
        Date secondVerifyStartTimeDate =  DateUtil.stringToDate(secondVerifyStartTime, DateUtil.YYYYMMDDHHMMSS);
        Date firstVerifyStartTimeDate = DateUtil.stringToDate(firstVerifyStartTime, DateUtil.YYYYMMDDHHMMSS);
        //判断第二次开始时间和第一次开始时间相差多少小时,如果第二次开始时间>第一次时间,再判断是否大于第二天
        if (secondVerifyStartTimeDate != null && firstVerifyStartTimeDate != null && DateUtil.getDifferenceHour(secondVerifyStartTimeDate, firstVerifyStartTimeDate) >= 1) {
            //判断第二次开始时间是否超过了第二天
            if (secondVerifyStartTimeDate.before(DateUtil.stringToDate(afterOneDayStr + " 00:00:00", DateUtil.YYYYMMDDHHMMSS))) {
                return secondVerifyStartTime;
            }
            //随机到相同小时数
        } else if (secondVerifyStartTimeDate != null && firstVerifyStartTimeDate != null && DateUtil.getDifferenceHour(secondVerifyStartTimeDate, firstVerifyStartTimeDate) == 0) {
            Date secondDate = secondVerifyStartTimeDate;
            Date date = DateUtil.getDayShift(secondDate, 1);
            if (Objects.equals(DateUtil.dateToString(date, DateUtil.YYYYMMDD), afterOneDayStr)) {
                return "";
            }
            if(date != null){
                return DateUtil.dateToString(date, DateUtil.YYYYMMDDHHMMSS);
            }
        } else if (firstVerifyStartTimeDate != null && firstVerifyStartTimeDate.before(secondVerifyStartTimeDate)) {
            return secondVerifyStartTime;
        }
        return "";
    }

    /**
     * 判断是否是主动认证
     * @param eventPOList
     * @return
     */
    @Override
    public List<Long> judgeIsIndependent(List<TmsVerifyEventPO> eventPOList){
        List<Long> verifyIds = Lists.newArrayList();
        if(CollectionUtils.isEmpty(eventPOList)){
            return verifyIds;
        }
        for(TmsVerifyEventPO tmsVerifyEventPO : eventPOList){
            if(Objects.equals(tmsVerifyEventPO.getVerifyReasonStatus(), TmsTransportConstant.VerifyEventTypeEnum.CONVENTIONAL_EVENT.getCode())){
                verifyIds.add(tmsVerifyEventPO.getId());
            }
            if(tmsVerifyEventPO.getNoticeTimes()!=null && tmsVerifyEventPO.getNoticeTimes() > 0 && Objects.equals(tmsVerifyEventPO.getVerifyReasonStatus(), TmsTransportConstant.VerifyEventTypeEnum.SPOT_CHECK_EVENT.getCode())){
                verifyIds.add(tmsVerifyEventPO.getId());
            }
        }
        return verifyIds;
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> saveDrvVerifyResult(SaveDrvVerifyResultRequestType request) {
        try {
            String resultCode = request.getResultCode();
            String resultDesc = request.getResultDesc();
            Long drvId = request.getDriverId();
            //公共返回结果并将事件标注为已验证同事新生成一个人脸事件
            insertFaceResultedEvent("",drvId, resultCode, resultDesc, request.getModifyUser());
            //验证错误次数累计
            if(StringUtils.isNotEmpty(resultCode) && !StringUtils.equals(resultCode,"0")){
                this.verifyFailCountCumulative(drvId);
            }
            //认证失败的需保存司机验证时的图片
            if(qconfig.getNeedSaveResultCodeList().contains(resultCode) || qconfig.getNeedSaveResultCodeList().contains("-1")){
                if(StringUtils.isNotEmpty(request.getVerifyPhoto())){
                    resultDesc = resultDesc + "("+request.getVerifyPhoto()+")";
                }
            }
            //插入验证记录表
            insertVerifyRecord(drvId, TmsTransportConstant.VerifyTypeEnum.FACE, "","",resultCode,resultDesc);
            return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    public Boolean verifyFailCountCumulative(Long driverId){
        String key = FACE_VERIFY_FAIL_COUNT_KEY + driverId;
        DLock lock = distributedLockConfig.getDistributedLockVerifyDrv(driverId);
        boolean locked = false;
        try {
            if (lock != null && (locked = lock.tryLock(DistributedLockConfig.PROCESSING_TIME, TimeUnit.SECONDS))) {
                String failCount = provider.get(key);
                if(StringUtils.isEmpty(failCount)){
                    provider.setex(key, RedisUtils.ONE_DAY, "1");
                }else {
                    CachePipeline cachePipeline = provider.getPipeline();
                    cachePipeline.incr(key);
                    cachePipeline.sync();
                }
            }
            return Boolean.TRUE;
        }catch (Exception e){
            return Boolean.FALSE;
        }finally {
            if (locked) {
                lock.unlock();
            }
        }
    }

    public Long insertVerifyRecord(Long verifySourceId, TmsTransportConstant.VerifyTypeEnum verifyTypeEnum,String requestContent,String responseContent,String verifyResultCode,String verifyFailReason){
        try {
            TmsVerifyRecordPO tmsVerifyRecordPO = new TmsVerifyRecordPO();
            tmsVerifyRecordPO.setVerifySourceId(verifySourceId);
            tmsVerifyRecordPO.setVerifyType(verifyTypeEnum.getCode());
            tmsVerifyRecordPO.setRequestContent(requestContent);
            tmsVerifyRecordPO.setResponseContent(responseContent);
            tmsVerifyRecordPO.setVerifyResultCode(verifyResultCode);
            tmsVerifyRecordPO.setVerifyFailReason(verifyFailReason);
            tmsVerifyRecordPO.setDatachangeCreatetime(DateUtil.getNow());
            tmsVerifyRecordPO.setDatachangeLasttime(DateUtil.getNow());
            tmsVerifyRecordPO.setCreateUser(TmsTransportConstant.TMS_DEFAULT_USERNAME);
            tmsVerifyRecordPO.setModifyUser(TmsTransportConstant.TMS_DEFAULT_USERNAME);
            return tmsVerifyRecordRepository.insert(tmsVerifyRecordPO);
        }catch (Exception e){
            return 0L;
        }
    }

}
