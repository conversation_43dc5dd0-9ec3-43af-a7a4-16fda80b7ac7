package com.ctrip.dcs.tms.transport.application.command;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.common.result.*;

import java.util.*;

/**
 * 人脸校验事件接口
 */
public interface TmsVerifyEventCommandService {


    Result<Boolean> pushDrvVerifyTimes(DrvVerifyTimesSOARequestType requestType);

    Result<Boolean> updateVerifyEvent(UpdateDrvVerifyEventSOARequestType requestType);

    /**
     * 司机设备变更，插入常规事件
     * @param drvImei
     * @param drvDriverPO
     * @return
     */
    Result<Boolean> insertConventionalEvent(CheckDrvLoginRequestType requestType, DrvDriverPO drvDriverPO);

    /**
     * 人脸校验后,生成一个新事件
     * @return
     */
    Boolean insertFaceResultedEvent(String drvImei,Long drvId,String verifyResult, String verifyResultDesc, String modifyUser);

    /**
     * 车辆校验后,生成一个新事件
     * @return
     */
    Boolean insertVehicleResultedEvent(String drvImei,Long drvId,String verifyResult, String verifyResultDesc, String modifyUser);


    /**
     * 抽查事件类型如果没事件，测新增抽查事件
     * @param requestType
     * @param sourceIdMap
     * @param verifyStartTime
     * @return
     */
    String insertCheckVerifyEvent(DrvIsNeedVerifySOARequestType requestType, Map<String, Long> sourceIdMap, String verifyStartTime);

    /**
     * 判断是否是主动认证
     * */
    List<Long> judgeIsIndependent(List<TmsVerifyEventPO> eventPOList);

    /**
    　* @description: 保存司机验证结果(司机端)
    　* <AUTHOR>
    　* @date 2021/11/4 11:23
    */
    Result<Boolean> saveDrvVerifyResult(SaveDrvVerifyResultRequestType request);
}