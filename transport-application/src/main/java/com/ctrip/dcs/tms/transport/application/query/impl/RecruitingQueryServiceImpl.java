package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.common.base.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctriposs.baiji.exception.*;
import com.fasterxml.jackson.core.type.*;
import com.google.common.base.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.sql.*;
import java.util.Objects;
import java.util.*;
import java.util.stream.*;

@Service("recruitingQueryService")
public class RecruitingQueryServiceImpl implements RecruitingQueryService {

    private static final Logger logger = LoggerFactory.getLogger(RecruitingQueryServiceImpl.class);

    @Autowired
    private VehicleRecruitingRepository vehicleRecruitingRepository;
    @Autowired
    private DrvRecruitingRepository drvRecruitingRepository;
    @Autowired
    private VehicleRepository vehicleRepository;
    @Autowired
    private DrvDrvierRepository drvDrvierRepository;
    @Autowired
    private EnumRepository enumRepository;
    @Autowired
    private ApprovalProcessAuthQconfig approvalProcessAuthQconfig;
    @Autowired
    private ProductionLineUtil productionLineUtil;
    @Autowired
    private TmsRecruitingApproveStepRepository stepRepository;
    @Autowired
    private TmsRecruitingApproveStepChildRepository childRepository;
    @Autowired
    private CertificateCheckQueryService checkQueryService;
    @Autowired
    private TmsApproveStepRecordRespository recordRespository;
    @Autowired
    TmsTransportQconfig qconfig;

    @Autowired
    DrvVehRecruitingQueryService drvVehRecruitingQueryService;

    @Override
    public Result<PageHolder<RecruitingSOAResponseDTO>> queryRecruitingSOAList(RecruitingSOARequestDTO recruitingSOARequestDTO, PaginatorDTO paginator) throws Exception {
        try {
            //去掉权限限制查询
            List<Integer> JurisdictionList = Lists.newArrayList();
            //获取当前登录信息
            Map<String, String> map = SessionHolder.getSessionSource();
            if(MapUtils.isEmpty(map) || map.get("accountType") == null){
                return getResult(Collections.emptyList(), paginator.getPageNo(), paginator.getPageSize(), 0);
            }
            Integer accountType = Integer.parseInt(map.get("accountType"));
            //招募类型必传,并且只能为1or2
            Integer recruitingType = recruitingSOARequestDTO.getRecruitingType();
            if(recruitingType == null){
                recruitingType = TmsTransportConstant.RecruitingTypeEnum.drv.getCode();
            }
            //招募列表兼容车辆招募,为了区分司机和车辆，查询条件中固定RecruitingType 便于查询
            if(Objects.equals(recruitingType, TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode())){
                return this.getVehResult(recruitingSOARequestDTO,paginator,JurisdictionList,accountType, recruitingType);
            }
            dealWithEncryptData(recruitingSOARequestDTO);
            List<Integer> lineCodeList = productionLineUtil.getIncludeProductionLineList(recruitingSOARequestDTO.getProLineList());
            Integer total = drvRecruitingRepository.findNewDrvRecruitingCount(recruitingSOARequestDTO, JurisdictionList,lineCodeList,accountType).intValue();
            if (total == null || total <= 0) {
                return getResult(Collections.emptyList(), paginator.getPageNo(), paginator.getPageSize(), 0);
            }
            List<DrvRecruitingPO> drvRecruitingList = Lists.newArrayList();
            //招募列表优化，先查询主键，再用主键查询信息，SlowSqlFuse5Switch=true(开优化)
            drvRecruitingList = queryDrvRecruitingListFromId(recruitingSOARequestDTO,paginator,lineCodeList,accountType);

            if (CollectionUtils.isEmpty(drvRecruitingList)) {
                return getResult(Collections.emptyList(), paginator.getPageNo(), paginator.getPageSize(), 0);
            }
            // line up
            List<Long> vehicleApproveIdList = Lists.newArrayListWithCapacity(drvRecruitingList.size());
            List<Long> vehicleIdList = Lists.newArrayListWithCapacity(drvRecruitingList.size());
            for (DrvRecruitingPO drvRecruitingPO : drvRecruitingList) {
                if (!legalCheck(drvRecruitingPO.getVehicleId())) {
                    continue;
                }
                if (drvRecruitingPO.getDrvFrom().intValue() == TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode().intValue()) {
                    vehicleApproveIdList.add(drvRecruitingPO.getVehicleId());
                } else {
                    vehicleIdList.add(drvRecruitingPO.getVehicleId());
                }
            }
            List<VehVehiclePO> vehiclePOList = vehicleRepository.queryVehVehicleByIds(vehicleIdList);
            Map<Long, VehVehiclePO> vehicleMap = Maps.uniqueIndex(vehiclePOList, new Function<VehVehiclePO, Long>() {
                @Override
                public Long apply(VehVehiclePO vehVehiclePO) {
                    return vehVehiclePO.getVehicleId();
                }
            });
            List<VehicleRecruitingPO> vehicleRecruitingList = vehicleRecruitingRepository.getVehicleRecruitingList(Sets.newHashSet(vehicleApproveIdList));
            Map<Long, VehicleRecruitingPO> vehicleRecruitingMap = Maps.uniqueIndex(vehicleRecruitingList, new Function<VehicleRecruitingPO, Long>() {
                @Override
                public Long apply(VehicleRecruitingPO vehicleRecruitingPO) {
                    return vehicleRecruitingPO.getVehicleId();
                }
            });
            return getResult(getRemark(assemble(drvRecruitingList, vehicleMap, vehicleRecruitingMap,recruitingType), recruitingType), paginator.getPageNo(), paginator.getPageSize(), total);
        } catch (Exception e) {
            logger.error("queryRecruitingSOAList error:", e);
            throw e;
        }
    }

    public List<DrvRecruitingPO> queryDrvRecruitingListFromId(RecruitingSOARequestDTO recruitingSOARequestDTO, PaginatorDTO paginator, List<Integer> lineCodeList,Integer accountType){
        try {
            List<Long> drvRecruitingIdsList = drvRecruitingRepository.findDrvRecruitingIdList(recruitingSOARequestDTO, paginator,lineCodeList,accountType);
            if(CollectionUtils.isEmpty(drvRecruitingIdsList)){
                return Lists.newArrayList();
            }
            return drvRecruitingRepository.queryDrvRecruitingListByIds(drvRecruitingIdsList);
        }catch (Exception e){
            logger.error("queryDrvRecruitingListFromIdError", e);
            return Lists.newArrayList();
        }
    }

    public void dealWithEncryptData(RecruitingSOARequestDTO requestDTO) {
        if (!Strings.isNullOrEmpty(requestDTO.getDrvIdcard())) {
            requestDTO.setDrvIdcard(TmsTransUtil.encrypt(requestDTO.getDrvIdcard(),KeyType.Identity_Card));
        }
        if (!Strings.isNullOrEmpty(requestDTO.getDrvPhone())) {
            String[] phoneArr = requestDTO.getDrvPhone().split(",|，");
            StringBuilder stringBuilder = new StringBuilder();
            for (String phoneStr : phoneArr) {
                stringBuilder.append(",").append(TmsTransUtil.encrypt(phoneStr,KeyType.Phone));
            }
            requestDTO.setDrvPhone(stringBuilder.toString().substring(1));
        }
    }

    @Override
    public Result<Boolean> checkRecruitingPhone(CheckRecruitingPhoneSOARequestType requestType) {
        Result.Builder<Boolean> result = Result.Builder.<Boolean>newResult();
        Boolean checkResult;
        try {
            checkResult = drvDrvierRepository.checkDrvOnly(TmsTransUtil.encrypt(requestType.getDrvPhone(), KeyType.Phone), TmsTransportConstant.DrvOnlyTypeEnum.PHONE.getCode());
        } catch (SQLException e) {
            checkResult = false;
            logger.error("checkDrvOnly requestType:{}", requestType, e);
        }
        return result.success()
                .withData(!checkResult)
                .build();
    }

    @Override
    public Result<Boolean> checkRecruitingAccount(CheckRecruitingAccountSOARequestType requestType) {
        Result.Builder<Boolean> result = Result.Builder.<Boolean>newResult();
        Boolean checkResult;
        Boolean recruitingResult;
        try {
            recruitingResult = drvRecruitingRepository.checkDrvOnly(requestType.getDrvAccount(), TmsTransportConstant.DrvOnlyTypeEnum.LOGIN_ACCOUNT.getCode(), ImmutableList.of(TmsTransportConstant.RecruitingApproverStatusEnum.supplier_turnDown.getCode(), TmsTransportConstant.RecruitingApproverStatusEnum.operating_turnDown.getCode()));
            checkResult = drvDrvierRepository.checkDrvOnly(requestType.getDrvAccount(), TmsTransportConstant.DrvOnlyTypeEnum.LOGIN_ACCOUNT.getCode());
        } catch (Exception e) {
            checkResult = false;
            recruitingResult = false;
            logger.error("checkRecruitingAccount requestType:{}", requestType, e);
        }
        return result.success()
                .withData(recruitingResult && !checkResult)
                .build();
    }

    @Override
    public Result<QueryApproveStepSOAResponseType> queryApproveStep(QueryApproveStepSOARequestType requestType) {
        try{
            Integer approveFrom = TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue();
            Map<String, String> map = SessionHolder.getSessionSource();
            if(MapUtils.isNotEmpty(map) && map.get("accountType")!=null){
                approveFrom = Integer.parseInt(map.get("accountType"));
            }
            Integer bdApproveStatus = null;
            Long vehicleId = null;
            Integer drvFrom = null;
            switch (requestType.getApproveType()){
                case 1:
                    DrvRecruitingPO drvRecruitingPO =  drvRecruitingRepository.queryByPK(requestType.getApproveSourceId());
                    if(!Objects.isNull(drvRecruitingPO)){
                        bdApproveStatus = drvRecruitingPO.getBdApproveStatus();
                        vehicleId = drvRecruitingPO.getVehicleId();
                        drvFrom = drvRecruitingPO.getDrvFrom();
                    }
                    break;
                case 2:
                    VehicleRecruitingPO vehicleRecruitingPO =  vehicleRecruitingRepository.queryByPK(requestType.getApproveSourceId());
                    if(!Objects.isNull(vehicleRecruitingPO)){
                        bdApproveStatus = vehicleRecruitingPO.getBdApproveStatus();
                        vehicleId = vehicleRecruitingPO.getVehicleId();
                    }
            }
            List<TmsRecruitingApproveStepPO> stepPOList = stepRepository.queryApproveStepList(requestType.getApproveSourceId(),requestType.getApproveType(),approveFrom);
            if(CollectionUtils.isEmpty(stepPOList)){
                return Result.Builder.<QueryApproveStepSOAResponseType>newResult().success().withData(new QueryApproveStepSOAResponseType()).build();
            }
            //只代表H5请求，工作台请求RequestFromH5 = null
            if(requestType.getApproveType() == 1 && Objects.equals(drvFrom, TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode())){
                List<TmsRecruitingApproveStepPO> vehStepList = stepRepository.queryApproveStepList(vehicleId,2,approveFrom);
                stepPOList = (List<TmsRecruitingApproveStepPO>) CollectionUtils.union(stepPOList,vehStepList);
            }
            stepPOList = stepPOList.stream().sorted(Comparator.comparing(TmsRecruitingApproveStepPO::getApproveItem)).collect(Collectors.toList());
            //司机对应证件核验记录
            Map<Integer, TmsCertificateCheckPO> checkPOMap = checkQueryService.queryCertificateCheckToMap(requestType.getApproveSourceId(), getCheckType(requestType.getApproveType()).getCode());
            //如果是H5入注司机，查询出车辆的核验信息
            if(Objects.equals(drvFrom, TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode())||
                    Objects.equals(requestType.getApproveType(), TmsTransportConstant.ApproveSourceTypeEnum.VEHICLE.getCode())){
                Map<Integer, TmsCertificateCheckPO> vehCheckPOMap = checkQueryService.queryCertificateCheckToMap(vehicleId, TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode());
                if(MapUtils.isNotEmpty(vehCheckPOMap)){
                    for(Map.Entry<Integer, TmsCertificateCheckPO> entry : vehCheckPOMap.entrySet()){
                        checkPOMap.put(entry.getKey(),entry.getValue());
                    }
                }
            }
            //通过单项ID查询对应的子项数据
            List<Long> idList = stepPOList.stream().map(TmsRecruitingApproveStepPO::getId).collect(Collectors.toList());
            List<TmsRecruitingApproveStepChildPO> stepChildPOList = childRepository.queryChildByStepIdList(idList);
            List<QueryApproveStepChildSOADTO> childItemList = Lists.newArrayList();
            //遍历子项数据
            for(TmsRecruitingApproveStepChildPO stepChildPO : stepChildPOList){
                QueryApproveStepChildSOADTO soadto = new QueryApproveStepChildSOADTO();
                BeanUtils.copyProperties(stepChildPO,soadto);
                soadto.setDatachangeLasttime(DateUtil.timestampToString(stepChildPO.getDatachangeLasttime(),DateUtil.YYYYMMDDHHMMSS));
                soadto.setChildId(stepChildPO.getId());
                soadto.setParentStepId(stepChildPO.getRecruitingApproveStepId());
                childItemList.add(soadto);
            }
            //计算审批通过数(通过+不审批+不通过)
            int approveThroughCount = 0;
            for(TmsRecruitingApproveStepPO stepPO:stepPOList){
                if(!Objects.equals(TmsTransportConstant.SingleApproveStatusEnum.WAITAPPROVE.getCode(),stepPO.getApproveStatus())){
                    approveThroughCount += 1;
                }
            }
            Map<Integer,String> singleItemMap = enumRepository.getSingleApproveItem();
            //遍历单项数据
            Map<Long,List<QueryApproveStepChildSOADTO>> resultChildItemList = childItemList.stream().collect(Collectors.groupingBy(QueryApproveStepChildSOADTO::getParentStepId));
            List<QueryApproveStepSOADTO> soadtoList = Lists.newArrayList();
            for(TmsRecruitingApproveStepPO stepPO:stepPOList){
                QueryApproveStepSOADTO stepSOADTO = new QueryApproveStepSOADTO();
                BeanUtils.copyProperties(stepPO,stepSOADTO);
                stepSOADTO.setApproveItemValue(singleItemMap.get(stepPO.getApproveItem()));
                stepSOADTO.setDatachangeLasttime(DateUtil.timestampToString(stepPO.getDatachangeLasttime(),DateUtil.YYYYMMDDHHMMSS));
                stepSOADTO.setApproveTime(DateUtil.timestampToString(stepPO.getApproveTime(),DateUtil.YYYYMMDDHHMMSS));
                if(Objects.equals(stepPO.getApproveStatus(), TmsTransportConstant.SingleApproveStatusEnum.WAITAPPROVE.getCode())||
                        Objects.equals(stepPO.getApproveStatus(), TmsTransportConstant.SingleApproveStatusEnum.NO_APPROVE.getCode())){
                    stepSOADTO.setModifyUser("");
                    stepSOADTO.setApproveTime("");
                }
                stepSOADTO.setChildItemList(getChildItemList(stepPO.getApproveItem(),resultChildItemList.get(stepPO.getId()),checkPOMap,bdApproveStatus,approveFrom));
                stepSOADTO.setFinalPassStatus(stepPO.getFinalPassStatus());
                soadtoList.add(stepSOADTO);
            }
            QueryApproveStepSOAResponseType soaResponseType = new QueryApproveStepSOAResponseType();
            soaResponseType.setData(soadtoList);
            soaResponseType.setApproveItemCount(stepPOList.size());
            soaResponseType.setApproveThroughCount(approveThroughCount);
            return Result.Builder.<QueryApproveStepSOAResponseType>newResult().success().withData(soaResponseType).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Result<Integer> queryRecruitingApproveStatus(QueryRecruitingApproveStatusSOARequestType requestType) {
        String drvPhone = TmsTransUtil.encrypt(requestType.getDrvPhone(), KeyType.Phone);
        try {
            List<DrvRecruitingPO> drvRecruitingPOList = drvRecruitingRepository.queryRecruitingDrvByPhone(drvPhone, requestType.getDrvRecruitingId());
            if (CollectionUtils.isEmpty(drvRecruitingPOList)) {
                return Result.Builder.<Integer>newResult().success().withData(-1).build();
            }
            //只查询三期版本之后的数据
            for (DrvRecruitingPO drvRecruitingPO : drvRecruitingPOList) {
                if (drvRecruitingPO.getVersionFlag() >= 3) {
                    return Result.Builder.<Integer>newResult().success().withData(drvRecruitingPO.getApproverStatus()).build();
                }
            }
            return Result.Builder.<Integer>newResult().fail().build();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Result<List<QueryApproveStepRecordSOADTO>> queryApproveStepRecord(QueryApproveStepRecordSOARequestType requestType) {
        List<ApproveStepRecordPO> recordPOList = recordRespository.queryList(requestType.getApproveSourceId(),requestType.getApproveType(), requestType.getRecruitingRecordIds());
        if (CollectionUtils.isEmpty(recordPOList)) {
            return Result.Builder.<List<QueryApproveStepRecordSOADTO>>newResult().success().withData(Collections.emptyList()).build();
        }
        //单项的记录
        List<ApproveStepRecordPO> stepRecordPOS = Lists.newArrayList();
        //子项表中的记录
        List<ApproveStepRecordPO> stepChildRecordPOS = Lists.newArrayList();
        //子项中 OCR和非OCR的记录
        List<ApproveStepRecordPO> stepChildPOS = Lists.newArrayList();
        //子项中 三方的记录
        List<ApproveStepRecordPO> childCertificatePOS = Lists.newArrayList();
        getDifferentiateRecordTypeColl(recordPOList,stepRecordPOS,stepChildRecordPOS,stepChildPOS,childCertificatePOS);
        Map<Long, List<ApproveStepRecordPO>> stepChildPOSMap = stepChildPOS.stream().collect(Collectors.groupingBy(ApproveStepRecordPO::getRecordId));
        Map<Integer, List<ApproveStepRecordPO>> childCertificateMap = childCertificatePOS.stream().collect(Collectors.groupingBy(ApproveStepRecordPO::getCertificateType));
        Map<Long, TmsRecruitingApproveStepPO> approveStepPOMap = getStepItem(stepRecordPOS);
        List<QueryApproveStepRecordSOADTO> stepRecordSOADTOS = resultData(stepRecordPOS,approveStepPOMap,stepChildPOSMap,childCertificateMap);
        return Result.Builder.<List<QueryApproveStepRecordSOADTO>>newResult().success().withData(stepRecordSOADTOS).build();
    }

    @Override
    public Result<QueryRecruitingDrvForPenaltyResponseType> queryRecruitingDrvForPenalty(QueryRecruitingDrvForPenaltyRequestType requestType) {
        QueryRecruitingDrvForPenaltyResponseType responseType = new QueryRecruitingDrvForPenaltyResponseType();
        if(requestType.getDriverId() == null && StringUtils.isEmpty(requestType.getDrivermobile())){
            return Result.Builder.<QueryRecruitingDrvForPenaltyResponseType>newResult().success().withData(responseType).build();
        }
        try {
            List<DrvRecruitingPO> drvRecruitingPOList = drvRecruitingRepository.queryRecruitingDrvByPhoneORId(TmsTransUtil.encrypt(requestType.getDrivermobile(), KeyType.Phone),requestType.getDriverId());
            if(CollectionUtils.isEmpty(drvRecruitingPOList)){
                return Result.Builder.<QueryRecruitingDrvForPenaltyResponseType>newResult().success().withData(responseType).build();
            }
            DrvRecruitingPO drvRecruitingPO = drvRecruitingPOList.get(0);
            if(Objects.isNull(drvRecruitingPO)){
                return Result.Builder.<QueryRecruitingDrvForPenaltyResponseType>newResult().success().withData(responseType).build();
            }
            responseType.setApproveStatus(drvRecruitingPO.getApproverStatus());
            responseType.setApproveAging(drvRecruitingPO.getApproveAging());
            responseType.setApproveSchedule(drvRecruitingPO.getSupplierApproveSchedule());
            if(Objects.equals(TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode(),drvRecruitingPO.getApproverStatus())){
                responseType.setApproveSchedule(drvRecruitingPO.getApproveSchedule());
            }

            return Result.Builder.<QueryRecruitingDrvForPenaltyResponseType>newResult().success().withData(responseType).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    private boolean legalCheck(Long vehicleId) {
        if (vehicleId == null || vehicleId.intValue() <= 0) {
            return false;
        }
        return true;
    }

    private Result<PageHolder<RecruitingSOAResponseDTO>> getResult(List<RecruitingSOAResponseDTO> list, Integer pageNo, Integer pageSize, Integer total) {
        return Result.Builder.<PageHolder<RecruitingSOAResponseDTO>>newResult()
                .success()
                .withData(PageHolder.of(list).pageIndex(pageNo).pageSize(pageSize).totalSize(total).build())
                .build();
    }

    private List<RecruitingSOAResponseDTO> assemble(List<DrvRecruitingPO> drvRecruitingList, Map<Long, VehVehiclePO> vehicleMap, Map<Long, VehicleRecruitingPO> vehicleRecruitingMap,Integer accountType) {
        List<RecruitingSOAResponseDTO> result = Lists.newArrayListWithCapacity(drvRecruitingList.size());
        Map<Integer,String> checkStatus = enumRepository.queryCheckStatusMap();
        Map<Integer,String> approverStatusMap = enumRepository.getRecruitingApproverStatus();
        for (DrvRecruitingPO drvRecruitingPO : drvRecruitingList) {
            RecruitingSOAResponseDTO responseDTO = new RecruitingSOAResponseDTO();
            BeanUtils.copyProperties(drvRecruitingPO,responseDTO);
            responseDTO.setDrvEnglishName(drvRecruitingPO.getDrvEnglishName());
            responseDTO.setApproveStatus(drvRecruitingPO.getApproverStatus());
            responseDTO.setRecruitingId(drvRecruitingPO.getDrvRecruitingId());
            responseDTO.setDrvName(drvRecruitingPO.getDrvName());
            responseDTO.setCityName(enumRepository.getCityName(drvRecruitingPO.getCityId()));
            responseDTO.setSupplierName(enumRepository.getSupplierName(drvRecruitingPO.getSupplierId()));
            responseDTO.setDrvLanguageName(enumRepository.getDrvLanguageName(drvRecruitingPO.getDrvLanguage(), enumRepository.getDrvLanguageMap()));
            responseDTO.setDatachangeCreatetime(DateUtil.timestampToString(drvRecruitingPO.getDatachangeCreatetime(), DateUtil.YYYYMMDDHHMMSS));
            if (drvRecruitingPO.getDrvFrom().intValue() == TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode().intValue()) {
                VehicleRecruitingPO vehicleRecruitingPO = vehicleRecruitingMap.get(drvRecruitingPO.getVehicleId());
                if (vehicleRecruitingPO != null) {
                    responseDTO.setVehicleLicense(vehicleRecruitingPO.getVehicleLicense());
                    responseDTO.setVehicleBrandName(enumRepository.getBandName(vehicleRecruitingPO.getVehicleBrandId()));
                    responseDTO.setVehicleTypeName(enumRepository.getVehicleTypeName(vehicleRecruitingPO.getVehicleTypeId()));
                }
            } else {
                VehVehiclePO vehiclePO = vehicleMap.get(drvRecruitingPO.getVehicleId());
                if (vehiclePO != null) {
                    responseDTO.setVehicleLicense(vehiclePO.getVehicleLicense());
                    responseDTO.setVehicleBrandName(enumRepository.getBandName(vehiclePO.getVehicleBrandId()));
                    responseDTO.setVehicleTypeName(enumRepository.getVehicleTypeName(vehiclePO.getVehicleTypeId()));
                }
            }
            responseDTO.setRecruitingType(TmsTransportConstant.RecruitingTypeEnum.drv.getCode());
            responseDTO.setCheckStatusName(checkStatus.get(drvRecruitingPO.getCheckStatus()));
            responseDTO.setProLineName(productionLineUtil.getProductionLineNames(drvRecruitingPO.getCategorySynthesizeCode()));
            responseDTO.setApproveAgingName(enumRepository.getApproveAgingName(drvRecruitingPO.getApproveAging()));
            if(Objects.equals(accountType, TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue())){
                responseDTO.setApproveSchedule(drvRecruitingPO.getSupplierApproveSchedule());
            }
            responseDTO.setApproveScheduleName(enumRepository.getApproveScheduleName(responseDTO.getApproveSchedule()));
            responseDTO.setApproveStatusName(approverStatusMap.get(drvRecruitingPO.getApproverStatus())==null?"":approverStatusMap.get(drvRecruitingPO.getApproverStatus()));
            responseDTO.setOversea(drvRecruitingPO.getInternalScope());
            responseDTO.setInjectFrom(drvRecruitingPO.getDrvFrom());
            result.add(responseDTO);
        }
        return result;
    }

    private List<RecruitingSOAResponseDTO> assembleVeh(List<VehicleRecruitingPO> vehRecruitingList,Integer accountType) {
        List<RecruitingSOAResponseDTO> result = Lists.newArrayListWithCapacity(vehRecruitingList.size());
        Map<Integer,String> checkStatus = enumRepository.queryCheckStatusMap();
        Map<Integer,String> approverStatusMap = enumRepository.getRecruitingApproverStatus();
        List<Long> vehicleIds = vehRecruitingList.stream().map(VehicleRecruitingPO::getVehicleId).collect(Collectors.toList());
        List<DrvRecruitingPO> drvRecruitingPOList =  drvRecruitingRepository.queryvRecruitingByVehicleIds(vehicleIds);
        Map<Long,Long> recruitingDrvVehIdMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(drvRecruitingPOList)){
            recruitingDrvVehIdMap = drvRecruitingPOList.stream().collect(Collectors.toMap(DrvRecruitingPO::getVehicleId, DrvRecruitingPO::getDrvRecruitingId,
                    (key1, key2) -> key2));
        }
        for (VehicleRecruitingPO vehRecruitingPO : vehRecruitingList) {
            RecruitingSOAResponseDTO responseDTO = new RecruitingSOAResponseDTO();
            BeanUtils.copyProperties(vehRecruitingPO,responseDTO);
            responseDTO.setApproveStatus(vehRecruitingPO.getApproverStatus());
            responseDTO.setRecruitingId(vehRecruitingPO.getVehicleId());
            responseDTO.setCityName(enumRepository.getCityName(vehRecruitingPO.getCityId()));
            responseDTO.setSupplierName(enumRepository.getSupplierName(vehRecruitingPO.getSupplierId()));
            responseDTO.setDatachangeCreatetime(DateUtil.timestampToString(vehRecruitingPO.getDatachangeCreatetime(), DateUtil.YYYYMMDDHHMMSS));
            responseDTO.setVehicleLicense(vehRecruitingPO.getVehicleLicense());
            responseDTO.setVehicleBrandName(enumRepository.getBandName(vehRecruitingPO.getVehicleBrandId()));
            responseDTO.setVehicleTypeName(enumRepository.getVehicleTypeName(vehRecruitingPO.getVehicleTypeId()));
            responseDTO.setVehRecruitingId(vehRecruitingPO.getVehicleId());
            responseDTO.setRecruitingType(TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode());
            responseDTO.setCheckStatusName(checkStatus.get(vehRecruitingPO.getCheckStatus()));
            responseDTO.setProLineName(productionLineUtil.getProductionLineNames(vehRecruitingPO.getCategorySynthesizeCode()));
            responseDTO.setApproveAgingName(enumRepository.getApproveAgingName(vehRecruitingPO.getApproveAging()));
            if(Objects.equals(accountType, TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue())){
                responseDTO.setApproveSchedule(vehRecruitingPO.getSupplierApproveSchedule());
            }
            responseDTO.setApproveScheduleName(enumRepository.getApproveScheduleName(responseDTO.getApproveSchedule()));
            responseDTO.setApproveStatusName(approverStatusMap.get(vehRecruitingPO.getApproverStatus())==null?"":approverStatusMap.get(vehRecruitingPO.getApproverStatus()));
            responseDTO.setOversea(enumRepository.getAreaScope(vehRecruitingPO.getCityId()));
            //如果是H5入注的车辆，返回的审批Id为车辆对应的招募司机ID
            if (Objects.equals(TmsTransportConstant.VehicleFromEnum.Veh_AUTO.getCode(), vehRecruitingPO.getVehicleFrom()) && MapUtils.isNotEmpty(recruitingDrvVehIdMap)) {
                responseDTO.setDrvRecruitingId(recruitingDrvVehIdMap.get(vehRecruitingPO.getVehicleId()) == null ? 0L : recruitingDrvVehIdMap.get(vehRecruitingPO.getVehicleId()));
            }
            responseDTO.setInjectFrom(vehRecruitingPO.getVehicleFrom());
            result.add(responseDTO);
        }

        return result;
    }

    /**
     * 车辆招募返回集
     * @param recruitingSOARequestDTO
     * @param paginator
     * @param JurisdictionList
     * @return
     */
    public Result<PageHolder<RecruitingSOAResponseDTO>> getVehResult(RecruitingSOARequestDTO recruitingSOARequestDTO, PaginatorDTO paginator,List<Integer> JurisdictionList,Integer accountType, Integer recruitingType) {
        try {
            Boolean newOldSwitch =  qconfig.getVehRecrListCompSwitch();
            //查询H5的车辆，首先映射到H5的司机，从司机中获取对应的车辆ID　
            List<Integer> lineCodeList = productionLineUtil.getIncludeProductionLineList(recruitingSOARequestDTO.getProLineList());
            Integer total = vehicleRecruitingRepository.countVehicleRecruitingList(recruitingSOARequestDTO,JurisdictionList,lineCodeList,accountType,newOldSwitch);
            if (total == null || total <= 0) {
                return getResult(Collections.emptyList(), paginator.getPageNo(), paginator.getPageSize(), 0);
            }
            List<VehicleRecruitingPO> vehicleRecruitingPOList = vehicleRecruitingRepository.queryVehicleRecruitingList(recruitingSOARequestDTO,JurisdictionList,paginator,lineCodeList,accountType,newOldSwitch);
            if(org.apache.commons.collections.CollectionUtils.isEmpty(vehicleRecruitingPOList)){
                return getResult(Collections.emptyList(), paginator.getPageNo(), paginator.getPageSize(), 0);
            }

            return getResult(getRemark(assembleVeh(vehicleRecruitingPOList,recruitingType), recruitingType), paginator.getPageNo(), paginator.getPageSize(), total);
        }catch (Exception e){
            logger.error("getVehResult error:", e);
        }
        return getResult(Collections.emptyList(), paginator.getPageNo(), paginator.getPageSize(), 0);
    }

    protected List<RecruitingSOAResponseDTO> getRemark(List<RecruitingSOAResponseDTO> recruitingSOAResponseDTOList, Integer recruitingType) {
        //查询司机、车辆审核不通过备注
        Result<List<QueryDrvVehRecruitingModRrdSOADTO>> listResult =
          drvVehRecruitingQueryService.queryDrvVehRecruitingModRrd(
            recruitingSOAResponseDTOList.stream().filter(
              vehicleRecruitingDTO -> Objects.equals(vehicleRecruitingDTO.getApproveStatus(), TmsTransportConstant.RecruitingApproverStatusEnum.operating_turnDown.getCode())
                || Objects.equals(vehicleRecruitingDTO.getApproveStatus(), TmsTransportConstant.RecruitingApproverStatusEnum.supplier_turnDown.getCode())).collect(Collectors.toList()).stream().map(RecruitingSOAResponseDTO::getRecruitingId).collect(Collectors.toList()),
            recruitingType);
        // 转换为Map，只保留第一个出现的值（最新的的一条数据）
        Map<Long, String> resultMap = new LinkedHashMap<>();
        for (QueryDrvVehRecruitingModRrdSOADTO entry : listResult.getData()) {
            resultMap.putIfAbsent(entry.getDrvRecruitingId(), entry.getRemark());
        }
        recruitingSOAResponseDTOList.forEach(recruitingDTO -> {
            // 设置备注
            recruitingDTO.setRemark(resultMap.getOrDefault(Objects.equals(recruitingType,
              TmsTransportConstant.RecruitingTypeEnum.drv.getCode()) ? recruitingDTO.getDrvRecruitingId() : recruitingDTO.getVehRecruitingId(), null));
        });
        return recruitingSOAResponseDTOList;
    }

    public List<QueryApproveStepChildSOADTO> getChildItemList(Integer approveItem,List<QueryApproveStepChildSOADTO> resultChildItemList,Map<Integer, TmsCertificateCheckPO> drvCheckPOMap,Integer bdApproveStatus,Integer approveFrom){
        TmsCertificateCheckPO tmsCertificateCheckPO = getCertificateCheck(approveItem,drvCheckPOMap);
        if(CollectionUtils.isEmpty(resultChildItemList)){
            resultChildItemList = new ArrayList();
        }
        if(Objects.isNull(tmsCertificateCheckPO) || (bdApproveStatus == 0 && Objects.equals(approveFrom,TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue()))){
            return resultChildItemList;
        }
        QueryApproveStepChildSOADTO soadto = new QueryApproveStepChildSOADTO();
        soadto.setChildId(tmsCertificateCheckPO.getId());
        soadto.setCheckStatus(getCheckStatus(tmsCertificateCheckPO.getCheckStatus()));
        soadto.setChildItem(TmsTransportConstant.ApproveChildItemEnum.COMPLIANCE.getCode());//子项类别(1-OCR识别,2-非OCR识别,3-证件核验)
        resultChildItemList.add(soadto);
        return resultChildItemList;
    }

    public static TmsTransportConstant.CertificateCheckTypeEnum getCheckType(Integer approveType){
        switch (approveType){
            case 1:return TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV;
            case 2:return TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE;
        }
        return TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV;
    }

    public static TmsCertificateCheckPO getCertificateCheck(Integer approveItem,Map<Integer, TmsCertificateCheckPO> drvCheckPOMap){
        TmsCertificateCheckPO tmsCertificateCheckPO = null;
        switch (approveItem){
            case 1: tmsCertificateCheckPO = drvCheckPOMap.get(TmsTransportConstant.CertificateTypeEnum.IDCARD.getCode());
                break;
            case 2:tmsCertificateCheckPO = drvCheckPOMap.get(TmsTransportConstant.CertificateTypeEnum.DRIVERLICENSE.getCode());
                break;
            case 4:tmsCertificateCheckPO = drvCheckPOMap.get(TmsTransportConstant.CertificateTypeEnum.NETDRVCTFCT.getCode());
                break;
            case 101: tmsCertificateCheckPO = drvCheckPOMap.get(TmsTransportConstant.CertificateTypeEnum.CARCERTILICENSE.getCode());
                break;
            case 102: tmsCertificateCheckPO = drvCheckPOMap.get(TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT.getCode());
        }
        return tmsCertificateCheckPO;
    }

    public Integer getCheckStatus(Integer certificateCheckStatus){
        switch (certificateCheckStatus){
            case 2:
            case 4:
                return TmsTransportConstant.CheckStatusEnum.INIT.getCode();
        }
        return certificateCheckStatus;
    }

    //单项、子项记录结果集
    public List<QueryApproveStepRecordSOADTO> resultData(List<ApproveStepRecordPO> stepRecordPOS,Map<Long, TmsRecruitingApproveStepPO> approveStepPOMap,Map<Long, List<ApproveStepRecordPO>> stepChildPOSMap,Map<Integer, List<ApproveStepRecordPO>> childCertificateMap){
        Map<Integer, String> singleItemMap = enumRepository.getSingleApproveItem();
        List<QueryApproveStepRecordSOADTO> stepRecordSOADTOS = Lists.newArrayList();
        for (ApproveStepRecordPO recordPO : stepRecordPOS) {
            TmsRecruitingApproveStepPO approveStep = approveStepPOMap.get(recordPO.getRecordId());
            if (Objects.isNull(approveStep)) {
                continue;
            }
            QueryApproveStepRecordSOADTO recordSOADTO = new QueryApproveStepRecordSOADTO();
            recordSOADTO.setStepId(recordPO.getRecordId());
            recordSOADTO.setModifyUser(recordPO.getModifyUser());
            recordSOADTO.setApproveItemName(singleItemMap.get(approveStep.getApproveItem()));
            if (StringUtils.isNotEmpty(recordPO.getRecordContent())) {
                List<TmsModContent> contentList = JsonUtil.fromJson(recordPO.getRecordContent(), new TypeReference<List<TmsModContent>>() {
                });
                for (TmsModContent modContent : contentList) {
                    recordSOADTO.setApproveStatus(Integer.parseInt(modContent.getChangeValue()));
                }
            }
            List<ApproveStepRecordPO> childRecordList = stepChildPOSMap.get(recordPO.getRecordId());
            List<QueryApproveStepChildRecordSOADTO> childRecordSOADTOList = Lists.newArrayList();
            childColl(childRecordList, childRecordSOADTOList,approveStep);
            childCertificateColl(childCertificateMap, approveStep.getApproveItem(), childRecordSOADTOList);
            recordSOADTO.setChildList(childRecordSOADTOList);
            stepRecordSOADTOS.add(recordSOADTO);
        }
        return stepRecordSOADTOS;
    }

    //子项表中结果集
    public void childColl(List<ApproveStepRecordPO> childRecordList,List<QueryApproveStepChildRecordSOADTO> childRecordSOADTOList,TmsRecruitingApproveStepPO approveStep){
        if(CollectionUtils.isNotEmpty(childRecordList)){
            for(ApproveStepRecordPO childRecord : childRecordList){
                QueryApproveStepChildRecordSOADTO childRecordSOADTO = buildSOADTO(childRecord);
                childRecordSOADTO.setChildItemName(CommonEnum.ApproveChildSharkEnum.getName(childRecord.getChildItem()));
                if(Objects.equals(approveStep.getApproveItem(), TmsTransportConstant.ApproveItemEnum.HeadPortrait.getCode())){
                    childRecordSOADTO.setChildItemName(CommonEnum.ApproveChildSharkEnum.HEAD_CAPACITYMGT.getText());
                }
                childRecordSOADTOList.add(childRecordSOADTO);
            }
        }
    }

    //三方结果集
    public void childCertificateColl(Map<Integer,List<ApproveStepRecordPO>> childCertificateMap,Integer item,List<QueryApproveStepChildRecordSOADTO> childRecordSOADTOList){
        List<ApproveStepRecordPO> approveStepRecordPOS = childCertificateMap.get(getMapingCertificateType(item));
        if(CollectionUtils.isEmpty(approveStepRecordPOS)){
            return;
        }
        for(ApproveStepRecordPO recordPO : approveStepRecordPOS){
            QueryApproveStepChildRecordSOADTO childRecordSOADTO = buildSOADTO(recordPO);
            childRecordSOADTO.setChildItemName(CommonEnum.ApproveChildCertificateSharkEnum.getName(recordPO.getCertificateType()));
            childRecordSOADTOList.add(childRecordSOADTO);
        }
    }

    //单项对应的三方证件类型
    public static Integer getMapingCertificateType(Integer approveItem){
        switch (approveItem){
            case 1: return TmsTransportConstant.CertificateTypeEnum.IDCARD.getCode();
            case 2:return TmsTransportConstant.CertificateTypeEnum.DRIVERLICENSE.getCode();
            case 4:return TmsTransportConstant.CertificateTypeEnum.NETDRVCTFCT.getCode();
            case 101: return TmsTransportConstant.CertificateTypeEnum.CARCERTILICENSE.getCode();
            case 102: return TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT.getCode();
        }
        return 0;
    }

    public QueryApproveStepChildRecordSOADTO buildSOADTO(ApproveStepRecordPO recordPO){
        QueryApproveStepChildRecordSOADTO childRecordSOADTO = new QueryApproveStepChildRecordSOADTO();
        childRecordSOADTO.setModifyUser(recordPO.getModifyUser());
        if(StringUtils.isNotEmpty(recordPO.getRecordContent())){
            List<TmsModContent> contentList = JsonUtil.fromJson(recordPO.getRecordContent(),new TypeReference<List<TmsModContent>>(){});
            for(TmsModContent modContent : contentList){
                childRecordSOADTO.setOrgCheckStatus(Integer.parseInt(modContent.getOriginalValue()));
                childRecordSOADTO.setChangeCheckStatus(Integer.parseInt(modContent.getChangeValue()));
            }
        }
        return childRecordSOADTO;
    }

    //按照记录类型 将单项、子项区分开
    public void getDifferentiateRecordTypeColl(List<ApproveStepRecordPO> recordPOList,List<ApproveStepRecordPO> stepRecordPOS,List<ApproveStepRecordPO> stepChildRecordPOS,List<ApproveStepRecordPO> stepChildPOS,List<ApproveStepRecordPO> childCertificatePOS){
        for (ApproveStepRecordPO stepRecordPO : recordPOList) {
            if (Objects.equals(stepRecordPO.getRecordType(), CommonEnum.ApproveStepRecordTypeEnum.SINGLE.getValue())) {
                stepRecordPOS.add(stepRecordPO);
            }
            if (Objects.equals(stepRecordPO.getRecordType(), CommonEnum.ApproveStepRecordTypeEnum.CHILD_SINGLE.getValue())) {
                stepChildRecordPOS.add(stepRecordPO);
            }
        }
        for (ApproveStepRecordPO recordPO : stepChildRecordPOS) {
            if (Objects.equals(recordPO.getChildItem(), TmsTransportConstant.ApproveChildItemEnum.OCR.getCode()) ||
                    Objects.equals(recordPO.getChildItem(), TmsTransportConstant.ApproveChildItemEnum.NON_OCR.getCode())) {
                stepChildPOS.add(recordPO);
            }
            if (Objects.equals(recordPO.getChildItem(), TmsTransportConstant.ApproveChildItemEnum.COMPLIANCE.getCode())) {
                childCertificatePOS.add(recordPO);
            }
        }
    }

    /***
    　* @description: 获取单项的分类映射
    　* <AUTHOR>
    　* @date 2022/4/25 10:36
    */
    public Map<Long,TmsRecruitingApproveStepPO> getStepItem(List<ApproveStepRecordPO> stepRecordPOS){
        if(CollectionUtils.isEmpty(stepRecordPOS)){
            return Maps.newHashMap();
        }
        Set<Long> stepIds = stepRecordPOS.stream().map(ApproveStepRecordPO::getRecordId).collect(Collectors.toSet());
        List<TmsRecruitingApproveStepPO> stepPOList =  stepRepository.queryApproveStepByIds(new ArrayList<>(stepIds));
        if(CollectionUtils.isEmpty(stepPOList)){
            return Maps.newHashMap();
        }

        return stepPOList.stream().collect(Collectors.toMap(TmsRecruitingApproveStepPO::getId,approveStepRecord -> approveStepRecord));
    }
}
