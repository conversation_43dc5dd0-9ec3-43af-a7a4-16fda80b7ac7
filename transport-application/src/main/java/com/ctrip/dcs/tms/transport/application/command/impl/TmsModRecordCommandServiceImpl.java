package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.platform.dal.dao.annotation.*;
import com.ctriposs.baiji.exception.*;
import com.fasterxml.jackson.core.type.*;
import com.google.common.collect.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import org.springframework.util.*;
import qunar.agile.*;

import java.sql.*;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2020/3/1 17:48
 */
@Service("tmsModRecordCommandService")
public class TmsModRecordCommandServiceImpl implements TmsModRecordCommandService {
    private static final Logger logger = LoggerFactory.getLogger(TmsModRecordCommandServiceImpl.class);

    @Autowired
    private ModRecordRespository modRecordRespository;

    @Autowired
    private EnumRepository enumRepository;

    @Autowired
    private TransportGroupRepositoryImpl transportGroupRepository;

    @Autowired
    private TransportGroupQueryService transportGroupQueryService;

    @Autowired
    private ChangeRecordAttributeNameQconfig changeRecordAttributeNameQconfig;

    @Autowired
    private TmsApproveStepRecordRespository stepRecordRespository;

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public void drvStatusUpdateModRrd(Long drvId, int originalStatus, int changedStatus,String modifyUser) {
        TmsModContent tmsModContent = new TmsModContent();
        tmsModContent.setAttributeKey(Constant.Fields.drv_status);
        tmsModContent.setAttributeName(changeRecordAttributeNameQconfig.getDriverRecordMap().get(Constant.Fields.drv_status));
        tmsModContent.setOriginalValue(TmsTransportConstant.DrvStatusEnum.getText(originalStatus));
        tmsModContent.setChangeValue(TmsTransportConstant.DrvStatusEnum.getText(changedStatus));
        modRecordRespository.insetModRecord(drvId, Lists.newArrayList(tmsModContent), CommonEnum.RecordTypeEnum.DRIVER,CommonEnum.ModTypeEnum.UPDATE,modifyUser);
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public void drvIntendVehicleTypeUpdateModRrd(Long drvId, String originalIntendVehicleType, String changedIntendVehicleType, String modifyUser) {
        TmsModContent tmsModContent = new TmsModContent();
        tmsModContent.setAttributeKey(Constant.Fields.drv_intendVehicleTypeId);
        tmsModContent.setAttributeName(changeRecordAttributeNameQconfig.getDriverRecordMap().get(Constant.Fields.drv_intendVehicleTypeId));
        tmsModContent.setOriginalValue(originalIntendVehicleType);
        tmsModContent.setChangeValue(changedIntendVehicleType);
        modRecordRespository.insetModRecord(drvId, Lists.newArrayList(tmsModContent), CommonEnum.RecordTypeEnum.DRIVER,CommonEnum.ModTypeEnum.UPDATE,modifyUser);
    }


    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public void transportGroupAddModRrd(Long transportGroupId,String modifyUser) {
        modRecordRespository.insetModRecord(transportGroupId, null, CommonEnum.RecordTypeEnum.TRANSPORT_GROUP,CommonEnum.ModTypeEnum.CREATE, modifyUser);
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public void transportGroupUpdateModRrd(TspTransportGroupPO transportGroupPO) {
        List<TmsModContent> tmsModContents = Lists.newArrayList();
        TspTransportGroupPO originalPO = transportGroupRepository.queryTransportGroupDetail(transportGroupPO.getTransportGroupId());
        boolean dispatcherMode = transportGroupQueryService.isDispatcherMode(transportGroupPO.getTransportGroupMode());
        if (!originalPO.getTransportGroupName().equals(transportGroupPO.getTransportGroupName()) ) {
            TmsModContent tmsModContent = new TmsModContent();
            tmsModContent.setAttributeKey(Constant.Fields.transportGroup_name);
            tmsModContent.setAttributeName(changeRecordAttributeNameQconfig.getTransportGroupRecordMap().get(Constant.Fields.transportGroup_name));
            tmsModContent.setOriginalValue(originalPO.getTransportGroupName());
            tmsModContent.setChangeValue(transportGroupPO.getTransportGroupName());
            tmsModContents.add(tmsModContent);
        }
        if (!originalPO.getTransportGroupMode().equals(transportGroupPO.getTransportGroupMode())) {
            TmsModContent tmsModContent = new TmsModContent();
            tmsModContent.setAttributeKey(Constant.Fields.transportGroup_mode);
            tmsModContent.setAttributeName(changeRecordAttributeNameQconfig.getTransportGroupRecordMap().get(Constant.Fields.transportGroup_mode));
            tmsModContent.setOriginalValue(TmsTransportConstant.TransportGroupModeEnum.getText(originalPO.getTransportGroupMode()));
            tmsModContent.setChangeValue(TmsTransportConstant.TransportGroupModeEnum.getText(transportGroupPO.getTransportGroupMode()));
            tmsModContents.add(tmsModContent);
        }
        if (dispatcherMode && !originalPO.getDispatcher().equals(transportGroupPO.getDispatcher())) {
            TmsModContent tmsModContent = new TmsModContent();
            tmsModContent.setAttributeKey(Constant.Fields.transportGroup_dispatcher);
            tmsModContent.setAttributeName(changeRecordAttributeNameQconfig.getTransportGroupRecordMap().get(Constant.Fields.transportGroup_dispatcher));
            tmsModContent.setOriginalValue(originalPO.getDispatcher());
            tmsModContent.setChangeValue(transportGroupPO.getDispatcher());
            tmsModContents.add(tmsModContent);
        }
        if (dispatcherMode && !originalPO.getDispatcherPhone().equals(transportGroupPO.getDispatcherPhone())) {
            TmsModContent tmsModContent = new TmsModContent();
            tmsModContent.setAttributeKey(Constant.Fields.transportGroup_dispatcherPhone);
            tmsModContent.setAttributeName(changeRecordAttributeNameQconfig.getTransportGroupRecordMap().get(Constant.Fields.transportGroup_dispatcherPhone));
            tmsModContent.setOriginalValue(originalPO.getDispatcherPhone());
            tmsModContent.setChangeValue(transportGroupPO.getDispatcherPhone());
            tmsModContents.add(tmsModContent);
        }
        if (dispatcherMode && !originalPO.getDispatcherLanguage().equals(transportGroupPO.getDispatcherLanguage())
                && !(Strings.isEmpty(originalPO.getDispatcherLanguage()) && Strings.isEmpty(transportGroupPO.getDispatcherLanguage()))) {
            TmsModContent tmsModContent = new TmsModContent();
            Map<String, String> languageMap = enumRepository.getDrvLanguageMap();
            tmsModContent.setAttributeKey(Constant.Fields.transportGroup_dispatcherLanguage);
            tmsModContent.setAttributeName(changeRecordAttributeNameQconfig.getTransportGroupRecordMap().get(Constant.Fields.transportGroup_dispatcherLanguage));
            tmsModContent.setOriginalValue(enumRepository.getDrvLanguageName(originalPO.getDispatcherLanguage(),languageMap));
            tmsModContent.setChangeValue(enumRepository.getDrvLanguageName(transportGroupPO.getDispatcherLanguage(),languageMap));
            tmsModContents.add(tmsModContent);
        }
        if (dispatcherMode && !originalPO.getTakeOrderLimitTime().equals(transportGroupPO.getTakeOrderLimitTime())) {
            TmsModContent tmsModContent = new TmsModContent();
            Map<Integer, String> takeOrderLimitTimeMap = enumRepository.getTakeOrderLimitTimeMap();
            tmsModContent.setAttributeKey(Constant.Fields.transportGroup_takeOrderLimitTime);
            tmsModContent.setAttributeName(changeRecordAttributeNameQconfig.getTransportGroupRecordMap().get(Constant.Fields.transportGroup_takeOrderLimitTime));
            tmsModContent.setOriginalValue(takeOrderLimitTimeMap.get(originalPO.getTakeOrderLimitTime()));
            tmsModContent.setChangeValue(takeOrderLimitTimeMap.get(transportGroupPO.getTakeOrderLimitTime()));
            tmsModContents.add(tmsModContent);
        }
        if (!CollectionUtils.isEmpty(tmsModContents)) {
            modRecordRespository.insetModRecord(transportGroupPO.getTransportGroupId(),tmsModContents,CommonEnum.RecordTypeEnum.TRANSPORT_GROUP,CommonEnum.ModTypeEnum.UPDATE, transportGroupPO.getModifyUser());
        }
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public void transportGroupStatusUpdateModRrd(Long transportGroupId, int originalStatus, int changedStatus,String modifyUser) {
        TmsModContent tmsModContent = new TmsModContent();
        tmsModContent.setAttributeKey(Constant.Fields.transportGroup_status);
        tmsModContent.setAttributeName(changeRecordAttributeNameQconfig.getTransportGroupRecordMap().get(Constant.Fields.transportGroup_status));
        tmsModContent.setOriginalValue(TmsTransportConstant.TransportGroupStatusEnum.getText(originalStatus));
        tmsModContent.setChangeValue(TmsTransportConstant.TransportGroupStatusEnum.getText(changedStatus));
        modRecordRespository.insetModRecord(transportGroupId, Lists.newArrayList(tmsModContent), CommonEnum.RecordTypeEnum.TRANSPORT_GROUP,CommonEnum.ModTypeEnum.UPDATE,modifyUser);
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Long drvVehRecruitingInsertModRrd(DrvVehRecruitingInsertModRrdDTO modRrdDTO) {
        try {
            List<TmsModContent> modContentsList = Lists.newArrayList();
            TmsModContent tmsModContent = new TmsModContent();
            tmsModContent.setAttributeKey(Constant.Fields.approver_status);
            tmsModContent.setAttributeName(changeRecordAttributeNameQconfig.getDrvVehRecruitingRecordMap().get(Constant.Fields.approver_status));
            tmsModContent.setChangeValue(String.valueOf(modRrdDTO.getApproverStatus()));
            modContentsList.add(tmsModContent);

            if (modRrdDTO.getApproverAperation() != null) {
                tmsModContent = new TmsModContent();
                tmsModContent.setAttributeKey(Constant.Fields.approver_operation);
                tmsModContent.setAttributeName(changeRecordAttributeNameQconfig.getDrvVehRecruitingRecordMap().get(Constant.Fields.approver_operation));
                tmsModContent.setChangeValue(String.valueOf(modRrdDTO.getApproverAperation()));
                modContentsList.add(tmsModContent);
            }

            tmsModContent = new TmsModContent();
            tmsModContent.setAttributeKey(Constant.Fields.approver_remark);
            tmsModContent.setAttributeName(changeRecordAttributeNameQconfig.getDrvVehRecruitingRecordMap().get(Constant.Fields.approver_remark));
            tmsModContent.setChangeValue(modRrdDTO.getRemark());
            modContentsList.add(tmsModContent);
            CommonEnum.RecordTypeEnum recordTypeEnum = CommonEnum.RecordTypeEnum.CHECK;
            if (Objects.equals(modRrdDTO.getRecruitingType(), TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode())) {
                recordTypeEnum = CommonEnum.RecordTypeEnum.VEH_CHECK;
            }
            return modRecordRespository.insetRecruitingRecord(modRrdDTO.getDrvRecruitingId(), modContentsList, recordTypeEnum, CommonEnum.ModTypeEnum.CREATE, modRrdDTO.getModifyUser());
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }

    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public void drvVehRecruitingInsertModRrdList(List<Long> recruitingIdList, int approveStatus, String operator, String remark,Integer recruitingType,Integer approverAperation) {
        if (CollectionUtils.isEmpty(recruitingIdList)) {
            return;
        }
        for (Long id : recruitingIdList) {
            DrvVehRecruitingInsertModRrdDTO modRrdDTO = new DrvVehRecruitingInsertModRrdDTO();
            modRrdDTO.setRemark(remark);
            modRrdDTO.setDrvRecruitingId(id);
            modRrdDTO.setModifyUser(operator);
            modRrdDTO.setApproverStatus(approveStatus);
            modRrdDTO.setRecruitingType(recruitingType);
            modRrdDTO.setApproverAperation(approverAperation);
            drvVehRecruitingInsertModRrd(modRrdDTO);
        }
    }

    @Override
    public void tmsinitDrvFreezeModRrd(List<TmsDrvFreezePO> freezeList) {
        if(CollectionUtils.isEmpty(freezeList)){
            return;
        }
        List<TmsModContent> modContentsList = Lists.newArrayList();
        for(TmsDrvFreezePO freezePO : freezeList){
            TmsModContent tmsModContent = new TmsModContent();
            tmsModContent.setAttributeKey(Constant.Fields.freezeHour);
            tmsModContent.setAttributeName(changeRecordAttributeNameQconfig.getDriverRecordMap().get(Constant.Fields.freezeHour));
            tmsModContent.setOriginalValue("");
            tmsModContent.setChangeValue(String.valueOf(freezePO.getFreezeHour()));
            modContentsList.add(tmsModContent);

             tmsModContent = new TmsModContent();
            tmsModContent.setAttributeKey(Constant.Fields.freezeReason);
            tmsModContent.setAttributeName(changeRecordAttributeNameQconfig.getDriverRecordMap().get(Constant.Fields.freezeReason));
            tmsModContent.setOriginalValue("");
            tmsModContent.setChangeValue(this.getFreezeReason(freezePO.getFreezeReason()));
            modContentsList.add(tmsModContent);

            tmsModContent = new TmsModContent();
            tmsModContent.setAttributeKey(Constant.Fields.unfreezeAction);
            tmsModContent.setAttributeName(changeRecordAttributeNameQconfig.getDriverRecordMap().get(Constant.Fields.unfreezeAction));
            tmsModContent.setOriginalValue("");
            tmsModContent.setChangeValue(String.valueOf(freezePO.getUnfreezeAction()));
            modContentsList.add(tmsModContent);

            if (!CollectionUtils.isEmpty(modContentsList)) {
                modRecordRespository.insetModRecord(freezePO.getDrvId(),modContentsList,CommonEnum.RecordTypeEnum.DRIVER,CommonEnum.ModTypeEnum.UPDATE, freezePO.getModifyUser());
            }
        }
    }

    @Override
    public void tmsDrvFreezeModRrd(TmsDrvFreezePO origin, TmsDrvFreezePO tarPO) {
        List<TmsModContent> tmsModContents = Lists.newArrayList();
        if(origin == null){
            tmsinitDrvFreezeModRrd(Arrays.asList(tarPO));
        }else{
            if(!Objects.equals(origin.getFreezeHour(),tarPO.getFreezeHour())){
                TmsModContent tmsModContent = new TmsModContent();
                tmsModContent.setAttributeKey(Constant.Fields.freezeHour);
                tmsModContent.setAttributeName(changeRecordAttributeNameQconfig.getDriverRecordMap().get(Constant.Fields.freezeHour));
                String freezeHour = "0";
                if(origin!=null){
                    freezeHour = String.valueOf(origin.getFreezeHour());
                }
                tmsModContent.setOriginalValue(freezeHour);
                tmsModContent.setChangeValue(String.valueOf(tarPO.getFreezeHour()));
                tmsModContents.add(tmsModContent);
            }
            if(StringUtils.isNotEmpty(tarPO.getFreezeReason())){
                TmsModContent tmsModContent = new TmsModContent();
                tmsModContent.setAttributeKey(Constant.Fields.freezeReason);
                tmsModContent.setAttributeName(changeRecordAttributeNameQconfig.getDriverRecordMap().get(Constant.Fields.freezeReason));
                tmsModContent.setOriginalValue("");
                tmsModContent.setChangeValue(this.getFreezeReason(tarPO.getFreezeReason()));
                tmsModContents.add(tmsModContent);
            }
            if(!Objects.equals(origin.getUnfreezeAction(),tarPO.getUnfreezeAction())){
                TmsModContent tmsModContent = new TmsModContent();
                tmsModContent.setAttributeKey(Constant.Fields.unfreezeAction);
                tmsModContent.setAttributeName(changeRecordAttributeNameQconfig.getDriverRecordMap().get(Constant.Fields.unfreezeAction));
                tmsModContent.setOriginalValue(origin == null?"0":String.valueOf(origin.getUnfreezeAction()));
                tmsModContent.setChangeValue(String.valueOf(tarPO.getUnfreezeAction()));
                tmsModContents.add(tmsModContent);
            }
            if (!CollectionUtils.isEmpty(tmsModContents)) {
                modRecordRespository.insetModRecord(tarPO.getDrvId(),tmsModContents,CommonEnum.RecordTypeEnum.DRIVER,CommonEnum.ModTypeEnum.UPDATE, tarPO.getModifyUser());
            }
        }
    }

    @Override
    public void tmsDrvUnFreezeModRrd(Long drvid, String unfreezeReason, String modifyUser) {
        TmsModContent tmsModContent = new TmsModContent();
        tmsModContent.setAttributeKey(Constant.Fields.unfreezeReason);
        tmsModContent.setAttributeName(changeRecordAttributeNameQconfig.getDriverRecordMap().get(Constant.Fields.unfreezeReason));
        tmsModContent.setOriginalValue("");
        tmsModContent.setChangeValue(unfreezeReason);
        modRecordRespository.insetModRecord(drvid, Lists.newArrayList(tmsModContent), CommonEnum.RecordTypeEnum.DRIVER,CommonEnum.ModTypeEnum.UPDATE,modifyUser);
    }

    @Override
    public void drvVehRecruitingInsertModRrdList(List<Long> recruitingIdList, CommonEnum.RecordTypeEnum recordTypeEnum, Integer checkStatus, String modifyUser, Integer orgCheckStatus,Integer certificateType) {
        for (Long aLong : recruitingIdList) {
            TmsModContent tmsModContent = new TmsModContent();
            tmsModContent.setAttributeKey(Constant.Fields.certificate_check_status);
            tmsModContent.setAttributeName(changeRecordAttributeNameQconfig.getDrvVehRecruitingRecordMap().get(Constant.Fields.certificate_check_status));
            tmsModContent.setChangeValue(String.format(changeRecordAttributeNameQconfig.getCertificateCheckRecordString(), TmsTransportConstant.CertificateTypeEnum.getText(certificateType), TmsTransportConstant.CheckStatusEnum.getText(orgCheckStatus),TmsTransportConstant.CheckStatusEnum.getText(checkStatus)));
            modRecordRespository.insetModRecord(aLong, Lists.newArrayList(tmsModContent), recordTypeEnum,CommonEnum.ModTypeEnum.CREATE,modifyUser);
        }
    }

    @Override
    public int tmsUpdateVerifyEventModRrd(Long verifySourceId, String orgVerifyStartTime, String tarVerifyStartTime, String modifyUser) {
        TmsModContent tmsModContent = new TmsModContent();
        tmsModContent.setAttributeKey(Constant.Fields.verifyevent_verify_start_time);
        tmsModContent.setAttributeName("");
        tmsModContent.setOriginalValue(orgVerifyStartTime);
        tmsModContent.setChangeValue(tarVerifyStartTime);
        modRecordRespository.insetModRecord(verifySourceId, Lists.newArrayList(tmsModContent), CommonEnum.RecordTypeEnum.VERIFY_EVENT,CommonEnum.ModTypeEnum.UPDATE,StringUtils.isEmpty(modifyUser)?"":modifyUser);
        return 1;
    }

    @Override
    public Result<Boolean> saveRecruitingOperationLog(Long recruitingId, Integer recruitingType, String modifyUser,Integer approverStatus) {
        CommonEnum.RecordTypeEnum recordTypeEnum = CommonEnum.RecordTypeEnum.CHECK;
        if(Objects.equals(recruitingType, TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode())){
            recordTypeEnum = CommonEnum.RecordTypeEnum.VEH_CHECK;
        }
        List<TmsModContent> modContentsList = Lists.newArrayList();
        TmsModContent tmsModContent = new TmsModContent();
        tmsModContent.setAttributeKey(Constant.Fields.approver_operation);
        tmsModContent.setAttributeName(changeRecordAttributeNameQconfig.getDrvVehRecruitingRecordMap().get(Constant.Fields.approver_operation));
        tmsModContent.setChangeValue(String.valueOf(TmsTransportConstant.ApproverAperationEnum.save_audit.getCode()));
        modContentsList.add(tmsModContent);

        if(approverStatus!=null){
            tmsModContent = new TmsModContent();
            tmsModContent.setAttributeKey(Constant.Fields.approver_status);
            tmsModContent.setAttributeName(changeRecordAttributeNameQconfig.getDrvVehRecruitingRecordMap().get(Constant.Fields.approver_status));
            tmsModContent.setChangeValue(String.valueOf(approverStatus));
            modContentsList.add(tmsModContent);
        }
        modRecordRespository.insetModRecord(recruitingId, modContentsList, recordTypeEnum,CommonEnum.ModTypeEnum.CREATE,modifyUser);
        return Result.Builder.<Boolean>newResult().success().withData(true).build();
    }

    @Override
    public Boolean saveRecruitingSingleRrd(Long recruitingId, String singleMod,Integer recruitingType, String modifyUser) {
        CommonEnum.RecordTypeEnum recordTypeEnum = CommonEnum.RecordTypeEnum.DRV_SINGLE_APPROVE;
        if(Objects.equals(recruitingType, TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode())){
            recordTypeEnum = CommonEnum.RecordTypeEnum.VEH_SINGLE_APPROVE;
        }
        TmsModContent tmsModContent = new TmsModContent();
        tmsModContent.setAttributeKey(Constant.Fields.single_approve_value);
        tmsModContent.setAttributeName("");
        tmsModContent.setOriginalValue("");
        tmsModContent.setChangeValue(singleMod);
        modRecordRespository.insetModRecord(recruitingId, Lists.newArrayList(tmsModContent), recordTypeEnum,CommonEnum.ModTypeEnum.CREATE,modifyUser);
        return null;
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Boolean saveSingleAndChildRrd(List<StepModRecordParams> recordParams, List<StepChildModRecordParams> childRecordParams, String modifyUser,Long recruitingId,Integer recruitingType) {
        Long recruitingRecordId =  getRecruitingRecordId(recruitingId,recruitingType);
        try {
            if(!CollectionUtils.isEmpty(recordParams)){
                for(StepModRecordParams stepModRecordParams : recordParams){
                    ApproveStepRecordPO approveStepRecordPO = buildApparoveStepRecordPO(stepModRecordParams.getStepId(),CommonEnum.ApproveStepRecordTypeEnum.SINGLE.getValue(),modifyUser,
                            recruitingRecordId,recruitingId,recruitingType,null,null,null,stepModRecordParams.getApproveStatus());
                    stepRecordRespository.insert(approveStepRecordPO);
                }
                if(!CollectionUtils.isEmpty(childRecordParams)){
                    saveChildSingleRrd(childRecordParams,modifyUser,recruitingRecordId,recruitingId,recruitingType);
                }
            }
            return Boolean.TRUE;
        } catch (SQLException e) {
           throw new BaijiRuntimeException(e);
        }
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Long drvVehRecruitingInsertModRrdList(Long recruitingId, int approveStatus, String operator, String remark, Integer recruitingType, Integer approverAperation) {
        DrvVehRecruitingInsertModRrdDTO modRrdDTO = new DrvVehRecruitingInsertModRrdDTO();
        modRrdDTO.setRemark(remark);
        modRrdDTO.setDrvRecruitingId(recruitingId);
        modRrdDTO.setModifyUser(operator);
        modRrdDTO.setApproverStatus(approveStatus);
        modRrdDTO.setRecruitingType(recruitingType);
        modRrdDTO.setApproverAperation(approverAperation);
        return drvVehRecruitingInsertModRrd(modRrdDTO);
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Long insertAutoPassStepRrd(Long recruitingId, Integer recruitingType, int approveStatus, Long stepId,Long recruitingRrdId) {
        try {
            ApproveStepRecordPO approveStepRecordPO = buildApparoveStepRecordPO(stepId,CommonEnum.ApproveStepRecordTypeEnum.SINGLE.getValue(),TmsTransportConstant.TMS_DEFAULT_USERNAME,
                    recruitingRrdId,recruitingId,recruitingType,null,null,null,approveStatus);
            return stepRecordRespository.insert(approveStepRecordPO);
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Long insertThirdCertificateRrd(Long recruitingId, Integer recruitingType, Long checkId,Integer certificateType, Integer orgCheckStatus, Integer newCheckStatus,String modifyUser) {
        if(Objects.equals(orgCheckStatus,newCheckStatus) || newCheckStatus == null || recruitingId == null || recruitingId <= 0 ){
            return 0L;
        }
        try {
            Long recruitingRecordId =  getRecruitingRecordId(recruitingId,recruitingType);
            ApproveStepRecordPO approveStepRecordPO = buildApparoveStepRecordPO(checkId,CommonEnum.ApproveStepRecordTypeEnum.CHILD_SINGLE.getValue(),modifyUser,
                    recruitingRecordId,recruitingId,recruitingType,TmsTransportConstant.ApproveChildItemEnum.COMPLIANCE.getCode(),certificateType,orgCheckStatus,newCheckStatus);
            return stepRecordRespository.insert(approveStepRecordPO);
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Boolean saveChildSingleRrd(List<StepChildModRecordParams> childRecordParams, String modifyUser,Long recruitingRecordId,Long recruitingId,Integer recruitingType) {
        if(!CollectionUtils.isEmpty(childRecordParams)){
            try {
                for(StepChildModRecordParams stepModRecordParams : childRecordParams){
                    ApproveStepRecordPO approveStepRecordPO = buildApparoveStepRecordPO(stepModRecordParams.getParentStepId(),CommonEnum.ApproveStepRecordTypeEnum.CHILD_SINGLE.getValue(),modifyUser,
                            recruitingRecordId,recruitingId,recruitingType,stepModRecordParams.getChildItem(),stepModRecordParams.getCertificateType(),stepModRecordParams.getOrgCheckStatus(),stepModRecordParams.getTarCheckStatus());
                    stepRecordRespository.insert(approveStepRecordPO);
                }
            } catch (SQLException e) {
                throw new BaijiRuntimeException(e);
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public Long insertAutoTurnDownStepRrd(Long recruitingId, Integer recruitingType, int approveStatus, Long stepId) {
        try {
            Long recruitingRecordId =  getRecruitingRecordId(recruitingId,recruitingType);
            ApproveStepRecordPO approveStepRecordPO = buildApparoveStepRecordPO(stepId,CommonEnum.ApproveStepRecordTypeEnum.SINGLE.getValue(),TmsTransportConstant.TMS_DEFAULT_USERNAME,
                    recruitingRecordId,recruitingId,recruitingType,null,null,null,approveStatus);
            return stepRecordRespository.insert(approveStepRecordPO);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Result<Boolean> insertRecruitingActiveRrd(Long recruitingId, Integer recruitingType, String modifyUser) {
        CommonEnum.RecordTypeEnum recordTypeEnum = CommonEnum.RecordTypeEnum.CHECK;
        if(Objects.equals(recruitingType, TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode())){
            recordTypeEnum = CommonEnum.RecordTypeEnum.VEH_CHECK;
        }
        List<TmsModContent> modContentsList = Lists.newArrayList();
        TmsModContent tmsModContent = new TmsModContent();
        tmsModContent.setAttributeKey(Constant.Fields.approver_remark);
        tmsModContent.setAttributeName(changeRecordAttributeNameQconfig.getDrvVehRecruitingRecordMap().get(Constant.Fields.approver_remark));
        tmsModContent.setChangeValue(SharkUtils.getSharkValue(SharkKeyConstant.universalDiscard));
        modContentsList.add(tmsModContent);
        modRecordRespository.insetModRecord(recruitingId, modContentsList, recordTypeEnum,CommonEnum.ModTypeEnum.CREATE,modifyUser);
        return Result.Builder.<Boolean>newResult().success().withData(true).build();
    }

    private String getFreezeReason(String freezeReason){
        if(StringUtils.isEmpty(freezeReason)){
            return "";
        }
        try{
            List<DrvFreezeReasonDTO> reasonDTOS = JsonUtil.fromJson(freezeReason, new TypeReference<List<DrvFreezeReasonDTO>>() {});
            if(CollectionUtils.isEmpty(reasonDTOS)){
                return "";
            }
            DrvFreezeReasonDTO freezeReasonDTO = reasonDTOS.get(0);
            if(Objects.isNull(freezeReasonDTO)){
                return "";
            }
            String freezeRea = freezeReasonDTO.getFreezeReason();
            if(StringUtils.isNotEmpty(freezeReasonDTO.getRemark())){
                freezeRea = freezeRea + "，remark："+freezeReasonDTO.getRemark();
            }
            return freezeRea;
        }catch (Exception e){
            logger.error(" frrezeReason error",e);
        }
        return "";
    }

    public Long getRecruitingRecordId(Long rrdId,Integer recruitingType){
        Integer rrdType = CommonEnum.RecordTypeEnum.CHECK.getCode();
        if(Objects.equals(recruitingType, TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode())){
            rrdType = CommonEnum.RecordTypeEnum.VEH_CHECK.getCode();
        }
        List<TmsModRecordPO>  tmsModRecordPOS =  modRecordRespository.queryModRecordList(rrdId, rrdType,null);
        if(CollectionUtils.isEmpty(tmsModRecordPOS)){
            return 0L;
        }
        return tmsModRecordPOS.get(0).getId();
    }

    public ApproveStepRecordPO buildApparoveStepRecordPO(Long recordId,Integer recordType,String modifyUser,Long recruitingRecordId,
                                                         Long recruitingId,Integer recruitingType,Integer childItem,Integer certificateType,Integer originalValue,Integer changeValue){
        ApproveStepRecordPO approveStepRecordPO = new ApproveStepRecordPO();
        approveStepRecordPO.setRecordId(recordId);
        approveStepRecordPO.setRecordType(CommonEnum.ApproveStepRecordTypeEnum.CHILD_SINGLE.getValue());
        approveStepRecordPO.setCreateUser(modifyUser);
        approveStepRecordPO.setModifyUser(modifyUser);
        approveStepRecordPO.setRecruitingRecordId(recruitingRecordId);
        approveStepRecordPO.setRecruitingId(recruitingId);
        approveStepRecordPO.setRecruitingType(recruitingType);
        approveStepRecordPO.setRecordType(recordType);
        approveStepRecordPO.setChildItem(childItem);
        approveStepRecordPO.setCertificateType(certificateType);
        List<TmsModContent> modContentsList = Lists.newArrayList();
        TmsModContent tmsModContent = new TmsModContent();
        tmsModContent.setAttributeKey(Constant.Fields.approver_status);
        tmsModContent.setChangeValue(String.valueOf(changeValue));
        if(originalValue != null){
            tmsModContent.setOriginalValue(String.valueOf(originalValue));
        }
        modContentsList.add(tmsModContent);
        approveStepRecordPO.setRecordContent(JsonUtil.toJson(modContentsList));
        return approveStepRecordPO;
    }
}
