package com.ctrip.dcs.tms.transport.application.command;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.common.result.*;

import java.util.*;

/**
 * 进单配置命令类接口
 * <AUTHOR>
 * @Date 2020/3/6 14:01
 */
public interface InOrderConfigCommandService {

    /**
     * 新增进单配置
     * @param intoOrderConfigPOList
     * @return
     */
    Result<Integer> addInOrderConfig(List<TspIntoOrderConfigPO> intoOrderConfigPOList);

    /**
     * 新增或更新进单配置
     * @param transportGroupId
     * @param configPOList
     * @return
     */
    Result<Boolean> addOrUpdateInOrderConfig(Long transportGroupId,List<TspIntoOrderConfigPO> configPOList);

}
