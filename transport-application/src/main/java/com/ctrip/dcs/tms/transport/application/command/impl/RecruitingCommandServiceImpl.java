package com.ctrip.dcs.tms.transport.application.command.impl;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.EpidemicPreventionControlEnum.ReportResultEnum.*;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant.*;

import java.sql.Date;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.CertificateCheckQueryService;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.dcs.tms.transport.application.query.TmsPmsproductQueryService;
import com.ctrip.dcs.tms.transport.application.query.VehicleQueryService;
import com.ctrip.dcs.tms.transport.application.query.impl.RecruitingQueryServiceImpl;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverDomainService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.messaging.qmq.TmsQmqProducer;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DriverAccountRegisterResultDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DrvAuditDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.VehicleAuditDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.ApprovalProcessAuthQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.ChangeRecordAttributeNameQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OverseasQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl.EnumRepositoryHelper;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.StepChildModRecordParams;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.StepModRecordParams;
import com.ctrip.dcs.tms.transport.infrastructure.service.IVRCallService;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.ctrip.platform.dal.dao.annotation.DalTransactional;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.google.common.collect.*;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.extra.pinyin.PinyinUtil;

@Service("recruitingCommandService")
public class RecruitingCommandServiceImpl implements RecruitingCommandService {

    private static final Logger log = LoggerFactory.getLogger(RedisUtils.class);

    private static final String pass_error_drv_code = "2001";//操作审批通过-司机入注失败
    private static final String pass_error_veh_code = "2002";//操作审批通过-车辆入注失败
    private static final String pass_error_all_code = "2003";//操作审批通过-司机车辆入注失败

    @Autowired
    private TmsModRecordCommandService tmsModRecordCommandService;
    @Autowired
    private VehicleRecruitingRepository vehicleRecruitingRepository;
    @Autowired
    private DrvRecruitingRepository drvRecruitingRepository;
    @Autowired
    private VehicleCommandService vehicleCommandService;
    @Autowired
    private CommonCommandService commonCommandService;
    @Autowired
    private DriverCommandService driverCommandService;
    @Autowired
    private VehicleQueryService vehicleQueryService;
    @Autowired
    private TmsPmsproductQueryService tmsPmsproductQueryService;
    @Autowired
    private VehicleRepository vehicleRepository;
    @Autowired
    private ModRecordRespository modRecordRespository;
    @Autowired
    private ChangeRecordAttributeNameQconfig changeRecordAttributeNameQconfig;
    @Autowired
    private ApprovalProcessAuthQconfig approvalProcessAuthQconfig;
    @Autowired
    private EnumRepository enumRepository;
    @Autowired
    private CertificateCheckQueryService queryService;
    @Autowired
    private TmsCertificateCheckRepository checkRepository;
    @Autowired
    private DrvEpidemicPreventionControlInfoRepository drvEpidemicPreventionControlInfoRepository;
    @Autowired
    private DrvVehRecruitingCommandService drvVehRecruitingCommandService;
    @Autowired
    private TmsQmqProducerCommandService tmsQmqProducerCommandService;
    @Autowired
    private TmsTransportQconfig tmsTransportQconfig;
    @Autowired
    TmsRecruitingApproveStepRepository stepRepository;
    @Autowired
    TmsRecruitingApproveStepChildRepository childRepository;
    @Autowired
    CommonCommandService commandService;
    @Autowired
    private ApprovalProcessAuthQconfig authConfig;
    @Autowired
    private ProductionLineUtil productionLineUtil;
    @Autowired
    private DriverQueryService driverQueryService;
    @Autowired
    OverseasQconfig overseasQconfig;

    @Autowired
    DriverAccountManagementHelper driverAccountManagementHelper;

    @Autowired
    NetCardCheckUtil netCardCheckUtil;

    @Autowired
    private TmsDrvInactiveReasonRepository tmsDrvInactiveReasonRepository;

    @Autowired
    private DriverDomainService driverDomainService;
    @Autowired
    private EnumRepositoryHelper enumRepositoryHelper;
    @Autowired
    private TmsQmqProducer tmsQmqProducer;
    @Autowired
    private IVRCallService ivrCallService;

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> recruitingUpdateStatus(List<Long> recruitingIdList, int approveStatus, String operator, String remark, Integer approveFrom, Integer approverAperation, List<RejectReason> remarkList) {
        try {

            //todo 拼接驳回原因
            if (StringUtils.isBlank(remark) && CollectionUtils.isNotEmpty(remarkList)) {
                StringJoiner remarkJoiner = new StringJoiner("，");
                remarkList.forEach(item -> {
                    remarkJoiner.add(SharkUtils.getSharkValue(item.getCode()));
                });
                remark = remarkJoiner.toString();
            }

            //操作工作台创建的车辆
            if(Objects.equals(approveFrom, TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode())){
                return this.recruitingVehUpdateStatus(recruitingIdList,approveStatus,operator,remark,approveFrom,null,approverAperation);
            }
            DrvRecruitingPO drvRecruitingPO = drvRecruitingRepository.queryByPK(recruitingIdList.get(0));
            if(Objects.isNull(drvRecruitingPO)){
                return Result.Builder.<Boolean>newResult().fail()
                        .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgUpdateError)).withData(false).build();
            }
            //废弃的数据，无审批操作
            if(!drvRecruitingPO.getActive()){
                return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.driverlistpageDiscardtipWrongAlreadydiscard)).build();
            }
            //平台审批通过，不能再操作审批
            if(Objects.equals(drvRecruitingPO.getApproverStatus(),TmsTransportConstant.RecruitingApproverStatusEnum.finish.getCode())){
                return Result.Builder.<Boolean>newResult().fail().withCode("603").withData(false).build();
            }
            Integer versionFlag = drvRecruitingPO.getVersionFlag() == null?0:drvRecruitingPO.getVersionFlag();
            //同一个角色下，不能重复审批
            if(checkNowRoleApproveStatus(drvRecruitingPO.getApproverStatus(),drvRecruitingPO.getCityId(),versionFlag)){
                return Result.Builder.<Boolean>newResult().fail().withCode("603").withData(false).build();
            }

            Boolean approveTimeFlag = checkStatusBol(drvRecruitingPO.getCheckStatus(),approveStatus);
            Integer bdTurnDownCount = drvRecruitingPO.getBdTurnDownCount() == null?0:drvRecruitingPO.getBdTurnDownCount();
            String vehModSnapshotValues = "";
            List<Long> vehicleIdList = drvRecruitingRepository.getVehicleIdList(recruitingIdList, TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode().intValue());
            //BD驳回，操作记录中备注显示所有不通过单项的原因
            remark =  turnDownReason(recruitingIdList.get(0), TmsTransportConstant.ApproveSourceTypeEnum.DRV.getCode(),CollectionUtils.isEmpty(vehicleIdList)? null :vehicleIdList.get(0),remark,approveStatus);
            Long recordId = tmsModRecordCommandService.drvVehRecruitingInsertModRrdList(recruitingIdList.get(0), approveStatus, operator, remark,approveFrom,getApproverAperation(approveStatus,approverAperation));
//            Boolean approveTimeFlag =  this.isModApproveTime(recruitingIdList.get(0),approveStatus,TmsTransportConstant.RecruitingTypeEnum.drv);
            if (!org.springframework.util.CollectionUtils.isEmpty(vehicleIdList) && vehicleIdList.get(0) > 0 ) {
                VehicleRecruitingPO vehicleRecruitingPO = vehicleRecruitingRepository.queryByPK(vehicleIdList.get(0));
                if(!Objects.isNull(vehicleRecruitingPO) && vehicleRecruitingPO.getBdApproveStatus() == 1){
                    vehModSnapshotValues = vehicleRecruitingPO.getModSnapshotValues();
                }
                vehicleRecruitingRepository.updateVehicleRecApproverStatus(vehicleIdList, approveStatus, remark, operator, netCardCheckUtil.getCheckStatus(vehicleRecruitingPO.getCityId()),approveTimeFlag,bdTurnDownCount);
                //approveStatus = 4 或 6 同步并且变更单项子项状态
                synChildCheckStatus(vehicleIdList.get(0),TmsTransportConstant.RecruitingTypeEnum.vehicle,approveStatus,operator,recordId,recruitingIdList.get(0),TmsTransportConstant.RecruitingTypeEnum.drv.getCode());
            }
            String ModSnapshotValues = drvRecruitingPO.getModSnapshotValues();
            if(drvRecruitingPO.getBdApproveStatus() == 0){
                ModSnapshotValues = "";
            }
            Integer modCount = drvRecruitingRepository.updateDrvRecApproverStatus(recruitingIdList, approveStatus, remark, operator, netCardCheckUtil.getCheckStatus(drvRecruitingPO.getCityId()),approveTimeFlag,bdTurnDownCount);
            if (modCount > 0) {
                //H5 供应商审批通过 接入三方审批
                if(Objects.equals(approveStatus,TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode())){
                    this.asyncCertificateCheck(recruitingIdList,vehicleIdList);
                    //进入三方核验-三期后
                    if (netCardCheckUtil.isNetCardNeedCheck(drvRecruitingPO.getCityId())) {
                        tmsQmqProducerCommandService.sendRecruitingCertificateCheckQMQ(recruitingIdList.get(0),TmsTransportConstant.RecruitingTypeEnum.drv.getCode(),ModSnapshotValues,vehModSnapshotValues,operator);
                    }
                }
                //approveStatus = 4 或 6 同步并且变更单项子项状态
                synChildCheckStatus(recruitingIdList.get(0),TmsTransportConstant.RecruitingTypeEnum.drv,approveStatus,operator,recordId,recruitingIdList.get(0),TmsTransportConstant.RecruitingTypeEnum.drv.getCode());
                //发送审批时效qmq
                if(approveTimeFlag){
                    tmsQmqProducerCommandService.sendRecruitingApproveAgingQMQ(recruitingIdList.get(0),TmsTransportConstant.RecruitingTypeEnum.drv.getCode(),approveStatus);
                }
                //更新招募进度
                tmsQmqProducerCommandService.sendRecruitingApproveScheduleQMQ(recruitingIdList.get(0),TmsTransportConstant.RecruitingTypeEnum.drv.getCode(), TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue());
                tmsQmqProducerCommandService.sendRecruitingApproveScheduleQMQ(recruitingIdList.get(0),TmsTransportConstant.RecruitingTypeEnum.drv.getCode(), TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue());
                return Result.Builder.<Boolean>newResult()
                        .success()
                        .withData(true)
                        .build();
            } else {
                return Result.Builder.<Boolean>newResult()
                        .success()
                        .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgUpdateError))
                        .withData(false)
                        .build();
            }
        } catch (SQLException throwables) {
            throw new BaijiRuntimeException(throwables);
        }
    }



    private Result<Boolean> recruitingVehUpdateStatus(List<Long> recruitingIdList, int approveStatus, String operator, String remark,Integer approveFrom,Integer checkStatus,Integer approverAperation) throws SQLException {
        VehicleRecruitingPO vehicleRecruitingPO = vehicleRecruitingRepository.queryByPK(recruitingIdList.get(0));
        if(Objects.isNull(vehicleRecruitingPO)){
            return Result.Builder.<Boolean>newResult().fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgUpdateError))
                    .withData(false)
                    .build();
        }
        //平台审批通过，不能再操作审批
        if(Objects.equals(vehicleRecruitingPO.getApproverStatus(),TmsTransportConstant.RecruitingApproverStatusEnum.finish.getCode())){
            return Result.Builder.<Boolean>newResult().fail().withCode("603").withData(false).build();
        }
        Integer versionFlag = vehicleRecruitingPO.getVersionFlag() == null?0:vehicleRecruitingPO.getVersionFlag();
        //当前角色已审批过，不能再次审批
        if(checkNowRoleApproveStatus(vehicleRecruitingPO.getApproverStatus(),vehicleRecruitingPO.getCityId(),versionFlag)){
            return Result.Builder.<Boolean>newResult().fail().withCode("603").withData(false).build();
        }
        Boolean approveTimeFlag =checkStatusBol(vehicleRecruitingPO.getCheckStatus(),approveStatus);
        Integer bdapproveStatus = vehicleRecruitingPO.getBdApproveStatus();
        String  modSnapshotValues = vehicleRecruitingPO.getModSnapshotValues();
        Integer bdTurnDownCount = vehicleRecruitingPO.getBdTurnDownCount() == null?0:vehicleRecruitingPO.getBdTurnDownCount();
        if(bdapproveStatus == 0){
            modSnapshotValues = "";
        }
        Integer count = vehicleRecruitingRepository.updateVehicleRecApproverStatus(recruitingIdList, approveStatus, remark, operator,netCardCheckUtil.getCheckStatus(vehicleRecruitingPO.getCityId()),approveTimeFlag,bdTurnDownCount);
        if(count > 0){
            remark =  turnDownReason(recruitingIdList.get(0), TmsTransportConstant.ApproveSourceTypeEnum.VEHICLE.getCode(),null,remark,approveStatus);
            Long recordId = tmsModRecordCommandService.drvVehRecruitingInsertModRrdList(recruitingIdList.get(0), approveStatus, operator, remark,approveFrom,getApproverAperation(approveStatus,approverAperation));
            //车辆方三核验
            if(Objects.equals(approveStatus,TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode())){
                this.vehicleRecruitingCheck(recruitingIdList);
                //进入三方核验-三期后
                if (netCardCheckUtil.isNetCardNeedCheck(vehicleRecruitingPO.getCityId())) {
                    tmsQmqProducerCommandService.sendRecruitingCertificateCheckQMQ(recruitingIdList.get(0), TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode(), "", modSnapshotValues, operator);
                }
            }
            //approveStatus = 4 或 6 同步并且变更单项子项状态
            synChildCheckStatus(recruitingIdList.get(0),TmsTransportConstant.RecruitingTypeEnum.vehicle,approveStatus,operator,recordId,recruitingIdList.get(0),TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode());
            //发送审批时效qmq
            if(approveTimeFlag){
                tmsQmqProducerCommandService.sendRecruitingApproveAgingQMQ(recruitingIdList.get(0),TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode(),approveStatus);
            }
            //更新招募进度
            tmsQmqProducerCommandService.sendRecruitingApproveScheduleQMQ(recruitingIdList.get(0),TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode(), TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue());
            tmsQmqProducerCommandService.sendRecruitingApproveScheduleQMQ(recruitingIdList.get(0),TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode(), TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue());
            return Result.Builder.<Boolean>newResult()
                    .success()
                    .withData(true)
                    .build();
        }
        return Result.Builder.<Boolean>newResult()
                .fail()
                .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgUpdateError))
                .withData(false)
                .build();
    }

    private Result<Boolean> recruitingVehUpdatePass(List<Long> recruitingIdList, boolean passStatus,String operator, String remark,Integer approveFrom) throws SQLException {
        List<VehicleRecruitingPO> vehicleRecruitingPOList = vehicleRecruitingRepository.getVehicleRecruitingList(new HashSet<>(recruitingIdList));
        if(CollectionUtils.isEmpty(vehicleRecruitingPOList)){
            return Result.Builder.<Boolean>newResult()
                    .success()
                    .withData(true)
                    .build();
        }
        for(VehicleRecruitingPO recruitingPO : vehicleRecruitingPOList){
            VehVehiclePO vehiclePO = new VehVehiclePO();
            if (recruitingPO.getRegstDate() != null) {
                vehiclePO.setRegstDate(new Date(recruitingPO.getRegstDate().getTime()));
            }
            //判断是否已经平台审批通过
            if (recruitingPO.getVehicleOfficialId() != null && recruitingPO.getVehicleOfficialId() > 0) {
                return Result.Builder.<Boolean>newResult().fail()
                        .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportQueryDataIsEmpty)).withData(false).build();
            }
            //废弃的数据，无审批操作
            if(!recruitingPO.getActive()){
                return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.driverlistpageDiscardtipWrongAlreadydiscard)).build();
            }
            BeanUtils.copyProperties(recruitingPO,vehiclePO);
            //车辆审核通过,判断是否在正式车辆中已存在相同车牌号
            if (!vehicleQueryService.isVehicleLicenseUniqueness(null, vehiclePO.getVehicleLicense()).getData()) {
                 return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportVehicleLicenseAlreadyExists)).build();
            }
            //重构二期之后境内车辆新增vin校验
            if (vehiclePO != null && vehiclePO.getVersionFlag() != null && vehiclePO.getVersionFlag() >= 2 && enumRepository.getAreaScope(vehiclePO.getCityId()) == AreaScopeTypeEnum.DOMESTIC.getCode()) {
                Boolean checkFlag = vehicleRepository.checkVehOnly(vehiclePO.getVin(),TmsTransportConstant.VehOnlyTypeEnum.vehicle_vin.getCode());
                if(checkFlag!=null && checkFlag){
                    return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportVinAlreadyExists)).build();
                }
            }
            vehiclePO.setVehicleStatus(passStatus ? TmsTransportConstant.DrvStatusEnum.ONLINE.getCode() : TmsTransportConstant.DrvStatusEnum.UNACT.getCode());
            //境外车辆 直接审批上线
            if(enumRepository.getAreaScope(vehiclePO.getCityId()) == AreaScopeTypeEnum.OVERSEAS.getCode()){
                vehiclePO.setVehicleStatus(TmsTransportConstant.DrvStatusEnum.ONLINE.getCode());
                //按城市配置，境外车辆审核通过后未激活
                if(CtripCommonUtils.verdictIdConfInside(enumRepository.getCountryId(vehiclePO.getCityId()),overseasQconfig.getVehicleUnactCountryConf())){
                    vehiclePO.setVehicleStatus(TmsTransportConstant.DrvStatusEnum.UNACT.getCode());
                }

            }
            vehiclePO.setVehicleColorId(recruitingPO.getVehicleColorId().longValue());
            Long vehicleId  = vehicleRepository.addVehicle(vehiclePO);
            if(vehicleId!=null && vehicleId > 0){
                recruitingPO.setModifyUser(operator);
                recruitingPO.setVehicleOfficialId(vehicleId);
                recruitingPO.setApproverStatus(TmsTransportConstant.RecruitingApproverStatusEnum.finish.getCode());
                recruitingPO.setCheckStatus(1);
                recruitingPO.setApproveSchedule(1);
                recruitingPO.setSupplierApproveSchedule(1);
                recruitingPO.setApproveAging(TmsTransportConstant.ApproveAgingEnum.NOTIMEOUT.getCode());
                // 同步设置
                vehicleRecruitingRepository.update(recruitingPO);
                tmsModRecordCommandService.drvVehRecruitingInsertModRrdList(recruitingIdList, TmsTransportConstant.RecruitingApproverStatusEnum.finish.getCode(), operator, remark,approveFrom, TmsTransportConstant.ApproverAperationEnum.pass_no_activation.getCode());
                //同步核验结果
                if(Objects.equals(enumRepository.getAreaScope(vehiclePO.getCityId()),AreaScopeTypeEnum.DOMESTIC.getCode())){
                    synchronousVehicleCheckInfo(recruitingPO.getVehicleId(),vehicleId,operator);
                }
                //同步单项
                synChildCheckStatus(recruitingIdList.get(0),TmsTransportConstant.RecruitingTypeEnum.vehicle,10,operator,null,null,null);
                //Vbk审核通过的车辆，将历史临派废弃车辆置为正式废弃
                if(enumRepository.getAreaScope(vehiclePO.getCityId()) == AreaScopeTypeEnum.OVERSEAS.getCode()){
                    vehicleCommandService.temToOfficialSendQmq(null,vehiclePO.getVehicleLicense(),vehiclePO.getModifyUser());
                }
                return Result.Builder.<Boolean>newResult().success().withData(true).build();
            }
        }
        return Result.Builder.<Boolean>newResult()
                .fail()
                .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgUpdateError))
                .withData(false)
                .build();
    }

    private Map<Long, Long> getVehicleRecruitingIdDrvRecruitingIdMap(List<DrvRecruitingPO> recruitingPOList) {
//        List<Long> vehicleIdList = Lists.newArrayListWithCapacity(recruitingPOList.size());
        Map<Long, Long> vehicleRecruitingIdDrvRecruitingIdMap = Maps.newHashMapWithExpectedSize(recruitingPOList.size());
        for (DrvRecruitingPO drvRecruitingPO : recruitingPOList) {
            if (drvRecruitingPO.getDrvFrom().intValue() == TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode().intValue()) {
//                vehicleIdList.add(drvRecruitingPO.getVehicleId());
                vehicleRecruitingIdDrvRecruitingIdMap.put(drvRecruitingPO.getVehicleId(), drvRecruitingPO.getDrvRecruitingId());
            }
        }
        return vehicleRecruitingIdDrvRecruitingIdMap;
    }

    /**
     * 1.校验车辆车牌号有效性
     * 2.添加车辆信息
     * 3.将信息回调到审批表
     */
    public Map<Long, Long> getVehicleRecruitingIdVehicleIdMap(Set<Long> vehicleRecruitingIdSet, String operator,boolean passStatus){
        if (CollectionUtils.isEmpty(vehicleRecruitingIdSet)) {
            return Maps.newHashMap();
        }
        Map<Long, Long> vehicleRecruitingIdVehicleIdMap = Maps.newHashMapWithExpectedSize(vehicleRecruitingIdSet.size());
        try {
            List<VehicleRecruitingPO> vehicleRecruitingPOList = vehicleRecruitingRepository.getVehicleRecruitingList(vehicleRecruitingIdSet);
            if (CollectionUtils.isEmpty(vehicleRecruitingPOList)) {
                return Maps.newHashMap();
            }
            for (VehicleRecruitingPO vehicleRecruitingPO : vehicleRecruitingPOList) {
                if (!vehicleQueryService.isVehicleLicenseUniqueness(null, vehicleRecruitingPO.getVehicleLicense()).getData()) {
                    if(enumRepository.getAreaScope(vehicleRecruitingPO.getCityId()) == AreaScopeTypeEnum.DOMESTIC.getCode()
                            ||(enumRepository.getAreaScope(vehicleRecruitingPO.getCityId()) == AreaScopeTypeEnum.OVERSEAS.getCode() && vehicleRecruitingPO.getVersionFlag() >=5)){
                        throw new Exception(String.format(SharkUtils.getSharkValue(SharkKeyConstant.transportVehicleLicenseAlreadyExists) + ":%s", vehicleRecruitingPO.getVehicleLicense()));
                    }else {
                        vehicleRecruitingIdVehicleIdMap.put(-1L,-1L);
                        return vehicleRecruitingIdVehicleIdMap;
                    }

                }
                //境内车辆新增vin校验
                if(vehicleRecruitingPO.getVersionFlag() >= 2 && enumRepository.getAreaScope(vehicleRecruitingPO.getCityId()) == AreaScopeTypeEnum.DOMESTIC.getCode() && vehicleRepository.checkVehOnly(vehicleRecruitingPO.getVin(),TmsTransportConstant.VehOnlyTypeEnum.vehicle_vin.getCode())){
                    throw new BaijiRuntimeException(String.format(SharkUtils.getSharkValue(SharkKeyConstant.transportVinAlreadyExists) + ":%s", vehicleRecruitingPO.getVin()));
                }
                Boolean hasdrv = Boolean.TRUE;
                //境外H5入注的车辆，进入正式表默认未绑定，和正式司机分开
                if(enumRepository.getAreaScope(vehicleRecruitingPO.getCityId()) == 1){
                    hasdrv = Boolean.FALSE;
                    //境外车辆审批通过直接上线
                    passStatus = Boolean.TRUE;
                    //按城市配置，境外车辆审核通过后未激活
                    if(CtripCommonUtils.verdictIdConfInside(enumRepository.getCountryId(vehicleRecruitingPO.getCityId()),overseasQconfig.getVehicleUnactCountryConf())){
                        passStatus = Boolean.FALSE;
                    }
                }
                //审批通过,入车辆正式表
                Long vehicleId = this.approveAddVehicle(vehicleRecruitingPO,hasdrv,passStatus);
                if (vehicleId == null || vehicleId == 0) {
                    throw new Exception("add vehicle error ");
                }
                // 操作人
                vehicleRecruitingPO.setModifyUser(operator);
                // 回填正式id
                vehicleRecruitingPO.setVehicleOfficialId(vehicleId);
                // 将状态置成完成
                vehicleRecruitingPO.setApproverStatus(TmsTransportConstant.RecruitingApproverStatusEnum.finish.getCode());
                vehicleRecruitingPO.setCheckStatus(1);
                vehicleRecruitingPO.setApproveAging(TmsTransportConstant.ApproveAgingEnum.NOTIMEOUT.getCode());
                vehicleRecruitingPO.setApproveSchedule(1);
                vehicleRecruitingPO.setSupplierApproveSchedule(1);
                // 同步设置
                vehicleRecruitingRepository.getVehicleRecruitingRepo().update(vehicleRecruitingPO);
                // recruitingId / vehicleId
                vehicleRecruitingIdVehicleIdMap.put(vehicleRecruitingPO.getVehicleId(), vehicleId);
                //同步核验结果
                if(Objects.equals(enumRepository.getAreaScope(vehicleRecruitingPO.getCityId()),AreaScopeTypeEnum.DOMESTIC.getCode())){
                    synchronousVehicleCheckInfo(vehicleRecruitingPO.getVehicleId(),vehicleId,operator);
                }
                //同步单项
                synChildCheckStatus(vehicleRecruitingPO.getVehicleId(),TmsTransportConstant.RecruitingTypeEnum.vehicle,10,operator,null,null,null);
            }
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }

        return vehicleRecruitingIdVehicleIdMap;
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> recruitingUpdatePass(List<Long> recruitingIdList, String operator, boolean passStatus, String remark, Integer approveFrom, List<RejectReason> remarkList) {
        String passCode = "";
        try {
            if (StringUtils.isBlank(remark) && CollectionUtils.isNotEmpty(remarkList)) {
                StringJoiner remarkJoiner = new StringJoiner("，");
                remarkList.forEach(item -> {
                    remarkJoiner.add(SharkUtils.getSharkValue(item.getCode()));
                });
                remark = remarkJoiner.toString();
            }
            //操作工作台创建的车辆
            if(Objects.equals(approveFrom, TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode())){
                return this.recruitingVehUpdatePass(recruitingIdList,passStatus,operator,remark,approveFrom);
            }

            List<DrvRecruitingPO> recruitingPOList = drvRecruitingRepository.getDrvRecruitingList(recruitingIdList);
            if (CollectionUtils.isEmpty(recruitingPOList) || (recruitingPOList.get(0).getDrvId() != null && recruitingPOList.get(0).getDrvId() > 0)) {
                return Result.Builder.<Boolean>newResult()
                        .success()
                        .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportQueryDataIsEmpty))
                        .withData(false)
                        .build();
            }
            //废弃的数据，无审批操作
            if(!recruitingPOList.get(0).getActive()){
                return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.driverlistpageDiscardtipWrongAlreadydiscard)).build();
            }
            // key drvRecruitingId / value vehicleRecruitingId
            Map<Long, Long> vehicleRecruitingIdDrvRecruitingIdMap = getVehicleRecruitingIdDrvRecruitingIdMap(recruitingPOList);
            Map<Long, Long> vehicleRecruitingIdVehicleIdMap = getVehicleRecruitingIdVehicleIdMap(vehicleRecruitingIdDrvRecruitingIdMap.keySet(), operator,passStatus);
            if(MapUtils.isNotEmpty(vehicleRecruitingIdVehicleIdMap) && vehicleRecruitingIdVehicleIdMap.get(-1L) != null){
                passCode = pass_error_veh_code;
            }
            for (DrvRecruitingPO drvRecruitingPO : recruitingPOList) {
                DrvDriverPO driverPO = new DrvDriverPO();
                driverPO.setDrvStatus(passStatus ? TmsTransportConstant.DrvStatusEnum.ONLINE.getCode() : TmsTransportConstant.DrvStatusEnum.UNACT.getCode());
                // 避免10 1期间境外与境内bd时差导致司机无法完成客人服务，故所有境外服务的司机直接审批通过并激活
                if ("true".equalsIgnoreCase(approvalProcessAuthQconfig.getExemptBDActivateSwitch())) {
                    if (drvRecruitingPO.getInternalScope() != null && drvRecruitingPO.getInternalScope().intValue() == 1) {
                        driverPO.setDrvStatus(TmsTransportConstant.DrvStatusEnum.ONLINE.getCode());
                        passStatus = true;
                    }
                }
                drvRecruitingPO.setModifyUser(operator);
                BeanUtils.copyProperties(drvRecruitingPO, driverPO);
                Long vehicleId;
                Long formalVehicleId = vehicleRecruitingIdVehicleIdMap.get(drvRecruitingPO.getVehicleId());//正式车辆ID
                Long vehcileRecruitingId = drvRecruitingPO.getVehicleId();//招募车辆ID
                // 如果是工作台来的审批记录就是车辆id 如则需要从map中拿到id
                driverPO.setVehicleId((vehicleId = vehicleRecruitingIdVehicleIdMap.get(drvRecruitingPO.getVehicleId())) == null ? drvRecruitingPO.getVehicleId() : vehicleId);
                //通过并激活，刷新 司机上线时间,否则给个默认时间
                if(passStatus){
                    driverPO.setOnlineTime(new Timestamp(System.currentTimeMillis()));
                }else{
                    driverPO.setOnlineTime(DateUtil.string2Timestamp("2020-01-01 00:00:00",DateUtil.YYYYMMDDHHMMSS));
                }
                driverPO.setVehBindTime(driverPO.getOnlineTime());
                driverPO.setCoopMode(TmsTransportConstant.DrvCoopModeEnum.NO.getCode());
                 //需求 1029819 没有英文名字则使用中文的名字的拼音
                if(StringUtils.isEmpty(drvRecruitingPO.getDrvEnglishName())&&StringUtils.isNotEmpty( drvRecruitingPO.getDrvName() )){
                    driverPO.setDrvEnglishName( PinyinUtil.getPinyin( drvRecruitingPO.getDrvName() , CharSequenceUtil.EMPTY) );
                }
                //举牌接机服务默认选项
                driverPO.setRaisingPickUp(raisingPickUpInitValue(driverPO.getCityId()));
                driverPO.setDatachangeCreatetime(DateUtil.getNow());
                //境外H5入注的司机，默认不和车辆绑定
                if(enumRepository.getAreaScope(drvRecruitingPO.getCityId()) == 1 && Objects.equals(drvRecruitingPO.getDrvFrom(),DrvFromEnum.DRV_AUTO.getCode())){
                    driverPO.setVehicleId(0L);
                    driverPO.setVehicleLicense("");
                    driverPO.setVehicleTypeId(0L);
                }
                //账户类型 默认 境内-ppm 境外-派安盈
                driverPO.setAccountType(enumRepository.getAreaScope(drvRecruitingPO.getCityId()) == AreaScopeTypeEnum.DOMESTIC.getCode().intValue() ? PayAccountTypeEnum.PPM.getCode() : PayAccountTypeEnum.PAIAY.getCode());

                // 判断创建为未激活还是已上线
                boolean setInactiveStatus = inLogInActiveCity(driverPO);

                /**
                 * 对于inLogInActiveCity方法中返回true的司机，需要插入未登端的未激活原因，同时进行IVR校验
                 * 对于inLogInActiveCity方法中返回false的司机，如果司机是H5页面的来源，那么直接上线，不进行IVR校验，如果不是H5页面的来源，那么不插入未登端的未激活原因，然后进行IVR校验
                 *
                 * 在IVRCallResultListener中获取IVR的结果，如果接通过了，然后判断未激活原因中是否还有数据，如果没有，那么直接上线。如果没有接通，那么插入未接通的未激活原因
                 */
                if(setInactiveStatus) {
                    Cat.logEvent(CatEventType.DRIVER_STATUS, "set_inactive_status", Event.SUCCESS, "reason:inLogInActiveCity_true");
                    driverPO.setDrvStatus(DrvStatusEnum.UNACT.getCode());
                } else {
                    // 如果inLogInActiveCity返回false，检查是否是H5来源
                    if (Objects.equals(driverPO.getDrvFrom(), TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode())) {
                        // H5来源直接上线，不需要IVR校验
                        Cat.logEvent(CatEventType.DRIVER_STATUS, "set_online_status", Event.SUCCESS, "reason:H5_source");
                        driverPO.setDrvStatus(DrvStatusEnum.ONLINE.getCode());
                    } else {
                        // 非H5来源，设置为未激活状态，后续进行IVR校验
                        Cat.logEvent(CatEventType.DRIVER_STATUS, "set_inactive_status", Event.SUCCESS, "reason:non_H5_source");
                        driverPO.setDrvStatus(DrvStatusEnum.UNACT.getCode());
                    }
                }
                Result<Long> result = driverCommandService.addDrvOfficial(driverPO);
                if (result != null && result.isSuccess()) {
                    // 判断创建为未激活还是已上线
                    if(setInactiveStatus) {
                        // 对于inLogInActiveCity方法中返回true的司机，需要插入未登端的未激活原因，同时进行IVR校验
                        Cat.logEvent(CatEventType.DRIVER_ACTIVATION, "insert_not_login_reason", Event.SUCCESS, "driverId:" + driverPO.getDrvId());
                        tmsDrvInactiveReasonRepository.insert(TmsDrvInactiveReasonPO.builder().drvId(driverPO.getDrvId()).reasonCode(DrvInActiveEnum.NOT_LOGIN.getCode()).reasonDesc(DrvInActiveEnum.getReasonDesc(DrvInActiveEnum.NOT_LOGIN.getCode())).active(true).build());
                        callIVR(driverPO);
                    } else {
                        Cat.logEvent(CatEventType.DRIVER_ACTIVATION, "process_active_driver", Event.SUCCESS, "driverId:" + driverPO.getDrvId());
                        // 对于inLogInActiveCity方法中返回false的司机
                        if (!Objects.equals(driverPO.getDrvFrom(), TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode())) {
                            Cat.logEvent(CatEventType.DRIVER_ACTIVATION, "non_h5_source", Event.SUCCESS, "driverId:" + driverPO.getDrvId());
                            // 非H5来源，需要进行IVR校验，但不插入未登端的未激活原因
                            callIVR(driverPO);
                        } else {
                            Cat.logEvent(CatEventType.DRIVER_ACTIVATION, "h5_source_no_ivr", Event.SUCCESS, "driverId:" + driverPO.getDrvId());
                        }
                        // H5来源的司机已经在前面设置为上线状态，不需要额外处理
                    }
                    // 调用账号中心创建账号
                    registryDriverAccountSuccess(driverPO, result.getData(), DrvStatusEnum.UNACT.getCode().intValue());
                    drvRecruitingPO.setModifyUser(operator);
                    drvRecruitingPO.setDrvId(result.getData());
                    drvRecruitingPO.setApproverStatus(TmsTransportConstant.RecruitingApproverStatusEnum.finish.getCode());
                    drvRecruitingPO.setCheckStatus(1);
                    drvRecruitingPO.setApproveAging(TmsTransportConstant.ApproveAgingEnum.NOTIMEOUT.getCode());
                    drvRecruitingPO.setApproveSchedule(1);
                    drvRecruitingPO.setSupplierApproveSchedule(1);
                    // 同步设置
                    drvRecruitingRepository.getDrvRecruitingRepo().update(drvRecruitingPO);
                    tmsModRecordCommandService.drvVehRecruitingInsertModRrdList(recruitingIdList, TmsTransportConstant.RecruitingApproverStatusEnum.finish.getCode(), operator, remark,approveFrom,TmsTransportConstant.ApproverAperationEnum.pass_no_activation.getCode());
                    // TODO 司机入驻携程大学
                    tmsQmqProducerCommandService.sendPushDataToCtripUniversityQmq(result.getData());
                    //同步三方核验结果
                    if(Objects.equals(driverPO.getInternalScope(),AreaScopeTypeEnum.DOMESTIC.getCode())){
                        synchronousDrvCheckInfo(drvRecruitingPO.getDrvRecruitingId(),result.getData(),drvRecruitingPO.getDrvFrom(),operator);
                    }
                    //处理核酸疫苗报告
                    TmsCertificateCheckPO nucleicAcidCheck = checkRepository.queryCertificateCheckByCondition(result.getData(), TmsTransportConstant.CertificateCheckTypeEnum.DRV.getCode(), TmsTransportConstant.CertificateTypeEnum.NUCLEIC_ACID.getCode());
                    //核酸为空,不同步数据
                    if(StringUtils.isEmpty(drvRecruitingPO.getNucleicAcidReportImg())){
                        nucleicAcidCheck = null;
                    }
                    //处理疫苗报告
                    TmsCertificateCheckPO vaccineCheck = checkRepository.queryCertificateCheckByCondition(result.getData(), TmsTransportConstant.CertificateCheckTypeEnum.DRV.getCode(), TmsTransportConstant.CertificateTypeEnum.VACCINE.getCode());
                    if(StringUtils.isEmpty(drvRecruitingPO.getVaccineReportImg())){
                        vaccineCheck = null;
                    }
                    //处理疫苗标签
                    dealTmsCertificateCheck(result.getData(), drvRecruitingPO, nucleicAcidCheck, vaccineCheck);
                    //同步单项
                    synChildCheckStatus(drvRecruitingPO.getDrvRecruitingId(),TmsTransportConstant.RecruitingTypeEnum.drv,10,operator,null,null,null);
                    //招募司机成功正式司机后，给司机端发消息，司机端计算司机等级
                    tmsQmqProducerCommandService.sendDriverRegisterQmq(result.getData(),driverPO.getCityId(),productionLineUtil.getShowProductionLineList(drvRecruitingPO.getCategorySynthesizeCode()));
                    // 去实名认证
                    realNameAuthentication(result.getData());
                } else {
                    //境内司机或是工作台创建的司机，原逻辑不变
                    //如果境外H5入注，司机没有车，如果司机审批通过失败，则按原逻辑返回错误码
                    if((Objects.equals(recruitingPOList.get(0).getInternalScope(),AreaScopeTypeEnum.OVERSEAS.getCode()) && (recruitingPOList.get(0).getVehicleId() == null || recruitingPOList.get(0).getVehicleId()<=0))
                            ||Objects.equals(recruitingPOList.get(0).getInternalScope(),AreaScopeTypeEnum.DOMESTIC.getCode()) || Objects.equals(DrvFromEnum.DRV_MANUAL.getCode(),recruitingPOList.get(0).getDrvFrom())
                            ||(Objects.equals(recruitingPOList.get(0).getInternalScope(),AreaScopeTypeEnum.OVERSEAS.getCode()) && recruitingPOList.get(0).getVersionFlag()>= 5)){
                        return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(result.getMsg()).withData(false).build();
                    }else {
                        //境外司机入注失败，如果车辆和司机都入注失败，则返回相应该code值
                        if(StringUtils.equals(pass_error_veh_code,passCode)){
                            passCode = pass_error_all_code;
                        }else {
                            passCode = pass_error_drv_code;
                        }
                    }
                }
            }
            if(StringUtils.isNotEmpty(passCode)){
                return Result.Builder.<Boolean>newResult().fail().withCode(passCode).build();
            }
        } catch (Exception throwables) {
            return Result.Builder.<Boolean>newResult()
                    .fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgUpdateError) + throwables.getLocalizedMessage())
                    .withData(false)
                    .build();
        }
        return Result.Builder.<Boolean>newResult()
                .success()
                .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgFinish))
                .withData(true)
                .build();
    }

    private void callIVR(DrvDriverPO driverPO) {
        try {
            // H5来源不进行IVR校验
            if (Objects.equals(driverPO.getDrvFrom(), DrvFromEnum.DRV_AUTO.getCode())) {
                Cat.logEvent(CatEventType.DRIVER_ACTIVATION, "callIVR_skip_h5_source", Event.SUCCESS, "driverId:" + driverPO.getDrvId());
                return;
            }
            if (Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(), driverPO.getInternalScope())) {
                Cat.logEvent(CatEventType.DRIVER_ACTIVATION, "callIVR_skip_domestic", Event.SUCCESS, "driverId:" + driverPO.getDrvId());
                return;
            }

            // 使用IVRCallService发起IVR电话验证
            Long taskId = ivrCallService.callPhoneForVerify(driverPO.getDrvPhone(), driverPO.getIgtCode(), "tms_driver_register");

            // 发送3分钟的延迟消息
            if (taskId != null) {
                Cat.logEvent(CatEventType.DRIVER_ACTIVATION, "callIVR_send_delay_message", Event.SUCCESS, "driverId:" + driverPO.getDrvId() + ",callTaskId:" + taskId);
                Map<String, Object> params = Maps.newHashMap();
                params.put("drvId", driverPO.getDrvId());
                params.put("callTaskId", taskId);
                params.put("phoneNumber", driverPO.getDrvPhone());
                tmsQmqProducer.sendDelayMessage(
                    TmsTransportConstant.QmqSubject.SUBJECT_IVR_CALL_VERIFICATION,
                    Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_IVR_CALL_VERIFICATION),
                    tmsTransportQconfig.getIvrCallVerificationDelayMinutes(),
                    TimeUnit.MINUTES,
                    params
                );
            } else {
                Cat.logEvent(CatEventType.DRIVER_ACTIVATION, "callIVR_no_task_id", "FAIL", "driverId:" + driverPO.getDrvId());
            }
        } catch (Exception e) {
            log.error("callIVR error", e);
        }
    }

    /**
     * 实名认证
     *
     * @param drvId
     */
    protected void realNameAuthentication(Long drvId) {
        try {
            tmsQmqProducerCommandService.sendRealNameAuth(Collections.singletonList(drvId));
        } catch (Exception e) {
            log.warn("sendRealNameAuth error", e);
        }
    }

    protected boolean registryDriverAccountSuccess(DrvDriverPO driverPO, Long id, Integer originStatus) {
        driverPO.setDrvId(id);
        DriverAccountRegisterResultDTO driverAccountRegisterResultDTO =
          driverAccountManagementHelper.registerDriverUserAccount(driverPO);
        if(driverAccountRegisterResultDTO.isNeedRegisterAccount()) { // 如果需要注册
            if (driverAccountRegisterResultDTO.isRegisterSuccess()) { // 如果注册成功, 更新司机账号
                driverCommandService.updateDrvUid(driverPO.getDrvId(), driverAccountRegisterResultDTO.getUid(), driverPO.getModifyUser(), driverAccountRegisterResultDTO.getPpmAccount(), driverAccountRegisterResultDTO.getQunarAccount());
            }else { // 注册失败,回滚状态
                driverCommandService.updateDrvStatus(Lists.newArrayList(id), originStatus, driverPO.getModifyUser());
                tmsDrvInactiveReasonRepository.insert(TmsDrvInactiveReasonPO.builder().drvId(driverPO.getDrvId()).reasonCode(DrvInActiveEnum.ACCOUNT_REGISTER_ERROR.getCode()).reasonDesc(driverAccountRegisterResultDTO.getErrorMsg()).build());
            }
        }
        return true;

    }

    protected boolean inLogInActiveCity(DrvDriverPO driverPO) {
        // 是否在白名单中
        return !overseasQconfig.getLoginActiveWithedSupplierList().stream().anyMatch(supplierId -> Objects.equals(supplierId, driverPO.getSupplierId()))
          && (overseasQconfig.getLoginActiveGrayCityList().contains(-1L)|| overseasQconfig.getLoginActiveGrayCityList().contains(driverPO.getCityId()));
    }

    public void dealTmsCertificateCheck(Long recruitingId, DrvRecruitingPO drvRecruitingPO,TmsCertificateCheckPO nucleicAcidCheck, TmsCertificateCheckPO vaccineCheck) throws SQLException {
        if (nucleicAcidCheck != null || vaccineCheck != null) {
            DrvEpidemicPreventionControlInfoPO po = new DrvEpidemicPreventionControlInfoPO();
            po.setDrvId(recruitingId);
            if (vaccineCheck == null) {
                po.setVaccineReportStatus(TmsTransportConstant.CheckStatusEnum.INIT.getCode());
                po.setVaccineReportResultStatus(INIT.getCode());
            } else {
                if (vaccineCheck.getCheckStatus().intValue() == TmsTransportConstant.CheckStatusEnum.THROUGH.getCode().intValue()) {
                    po.setVaccineReportStatus(TmsTransportConstant.CheckStatusEnum.THROUGH.getCode());
                    po.setVaccineReportResultStatus(PASS.getCode());
                } else {
                    po.setVaccineReportStatus(TmsTransportConstant.CheckStatusEnum.ERROR.getCode());
                    po.setVaccineReportResultStatus(HUMAN_AUDIT_FAILURE.getCode());
                }
                po.setVaccineReportUrl(drvRecruitingPO.getVaccineReportImg());
                po.setVaccineReportDescribe(drvRecruitingPO.getOcrVaccineData());
                po.setVaccineReportTempContent(drvRecruitingPO.getOcrVaccineData());
                if (!Strings.isNullOrEmpty(drvRecruitingPO.getVaccinationTimeList())) {
                    String[] timeList = drvRecruitingPO.getVaccinationTimeList().split(",");
                    po.setVaccinationCount(timeList.length);
                    for (int i = 0; i < timeList.length; i++) {
                        java.util.Date date = DateUtil.stringToDate(timeList[i], DateUtil.YYYYMMDD);
                        if (date == null) {continue;}
                        Date vaccinationDate = new Date(date.getTime());
                        if (i == 0) {
                            po.setFirstVaccinationTime(vaccinationDate);
                        }
                        if (i == 1) {
                            po.setSecondVaccinationTime(vaccinationDate);
                        }
                        if (i == 2) {
                            po.setThirdVaccinationTime(vaccinationDate);
                        }
                    }
                }
                SaveDriverSafetyInfoSOARequestType ocrRequest = JsonUtil.fromJson(drvRecruitingPO.getOcrVaccineData(), new TypeReference<SaveDriverSafetyInfoSOARequestType>() {
                });
                po.setVaccineName(ocrRequest.getVaccineName());
            }
            if (nucleicAcidCheck == null) {
                po.setNucleicAcidReportStatus(TmsTransportConstant.CheckStatusEnum.INIT.getCode());
                po.setNucleicAcidReportResultStatus(INIT.getCode());
            } else {
                if (nucleicAcidCheck.getCheckStatus().intValue() == TmsTransportConstant.CheckStatusEnum.THROUGH.getCode().intValue()) {
                    po.setNucleicAcidReportStatus(TmsTransportConstant.CheckStatusEnum.THROUGH.getCode());
                    po.setNucleicAcidReportResultStatus(PASS.getCode());
                } else {
                    po.setNucleicAcidReportStatus(TmsTransportConstant.CheckStatusEnum.ERROR.getCode());
                    po.setNucleicAcidReportResultStatus(HUMAN_AUDIT_FAILURE.getCode());
                }
                po.setNucleicAcidReportUrl(drvRecruitingPO.getNucleicAcidReportImg());
                po.setNucleicAcidReportDescribe(drvRecruitingPO.getOcrNucleicAcidData());
                po.setNucleicAcidReportTempContent(drvRecruitingPO.getOcrNucleicAcidData());
                po.setNucleicAcidTestingTime(drvRecruitingPO.getNucleicAcidTestingTime());
                SaveDriverSafetyInfoSOARequestType ocrRequest = JsonUtil.fromJson(drvRecruitingPO.getOcrNucleicAcidData(), new TypeReference<SaveDriverSafetyInfoSOARequestType>() {
                });
                po.setNucleicAcidTestingResult(ocrRequest.getNucleicAcidTestingResult());
            }
            drvEpidemicPreventionControlInfoRepository.addEpidemicPreventionControlInfo(po);
        }
    }

    @Override    //动作 1:供应商通过 2:供应商驳回 3:运营驳回 4:运营通过并激活 5:运营通过未激活
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> approveRoute(RecruitingApproveSOARequestType recruitingApproveSOARequestType,Integer approveFrom) {
        switch (recruitingApproveSOARequestType.getData().getAction()) {
            case 0:
                return recruitingUpdateStatus(recruitingApproveSOARequestType.getData().getMediumIdList(), TmsTransportConstant.RecruitingApproverStatusEnum.wait_Approve.getCode(), recruitingApproveSOARequestType.getData().getOperator(), recruitingApproveSOARequestType.getData().getRemark(), approveFrom,recruitingApproveSOARequestType.getData().getApproverAperation(),recruitingApproveSOARequestType.getData().getRemarkList());
            case 1:
                return recruitingUpdateStatus(recruitingApproveSOARequestType.getData().getMediumIdList(), TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode(), recruitingApproveSOARequestType.getData().getOperator(), recruitingApproveSOARequestType.getData().getRemark(), approveFrom,recruitingApproveSOARequestType.getData().getApproverAperation(),recruitingApproveSOARequestType.getData().getRemarkList());
            case 2:
                return recruitingUpdateStatus(recruitingApproveSOARequestType.getData().getMediumIdList(), TmsTransportConstant.RecruitingApproverStatusEnum.supplier_turnDown.getCode(), recruitingApproveSOARequestType.getData().getOperator(), recruitingApproveSOARequestType.getData().getRemark(), approveFrom,recruitingApproveSOARequestType.getData().getApproverAperation(),recruitingApproveSOARequestType.getData().getRemarkList());
            case 3:
                return recruitingUpdateStatus(recruitingApproveSOARequestType.getData().getMediumIdList(), TmsTransportConstant.RecruitingApproverStatusEnum.operating_turnDown.getCode(), recruitingApproveSOARequestType.getData().getOperator(), recruitingApproveSOARequestType.getData().getRemark(), approveFrom,recruitingApproveSOARequestType.getData().getApproverAperation(),recruitingApproveSOARequestType.getData().getRemarkList());
            case 4: // 审核通过直接上线
                return recruitingUpdatePass(recruitingApproveSOARequestType.getData().getMediumIdList(), recruitingApproveSOARequestType.getData().getOperator(), true, recruitingApproveSOARequestType.getData().getRemark(), approveFrom,recruitingApproveSOARequestType.getData().getRemarkList());
            case 5: // 审核通过，未激活
                return recruitingUpdatePass(recruitingApproveSOARequestType.getData().getMediumIdList(), recruitingApproveSOARequestType.getData().getOperator(), false, recruitingApproveSOARequestType.getData().getRemark(),approveFrom,recruitingApproveSOARequestType.getData().getRemarkList());
        }
        return null;
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> recruitingApproveAdd(String roleCode, Integer areaScope, Integer drvFrom, Long mediumId, String operator, String remark,Integer approveFrom) {
        Map<String, Integer> codeMap = commonCommandService.getJurisdictionMap(roleCode, areaScope, drvFrom, TmsTransportConstant.RecruitingApproverStatusEnum.wait_Approve.getCode());
        Integer defaultPassFlowJurisdiction;
        if (codeMap == null || (defaultPassFlowJurisdiction = codeMap.get("pass")) == null) {
            // 无变更权限操作
            return Result.Builder.<Boolean>newResult()
                    .success()
                    .withData(true)
                    .build();
        }
        RecruitingApproveSOARequestType soaRequestType = new RecruitingApproveSOARequestType();
        RecruitingApproveSOARequestDTO data = new RecruitingApproveSOARequestDTO();
        data.setAction(getDefaultAddAction(defaultPassFlowJurisdiction));
        data.setMediumIdList(ImmutableList.of(mediumId));
        data.setOperator(operator);
        data.setRemark(remark);
        data.setApproverAperation(TmsTransportConstant.ApproverAperationEnum.work_register_submit.getCode());
        soaRequestType.setData(data);
        Result<Boolean> result = approveRoute(soaRequestType, approveFrom);
        return result;
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Boolean initSingleApprovalData(Long approveSourceId, Integer approveSourceType,String modifyUser, VehicleAddSOARequestType vehicleRequest, DrvAddSOARequestType drvRequest,List<InitOcrChildCheckStatusSOADTO> checkStatusSOADTOList,Map<String,Object> nucleicAcidMap) {
        //{"1":{"1":"1,2","2":"1","3":"2","4":"1","5":"1,2","6":"1,2","7":"1","8":"1"},"2":{"101":"1,2","102":"1,2","103":"1,2"}}
        String singleValue = tmsTransportQconfig.getInitSingleValue();
        if(StringUtils.isEmpty(singleValue)){
            return Boolean.TRUE;
        }
        Map<Integer,Map<Integer,String>> totalSingleMap = JsonUtil.fromJson(singleValue, new TypeReference<Map<Integer, Map<Integer, String>>>() {
        });
        if(MapUtils.isEmpty(totalSingleMap)){
            return Boolean.TRUE;
        }
        Map<String,Integer> initChildCheckStatusMap = this.getInitChildCheckStatus(checkStatusSOADTOList);
        try {
            //按角色数初始化审批单项，为了后续操作不同角色单项(供应商,BD)
            Map<Integer,String> singleMap = totalSingleMap.get(approveSourceType);
            if(netCardCheckUtil.isNetCardNoNeedCheck(getCityId(vehicleRequest, drvRequest))) {
                singleMap.remove(ApproveItemEnum.net_people.getCode());
                singleMap.remove(ApproveItemEnum.net_vehile.getCode());
            }

            for(TmsTransportConstant.AccountTypeEnum accountTypeEnum : TmsTransportConstant.AccountTypeEnum.values()){
                for(Map.Entry<Integer,String> entry: singleMap.entrySet()){
                    String entryValue = entry.getValue();

                    TmsRecruitingApproveStepPO stepPO = new TmsRecruitingApproveStepPO();
                    stepPO.setApproveSourceId(approveSourceId);
                    stepPO.setApproveType(approveSourceType);
                    stepPO.setApproveItem(entry.getKey());
                    Integer singleApproveStatus = initApproveStatus(entry.getKey(),vehicleRequest,drvRequest);
                    stepPO.setApproveStatus(singleApproveStatus);
                    if(Objects.equals(accountTypeEnum.getValue(),TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue())&&
                            Objects.equals(singleApproveStatus,TmsTransportConstant.SingleApproveStatusEnum.WAITAPPROVE.getCode())){
                        stepPO.setApproveStatus(TmsTransportConstant.SingleApproveStatusEnum.APPROVE_THROUGH.getCode());
                        stepPO.setApproveTime(DateUtil.getNow());
                    }
                    stepPO.setApproveFrom(accountTypeEnum.getValue());
                    stepPO.setCreateUser(modifyUser);
                    stepPO.setModifyUser(modifyUser);
                    Long approveStepId = stepRepository.insert(stepPO);
                    //不审批的单项，没有子项
                    if(StringUtils.isEmpty(entryValue)|| Objects.equals(singleApproveStatus, TmsTransportConstant.SingleApproveStatusEnum.NO_APPROVE.getCode())){
                        continue;
                    }
                    List<Integer> childItem = Arrays.stream(entryValue.split(",|，")).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList());
                    for(Integer integer : childItem){
                        TmsRecruitingApproveStepChildPO childPO = new TmsRecruitingApproveStepChildPO();
                        childPO.setRecruitingApproveStepId(approveStepId);
                        childPO.setChildItem(integer);
                        Integer checkStatus  =  initChildCheckStatusMap.get(entry.getKey()+"_"+integer);
                        if(checkStatus == null){
                            checkStatus = TmsTransportConstant.CheckStatusEnum.INIT.getCode();
                        }
                        childPO.setCheckStatus(checkStatus);
                        //系统自动生成核酸疫苗子标签
                        Map<String,Object> vaccineNucleicChildMap = vaccineNucleicChildMap(entry.getKey(),integer,nucleicAcidMap);
                        if(MapUtils.isNotEmpty(vaccineNucleicChildMap)){
                            if(vaccineNucleicChildMap.get(TmsTransportConstant.RESULTINFO_KEY)!=null){
                                childPO.setCheckStatus(TmsTransportConstant.CheckStatusEnum.THROUGH.getCode());
                            }
                            if(vaccineNucleicChildMap.get(TmsTransportConstant.NUCLEICACIDVACCINE_DESC)!=null){
                                childPO.setChildDesc((String) vaccineNucleicChildMap.get(TmsTransportConstant.NUCLEICACIDVACCINE_DESC));
                            }
                        }
                        childPO.setCreateUser(modifyUser);
                        childPO.setModifyUser(modifyUser);
                        childRepository.insert(childPO);
                    }
                }
            }
            return Boolean.TRUE;
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    protected Long getCityId(VehicleAddSOARequestType vehicleRequest, DrvAddSOARequestType drvRequest) {
        if (vehicleRequest != null) {
            return vehicleRequest.getCityId();
        }
        if (drvRequest != null) {
            return drvRequest.getCityId();
        }
        return null;
    }


    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Boolean initH5SingleApprovalData(Long approveSourceDrvId, Long approveSourceVehicleId, DrvVehRecruitingAddSOARequestType requestType, String modifyUser, Map<String, Object> nucleicAcidMap) {
        //{"1":{"1":"1,2","2":"1","3":"2","4":"1","5":"1,2","6":"1,2","7":"1","8":"1"},"2":{"101":"1,2","102":"1,2","103":"1,2"}}
        String singleValue = tmsTransportQconfig.getInitSingleValue();
        if (StringUtils.isEmpty(singleValue)) {
            return Boolean.TRUE;
        }
        Map<Integer, Map<Integer, String>> totalSingleMap = JsonUtil.fromJson(singleValue, new TypeReference<Map<Integer, Map<Integer, String>>>() {
        });
        if (MapUtils.isEmpty(totalSingleMap)) {
            return Boolean.TRUE;
        }
        try {
            //遍历单项、子项数据
            for (Map.Entry<Integer, Map<Integer, String>> entry : totalSingleMap.entrySet()) {
                Map<Integer, String> mapValue = entry.getValue();
                if (entry.getKey().intValue() == TmsTransportConstant.SingleApproveTypeEnum.DRV.getCode().intValue()) {//司机
                    if (netCardCheckUtil.isNetCardNoNeedCheck(requestType.getCityId())) { // 特定城市不生成网约车证核验项
                        mapValue.remove(ApproveItemEnum.net_people.getCode());
                    }
                    insertSingleValue(mapValue, requestType, TmsTransportConstant.SingleApproveTypeEnum.DRV, approveSourceDrvId, modifyUser, nucleicAcidMap);
                }
                if (entry.getKey().intValue() == TmsTransportConstant.SingleApproveTypeEnum.VEH.getCode().intValue()) {//车辆
                    if (netCardCheckUtil.isNetCardNoNeedCheck(requestType.getCityId())) { // 特定城市不生成网约车证核验项
                        mapValue.remove(ApproveItemEnum.net_vehile.getCode());
                    }
                    insertSingleValue(mapValue, requestType, TmsTransportConstant.SingleApproveTypeEnum.VEH, approveSourceVehicleId, modifyUser, Maps.newHashMap());
                }
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> singleApproval(SingleApprovalSOARequestType requestType) {
        if(CollectionUtils.isEmpty(requestType.getSingleList())){
            return Result.Builder.<Boolean>newResult().success().build();
        }

        //获取当前操作角色,如果未登录，提示权限有问题
        Map<String, String> map = SessionHolder.getSessionSource();
        if(MapUtils.isEmpty(map) || map.get("accountType") == null){
            return Result.Builder.<Boolean>newResult().fail().withCode("403").build();
        }
        //todo 如果单项操作通过,则要判断子项中都为通过
        try {
            Integer approverStatus = null;
            Integer accountType = Integer.parseInt(map.get("accountType"));
            Long cityId = null;
            switch (requestType.getRecruitingType()){
                case 1:DrvRecruitingPO drvRecruitingPO = drvRecruitingRepository.queryByPK(requestType.getRecruitingId());
                    if(!Objects.isNull(drvRecruitingPO)){
                        approverStatus = drvRecruitingPO.getApproverStatus();
                        cityId = drvRecruitingPO.getCityId();
                    }
                    break;
                case 2:VehicleRecruitingPO vehicleRecruitingPO = vehicleRecruitingRepository.queryByPK(requestType.getRecruitingId());
                    if(!Objects.isNull(vehicleRecruitingPO)){
                        approverStatus = vehicleRecruitingPO.getApproverStatus();
                        cityId = vehicleRecruitingPO.getCityId();
                    }
            }
            //如果当前操作角色是BD,则判断当前招募信息审批状态是否是待BD审批,如果不是,则提示语
            if(Objects.equals(accountType, TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue())){
                if(!Objects.equals(approverStatus, TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode())){
                    return Result.Builder.<Boolean>newResult().fail().withCode("601").build();
                }
            }
            //整体审批过，无须再审批
            if(checkNowRoleApproveStatus(approverStatus,cityId,3)){
                return Result.Builder.<Boolean>newResult().fail().withCode("603").withData(false).build();
            }

            List<Long> approveStepId = requestType.getSingleList().stream().map(SingleApprovalSOADTO::getApproveStepId).collect(Collectors.toList());
            List<TmsRecruitingApproveStepPO> stepPOList  =  stepRepository.queryApproveStepByIds(approveStepId);
            List<Long> ids = Lists.newArrayList();
            for(TmsRecruitingApproveStepPO stepPO : stepPOList){
                if(!Objects.equals(stepPO.getApproveStatus(), TmsTransportConstant.SingleApproveStatusEnum.WAITAPPROVE.getCode())){
                    ids.add(stepPO.getId());
                }
                //TODO 判断是否有合规的审核项
                if(Objects.equals(stepPO.getApproveItem(), TmsTransportConstant.ApproveItemEnum.vehicle_compliance.getCode())){
                    Integer approveStatus = stepPO.getApproveStatus();
                }
            }

            //操作单项中如果不是待审核，则单项审批失败 602-该单项已审核完成
            // 且是国内的，国外的对于已审核的数据可以放开
            if(ids.size() > 0 && Objects.equals(enumRepository.getAreaScope(cityId),AreaScopeTypeEnum.DOMESTIC.getCode().intValue())){
                return Result.Builder.<Boolean>newResult().fail().withCode("602").build();
            }
            //运营工作台→平台审核 记录单项和子标签的操作
            if(Objects.equals(accountType, TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue())){
                tmsModRecordCommandService.saveSingleAndChildRrd(getStepModColl(requestType.getSingleList(),requestType.getModifyUser()),getChildStepModColl(requestType.getSingleList(),requestType.getModifyUser()),requestType.getModifyUser(),requestType.getRecruitingId(),requestType.getRecruitingType());
            }
            for(SingleApprovalSOADTO soadto : requestType.getSingleList()){
                //更新单项信息
                int count = stepRepository.updateApproveStatus(soadto.getApproveStepId(), soadto.getApproveStatus(), soadto.getApproveReason(),accountType,requestType.getModifyUser());
                if(count > 0){
                    //更新子项信息
                    List<ApproveStepChildListSOADTO> childList = soadto.getChildList();
                    if(CollectionUtils.isNotEmpty(childList)){
                        for(ApproveStepChildListSOADTO checksoadto : childList){
                            //因为子标签项不在同一个数据源，所以按标签项更亲不同的表，三方核验还是原有逻辑(1,2表示OCR识别/非OCR识别,3表示三方核验)
                            switch (checksoadto.getChildItem()){
                                case 1:
                                case 2:childRepository.updateChildCheckStatus(checksoadto.getApproveStepChildId(),checksoadto.getCheckStatus(),requestType.getModifyUser());
                                break;
                                case 3:checkRepository.updateCheckStatus(checksoadto.getApproveStepChildId(),checksoadto.getCheckStatus(),requestType.getModifyUser());
                            }
                        }
                    }
                    //保存每次审批单项的记录
                    tmsModRecordCommandService.saveRecruitingSingleRrd(requestType.getRecruitingId(),JsonUtil.toJson(requestType.getSingleList()),requestType.getRecruitingType(),requestType.getModifyUser());
                }
                //更新招募进度
                tmsQmqProducerCommandService.sendRecruitingApproveScheduleQMQ(requestType.getRecruitingId(),requestType.getRecruitingType(),accountType);
            }
            return Result.Builder.<Boolean>newResult().success().build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    private Integer getDefaultAddAction(Integer defaultPassFlowJurisdiction) {
        if (defaultPassFlowJurisdiction.intValue() == TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode().intValue()) {
            return TmsTransportConstant.RecruitingApproverActionEnum.supplier_pass.getCode();
        }
        if (defaultPassFlowJurisdiction.intValue() == TmsTransportConstant.RecruitingApproverStatusEnum.finish.getCode().intValue()) {
            return TmsTransportConstant.RecruitingApproverActionEnum.operating_finish_offline.getCode();
        }
        return null;
    }

    private Long approveAddVehicle(VehicleRecruitingPO vehicleRecruitingPO,Boolean hasDrv,boolean passStatus){
        if (!tmsPmsproductQueryService.checkSkuIsExist(vehicleRecruitingPO.getSupplierId())) {
            return 0L;
        }
        VehVehiclePO po =new  VehVehiclePO();
        BeanUtils.copyProperties(vehicleRecruitingPO,po);
        po.setHasDrv(hasDrv);
        po.setVehicleColorId(vehicleRecruitingPO.getVehicleColorId().longValue());
        po.setVehicleStatus(passStatus ? TmsTransportConstant.VehStatusEnum.ONLINE.getCode() : TmsTransportConstant.VehStatusEnum.UNACT.getCode());
        if(vehicleRecruitingPO.getRegstDate()!=null){
            po.setRegstDate(Date.valueOf(DateUtil.timestampToString(vehicleRecruitingPO.getRegstDate(),DateUtil.YYYYMMDD)));
        }
        po.setDatachangeCreatetime(DateUtil.getNow());
        Long vehicleId = vehicleRepository.addVehicle(po);
        if(vehicleId == null || vehicleId <=0){
            return 0L;
        }
        modRecordRespository.insetModRecord(vehicleId, null, CommonEnum.RecordTypeEnum.VEHICLE, changeRecordAttributeNameQconfig.getDrvVehRecruitingRecordMap(), vehicleRecruitingPO.getModifyUser());
        //Vbk审核通过的车辆，将历史临派废弃车辆置为正式废弃
        if(enumRepository.getAreaScope(po.getCityId()) == AreaScopeTypeEnum.OVERSEAS.getCode()){
            vehicleCommandService.temToOfficialSendQmq(null,po.getVehicleLicense(),po.getModifyUser());
        }
        return vehicleId;
    }

    //H5供应商审批过后，需进入三方核验
    private void asyncCertificateCheck(List<Long> drvRecruitingIds,List<Long> vehicleRecruitingIds){
        try {
            List<DrvRecruitingPO> drvRecruitingList =   drvRecruitingRepository.getDrvRecruitingList(drvRecruitingIds);
            if(CollectionUtils.isEmpty(drvRecruitingList)){
                return;
            }
            //司机对应的审验
            for(DrvRecruitingPO drvRecruitingPO : drvRecruitingList){
                //境外司机不核验或者是人工录入司机不核验,三期前的逻辑
                if(Objects.equals(drvRecruitingPO.getInternalScope(),AreaScopeTypeEnum.OVERSEAS.getCode()) || drvRecruitingPO.getVersionFlag() >= 3){
                    continue;
                }
                DrvDriverPO drvDriverPO = new DrvDriverPO();
                BeanUtils.copyProperties(drvRecruitingPO,drvDriverPO);
                drvDriverPO.setDrvId(drvRecruitingPO.getDrvRecruitingId());
                queryService.asyncDrvCertificateCheck(DrvAuditDTO.buildNewAddRecruitingDrvDTO(drvDriverPO,TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV.getCode()),Boolean.TRUE);

                drvVehRecruitingCommandService.toDoNucleicAcidLabel(drvRecruitingPO.getDrvRecruitingId(), drvRecruitingPO.getCityId(), drvRecruitingPO.getDrvName(), drvRecruitingPO.getDrvIdcard(), BaseUtil.dealWithDateToStr(drvRecruitingPO.getNucleicAcidTestingTime()), drvRecruitingPO.getOcrNucleicAcidData());
                List<String> timeList = Strings.isNullOrEmpty(drvRecruitingPO.getVaccinationTimeList()) ? Lists.newArrayList() : Lists.newArrayList(drvRecruitingPO.getVaccinationTimeList().split(","));
                drvVehRecruitingCommandService.toDoVaccineLabel(drvRecruitingPO.getDrvRecruitingId(), drvRecruitingPO.getDrvName(), drvRecruitingPO.getDrvIdcard(), timeList, drvRecruitingPO.getOcrVaccineData());
            }
            if(CollectionUtils.isEmpty(vehicleRecruitingIds)){
                return;
            }
            //车辆方三核验
            this.vehicleRecruitingCheck(vehicleRecruitingIds);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    private void vehicleRecruitingCheck(List<Long> vehicleRecruitingIds){
        try {
            Set<Long> longSet = vehicleRecruitingIds.stream().collect(Collectors.toSet());
            List<VehicleRecruitingPO> vehicleRecruitingPOList = vehicleRecruitingRepository.getVehicleRecruitingList(longSet);
            if(CollectionUtils.isEmpty(vehicleRecruitingPOList)){
                return;
            }
            //车辆对应的审验
            for(VehicleRecruitingPO vehicleRecruitingPO : vehicleRecruitingPOList){
                //境外司机不核验或者是人工录入车辆不核验
                if(Objects.equals(enumRepository.getAreaScope(vehicleRecruitingPO.getCityId()),AreaScopeTypeEnum.OVERSEAS.getCode()) || vehicleRecruitingPO.getVersionFlag() >= 3){
                    continue;
                }
                VehVehiclePO vehiclePO = new VehVehiclePO();
                BeanUtils.copyProperties(vehicleRecruitingPO,vehiclePO);
                queryService.asyncVehicleCertificateCheck(VehicleAuditDTO.updateBuildVehicleDTO(vehiclePO,new VehVehiclePO(), TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode()),Boolean.TRUE);
            }
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    private void synchronousDrvCheckInfo(Long drvRecruitingId,Long drvId,Integer drvFrom,String operator){
        optationData(drvRecruitingId,drvId,TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV.getCode(),TmsTransportConstant.CertificateCheckTypeEnum.DRV.getCode(),operator);
        if(Objects.equals(drvFrom, TmsTransportConstant.DrvFromEnum.DRV_MANUAL.getCode())){
            return;
        }
    }
    private void synchronousVehicleCheckInfo(Long vehcileRecruitingId,Long vehicleId,String operator){
        optationData(vehcileRecruitingId,vehicleId,TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode(),TmsTransportConstant.CertificateCheckTypeEnum.VEHICLE.getCode(),operator);
    }

    private void optationData(Long checkId,Long newCheckid,Integer checkType,Integer newCheckType,String operator){
        Map<Integer, TmsCertificateCheckPO> checkPOMap = queryService.queryCertificateCheckToMap(checkId,checkType);
        if(MapUtils.isEmpty(checkPOMap)){
            return;
        }
        //复制核验数据到正式表
        List<TmsCertificateCheckPO> insertCheck = Lists.newArrayList();
        for(Map.Entry<Integer, TmsCertificateCheckPO> entry : checkPOMap.entrySet()){
            TmsCertificateCheckPO checkPO = entry.getValue();
            checkPO.setCheckId(newCheckid);
            checkPO.setCheckType(newCheckType);
            checkPO.setCheckStatus(TmsTransportConstant.CheckStatusEnum.THROUGH.getCode());
            checkPO.setCreateUser(operator);
            checkPO.setModifyUser(operator);
            insertCheck.add(checkPO);
        }
        checkRepository.batchInsertCheckRecord(insertCheck);
    }

    /**
    　* @description: 封装模拟平台驳回入参
    　* <AUTHOR>
    　* @date 2021/10/9 17:58
    */
    public static RecruitingApproveSOARequestType buildRequest(Long recruitingId){
        RecruitingApproveSOARequestType approveSOARequestType = new RecruitingApproveSOARequestType();
        RecruitingApproveSOARequestDTO requestDTO = new RecruitingApproveSOARequestDTO();
        requestDTO.setAction(3);
        requestDTO.setMediumIdList(Arrays.asList(recruitingId));
        requestDTO.setOperator(TmsTransportConstant.TMS_DEFAULT_USERNAME);
        requestDTO.setApproverAperation(TmsTransportConstant.ApproverAperationEnum.system_auto_audit.getCode());
        approveSOARequestType.setData(requestDTO);
        return approveSOARequestType;
    }

    /**
    　* @description: 根据创建时入参，判断某个单项是否需要审批
    　* <AUTHOR>
    　* @date 2021/12/6 14:49
    */
    public Integer initApproveStatus(Integer approveItem, VehicleAddSOARequestType vehicleRequest, DrvAddSOARequestType drvRequest) {
        switch (approveItem) {
            case 4:
                if (!Objects.isNull(drvRequest) && StringUtils.isEmpty(drvRequest.getNetVehiclePeoImg()) &&
                        StringUtils.isEmpty(drvRequest.getOtherCertificateImg()) &&
                        StringUtils.isEmpty(drvRequest.getNetAppealMaterials())) {
                    return TmsTransportConstant.SingleApproveStatusEnum.NO_APPROVE.getCode();
                }
                break;
            case 5:
                if (!Objects.isNull(drvRequest) && StringUtils.isEmpty(drvRequest.getVaccineReportImg()) &&
                        org.apache.commons.collections.CollectionUtils.isEmpty(drvRequest.getVaccinationTimeList())) {
                    return TmsTransportConstant.SingleApproveStatusEnum.NO_APPROVE.getCode();
                }
                break;
            case 6:
                if (!Objects.isNull(drvRequest) && StringUtils.isEmpty(drvRequest.getNucleicAcidReportImg()) &&
                        StringUtils.isEmpty(drvRequest.getNucleicAcidTestingTime())) {
                    return TmsTransportConstant.SingleApproveStatusEnum.NO_APPROVE.getCode();
                }
                break;
            case 102:
                if (!Objects.isNull(vehicleRequest) && StringUtils.isEmpty(vehicleRequest.getNetTansCtfctImg()) &&
                        StringUtils.isEmpty(vehicleRequest.getNetAppealMaterials())) {
                    return TmsTransportConstant.SingleApproveStatusEnum.NO_APPROVE.getCode();
                }
        }
        return TmsTransportConstant.SingleApproveStatusEnum.WAITAPPROVE.getCode();
    }

    /**
    　* @description: 根据创建时入参，判断某个单项是否需要审批
    　* <AUTHOR>
    　* @date 2021/12/6 14:49
    */
    public Integer initH5ApproveStatus(Integer approveItem, DrvVehRecruitingAddSOARequestType drvVehRequest) {
        if(Objects.isNull(drvVehRequest)){
            return TmsTransportConstant.SingleApproveStatusEnum.WAITAPPROVE.getCode();
        }
        switch (approveItem) {
            case 4:
                if (StringUtils.isEmpty(drvVehRequest.getNetVehiclePeoImg()) &&
                        StringUtils.isEmpty(drvVehRequest.getOtherCertificateImg()) &&
                        StringUtils.isEmpty(drvVehRequest.getNetAppealMaterials())) {
                    return TmsTransportConstant.SingleApproveStatusEnum.NO_APPROVE.getCode();
                }
                break;
            case 5:
                if (StringUtils.isEmpty(drvVehRequest.getVaccineReportImg()) &&
                        org.apache.commons.collections.CollectionUtils.isEmpty(drvVehRequest.getVaccinationTimeList())) {
                    return TmsTransportConstant.SingleApproveStatusEnum.NO_APPROVE.getCode();
                }
                break;
            case 6:
                if (StringUtils.isEmpty(drvVehRequest.getNucleicAcidReportImg()) &&
                        StringUtils.isEmpty(drvVehRequest.getNucleicAcidTestingTime())) {
                    return TmsTransportConstant.SingleApproveStatusEnum.NO_APPROVE.getCode();
                }
                break;
            case 102:
                if (StringUtils.isEmpty(drvVehRequest.getNetTansCtfctImg()) &&
                        StringUtils.isEmpty(drvVehRequest.getNetAppealMaterials())) {
                    return TmsTransportConstant.SingleApproveStatusEnum.NO_APPROVE.getCode();
                }
        }
        return TmsTransportConstant.SingleApproveStatusEnum.WAITAPPROVE.getCode();
    }

    public Boolean insertSingleValue(Map<Integer,String> singleMap, DrvVehRecruitingAddSOARequestType requestType,TmsTransportConstant.SingleApproveTypeEnum singleApproveTypeEnum, Long approveSourceId, String modifyUser,Map<String,Object> nucleicAcidMap){
        try {
            for(TmsTransportConstant.AccountTypeEnum accountTypeEnum : TmsTransportConstant.AccountTypeEnum.values()){
                for(Map.Entry<Integer,String> entry: singleMap.entrySet()){
                    String entryValue = entry.getValue();
                    TmsRecruitingApproveStepPO stepPO = new TmsRecruitingApproveStepPO();
                    stepPO.setApproveSourceId(approveSourceId);
                    stepPO.setApproveType(singleApproveTypeEnum.getCode());
                    stepPO.setApproveFrom(accountTypeEnum.getValue());
                    stepPO.setApproveItem(entry.getKey());
                    Integer singleApproveStatus = initH5ApproveStatus(entry.getKey(),requestType);
                    stepPO.setApproveStatus(singleApproveStatus);
                    stepPO.setCreateUser(modifyUser);
                    stepPO.setModifyUser(modifyUser);
                    Long approveStepId = stepRepository.insert(stepPO);
                    //不审批的单项,子项不创建
                    if(StringUtils.isEmpty(entryValue) || Objects.equals(singleApproveStatus, TmsTransportConstant.SingleApproveStatusEnum.NO_APPROVE.getCode())){
                        continue;
                    }
                    List<Integer> childItem = Arrays.stream(entryValue.split(",|，")).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList());
                    for(Integer integer : childItem){
                        TmsRecruitingApproveStepChildPO childPO = new TmsRecruitingApproveStepChildPO();
                        childPO.setRecruitingApproveStepId(approveStepId);
                        childPO.setChildItem(integer);
                        childPO.setCheckStatus(TmsTransportConstant.CheckStatusEnum.INIT.getCode());
                        //系统自动生成核酸疫苗子标签
                        Map<String,Object> vaccineNucleicChildMap = vaccineNucleicChildMap(entry.getKey(),integer,nucleicAcidMap);
                        if(MapUtils.isNotEmpty(vaccineNucleicChildMap)){
                            if(vaccineNucleicChildMap.get(TmsTransportConstant.RESULTINFO_KEY)!=null){
                                childPO.setCheckStatus(TmsTransportConstant.CheckStatusEnum.THROUGH.getCode());
                            }
                            if(vaccineNucleicChildMap.get(TmsTransportConstant.NUCLEICACIDVACCINE_DESC)!=null){
                                childPO.setChildDesc((String) vaccineNucleicChildMap.get(TmsTransportConstant.NUCLEICACIDVACCINE_DESC));
                            }
                        }
                        childPO.setCreateUser(modifyUser);
                        childPO.setModifyUser(modifyUser);
                        childRepository.insert(childPO);
                    }
                }
            }
            return Boolean.TRUE;
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    public Map<String,Object> vaccineNucleicChildMap(Integer approveItem,Integer childItem,Map<String,Object> nucleicAcidMap){
        Map<String,Object> vaccineNucleicChildMap = Maps.newHashMap();
        if((!Objects.equals(approveItem, TmsTransportConstant.ApproveItemEnum.vaccine.getCode())&&
                !Objects.equals(approveItem, TmsTransportConstant.ApproveItemEnum.nucleicAcid.getCode())) ||
                !Objects.equals(childItem, TmsTransportConstant.ApproveChildItemEnum.OCR.getCode()) || MapUtils.isEmpty(nucleicAcidMap)){
            return vaccineNucleicChildMap;
        }
        if(nucleicAcidMap.get(NUCLEICACIDVACCINE_ITEM+approveItem)!=null){
            Integer childCheckStatus = (Integer) nucleicAcidMap.get(NUCLEICACIDVACCINE_ITEM+approveItem);
            if(Objects.equals(childCheckStatus, TmsTransportConstant.CheckStatusEnum.THROUGH.getCode())){
                vaccineNucleicChildMap.put(TmsTransportConstant.RESULTINFO_KEY,TmsTransportConstant.CheckStatusEnum.THROUGH.getCode());
            }else {
                if(nucleicAcidMap.get(NUCLEICACIDVACCINE_DESC+ approveItem)!=null){
                    vaccineNucleicChildMap.put(TmsTransportConstant.NUCLEICACIDVACCINE_DESC,(String) nucleicAcidMap.get(NUCLEICACIDVACCINE_DESC+ approveItem));
                }
            }
        }
        return vaccineNucleicChildMap;
    }

    public static Map<String,Integer> getInitChildCheckStatus(List<InitOcrChildCheckStatusSOADTO> list){
        Map<String,Integer> map = Maps.newHashMap();
        if(CollectionUtils.isEmpty(list)){
            return map;
        }
        for(InitOcrChildCheckStatusSOADTO checkStatusSOADTO : list){
            map.put(checkStatusSOADTO.getApproveItem()+"_"+checkStatusSOADTO.getChildItem(),checkStatusSOADTO.getCheckStatus());
        }
        return map;
    }

    /**
     * 单项审核
     * @param recruitingId
     * @param recruitingTypeEnum
     * @param approveStatus
     * @param moifyUser
     * @param recordId
     * @param requestRecruitingId
     * @param requestRecruitingType
     * @return
     */
    public Boolean synChildCheckStatus(Long recruitingId, TmsTransportConstant.RecruitingTypeEnum recruitingTypeEnum, int approveStatus, String moifyUser,Long recordId,Long requestRecruitingId,Integer requestRecruitingType) {
        Integer versionFlag = null;
        Integer approveType = null;
        String igtCode = "";
        String drvPhone = "";
        Long cityId = null;
        Integer vehicleFrom =null;
        Integer drvFrom = null;
        Long supplierId = null;
        String drvName = "";
        String datachangeCreatetime = "";
        try {
            switch (recruitingTypeEnum) {
                case drv:
                    DrvRecruitingPO drvRecruitingPO = drvRecruitingRepository.queryByPK(recruitingId);
                    if (!Objects.isNull(drvRecruitingPO)) {
                        versionFlag = drvRecruitingPO.getVersionFlag();
                    }
                    approveType = TmsTransportConstant.SingleApproveTypeEnum.DRV.getCode();
                    igtCode = drvRecruitingPO.getIgtCode();
                    drvPhone = TmsTransUtil.decrypt(drvRecruitingPO.getDrvPhone(),KeyType.Phone);
                    cityId = drvRecruitingPO.getCityId();
                    drvFrom = drvRecruitingPO.getDrvFrom();
                    drvName = drvRecruitingPO.getDrvName();
                    datachangeCreatetime = DateUtil.timestampToString(drvRecruitingPO.getDatachangeCreatetime(),DateUtil.YYYYMMDD);
                    supplierId = drvRecruitingPO.getSupplierId();
                    break;
                case vehicle:
                    VehicleRecruitingPO vehicleRecruitingPO = vehicleRecruitingRepository.queryByPK(recruitingId);
                    if (!Objects.isNull(vehicleRecruitingPO)) {
                        versionFlag = vehicleRecruitingPO.getVersionFlag();
                    }
                    approveType = TmsTransportConstant.SingleApproveTypeEnum.VEH.getCode();
                    cityId = vehicleRecruitingPO.getCityId();
                    vehicleFrom = vehicleRecruitingPO.getVehicleFrom();
            }
            //只有三期会有单项数据,不包含境外
            if (versionFlag == null || versionFlag < 3 || cityId == null || enumRepository.getAreaScope(cityId) == AreaScopeTypeEnum.OVERSEAS.getCode().intValue()) {
                return Boolean.FALSE;
            }
            //供应商驳回到H5,发短信给司机，附带司机招募详情链接
            if(approveStatus == TmsTransportConstant.RecruitingApproverStatusEnum.supplier_turnDown.getCode().intValue() &&
            Objects.equals(TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode(),drvFrom)){
                this.sendSmsToH5(drvPhone,drvName,datachangeCreatetime,supplierId,recruitingId,igtCode);
                return Boolean.TRUE;
            }
            List<TmsRecruitingApproveStepPO> stepPOList = stepRepository.queryApproveStepList(recruitingId, approveType);
            Map<Integer, List<TmsRecruitingApproveStepPO>> integerListMap = stepPOList.stream().collect(Collectors.groupingBy(TmsRecruitingApproveStepPO::getApproveFrom));
            List<Long> idList = stepPOList.stream().map(TmsRecruitingApproveStepPO::getId).collect(Collectors.toList());
            List<Long> bdStepIds = Lists.newArrayList();
            Map<Integer, TmsRecruitingApproveStepPO> bdSingleMap = Maps.newHashMap();
            List<TmsRecruitingApproveStepPO> supplierStepList = Lists.newArrayList();
            List<TmsRecruitingApproveStepPO> bdStepList = Lists.newArrayList();
            encapsulationField(integerListMap,bdStepIds,bdSingleMap,supplierStepList,bdStepList);
            List<TmsRecruitingApproveStepChildPO> childPOList = childRepository.queryChildByStepIdList(idList);
            Map<Long, List<TmsRecruitingApproveStepChildPO>> childMap = childPOList.stream().collect(Collectors.groupingBy(TmsRecruitingApproveStepChildPO::getRecruitingApproveStepId));
            //供应商获取子标签状态
            Map<String, Integer> supplierChildStatusMap = childStatuaMap(supplierStepList, childMap);
            //BD获取 子标签状态
            Map<String, Integer> bdChildStatusMap = childStatuaMap(bdStepList, childMap);
            switch (approveStatus) {
                //H5提交到供应商审批,将供应商单项状态置为待审批
                case 0:
                    stepRepository.updateWaitApproveStatus(recruitingId, approveType, TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue(), moifyUser);
                    break;
                //BD待审核,将BD不通过的单项状态置为待审核,同时供应商子标签同步到BD
                case 4:
                    //BD单项置为待审核
                    stepRepository.updateWaitApproveStatus(recruitingId, approveType, TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue(), moifyUser);
                    //同步子标签
                    updateChildStatus(bdStepList, childMap, supplierChildStatusMap, moifyUser);
                    //供应商提交到BD审批，如果子项标签都是通过,则单项判定为系统自动通过
                    systemAutoPassSingle(bdStepList,supplierStepList,childMap,recruitingId,recruitingTypeEnum.getCode(),vehicleFrom,recordId,requestRecruitingId,requestRecruitingType);
                    //待平台审批，记录待审核的单项子标签的状态记录
                    oFflineChildStatusRecord(bdStepList, childMap, supplierChildStatusMap, moifyUser,recordId,requestRecruitingId,requestRecruitingType);
                    break;
                //BD驳回,将供应商不通过的单项置为待审核,并且将BD通过的子标签同步到供应商
                case 6:recruitingApprove6(supplierStepList,bdSingleMap,childMap,bdChildStatusMap,moifyUser);
                    break;
                    //通过到正式表，将BD单项操作人和时间同步到供应商
                case 10:approvePass10(bdStepList,supplierStepList);
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    //供应商驳回到H5,发短信给司机，附带司机招募详情链接
    public Boolean sendSmsToH5(String drvPhone,String drvName,String datachangeCreatetime,Long supplierId,Long recruitingId,String igtCode){
        String smsCode = approvalProcessAuthQconfig.getDrvRecruitingCode();
        Map<String,String> params = Maps.newHashMap();
        params.put("drvName",drvName);
        params.put("createDate",datachangeCreatetime);
        params.put("drvRecruitingDetailUrl",getShortUrl(approvalProcessAuthQconfig.getDrvRecruitingDetailUrl()+"&supplierId="+supplierId+"&drvRecruitingId="+recruitingId));
        commandService.sendMessageByPhone(igtCode, TmsTransUtil.decrypt(drvPhone, KeyType.Phone),smsCode,params);
        return Boolean.TRUE;
    }

    /**
    　* @description: 通过到正式表，将BD单项操作人和时间同步到供应商
    　* <AUTHOR>
    　* @date 2022/1/12 11:21
    */
    public Boolean approvePass10(List<TmsRecruitingApproveStepPO> bdStepList,List<TmsRecruitingApproveStepPO> supplierStepList){
        if(CollectionUtils.isEmpty(bdStepList) || CollectionUtils.isEmpty(supplierStepList)){
            return Boolean.FALSE;
        }
        try {
            Map<Integer,TmsRecruitingApproveStepPO> bdMap = Maps.newHashMap();
            for(TmsRecruitingApproveStepPO stepPO : bdStepList){
                bdMap.put(stepPO.getApproveItem(),stepPO);
            }
            for(TmsRecruitingApproveStepPO stepPO : supplierStepList){
                TmsRecruitingApproveStepPO bdStepPO = bdMap.get(stepPO.getApproveItem());
                if(!Objects.isNull(bdStepPO)){
                    stepPO.setModifyUser(bdStepPO.getModifyUser());
                    stepPO.setApproveTime(bdStepPO.getApproveTime());
                    stepPO.setFinalPassStatus(1);
                    stepRepository.update(stepPO);
                }
            }
            return Boolean.TRUE;
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    //BD驳回,将供应商不通过的单项置为待审核,并且将BD通过的子标签同步到供应商
    public Boolean recruitingApprove6(List<TmsRecruitingApproveStepPO> supplierStepList,Map<Integer, TmsRecruitingApproveStepPO> bdSingleMap,Map<Long, List<TmsRecruitingApproveStepChildPO>> childMap,Map<String, Integer> bdChildStatusMap,String moifyUser){
        if(CollectionUtils.isEmpty(supplierStepList)){
            return Boolean.FALSE;
        }
        try {
            for (TmsRecruitingApproveStepPO stepPO : supplierStepList) {
                TmsRecruitingApproveStepPO bDSingleStep = bdSingleMap.get(stepPO.getApproveItem());
                if(!Objects.isNull(bDSingleStep) && stepPO.getFinalPassStatus().intValue() == 0){
                    if (Objects.equals(bDSingleStep.getApproveStatus(), TmsTransportConstant.SingleApproveStatusEnum.APPROVE_THROUGH.getCode()) ) {
                        stepPO.setApproveTime(bDSingleStep.getApproveTime());
                        stepPO.setModifyUser(bDSingleStep.getModifyUser());
                        stepPO.setApproveReason(bDSingleStep.getApproveReason());
                        stepPO.setApproveStatus(TmsTransportConstant.SingleApproveStatusEnum.APPROVE_THROUGH.getCode());
                        stepPO.setFinalPassStatus(1);
                        stepRepository.update(stepPO);
                    }
                    if (Objects.equals(bDSingleStep.getApproveStatus(), TmsTransportConstant.SingleApproveStatusEnum.APPROVE_NO_THROUGH.getCode())) {
                        stepRepository.updateApproveStatus(stepPO.getId(), TmsTransportConstant.SingleApproveStatusEnum.WAITAPPROVE.getCode(),bDSingleStep.getApproveReason(),1, moifyUser);
                    }
                }
            }
            //同步子标签
            updateChildStatus(supplierStepList, childMap, bdChildStatusMap, moifyUser);
            return Boolean.TRUE;
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    public Map<String,Integer> childStatuaMap(List<TmsRecruitingApproveStepPO> stepList,Map<Long,List<TmsRecruitingApproveStepChildPO>> childMap){
        Map<String,Integer> childStatusMap = Maps.newHashMap();
        for(TmsRecruitingApproveStepPO stepPO : stepList){
            List<TmsRecruitingApproveStepChildPO> stepChildPOList = childMap.get(stepPO.getId());
            if(CollectionUtils.isEmpty(stepChildPOList)){
                continue;
            }
            for(TmsRecruitingApproveStepChildPO childPO : stepChildPOList){
                childStatusMap.put(stepPO.getApproveItem()+"_"+ childPO.getChildItem(),childPO.getCheckStatus());
            }
        }
        return childStatusMap;
    }

    public Boolean updateChildStatus(List<TmsRecruitingApproveStepPO> stepList,Map<Long,List<TmsRecruitingApproveStepChildPO>> childMap,Map<String,Integer> childStatusMap,String moifyUser){
        for(TmsRecruitingApproveStepPO stepPO : stepList){
            List<TmsRecruitingApproveStepChildPO> stepChildPOList = childMap.get(stepPO.getId());
            if(CollectionUtils.isEmpty(stepChildPOList)){
                continue;
            }
            for(TmsRecruitingApproveStepChildPO childPO : stepChildPOList){
                Integer childStatus = childStatusMap.get(stepPO.getApproveItem()+"_"+ childPO.getChildItem());
                if(childStatus!=null){
                    childRepository.updateChildCheckStatus(childPO.getId(),childStatus,moifyUser);
                }
            }
        }
        return Boolean.TRUE;
    }

    public void encapsulationField(Map<Integer, List<TmsRecruitingApproveStepPO>> integerListMap,List<Long> bdStepIds,Map<Integer, TmsRecruitingApproveStepPO> bdSingleMap,List<TmsRecruitingApproveStepPO> supplierStepList,List<TmsRecruitingApproveStepPO> bdStepList){
        for (Map.Entry<Integer, List<TmsRecruitingApproveStepPO>> entry : integerListMap.entrySet()) {
            Integer accountType = entry.getKey();
            List<TmsRecruitingApproveStepPO> approveStepPOList = entry.getValue();
            if (Objects.equals(accountType, TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue())) {
                for (TmsRecruitingApproveStepPO stepPO : approveStepPOList) {
                    bdSingleMap.put(stepPO.getApproveItem(), stepPO);
                    bdStepIds.add(stepPO.getId());
                    bdStepList.add(stepPO);
                }
            }
            if (Objects.equals(accountType, TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue())) {
                for (TmsRecruitingApproveStepPO stepPO : approveStepPOList) {
                    supplierStepList.add(stepPO);
                }
            }
        }
    }

    public String turnDownReason(Long approveSourceId,Integer approveType,Long vehApproveSourceId,String remark,Integer approveStatus){
        if(!Objects.equals(approveStatus,TmsTransportConstant.RecruitingApproverStatusEnum.operating_turnDown.getCode())&&
                !Objects.equals(approveStatus,TmsTransportConstant.RecruitingApproverStatusEnum.supplier_turnDown.getCode())){
            return remark;
        }

        List<Long> sourceIds = Lists.newArrayList();
        sourceIds.add(approveSourceId);
        if(vehApproveSourceId!=null){
            sourceIds.add(vehApproveSourceId);
        }
        if(sourceIds.size() == 2){
            approveType = null;
        }
        Integer approveFrom = TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue();
        if(Objects.equals(approveStatus,TmsTransportConstant.RecruitingApproverStatusEnum.supplier_turnDown.getCode())){
            approveFrom = TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue();
        }
        List<TmsRecruitingApproveStepPO> approveStepPOList = stepRepository.queryNoThroughReason(sourceIds,approveType, approveFrom);
        if(CollectionUtils.isEmpty(approveStepPOList)){
            return remark;
        }
        StringBuilder reason = new StringBuilder();
        for(TmsRecruitingApproveStepPO stepPO : approveStepPOList){
            if(StringUtils.isNotEmpty(stepPO.getApproveReason())){
                reason.append(TmsTransportConstant.ApproveItemEnum.getText(stepPO.getApproveItem()) + "："+ stepPO.getApproveReason()).append("。</br> ");
            }
        }
        if(StringUtils.isEmpty(reason)){
            return remark;
        }
        reason.deleteCharAt(reason.length() -1);
        return reason.toString();

    }

    public Boolean systemAutoPassSingle(List<TmsRecruitingApproveStepPO> bdStepList,List<TmsRecruitingApproveStepPO> supplierStepList,Map<Long, List<TmsRecruitingApproveStepChildPO>> childMap,Long recruitingId,Integer recruitingType,Integer vehicleFrom,Long recordId,Long requestRecruitingId,Integer requestRecruitingType){
        Map<Integer,Boolean> supplierChildMap = Maps.newHashMap();

        if(CollectionUtils.isEmpty(bdStepList) || CollectionUtils.isEmpty(supplierStepList)){
            return Boolean.FALSE;
        }
        //遍历出 供应商单项对应的子项全部通过,key=单项类型,value = 是否通过标签,true:通过
        for(TmsRecruitingApproveStepPO stepPO : supplierStepList){
            List<TmsRecruitingApproveStepChildPO> childPOList = childMap.get(stepPO.getId());
            Boolean childCheckStatus = Boolean.TRUE;
            if(CollectionUtils.isNotEmpty(childPOList)){
                for(TmsRecruitingApproveStepChildPO childPO : childPOList){
                    if(!Objects.equals(childPO.getCheckStatus(), TmsTransportConstant.CheckStatusEnum.THROUGH.getCode())){
                        childCheckStatus = Boolean.FALSE;
                        break;
                    }
                }
            }
            if(childCheckStatus){
                supplierChildMap.put(stepPO.getApproveItem(),Boolean.TRUE);
            }
        }
        //司机对应证件核验记录
        Map<Integer, TmsCertificateCheckPO> checkPOMap = queryService.queryCertificateCheckToMap(recruitingId, RecruitingQueryServiceImpl.getCheckType(recruitingType).getCode());
        if(MapUtils.isNotEmpty(checkPOMap)){
            for(Map.Entry<Integer, TmsCertificateCheckPO> entry : checkPOMap.entrySet()){
                checkPOMap.put(entry.getKey(),entry.getValue());
            }
        }
        //处理BD单项,单项审批通过和不审核 不需要处理
        for(TmsRecruitingApproveStepPO stepPO : bdStepList){
            if(Objects.equals(stepPO.getApproveStatus(), TmsTransportConstant.SingleApproveStatusEnum.APPROVE_THROUGH.getCode())||
                    Objects.equals(stepPO.getApproveStatus(), TmsTransportConstant.SingleApproveStatusEnum.NO_APPROVE.getCode())){
                continue;
            }
            //如果是H5入住的车,车辆基础信息自动通过 20220412修改---司机基础信息自动通过
            if((Objects.equals(stepPO.getApproveItem(), TmsTransportConstant.ApproveItemEnum.veh_basis.getCode()) &&
                    Objects.equals(vehicleFrom, TmsTransportConstant.VehicleFromEnum.Veh_AUTO.getCode())) ||
                    Objects.equals(stepPO.getApproveItem(), TmsTransportConstant.ApproveItemEnum.drv_basis.getCode())){
                systemAutoUpdatePass(stepPO,recordId,requestRecruitingId,requestRecruitingType);
                List<TmsRecruitingApproveStepChildPO> vehChildList = childMap.get(stepPO.getId());
                if(CollectionUtils.isNotEmpty(vehChildList)){
                    for(TmsRecruitingApproveStepChildPO childPO : vehChildList){
                        if(!Objects.equals(childPO.getCheckStatus(), TmsTransportConstant.CheckStatusEnum.THROUGH.getCode())){
                            childPO.setCheckStatus(TmsTransportConstant.CheckStatusEnum.THROUGH.getCode());
                            childPO.setModifyUser(TmsTransportConstant.TMS_DEFAULT_USERNAME);
                            childRepository.update(childPO);
                        }
                    }
                }
                continue;
            }
            TmsCertificateCheckPO tmsCertificateCheckPO = RecruitingQueryServiceImpl.getCertificateCheck(stepPO.getApproveItem(),checkPOMap);
            //如果供应商单项中对应的子项全部是通过,则BD对应的单项自动通过
             Boolean passFlag =  supplierChildMap.get(stepPO.getApproveItem());
            //如果身份证、驾驶证、网约车双证子标签是通过，则系统自动通过
            if(passFlag!=null && passFlag){
                passFlag = judgeCertificateCheckPassFlag(stepPO.getApproveItem(),tmsCertificateCheckPO);
            }
            //各别单项中存在三方子顶，判断单项自动通过时，也要判断对应的三方子项是否通过
            if(!Objects.isNull(tmsCertificateCheckPO) && !Objects.equals(tmsCertificateCheckPO.getCheckStatus(), TmsTransportConstant.CheckStatusEnum.THROUGH.getCode())){
                passFlag = Boolean.FALSE;
            }
             if(passFlag!=null && passFlag){
                 systemAutoUpdatePass(stepPO,recordId,requestRecruitingId,requestRecruitingType);
             }
        }
        return Boolean.TRUE;
    }

    public Boolean judgeCertificateCheckPassFlag(Integer approveItem,TmsCertificateCheckPO tmsCertificateCheckPO){
        log.info("judgeCertificateCheckPassFlag","prams:{}",approveItem);
        if(!Objects.equals(approveItem, TmsTransportConstant.ApproveItemEnum.identity.getCode()) &&
                !Objects.equals(approveItem, TmsTransportConstant.ApproveItemEnum.driverlicense.getCode())&&
                !Objects.equals(approveItem, TmsTransportConstant.ApproveItemEnum.net_people.getCode())&&
                !Objects.equals(approveItem, TmsTransportConstant.ApproveItemEnum.net_vehile.getCode())){
            return Boolean.TRUE;
        }
        if(Objects.isNull(tmsCertificateCheckPO)){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public Boolean systemAutoUpdatePass(TmsRecruitingApproveStepPO stepPO,Long recordId,Long requestRecruitingId,Integer requestRecruitingType){
        stepPO.setApproveStatus(TmsTransportConstant.SingleApproveStatusEnum.APPROVE_THROUGH.getCode());
        stepPO.setModifyUser(TmsTransportConstant.TMS_DEFAULT_USERNAME);
        stepPO.setCreateUser(TmsTransportConstant.TMS_DEFAULT_USERNAME);
        stepPO.setApproveTime(DateUtil.getNow());
        stepPO.setFinalPassStatus(1);
        stepRepository.update(stepPO);
        //保存单项审批记录
        tmsModRecordCommandService.insertAutoPassStepRrd(requestRecruitingId,requestRecruitingType,TmsTransportConstant.SingleApproveStatusEnum.APPROVE_THROUGH.getCode(),stepPO.getId(),recordId);
        return Boolean.TRUE;
    }

    public Boolean checkStatusBol(Integer checkStatus,Integer approveStatus){
        if(Objects.equals(approveStatus,TmsTransportConstant.RecruitingApproverStatusEnum.wait_Approve.getCode())||
                Objects.equals(approveStatus,TmsTransportConstant.RecruitingApproverStatusEnum.operating_turnDown.getCode())){
            return Boolean.TRUE;
        }
        if(Objects.equals(checkStatus,TmsTransportConstant.RecruitingCheckStatusEnum.HASCOMPLETED.getCode()) &&
                Objects.equals(approveStatus,TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode())){
                return Boolean.TRUE;
            }
        return Boolean.FALSE;
    }

    /***
    　* @description: 生成短域名  http://conf.ctripcorp.com/pages/viewpage.action?pageId=133520812
    　* <AUTHOR>
    　* @date 2022/1/10 17:16
    */
    public String getShortUrl(String url) {
        String result = null;
        try {
            HashMap<String,String> params = Maps.newLinkedHashMapWithExpectedSize(2);
            params.put("Content-Type","application/json");
            params.put(Constant.shortUrlHeaderKey, JsonUtil.toJson(ImmutableMap.of(Constant.shortUrlReqStructure[0],"100025330",Constant.shortUrlReqStructure[1],1)));
            result = HttpClientUtils.sendPostByJson(authConfig.getShortUrl(), url, params);
            if (Strings.isNullOrEmpty(result)) {
                return url;
            }
            Map<String, Object> resMap = JacksonSerializer.INSTANCE().deserialize(result, Map.class);
            if (resMap == null || !resMap.containsKey(Constant.shortUrlResStructure[0]) || !resMap.containsKey(Constant.shortUrlResStructure[1]) || !(boolean)resMap.get(Constant.shortUrlResStructure[0])) {
                return url;
            }
            return authConfig.getShowUrlAPI() + resMap.get(Constant.shortUrlResStructure[1]).toString();
        } catch (Exception e) {
            return url;
        }
    }

    public Integer getApproverAperation(Integer approveStatus,Integer approverAperation){
        if(Objects.equals(approveStatus,TmsTransportConstant.RecruitingApproverStatusEnum.supplier_turnDown.getCode())||
                Objects.equals(approveStatus,TmsTransportConstant.RecruitingApproverStatusEnum.operating_turnDown.getCode())){
            return TmsTransportConstant.ApproverAperationEnum.approve_rejected.getCode();
        }
        if(Objects.equals(approveStatus,TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode())){
            return TmsTransportConstant.ApproverAperationEnum.approve_pass.getCode();
        }
        return approverAperation;
    }

    public Boolean checkNowRoleApproveStatus(Integer approveStatus,Long cityId,Integer versionFlag){
        Map<String, String> map = SessionHolder.getSessionSource();
        if(MapUtils.isEmpty(map) || map.get("accountType")==null){
           return Boolean.FALSE;
        }
        if(Objects.equals(enumRepository.getAreaScope(cityId),AreaScopeTypeEnum.OVERSEAS.getCode().intValue())){
            return Boolean.FALSE;
        }
        if(versionFlag.intValue() < 3){
            return Boolean.FALSE;
        }
        Integer approveFrom = Integer.parseInt(map.get("accountType"));
        if(Objects.equals(approveFrom, TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue())){
            if(!Objects.equals(approveStatus,TmsTransportConstant.RecruitingApproverStatusEnum.wait_Approve.getCode())&&
                    !Objects.equals(approveStatus,TmsTransportConstant.RecruitingApproverStatusEnum.operating_turnDown.getCode())){
                return Boolean.TRUE;
            }
        }
        if(Objects.equals(approveFrom, TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue())){
            if(!Objects.equals(approveStatus,TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode())){
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    /***
    　* @description: 转单项记录VO
    　* <AUTHOR>
    　* @date 2022/4/26 11:28
    */
    public List<StepModRecordParams> getStepModColl(List<SingleApprovalSOADTO> approvalSOADTOS, String modifyUser){
        List<StepModRecordParams> paramsList = Lists.newArrayList();
        for(SingleApprovalSOADTO soadto : approvalSOADTOS){
            StepModRecordParams params = new StepModRecordParams();
            params.setStepId(soadto.getApproveStepId());
            params.setApproveStatus(soadto.getApproveStatus());
            params.setModifyUser(modifyUser);
            paramsList.add(params);
        }
        return paramsList;
    }

    /**
    　* @description: 转子项记录VO
    　* <AUTHOR>
    　* @date 2022/4/26 11:28
    */
    public List<StepChildModRecordParams> getChildStepModColl(List<SingleApprovalSOADTO> approvalSOADTOS,String modifyUser){
        List<Long> childStepIds = Lists.newArrayList();
        List<Long> certificateIds = Lists.newArrayList();
        Map<String,Integer> tarCheckStatusMap = Maps.newHashMap();
        setChildItemGroupColl(childStepIds,certificateIds,tarCheckStatusMap,approvalSOADTOS);
        List<StepChildModRecordParams> childRecordParams = Lists.newArrayList();
        setChildStepColl(childStepIds,tarCheckStatusMap,modifyUser,childRecordParams);
        setCertificateColl(certificateIds,tarCheckStatusMap,modifyUser,childRecordParams);
        return childRecordParams;
    }

    /**
    　* @description: 按子项类型区分不同的集合
    　* <AUTHOR>
    　* @date 2022/4/26 11:29
    */
    public void setChildItemGroupColl(List<Long> childStepIds,List<Long> certificateIds,Map<String,Integer> tarCheckStatusMap,List<SingleApprovalSOADTO> approvalSOADTOS){
        for(SingleApprovalSOADTO soadto : approvalSOADTOS){
            if(CollectionUtils.isNotEmpty(soadto.getChildList())){
                for(ApproveStepChildListSOADTO childListSOADTO : soadto.getChildList()){
                    if(Objects.equals(childListSOADTO.getChildItem(), TmsTransportConstant.ApproveChildItemEnum.OCR.getCode())||
                            Objects.equals(childListSOADTO.getChildItem(), TmsTransportConstant.ApproveChildItemEnum.NON_OCR.getCode())){
                        childStepIds.add(childListSOADTO.getApproveStepChildId());
                        tarCheckStatusMap.put(childListSOADTO.getApproveStepChildId()+"_" + childListSOADTO.getChildItem(),childListSOADTO.getCheckStatus());
                    }
                    if(Objects.equals(childListSOADTO.getChildItem(),TmsTransportConstant.ApproveChildItemEnum.COMPLIANCE.getCode())){
                        certificateIds.add(childListSOADTO.getApproveStepChildId());
                        tarCheckStatusMap.put(childListSOADTO.getApproveStepChildId()+"_" + TmsTransportConstant.ApproveChildItemEnum.COMPLIANCE.getCode(),childListSOADTO.getCheckStatus());
                    }
                }
            }
        }
    }

    /**
    　* @description: 子项表里的转换
    　* <AUTHOR>
    　* @date 2022/4/26 11:30
    */
    public void setChildStepColl(List<Long> childStepIds,Map<String,Integer> tarCheckStatusMap,String modifyUser,List<StepChildModRecordParams> childRecordParams){
        if(CollectionUtils.isNotEmpty(childStepIds)){
            List<TmsRecruitingApproveStepChildPO> childPOList =  childRepository.queryChildByIds(childStepIds);
            if(CollectionUtils.isNotEmpty(childPOList)){
                for(TmsRecruitingApproveStepChildPO stepChildPO : childPOList){
                    Integer checkStatus = tarCheckStatusMap.get(stepChildPO.getId()+"_"+stepChildPO.getChildItem());
                    if(checkStatus == null || Objects.equals(checkStatus,stepChildPO.getCheckStatus())){
                        continue;
                    }
                    StepChildModRecordParams params = new StepChildModRecordParams();
                    params.setStepChildId(stepChildPO.getId());
                    params.setParentStepId(stepChildPO.getRecruitingApproveStepId());
                    params.setChildItem(stepChildPO.getChildItem());
                    params.setOrgCheckStatus(stepChildPO.getCheckStatus());
                    params.setTarCheckStatus(checkStatus);
                    params.setModifyUser(modifyUser);
                    childRecordParams.add(params);
                }
            }
        }
    }

    /**
    　* @description: 三方记录的转换
    　* <AUTHOR>
    　* @date 2022/4/26 11:31
    */
    public void setCertificateColl(List<Long> certificateIds,Map<String,Integer> tarCheckStatusMap,String modifyUser,List<StepChildModRecordParams> childRecordParams){
        if(CollectionUtils.isNotEmpty(certificateIds)){
            List<TmsCertificateCheckPO> childPOList =  checkRepository.queryCerCheckListByIds(certificateIds);
            if(CollectionUtils.isNotEmpty(childPOList)){
                for(TmsCertificateCheckPO stepChildPO : childPOList){
                    Integer checkStatus = tarCheckStatusMap.get(stepChildPO.getId()+"_"+TmsTransportConstant.ApproveChildItemEnum.COMPLIANCE.getCode());
                    if(checkStatus == null || Objects.equals(checkStatus,stepChildPO.getCheckStatus())){
                        continue;
                    }
                    StepChildModRecordParams params = new StepChildModRecordParams();
                    params.setStepChildId(stepChildPO.getCheckId());
                    params.setParentStepId(stepChildPO.getCheckId());
                    params.setChildItem(TmsTransportConstant.ApproveChildItemEnum.COMPLIANCE.getCode());
                    params.setOrgCheckStatus(stepChildPO.getCheckStatus());
                    params.setCertificateType(stepChildPO.getCertificateType());
                    params.setTarCheckStatus(checkStatus);
                    params.setModifyUser(modifyUser);
                    childRecordParams.add(params);
                }
            }
        }
    }

    /**
    　* @description: 供应商提交到平台后，BD角色的子项会发生改变，将变化的子项标签存㫅
    　* <AUTHOR>
    　* @date 2022/4/24 19:12
    */
    public Boolean oFflineChildStatusRecord(List<TmsRecruitingApproveStepPO> stepList,Map<Long,List<TmsRecruitingApproveStepChildPO>> childMap,Map<String,Integer> childStatusMap,String moifyUser,Long recordId,Long requestRecruitingId,Integer requestRecruitingType){
        List<StepChildModRecordParams> childRecordParams = Lists.newArrayList();
        for(TmsRecruitingApproveStepPO stepPO : stepList){
            List<TmsRecruitingApproveStepChildPO> stepChildPOList = childMap.get(stepPO.getId());
            if(CollectionUtils.isEmpty(stepChildPOList) ||
                    Objects.equals(stepPO.getApproveStatus(), TmsTransportConstant.SingleApproveStatusEnum.APPROVE_THROUGH.getCode())||
                    Objects.equals(stepPO.getApproveStatus(), TmsTransportConstant.SingleApproveStatusEnum.NO_APPROVE.getCode())){
                continue;
            }
            for(TmsRecruitingApproveStepChildPO childPO : stepChildPOList){
                Integer childStatus = childStatusMap.get(stepPO.getApproveItem()+"_"+ childPO.getChildItem());
                if(childStatus != null && Objects.equals(childStatus, TmsTransportConstant.CheckStatusEnum.THROUGH.getCode())){
                    StepChildModRecordParams recordParams = new StepChildModRecordParams();
                    recordParams.setParentStepId(stepPO.getId());
                    recordParams.setModifyUser(moifyUser);
                    recordParams.setOrgCheckStatus(childPO.getCheckStatus());
                    recordParams.setTarCheckStatus(childStatus);
                    recordParams.setChildItem(childPO.getChildItem());
                    recordParams.setStepChildId(childPO.getId());
                    childRecordParams.add(recordParams);
                }
            }
        }
        tmsModRecordCommandService.saveChildSingleRrd(childRecordParams,moifyUser,recordId,requestRecruitingId,requestRecruitingType);
        return Boolean.TRUE;
    }

    public Boolean raisingPickUpInitValue(Long cityId) {
        if (cityId == null) {
            return Boolean.FALSE;
        }
        Map<Integer, Boolean> valuesMap = tmsTransportQconfig.getRaisingPickUpValuesMap();
        if (MapUtils.isEmpty(valuesMap)) {
            return Boolean.FALSE;
        }
        Boolean v = valuesMap.get(enumRepository.getAreaScope(cityId));
        return v == null ? Boolean.FALSE : v;
    }

    @Override
    public Result<Boolean> saveOverseasTags(SaveOverseasTagsSOARequestType requestType) {
        if(CollectionUtils.isEmpty(requestType.getParamsList())){
            return Result.Builder.<Boolean>newResult().success().build();
        }

        //获取当前操作角色,如果未登录，提示权限有问题
        String accountTypeStr = SessionHolder.getRestSessionAccountType();
        if(StringUtils.isEmpty(accountTypeStr)){
            return Result.Builder.<Boolean>newResult().fail().withCode("403").build();
        }
        String modifyUser = SessionHolder.getRestSessionAccountName();
        try {
            Integer accountType = Integer.parseInt(accountTypeStr);
            //如果当前操作角色是BD,则判断当前招募信息审批状态是否是待BD审批,如果不是,则提示语
            if(!Objects.equals(accountType, TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue())){
                return Result.Builder.<Boolean>newResult().fail().withCode("601").build();
            }

            List<Long> approveStepId = requestType.getParamsList().stream().map(SaveOverseasTagsParamsSOAVO::getStepId).collect(Collectors.toList());
            List<TmsRecruitingApproveStepPO> stepPOList  =  stepRepository.queryApproveStepByIds(approveStepId);
            List<Long> ids = Lists.newArrayList();
            for(TmsRecruitingApproveStepPO stepPO : stepPOList){
                if(!Objects.equals(stepPO.getApproveStatus(), TmsTransportConstant.SingleApproveStatusEnum.WAITAPPROVE.getCode())){
                    ids.add(stepPO.getId());
                }
            }

            //操作单项中如果不是待审核，则单项审批失败 602-该单项已审核完成
            if(ids.size() > 0){
                return Result.Builder.<Boolean>newResult().fail().withCode("602").build();
            }
            //运营工作台→平台审核 记录单项和子标签的操作
            tmsModRecordCommandService.saveSingleAndChildRrd(getOverseasStepModColl(requestType.getParamsList(),modifyUser),Lists.newArrayList(),modifyUser,requestType.getRecruitingId(),requestType.getRecruitingType());

            for(SaveOverseasTagsParamsSOAVO soadto : requestType.getParamsList()){
                //更新单项信息
                int count = stepRepository.updateApproveStatus(soadto.getStepId(), soadto.getCheckStatus(), soadto.getApproveReason(),accountType,modifyUser);
                if(count > 0){
                    //保存每次审批单项的记录
                    tmsModRecordCommandService.saveRecruitingSingleRrd(requestType.getRecruitingId(),JsonUtil.toJson(requestType.getParamsList()),requestType.getRecruitingType(),modifyUser);
                }
                //更新招募进度
                tmsQmqProducerCommandService.sendRecruitingApproveScheduleQMQ(requestType.getRecruitingId(),requestType.getRecruitingType(),accountType);
            }
            return Result.Builder.<Boolean>newResult().success().build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Boolean initOverseasSingleData(Long approveSourceDrvId, Long approveSourceVehId,SingleApproveTypeEnum approveTypeEnum, List<OcrPassStatusModelSOA> occPassList, String modifyUser) {
        if(approveSourceDrvId == null){
            return Boolean.TRUE;
        }
        try {
            for(OcrPassStatusModelSOA modelSOA : occPassList){
                TmsRecruitingApproveStepPO stepPO = new TmsRecruitingApproveStepPO();
                stepPO.setApproveSourceId(approveSourceDrvId);
                stepPO.setApproveType(approveTypeEnum.getCode());
                stepPO.setApproveFrom(AccountTypeEnum.OFFLINE.getValue());
                stepPO.setApproveItem(modelSOA.getOcrItem());
                stepPO.setApproveStatus(SingleApproveStatusEnum.WAITAPPROVE.getCode());
                stepPO.setCreateUser(modifyUser);
                stepPO.setModifyUser(modifyUser);
                //兼容车辆，如果H5带车，则车辆生成车的单项数据
                if(approveSourceVehId != null && approveSourceVehId > 0 &&  ( Objects.equals(modelSOA.getOcrItem(),ApproveItemEnum.vehicle_number.getCode())
                        || Objects.equals(modelSOA.getOcrItem(),ApproveItemEnum.vehicle_compliance.getCode()) )) {
                    stepPO.setApproveSourceId(approveSourceVehId);
                    stepPO.setApproveType(SingleApproveTypeEnum.VEH.getCode());
                }
                //境外OCR校全通过，则默认系统审批通过
                if(Objects.equals(modelSOA.getPassStatus(),OverseasOCRPassStatusEnum.pass.getCode())){
                    stepPO.setApproveStatus(SingleApproveStatusEnum.APPROVE_THROUGH.getCode());
                    stepPO.setApproveTime(DateUtil.getNow());
                    stepPO.setCreateUser(TMS_DEFAULT_USERNAME);
                    stepPO.setModifyUser(TMS_DEFAULT_USERNAME);
                }
                stepRepository.insert(stepPO);
            }
            return Boolean.TRUE;
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    /**
     　* @description: 工作台境外司机新逻辑，新增OCR识别，如果识别通过，则直接审批通过,如果OCR不通过,则进入平台审批
     　* <AUTHOR>
     　* @date 2023/6/26 10:49
     */
    @Override
    public Result<Boolean> overseasNewBusinessApprove(Long supplierId, Integer versionFlag, Integer areaScope, Long recruitingId,List<OcrPassStatusModelSOA> passStatusList, RecruitingTypeEnum recruitingTypeEnum, SingleApproveTypeEnum approveTypeEnum, String modifyUser) {
        try{
            //当前供应商在灰度中
            if (driverQueryService.overseasIsBusiness(supplierId, versionFlag, areaScope,BusinessTypeEnum.OTHER)) {
                //如果OCR校验通过,则直接进入正式表,如果OCR没有传或不通过，则进入平台审批
                if (CtripCommonUtils.ocrIsPass(passStatusList)) {
                    //直接审批通过置正式司机车辆
                    Result<Boolean> result = recruitingUpdatePass(Arrays.asList(recruitingId), modifyUser, Boolean.TRUE, "", recruitingTypeEnum.getCode(), new ArrayList<>());
                    if (!result.isSuccess()) {
                        return Result.Builder.<Boolean>newResult().fail().withCode(result.getCode()).withMsg(result.getMsg()).build();
                    }
                } else {
                    //进入平台审批流程
                    Result<Boolean> result = recruitingUpdateStatus(Arrays.asList(recruitingId), TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode(), SessionHolder.getRestSessionAccountName(), "", recruitingTypeEnum.getCode(), TmsTransportConstant.ApproverAperationEnum.work_register_submit.getCode(),new ArrayList<>());
                    if (!result.isSuccess()) {
                        return Result.Builder.<Boolean>newResult().fail().withCode(result.getCode()).withMsg(result.getMsg()).build();
                    }
                }
                //生成单项数据，单项都为通过
                initOverseasSingleData(recruitingId, null, approveTypeEnum, passStatusList, modifyUser);
            }
            return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    public List<StepModRecordParams> getOverseasStepModColl(List<SaveOverseasTagsParamsSOAVO> approvalSOADTOS, String modifyUser){
        List<StepModRecordParams> paramsList = Lists.newArrayList();
        for(SaveOverseasTagsParamsSOAVO soadto : approvalSOADTOS){
            StepModRecordParams params = new StepModRecordParams();
            params.setStepId(soadto.getStepId());
            params.setApproveStatus(soadto.getCheckStatus());
            params.setModifyUser(modifyUser);
            paramsList.add(params);
        }
        return paramsList;
    }
}
