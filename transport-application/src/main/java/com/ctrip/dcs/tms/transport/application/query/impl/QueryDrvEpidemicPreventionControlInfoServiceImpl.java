package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang.time.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;
import java.util.function.*;
import java.util.stream.*;

@Service("queryDrvEpidemicPreventionControlInfoService")
public class QueryDrvEpidemicPreventionControlInfoServiceImpl implements QueryDrvEpidemicPreventionControlInfoService {

    private final Date dateLimit = DateUtil.stringToDate("2021-01-01", DateUtil.YYYYMMDD);

    @Autowired
    private DrvDrvierRepository drvierRepository;

    @Autowired
    private TmsTransportQconfig tmsTransportQconfig;

    @Autowired
    private EpidemicPreventionControlQconfig epidemicPreventionControlQconfig;

    @Autowired
    private DrvEpidemicPreventionControlInfoRepository drvEpidemicPreventionControlInfoRepository;

    @Override
    public List<ReportInfo> queryEpidemicPreventionControlInfoByDrvList(List<Long> drvIdList) {
        List<DrvDriverPO> driverPOList = drvierRepository.queryDrvList(drvIdList);
        if (CollectionUtils.isEmpty(driverPOList)) {
            return Collections.emptyList();
        }
        List<ReportInfo> result = Lists.newArrayListWithExpectedSize(drvIdList.size());
        List<DrvEpidemicPreventionControlInfoPO> infoPOS = drvEpidemicPreventionControlInfoRepository.queryByDrvIdList(drvIdList);
        Map<Long, DrvEpidemicPreventionControlInfoPO> epidemicPreventionControlInfoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(infoPOS)) {
            epidemicPreventionControlInfoMap = infoPOS.stream().collect(Collectors.toMap(DrvEpidemicPreventionControlInfoPO::getDrvId, Function.identity(), (key1, key2) -> key2));
        }

        for (DrvDriverPO driverPO : driverPOList) {
            ReportInfo reportInfo = new ReportInfo();
            DrvEpidemicPreventionControlInfoPO infoPO = epidemicPreventionControlInfoMap.get(driverPO.getDrvId());
            reportInfo.setNucleicAcidExpirationDate(getNucleicAcidExpirationDate(epidemicPreventionControlQconfig.getEpidemicPreventionControlCityMap(driverPO.getCityId()), infoPO));
            if (infoPO == null) {
                reportInfo.setVaccinationCount(0);
                reportInfo.setVaccinationStatus(false);
                reportInfo.setNucleicAcidTestingStatus(false);
                reportInfo.setNucleicAcidUploadDays(0);
                reportInfo.setNucleicAcidTestingResult(0);
            } else {
                if (infoPO.getVaccineReportStatus() != null && infoPO.getVaccineReportStatus().intValue() == TmsTransportConstant.CheckStatusEnum.THROUGH.getCode().intValue()) {
                    reportInfo.setVaccinationCount(infoPO.getVaccinationCount() == null ? 0 : infoPO.getVaccinationCount());
                    reportInfo.setVaccinationStatus(reportInfo.getVaccinationCount() > 0);
                } else {
                    reportInfo.setVaccinationCount(0);
                    reportInfo.setVaccinationStatus(false);
                }
                reportInfo.setNucleicAcidTestingResult(infoPO.getNucleicAcidReportStatus() == null ? EpidemicPreventionControlEnum.ReportUpdateStatusEnum.NOT_UPLOAD.getCode() : (infoPO.getNucleicAcidReportStatus() <= TmsTransportConstant.CheckStatusEnum.INIT.getCode() ? EpidemicPreventionControlEnum.ReportUpdateStatusEnum.NOT_UPLOAD.getCode() : EpidemicPreventionControlEnum.ReportUpdateStatusEnum.UPLOADED.getCode()));
                reportInfo.setNucleicAcidTestingStatus(Objects.equals(infoPO.getNucleicAcidReportStatus(), TmsTransportConstant.CheckStatusEnum.THROUGH.getCode()));
                reportInfo.setVaccinationResult(infoPO.getVaccineReportStatus() == null ? EpidemicPreventionControlEnum.ReportUpdateStatusEnum.NOT_UPLOAD.getCode() : (infoPO.getVaccineReportStatus() <= TmsTransportConstant.CheckStatusEnum.INIT.getCode() ? EpidemicPreventionControlEnum.ReportUpdateStatusEnum.NOT_UPLOAD.getCode() : EpidemicPreventionControlEnum.ReportUpdateStatusEnum.UPLOADED.getCode()));
                reportInfo.setNucleicAcidUploadDays(0);
                if (infoPO.getNucleicAcidReportStatus() != null && infoPO.getNucleicAcidReportStatus().intValue() == TmsTransportConstant.CheckStatusEnum.THROUGH.getCode().intValue()) {
                    if (infoPO.getNucleicAcidTestingTime() != null && dateLimit.before(infoPO.getNucleicAcidTestingTime())) {
                        Long diffDays = ((new Date()).getTime() - infoPO.getNucleicAcidTestingTime().getTime()) / Constant.MillisecondsPerDay;
                        reportInfo.setNucleicAcidUploadDays(diffDays.intValue());
                    }
                }
            }
            reportInfo.setCityId(driverPO.getCityId());
            reportInfo.setDriverId(driverPO.getDrvId());
            reportInfo.setContent(epidemicPreventionControlQconfig.getEpidemicPreventionControlCityMap(driverPO.getCityId()).getEpidemicPreventionControlCopyWriting());
            reportInfo.setConstraintReportStatus(epidemicPreventionControlQconfig.getEpidemicPreventionControlCityMap(driverPO.getCityId()).getReportConstraintModel());
            reportInfo.setVaccinationNumber(tmsTransportQconfig.getVaccinationNumber());
            result.add(reportInfo);
        }
        return result;
    }

    @Override
    public List<ReportInfo> queryEpidemicPreventionControlInfoByCityList(List<Long> cityIdList) {
        List<ReportInfo> result = Lists.newArrayListWithExpectedSize(cityIdList.size());
        for (Long cityId : cityIdList) {
            ReportInfo reportInfo = new ReportInfo();
            reportInfo.setCityId(cityId);
            reportInfo.setConstraintReportStatus(epidemicPreventionControlQconfig.getEpidemicPreventionControlCityMap(cityId).getReportConstraintModel());
            result.add(reportInfo);
        }
        return result;
    }

    private String getNucleicAcidExpirationDate(EpidemicPreventionControlCityInfoDTO cityInfoDTO, DrvEpidemicPreventionControlInfoPO infoPO) {
        String hourMinute = " 23:59:59";
        StringBuilder stringBuilder = new StringBuilder();
        if (tmsTransportQconfig.getOutbreakEnd() || Objects.equals(cityInfoDTO.getReportConstraintModel(), EpidemicPreventionControlEnum.AskReportStatusEnum.NOT_NEED.getCode())) {
            Calendar calendar = Calendar.getInstance();
            Date date = new Date();
            calendar.setTime(date);
            calendar.add(Calendar.YEAR, 1);
            stringBuilder.append(calendar.get(Calendar.YEAR));
            stringBuilder.append("-07-01");
            stringBuilder.append(hourMinute);
            return stringBuilder.toString();
        }
        if (infoPO == null || infoPO.getNucleicAcidTestingTime() == null || dateLimit.getTime() > infoPO.getNucleicAcidTestingTime().getTime()) {
            return null;
        }
        stringBuilder.append(DateUtil.dateToString(DateUtils.addSeconds(DateUtils.addDays(infoPO.getNucleicAcidTestingTime(), cityInfoDTO.getSamplingDateEffectiveDays() + 1), -1), DateUtil.YYYYMMDDHHMMSS));
        return stringBuilder.toString();
    }

}
