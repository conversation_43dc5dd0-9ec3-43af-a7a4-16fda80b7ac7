package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.common.base.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctriposs.baiji.exception.*;
import com.dianping.cat.utils.*;
import com.fasterxml.jackson.core.type.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.springframework.beans.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Description 证件
 * @Date 14:12 2020/9/11
 * @Param
 * @return
 **/
@Service
public class CertificateQueryServiceImpl implements CertificateQueryService {

    private static final Logger logger = LoggerFactory.getLogger(CertificateQueryServiceImpl.class);

    @Autowired
    TmsCertificateConfigRepository configRepository;
    @Autowired
    EnumRepository enumRepository;
    @Autowired
    TmsTransportQconfig qconfig;
    @Autowired
    private ProductionLineUtil productionLineUtil;
    @Autowired
    private TmsCertificateConfigWhiteQconfig whiteQconfig;

    @Override
    public Result<PageHolder<QueryCertificateConfigListSOADTO>> queryCertificateConfigList(QueryCertificateConfigListSOARequestType requestType) {
        logger.info("queryCertificateConfigList,parsms:{}",requestType.toString());
        PageHolder pageHolder = null;
        int count = configRepository.countTmsCertificateConfig(requestType.getCityIds(), requestType.getProductLines());
        PaginatorDTO paginatorDTO = requestType.getPaginator();
        if (count == 0) {
            pageHolder = PageHolder.of(new ArrayList<>()).pageIndex(paginatorDTO.getPageNo()).pageSize(paginatorDTO.getPageSize()).totalSize(count).build();
            return Result.Builder.<PageHolder<QueryCertificateConfigListSOADTO>>newResult().success().withData(pageHolder).build();
        }
        List<TmsCertificateConfigPO> configPOList = configRepository.queryTmsCertificateConfiList(requestType.getCityIds(), requestType.getProductLines(), paginatorDTO.getPageNo(), paginatorDTO.getPageSize());
        if (CollectionUtils.isEmpty(configPOList)) {
            pageHolder = PageHolder.of(new ArrayList<>()).pageIndex(paginatorDTO.getPageNo()).pageSize(paginatorDTO.getPageSize()).totalSize(count).build();
            return Result.Builder.<PageHolder<QueryCertificateConfigListSOADTO>>newResult().success().withData(pageHolder).build();
        }
        List<QueryCertificateConfigListSOADTO> resultdata = Lists.newArrayList();
        configPOList.forEach(configPO -> {
            QueryCertificateConfigListSOADTO soadto = new QueryCertificateConfigListSOADTO();
            BeanUtils.copyProperties(configPO, soadto);
            soadto.setProductLineName(productionLineUtil.getCategoryName(configPO.getProductLine().longValue()));
            soadto.setDatachangeLasttime(DateUtil.timestampToString(configPO.getDatachangeLasttime(),DateUtil.YYYYMMDDHHMMSS));
            soadto.setDatachangeCreatetime(DateUtil.timestampToString(configPO.getDatachangeCreatetime(),DateUtil.YYYYMMDDHHMMSS));
            soadto.setCityName(enumRepository.getCityName(configPO.getCityId()));
            soadto.setActive(configPO.getActive());
            if (StringUtils.isNotEmpty(configPO.getCertificateConfig())) {
                soadto.setCertificateConfig(JsonUtil.fromJson(configPO.getCertificateConfig(), new TypeReference<List<CertificateConfigSOADTO>>() {
                }));
            } else {
                soadto.setCertificateConfig(new ArrayList<>());
            }
            soadto.setVehicleTypeIds(new ArrayList<>(BaseUtil.getLongSet(configPO.getVehicleTypeId())));
            resultdata.add(soadto);
        });
        pageHolder = PageHolder.of(resultdata).pageIndex(paginatorDTO.getPageNo()).pageSize(paginatorDTO.getPageSize()).totalSize(count).build();
        return Result.Builder.<PageHolder<QueryCertificateConfigListSOADTO>>newResult().success().withData(pageHolder).build();
    }

    @Override
    public Result<List<QueryCertificateConfigListSOADTO>> queryCertificateConfigInfo(QueryCertificateConfigInfoSOARequestType requestType) {
        List<QueryCertificateConfigListSOADTO> resultdata = Lists.newArrayList();
        if (CollectionUtils.isEmpty(requestType.getCityIds()) || CollectionUtils.isEmpty(requestType.getProductLines())) {
            return Result.Builder.<List<QueryCertificateConfigListSOADTO>>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgRequiredParameterMissing)).build();
        }
        try {
            //当前城市没有配置时，返回一个默认的配置
            List<TmsCertificateConfigPO> configPOList = newQueryMethod(configRepository.queryConfigByCityAndProLine(requestType.getCityIds(), requestType.getProductLines()),requestType.getVehicleTypeIds());
            if (CollectionUtils.isEmpty(configPOList)) {
                return Result.Builder.<List<QueryCertificateConfigListSOADTO>>newResult().success().withData(this.defaultConfig()).build();
            }
            configPOList.forEach(configPO -> {
                QueryCertificateConfigListSOADTO soadto = new QueryCertificateConfigListSOADTO();
                BeanUtils.copyProperties(configPO, soadto);
                soadto.setProductLineName(productionLineUtil.getCategoryName(configPO.getProductLine().longValue()));
                soadto.setCityName(enumRepository.getCityName(configPO.getCityId()));
                soadto.setActive(configPO.getActive());
                if (StringUtils.isNotEmpty(configPO.getCertificateConfig())) {
                    soadto.setCertificateConfig(JsonUtil.fromJson(configPO.getCertificateConfig(), new TypeReference<List<CertificateConfigSOADTO>>() {
                    }));
                } else {
                    soadto.setCertificateConfig(new ArrayList<>());
                }
                soadto.setVehicleTypeIds(new ArrayList<>(BaseUtil.getLongSet(configPO.getVehicleTypeId())));
                resultdata.add(soadto);
            });
            return Result.Builder.<List<QueryCertificateConfigListSOADTO>>newResult().success().withData(resultdata).build();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Result<QueryCertificateConfigListSOADTO> queryCertificateConfigDetail(QueryCertificateConfigDetailSOARequestType requestType) {
        try {
            TmsCertificateConfigPO configPO = configRepository.queryByPK(requestType.getId());
            if (Objects.isNull(configPO)) {
                return Result.Builder.<QueryCertificateConfigListSOADTO>newResult().success().withData(null).build();
            }
            QueryCertificateConfigListSOADTO soadto = new QueryCertificateConfigListSOADTO();
            BeanUtils.copyProperties(configPO, soadto);
            soadto.setProductLineName(productionLineUtil.getCategoryName(configPO.getProductLine().longValue()));
            soadto.setCityName(enumRepository.getCityName(configPO.getCityId()));
            soadto.setActive(configPO.getActive());
            if (StringUtils.isNotEmpty(configPO.getCertificateConfig())) {
                soadto.setCertificateConfig(JsonUtil.fromJson(configPO.getCertificateConfig(), new TypeReference<List<CertificateConfigSOADTO>>() {
                }));
            } else {
                soadto.setCertificateConfig(new ArrayList<>());
            }
            soadto.setVehicleTypeIds(new ArrayList<>(BaseUtil.getLongSet(configPO.getVehicleTypeId())));
            return Result.Builder.<QueryCertificateConfigListSOADTO>newResult().success().withData(soadto).build();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Result<Boolean> certificateConfigIsWhite(CertificateConfigIsWhiteSOARequestType requestType) {
        Long sourceId = requestType.getSourceId();
        if(sourceId <= 0){
            return Result.Builder.<Boolean>newResult().success().withData(Boolean.FALSE).build();
        }
        Boolean whiteFlag = Boolean.FALSE;
        switch (requestType.getSourceType()){
            case 1:
                if(whiteQconfig.getDrvCertificateConfigWhiteList().contains(sourceId)){
                    whiteFlag = Boolean.TRUE;
                }
                break;
            case 2:
                if(whiteQconfig.getVehCertificateConfigWhiteList().contains(sourceId)){
                    whiteFlag = Boolean.TRUE;
                }
                break;
        }
        return Result.Builder.<Boolean>newResult().success().withData(whiteFlag).build();
    }

    private List<QueryCertificateConfigListSOADTO> defaultConfig(){
        List<QueryCertificateConfigListSOADTO> list = Lists.newArrayList();
        QueryCertificateConfigListSOADTO soadto = new QueryCertificateConfigListSOADTO();
        soadto.setCertificateConfig(qconfig.defaultCeCertificateConfigList());
        soadto.setVehicleTypeIds(Arrays.asList(-1L));
        list.add(soadto);
        return list;
    }

    //新增车型过滤，如果多条配置，则取一个值
    public List<TmsCertificateConfigPO> newQueryMethod(List<TmsCertificateConfigPO> configPOList, List<Long> vehicleTypeIds) {
        List<TmsCertificateConfigPO> resultData = Lists.newArrayList();
        if (CollectionUtils.isEmpty(configPOList)) {
            return resultData;
        }
        //如果没有传车型，则走下面逻辑
        if (CollectionUtils.isEmpty(vehicleTypeIds)) {
            return queryScreenData(configPOList);
        }
        for (TmsCertificateConfigPO config : configPOList) {
            if (StringUtils.isNotEmpty(config.getVehicleTypeId())) {
                List<Long> vehicleTypId = Arrays.stream(config.getVehicleTypeId().split(",")).mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
                if (CollectionUtils.intersection(vehicleTypeIds, vehicleTypId).size() > 0) {
                    resultData.add(config);
                    return resultData;
                }
            }
        }
        return queryScreenData(configPOList);
    }

    //如果配置多条车型数据，先判断是否有全部车型配置，如果有则直接返回，没有全部车型，则查询是否有无车型配置，有则返回，没有则返空值
    public List<TmsCertificateConfigPO> queryScreenData(List<TmsCertificateConfigPO> configPOList){
        List<TmsCertificateConfigPO> resultData = Lists.newArrayList();
        for (TmsCertificateConfigPO config : configPOList) {
            if (StringUtils.isNotEmpty(config.getVehicleTypeId())) {
                List<Long> vehicleTypId = Arrays.stream(config.getVehicleTypeId().split(",")).mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
                if (CollectionUtils.intersection(Arrays.asList(-1L), vehicleTypId).size() > 0) {
                    resultData.add(config);
                    return resultData;
                }
            }
        }
        return queryNOVehicleTypeData(configPOList);
    }

    //查询配置中没有车型的数据，如果没查到则返空
    public List<TmsCertificateConfigPO> queryNOVehicleTypeData(List<TmsCertificateConfigPO> configPOList){
        List<TmsCertificateConfigPO> resultData = Lists.newArrayList();
        for (TmsCertificateConfigPO config : configPOList) {
            if (StringUtils.isEmpty(config.getVehicleTypeId())) {
                resultData.add(config);
                return resultData;
            }
        }
        return resultData;
    }
}
