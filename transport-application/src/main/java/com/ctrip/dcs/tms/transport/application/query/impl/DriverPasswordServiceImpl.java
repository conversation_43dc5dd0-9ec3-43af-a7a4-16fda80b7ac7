package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.math.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.regex.*;

/**
 * <AUTHOR>
 * @since 2020/9/21 17:15
 */
@Component
public class DriverPasswordServiceImpl implements DriverPasswordService {

  private static final Logger logger = LoggerFactory.getLogger(DriverPasswordService.class);

  private static final Pattern PATTERN = Pattern.compile("^(?=.*[0-9])(?=.*[a-zA-Z])[0-9a-zA-Z]{6,15}$");

  @Autowired
  private ApprovalProcessAuthQconfig authConfig;

  @Override
  public boolean isPasswordValid(String password) {
    return PATTERN.matcher(password).matches();
  }

  @Override
  public boolean isPasswordEqual(String pwd, String encPwd, String hashSalt) {
    logger.info("pwd:"+pwd+"，hashSalt："+hashSalt+"，encPwd："+encPwd);
    String encryptPwd = encryptPwd(pwd, hashSalt);
    return encPwd.equals(encryptPwd);
  }

  @Override
  public String genPwdSalt() {
    int temp = (int) (((1 + RandomUtils.nextInt(9)) + Math.random()) * 100000);
    long microTime = System.nanoTime() / 1000L;
    temp = temp % (int) microTime;
    return String.valueOf(temp);
  }

  @Override
  public String encryptPwd(String pwd, String hashSalt) {
    logger.info("pwd:"+pwd+"，hashSalt："+hashSalt);
    String firstHash = MD5Util.digestByMD5(pwd, "", null, "UTF-8");
    return MD5Util.digestByMD5(firstHash, authConfig.getPwdKey(), hashSalt, "UTF-8");
  }

  @Override
  public String genResetPwd() {
    /*密码位数为8-16个字符,且区分大小写,支持字母(a-z,A-Z)、数字(0-9)及“_@#$”符号*/
    return getPassWord();
  }
  /**
   * 测试100万次生成重复率为0
   * 密码位数为8-16个字符,且区分大小写,支持字母(a-z,A-Z)、数字(0-9)及“_@#$”符号
   * @return
   */
  private static String getPassWord(){
    char[] letters = new char[]{'A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q',
            'R','S','T','U','V','W','X','Y','Z','a','b','c','d','e','f','g','h','i',
            'j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z',
            '0','1','2','3','4','5','6','7','8','9','_','@','#','$'};
    /*随机选择8个字符*/
    boolean numberFlag = false;
    boolean characterFlag = false;
    boolean upperLetterFlag = false;
    boolean lowerLetterFlag = false;
    StringBuilder passWord = new StringBuilder();
    for(int i=0;i<8;i++){
      int randomNumber = RandomUtils.nextInt(0,66);
      if(randomNumber >= 0 && randomNumber <= 25){
        upperLetterFlag = true;
      }else if(randomNumber >= 26 && randomNumber <= 51){
        lowerLetterFlag = true;
      }else if(randomNumber >= 52 && randomNumber <= 61){
        numberFlag = true;
      }else if(randomNumber >= 62 && randomNumber <= 65){
        characterFlag = true;
      }
      passWord.append(letters[randomNumber]);
    }
    /*校验规则至少包含一个小写字符，一个大写字符，一个数字，一个符号
    根据规则校验结果进行调整，缺少则补位*/
    if(!upperLetterFlag){
      int randomNumber = RandomUtils.nextInt(0,25);
      passWord.append(letters[randomNumber]);
    }else if(!lowerLetterFlag){
      int randomNumber = RandomUtils.nextInt(26,51);
      passWord.append(letters[randomNumber]);
    }else if(!numberFlag){
      int randomNumber = RandomUtils.nextInt(52,61);
      passWord.append(letters[randomNumber]);
    }else if(!characterFlag){
      int randomNumber = RandomUtils.nextInt(62,65);
      passWord.append(letters[randomNumber]);
    }
    return passWord.toString();
  }
}
