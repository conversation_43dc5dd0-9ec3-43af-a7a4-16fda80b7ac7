package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.constant.*;
import com.ctrip.platform.dal.dao.annotation.*;
import com.google.common.collect.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.sql.*;
import java.util.*;

/**
 * 进单配置操作
 * <AUTHOR>
 * @Date 2020/3/6 14:04
 */
@Service("inOrderConfigCommandService")
public class InOrderConfigCommandServiceImpl implements InOrderConfigCommandService {

    @Autowired
    private InOrderConfigRepository inOrderConfigRepository;
    @Autowired
    private EnumRepository enumRepository;

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Integer> addInOrderConfig(List<TspIntoOrderConfigPO> intoOrderConfigPOList) {
        int count = inOrderConfigRepository.addInOrderConfig(intoOrderConfigPOList);
        if(count <= 0){
            throw new RuntimeException(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgPointIsError));
        }
        return Result.Builder.<Integer>newResult()
                .success()
                .withCode(ServiceResponseConstants.ResStatus.SUCCESS_CODE)
                .withData(count)
                .build();
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> addOrUpdateInOrderConfig(Long transportGroupId, List<TspIntoOrderConfigPO> configPOList) {
        //获取已存在的所有进单配置
        List<TspIntoOrderConfigPO> allConfigPOS = inOrderConfigRepository.queryInOrderConfigs(transportGroupId, null);

        List<TspIntoOrderConfigPO> needUpdate = Lists.newArrayList();
        List<TspIntoOrderConfigPO> needAdd = Lists.newArrayList();
        //分类
        configPOClassify(configPOList,allConfigPOS,needUpdate,needAdd);

        //更新进单配置
        inOrderConfigRepository.updateInOrderConfig(needUpdate);
        //新增进单配置
        inOrderConfigRepository.addInOrderConfig(needAdd);

        return Result.Builder.<Boolean>newResult()
                .success()
                .withCode(ServiceResponseConstants.ResStatus.SUCCESS_CODE)
                .withData(true)
                .build();
    }

    /**
     * 进单配置区分（区分需要新增/更新）
     */
    private void configPOClassify(List<TspIntoOrderConfigPO> request,List<TspIntoOrderConfigPO> all,List<TspIntoOrderConfigPO> needUpdate,List<TspIntoOrderConfigPO> needAdd){
        Map<Integer, Map<String,TspIntoOrderConfigPO>> requestMap = Maps.newHashMap();

        request.stream().forEach(intoOrderConfigPO -> {
            Integer locationType = intoOrderConfigPO.getLocationType();
            Map<String, TspIntoOrderConfigPO> locationMap = requestMap.computeIfAbsent(locationType, k -> Maps.newHashMap());
            locationMap.put(intoOrderConfigPO.getLocationCode(),intoOrderConfigPO);
        });

        all.forEach(configPO -> {
            Map<String, TspIntoOrderConfigPO> map = requestMap.get(configPO.getLocationType());
            if (map == null) {
                needUpdate.add(dealUpdatePO(configPO,null));
            }else {
                if (map.containsKey(configPO.getLocationCode())) {
                    needUpdate.add(dealUpdatePO(configPO,map.get(configPO.getLocationCode())));
                    map.remove(configPO.getLocationCode());
                }else {
                    needUpdate.add(dealUpdatePO(configPO,null));
                }
            }
        });
        //收集需要新增的PO
        for (Map<String, TspIntoOrderConfigPO> map : requestMap.values()) {
            map.values().forEach(configPO -> {
                configPO.setId(null);
                configPO.setDatachangeCreatetime(configPO.getDatachangeLasttime());
                configPO.setCreateUser(configPO.getModifyUser());
                configPO.setCountryId(enumRepository.getCountryId(configPO.getCityId()));
                needAdd.add(configPO);
            });
        }
    }

    //收集需要更新的PO
    private TspIntoOrderConfigPO dealUpdatePO(TspIntoOrderConfigPO originalConfigPO,TspIntoOrderConfigPO updateConfigPO){
        if (updateConfigPO != null) {
            originalConfigPO.setModifyUser(updateConfigPO.getModifyUser());
            originalConfigPO.setCountryId(enumRepository.getCountryId(updateConfigPO.getCityId()));
            originalConfigPO.setCountryName(updateConfigPO.getCountryName());
            originalConfigPO.setCityId(updateConfigPO.getCityId());
            originalConfigPO.setConfig(updateConfigPO.getConfig());
            originalConfigPO.setDatachangeLasttime(updateConfigPO.getDatachangeLasttime());
            originalConfigPO.setActive(updateConfigPO.getActive());
        }else {
            originalConfigPO.setActive(false);
            originalConfigPO.setDatachangeLasttime(new Timestamp(System.currentTimeMillis()));
        }
        return originalConfigPO;
    }
}
