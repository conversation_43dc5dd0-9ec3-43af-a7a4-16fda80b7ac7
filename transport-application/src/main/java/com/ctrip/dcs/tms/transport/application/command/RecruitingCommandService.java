package com.ctrip.dcs.tms.transport.application.command;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.igt.framework.common.result.*;

import java.util.*;

public interface RecruitingCommandService {

    /**
     * 供应商通过
     * 供应商驳回
     * 运营驳回
     * 操作仅是对状态的变更 同时更新司机审批表及车辆审批表
     */
    Result<Boolean> recruitingUpdateStatus(List<Long> recruitingIdList, int approveStatus, String operator, String remark, Integer approveFrom, Integer approverAperation, List<RejectReason> remarkList);

    /**
     * 通过并激活
     * 通过未激活 同时落库司机表及车辆表
     * passStatus ：true 审核通过就立刻上线； false 审核通过，状态走到未激活
     */
    Result<Boolean> recruitingUpdatePass(List<Long> recruitingIdList, String operator, boolean passStatus, String remark, Integer approveFrom, List<RejectReason> remarkList);

    /**
     * 审批路由接口
     * approveFrom 审批来源 1.司机H5,2.工作台创建司机,3.工作台创建车辆
     */
    Result<Boolean> approveRoute(RecruitingApproveSOARequestType recruitingApproveSOARequestType,Integer approveFrom);

    /**
     * 权限添加接口
     */
    Result<Boolean> recruitingApproveAdd(String roleCode, Integer areaScope, Integer drvFrom, Long mediumId, String operator, String remark,Integer approveFrom);

    /**
    　* @description: 初始化单项数据
    　* <AUTHOR>
    　* @date 2021/12/1 16:22
    */
    Boolean initSingleApprovalData(Long approveSourceId, Integer approveSourceType, String modifyUser, VehicleAddSOARequestType vehicleRequest, DrvAddSOARequestType drvRequest, List<InitOcrChildCheckStatusSOADTO> checkStatusSOADTOList, Map<String,Object> nucleicAcidMap);

    /**
    　* @description: H5初始化单项数据
    　* <AUTHOR>
    　* @date 2021/12/6 16:30
    */
    Boolean initH5SingleApprovalData(Long approveSourceDrvId, Long approveSourceVehicleId, DrvVehRecruitingAddSOARequestType requestType, String modifyUser,Map<String,Object> nucleicAcidMap);

    /**
    　* @description: 单项审批
    　* <AUTHOR>
    　* @date 2021/12/6 19:18
    */
    Result<Boolean> singleApproval(SingleApprovalSOARequestType requestType);

    /**
     　* @description: 保存标签
     　* <AUTHOR>
     　* @date 2023/6/8 16:36
     */
    Result<Boolean> saveOverseasTags(SaveOverseasTagsSOARequestType requestType);

    /**
     　* @description: 工作台创建单项
     　* <AUTHOR>
     　* @date 2023/6/9 16:34
     */
    Boolean initOverseasSingleData(Long approveSourceDrvId, Long approveSourceVehicleId, TmsTransportConstant.SingleApproveTypeEnum approveTypeEnum, List<OcrPassStatusModelSOA> occPassList, String modifyUser);

    /**
     　* @description: 境外新逻辑自动审批
     　* <AUTHOR>
     　* @date 2023/6/26 10:25
     */
    Result<Boolean> overseasNewBusinessApprove(Long supplierId,Integer versionFlag,Integer areaScope,Long recruitingId,List<OcrPassStatusModelSOA> passStatusList, TmsTransportConstant.RecruitingTypeEnum recruitingTypeEnum,TmsTransportConstant.SingleApproveTypeEnum approveTypeEnum,String modifyUser);

}
