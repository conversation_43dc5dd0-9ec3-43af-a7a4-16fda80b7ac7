package com.ctrip.dcs.tms.transport.task.application.type.impl;

import com.ctrip.dcs.tms.transport.api.model.IsNeedCreateTaskRequestType;
import com.ctrip.dcs.tms.transport.api.model.SaveIsNeedCreateTaskRequestType;
import com.ctrip.dcs.tms.transport.api.model.UploadVehicleDispatchPhotoListRequestType;
import com.ctrip.dcs.tms.transport.api.model.VehicleDetailDTO;
import com.ctrip.dcs.tms.transport.api.model.VehicleDispatchPhotoDTO;
import com.ctrip.dcs.tms.transport.api.model.VehicleDispatchPhotoTaskConfigParamDTO;
import com.ctrip.dcs.tms.transport.application.command.TmsQmqProducerCommandService;
import com.ctrip.dcs.tms.transport.application.dto.SimpleDriverInfoDTO;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.dcs.tms.transport.application.query.VehicleQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ErrorCodeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.RedisUtils;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.SharkUtils;
import com.ctrip.dcs.tms.transport.task.application.type.TaskService;
import com.ctrip.dcs.tms.transport.task.application.type.VehicleDispatchPhotoTaskService;
import com.ctrip.dcs.tms.transport.task.infrastructure.common.enums.NeedCreateTaskEnum;
import com.ctrip.dcs.tms.transport.task.infrastructure.common.enums.TaskTypeEnum;
import com.ctrip.dcs.tms.transport.task.infrastructure.common.qconfig.TaskVehicleDispatchBusinessQconfig;
import com.ctrip.dcs.tms.transport.task.infrastructure.port.repository.impl.TaskVehicleDispatchPhotoApprovalRepository;
import com.ctrip.dcs.tms.transport.task.infrastructure.value.UploadVehicleDispatchPhotoList;
import com.ctrip.dcs.tms.transport.task.infrastructure.value.VehicleDispatchPhoto;
import com.ctrip.dcs.tms.transport.task.infrastructure.value.VehicleDispatchPhotoItemConfig;
import com.ctrip.dcs.tms.transport.task.infrastructure.value.VehicleDispatchPhotoTaskConfig;
import com.ctrip.igt.framework.common.result.Result;
import com.dianping.cat.Cat;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class VehicleDispatchPhotoTaskServiceImpl implements VehicleDispatchPhotoTaskService, TaskService {

  private static final String DEFAULT_CONFIG = "economy";
  private static final String VEHICLE_DISPATCH_PHOTO_TASK_REDIS_KEY_PREFIX = "dcs_vdp_";

  @Autowired
  VehicleQueryService vehicleQueryService;

  @Autowired
  DriverQueryService driverQueryService;

  @Autowired
  TaskVehicleDispatchPhotoApprovalRepository repository;

  @Autowired
  TaskVehicleDispatchBusinessQconfig taskBusinessQconfig;

  @Autowired
  TmsQmqProducerCommandService tmsQmqProducerCommandService;


  @Override
  public Result<String> queryTaskConfig(String taskConfigParam) {
    VehicleDispatchPhotoTaskConfigParamDTO param =
      JsonUtil.fromJson(taskConfigParam, new TypeReference<VehicleDispatchPhotoTaskConfigParamDTO>() {
      });
    Result<VehicleDetailDTO> vehicleDetail = vehicleQueryService.queryVehicleDetail(param.getVehicleId());
    if(vehicleDetail.getData() == null) {
      return Result.Builder.<String>newResult().fail().withCode(ErrorCodeEnum.TRANSPORT_VEHICLE_NOT_EXISTS.getCode()).withMsg(SharkUtils.getSharkValue(
        SharkKeyConstant.transportVehicleIsEmpty)).build();
    }
    return getConfig(vehicleDetail.getData().getVehicleTypeId());
  }

  protected Result<String> getConfig(Long vehicleTypeId) {
    String configJson = taskBusinessQconfig.getVehicleTaskConfig().values().stream()
      .filter(data -> data.getVehicleTypeIdList().contains(vehicleTypeId))
      .findFirst()
      .map(data -> JsonUtil.toJson(getMultiLanguage(data.getPhotoList())))
      .orElseGet(() -> JsonUtil.toJson(getMultiLanguage(taskBusinessQconfig.getVehicleTaskConfig().get(DEFAULT_CONFIG).getPhotoList())));
    return Result.Builder.<String>newResult().success().withData(configJson).build();
  }

  protected VehicleDispatchPhotoTaskConfig getMultiLanguage(List<VehicleDispatchPhotoItemConfig> photoList) {
    Optional.ofNullable(photoList).orElse(Lists.newArrayList()).forEach(photo -> {
      // 转shark
      photo.setPhotoTypeName(SharkUtils.getSharkValue(photo.getPhotoTypeName(), photo.getPhotoTypeName()));
      photo.setPhotoTypeDes(SharkUtils.getSharkValue(photo.getPhotoTypeDes(), photo.getPhotoTypeDes()));
    });
    VehicleDispatchPhotoTaskConfig config = new VehicleDispatchPhotoTaskConfig();
    config.setPhotoList(photoList);
    return config;
  }

  @Override
  public Result<NeedCreateTaskEnum> isNeedCreateTask(IsNeedCreateTaskRequestType requestType) {
    SimpleDriverInfoDTO driver = driverQueryService.querySimpleDriver(requestType.getDrvId());
    if (driver == null || (!taskBusinessQconfig.getVehicleDispatchGrayCityList().contains(-1L) && !taskBusinessQconfig.getVehicleDispatchGrayCityList().contains(driver.getCityId()))) {
      return Result.Builder.<NeedCreateTaskEnum>newResult().success().withData(NeedCreateTaskEnum.NOT_NEED).build();
    }
    Integer value = RedisUtils.getInteger(VEHICLE_DISPATCH_PHOTO_TASK_REDIS_KEY_PREFIX + requestType.getDrvId());
    return Result.Builder.<NeedCreateTaskEnum>newResult().success().withData(NeedCreateTaskEnum.getByCode(value)).build();
  }

  @Override
  public Result<Boolean> saveIsNeedCreateTask(SaveIsNeedCreateTaskRequestType requestType) {
    RedisUtils.setString(VEHICLE_DISPATCH_PHOTO_TASK_REDIS_KEY_PREFIX + requestType.getDriverId(), DateUtil.getSecondsFromEndOffDay(), String.valueOf(NeedCreateTaskEnum.getByCode(requestType.getFlag()).getCode()));
    return Result.Builder.<Boolean>newResult().success().withData(true).build();
  }

  @Override
  public boolean match(TaskTypeEnum taskTypeEnum) {
    return TaskTypeEnum.VEHICLE_DISPATCH_PHOTO == taskTypeEnum;
  }

  @Override
  public Result<Boolean> uploadVehicleDispatchPhotoList(UploadVehicleDispatchPhotoListRequestType requestType) {
    Cat.logEvent(TaskTypeEnum.VEHICLE_DISPATCH_PHOTO.getCode(), "upload_count");
    Result<VehicleDetailDTO> vehicleDetail = vehicleQueryService.queryVehicleDetail(requestType.getVehicleId());
    SimpleDriverInfoDTO driver = driverQueryService.querySimpleDriver(requestType.getDrvId());

    Result<Boolean> check = check(requestType, driver, vehicleDetail);
    if (check == null || !check.isSuccess()) {
      return check;
    }
    Long insert = repository.insert(convert(requestType, vehicleDetail.getData(), driver));
    // 发送审核信息
    tmsQmqProducerCommandService.sendDrvOutCheck(insert);

    // 设置司机不在需要上传照片任务
    SaveIsNeedCreateTaskRequestType newRequestType = new SaveIsNeedCreateTaskRequestType();
    newRequestType.setDriverId(requestType.getDrvId());
    newRequestType.setFlag(NeedCreateTaskEnum.NOT_NEED.getCode());
    this.saveIsNeedCreateTask(newRequestType);
    return Result.Builder.<Boolean>newResult().success().build();
  }

  @Nullable
  protected Result<Boolean> check(UploadVehicleDispatchPhotoListRequestType requestType,
    SimpleDriverInfoDTO driver, Result<VehicleDetailDTO> vehicleDetail) {
    if(driver == null) {
      return Result.Builder.<Boolean>newResult().fail().withCode(ErrorCodeEnum.TRANSPORT_DRIVER_NOT_EXISTS.getCode())
        .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.driverNotExist)).build();
    }

    if(vehicleDetail.getData() == null) {
      return Result.Builder.<Boolean>newResult().fail().withCode(ErrorCodeEnum.TRANSPORT_VEHICLE_NOT_EXISTS.getCode())
        .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportVehicleIsEmpty)).build();
    }

    if (CollectionUtils.isEmpty(requestType.getPhotoList())) {
      return Result.Builder.<Boolean>newResult().fail().withCode(ErrorCodeEnum.TRANSPORT_VEHICLE_DISPATCH_PHOTO_MUST_NOT_EMPTY.getCode())
        .withMsg(SharkUtils.getSharkValue(ErrorCodeEnum.TRANSPORT_VEHICLE_DISPATCH_PHOTO_MUST_NOT_EMPTY.getMessage())).build();
    }
    return Result.Builder.<Boolean>newResult().success().build();
  }

  protected UploadVehicleDispatchPhotoList convert(UploadVehicleDispatchPhotoListRequestType requestType, VehicleDetailDTO vehicle, SimpleDriverInfoDTO driver) {
      UploadVehicleDispatchPhotoList dto = new UploadVehicleDispatchPhotoList();
      dto.setVehicleId(requestType.getVehicleId());
      dto.setVehicleLicense(vehicle.getVehicleLicense());
      dto.setVehicleTypeId(vehicle.getVehicleTypeId());
      dto.setDrvId(requestType.getDrvId());
      dto.setDrvName(driver.getDriverName());
      dto.setBusinessId(requestType.getBusinessId());
      dto.setSupplierId(driver.getSupplierId());
      dto.setPhotoList(convertPhotoList(requestType.getPhotoList()));
      dto.setCityId(vehicle.getCityId());
      return dto;
  }

  protected List<VehicleDispatchPhoto> convertPhotoList(List<VehicleDispatchPhotoDTO> photoList) {
    return photoList.stream().map(this::convertPhoto).collect(Collectors.toList());
  }

  protected VehicleDispatchPhoto convertPhoto(VehicleDispatchPhotoDTO photo) {
    VehicleDispatchPhoto dto = new VehicleDispatchPhoto();
    dto.setPhotoUrl(photo.getPhotoUrl());
    dto.setPhotoType(photo.getPhotoType());
    return dto;
  }
}
