package com.ctrip.dcs.tms.transport.application.command;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.common.result.*;

public interface SaveDriverSafetyInfoCommandService {

    /**
     * 保存核酸报告疫苗等情况
     */
    Result<String> saveDriverSafetyInfo(SaveDriverSafetyInfoSOARequestType request);
}
