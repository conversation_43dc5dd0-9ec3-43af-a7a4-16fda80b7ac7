package com.ctrip.dcs.tms.transport.application.query;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.common.result.*;

import java.util.*;

/**
 * 进单配置
 * <AUTHOR>
 * @Date 2020/3/6 14:01
 */
public interface InOrderConfigQueryService {

    /**
     * 检查配置
     * @param configSOATypes
     * @return
     */
    boolean checkConfig(List<InOrderConfigSOAType> configSOATypes);

    /**
     * 按运力组id查询进单配置
     * @param transportGroupId
     * @return
     */
    Result<List<TspIntoOrderConfigPO>> queryInOrderConfigs(Long transportGroupId,Integer active);

}
