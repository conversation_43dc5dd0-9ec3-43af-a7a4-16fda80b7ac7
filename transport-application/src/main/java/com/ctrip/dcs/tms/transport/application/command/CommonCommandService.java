package com.ctrip.dcs.tms.transport.application.command;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.igt.framework.common.result.*;

import java.util.*;

public interface CommonCommandService {

    Result<String> sendMessageByPhone4China(String mobilePhone,String site);
    Result<String> sendMessageByPhone4China(SendVerificationPhoneSOARequestType request);

    Result<String> checkPoneCode4China(String code, String mobilePhone, String countryCode, String site);

    Map<String,Integer> getJurisdictionMap(String roleCode,Integer areaScope,Integer drvFrom,Integer approveStatus);

    List<Integer> getAuthCode(List<Integer> sourceCodeList);

    String[] createQunarAccount(DrvDriverPO driverPO);

    String createCtripAccount(DrvDriverPO driverPO);

    void updateaCtripAccount(DrvDriverPO driverPO);

     /**
     * 发送短信
     * @param mobilePhone
     * @param messageCode
     * @param igtCode
     * @return
     */
    Result<Boolean> sendMessageByPhone(String igtCode,String mobilePhone,String messageCode,Map<String,String> params);

    /**
     * 发送邮件
     * @param receiverEmail 收件人
     * @param content 发送内容
     * @return
     */
    Result<Boolean> sendEmail(String subject,String receiverEmail,String content);

    /**
     * 发送审批通过，上线成功短信
     * */
    Result<Boolean> sendJoinMessageByPhone(Long drvId,String igtCode,String mobilePhone);

    /**
     * 驾驶证核验
     * @param name
     * @param drvCardNo
     * @return
     */
    Result<QueryDrvCardInfoSOAResponseType> queryDrvCardInfo(String name, String drvCardNo);

    /**
     * 行驶证核验
     * @param plateNumber 车牌号
     * @param plateType 号牌种类
     * @return
     */
    Result<QueryVehicleCardInfoSOAResponseType> queryVehicleCardInfo(String plateNumber, String plateType);


    /**
     * 查询网约车驾驶证信息
     * @param drvCardNo 驾驶证号
     * @response
     * @return 返回驾驶证信息
     */
    Result<QueryNetDrvCardInfoSOAResponseType> queryNetDrvCardInfo(String  drvCardNo);
    /**
     * 查询网约车驾驶证信息
     * @param paramDTO 驾驶证号
     * @response
     * @return 返回驾驶证信息
     */
    Result<QueryNetCertResultDTO> queryNetDrvCardInfo(QueryNetDrvCardInfoParamDTO paramDTO);

    /**
     * 查询网约车行驶证信息
     * @param vehicleLicense 车牌号
     * @response
     * @return 返回驾驶证信息
     */
    Result<QueryNetVehicleCardInfoSOAResponseType> queryNetVehicleCardInfo(String  vehicleLicense);

    /**
     * 查询网约车行驶证信息
     * @param paramDTO 车牌号
     * @response
     * @return 返回驾驶证信息
     */
    Result<QueryNetCertResultDTO> queryNetVehicleCardInfo(QueryNetVehicleCardInfoParamDTO paramDTO);
    /**
     * OCR-DrivingLicense驾驶证识别
     */
    DrvDriverPO drivingLicenseOCR(Long drvId, String imageUrl);

    /**
     * 判断供应商是否是自营
     * @param supplierId
     * @return
     */
    Boolean judgeSupplierIsZY(Long supplierId);

    /**
     * OCR-VehicleDrivingLicense行驶证识别
     */
    VehVehiclePO vehicleDrivingLicenseImgOCR(Long vehicleId, String imageUrl);

    /**
     * 判断供应商是否是自营
     * @param supplierId
     * @return
     */
    Boolean judgeSupplierIsZYById(Long supplierId);

    /**
     * 查询TTS完单量
     * @param cityId
     * @param vehicleTypeId
     * @param useVehicleDate
     * @return
     */
    QueryTodayOrderCountDTO queryTodayOrderCount(Long cityId, Long vehicleTypeId, String useVehicleDate);

    /***
    　* @description: 查询城市对应的证件图例和核验地址
    　* <AUTHOR>
    　* @date 2021/8/17 14:58
    */
    Result<List<QueryCityNetConfigListSOADTO>> queryCityNetConfigList(QueryCityNetConfigListSOARequestType requestType);


    Result<String> sendVoiceCode(String mobilePhone, String countryCode, String site);

}
