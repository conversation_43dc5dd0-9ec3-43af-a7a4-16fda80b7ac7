package com.ctrip.dcs.tms.transport.application.command;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.common.result.*;

/**
 * <AUTHOR>
 * @since 2020/9/22 16:00
 */
public interface DriverAccountCommandService {

  /**
   * 重置密码
   *
   * @param loginAccount 登录帐号(手机、账户或者邮箱)
   * @return
   */
  Result<DrvDriverPO> resetPwd(String loginAccount);

  /**
   * 更新密码
   *
   * @param drvId 司机ID
   * @param oldPwd 旧密码
   * @param newPwd 新密码
   * @param confirmPwd 确认密码
   * @return
   */
  Result<DrvDriverPO> updatePwd(Long drvId, String oldPwd, String newPwd, String confirmPwd);
}
