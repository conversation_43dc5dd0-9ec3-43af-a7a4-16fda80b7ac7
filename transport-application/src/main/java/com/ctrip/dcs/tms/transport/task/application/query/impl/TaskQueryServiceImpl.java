package com.ctrip.dcs.tms.transport.task.application.query.impl;

import com.ctrip.dcs.tms.transport.api.model.IsNeedCreateTaskRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryTaskConfigRequestType;
import com.ctrip.dcs.tms.transport.task.application.type.TaskManageService;
import com.ctrip.dcs.tms.transport.task.infrastructure.common.enums.NeedCreateTaskEnum;
import com.ctrip.igt.framework.common.result.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TaskQueryServiceImpl implements TaskQueryService{

  @Autowired
  private TaskManageService taskManageService;

  @Override
  public Result<String> queryTaskConfig(QueryTaskConfigRequestType requestType) {
    return taskManageService.queryTaskConfig(requestType);
  }

  @Override
  public Result<NeedCreateTaskEnum> isNeedCreateTask(IsNeedCreateTaskRequestType requestType) {
    return taskManageService.isNeedCreateTask(requestType);
  }
}
