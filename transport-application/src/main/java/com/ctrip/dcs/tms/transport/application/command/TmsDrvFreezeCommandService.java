package com.ctrip.dcs.tms.transport.application.command;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.common.result.*;

import java.util.*;

/**
* 司机车辆审批类接口
* <AUTHOR>
* @date 2020/4/20 15:16
*/
public interface TmsDrvFreezeCommandService {

    /**
     * 添加司机冻结审批
     * @param requestType
     * @return
     */
    Result<Boolean> addDrvFreeze(TmsDrvFreezeAddSOARequestType requestType, boolean isNucleicAcidVaccine);

    /**
     * 司机解冻
     * @param requestType
     * @return
     */
    Result<Boolean> drvUnFreeze(DrvUnFreezeSOARequestType requestType);

    /**
     * 批量冻结司机
     * @param requestType
     * @return
     */
    Result<List<QueryDoDrvFreezeSOADTO>> batchDrvFreeze(DrvFreezeBatchSOARequestType requestType);

    /**
     * 解冻司机
     */
    Result<Boolean> execUnfreezeDrv(TmsDrvFreezePO drvFreezePO, DrvDriverPO drvDriverPO, Integer hours, boolean dateCheck);

    /**
     * 查询当前司机是否因报告原因已冻结
     */
    boolean checkReportFreezeStatus(Long drvId);

    /**
     * 判罚司机下线
     * 针对判罚司机的下线做 冻结7天后下线逻辑处理
     * iwork：http://iwork.ctripcorp.com/#/carddetail/3759/6754/0/968750
     * */
    Result<Boolean> penaltyDrvFreeze(List<Long> drvIdList, String modifyUser, String reason);

    /**
    　* @description: 确认司机解冻后是否上线
    　* <AUTHOR>
    　* @date 2022/10/13 15:24
    */
    Result<Boolean> drvUnfreezeConfirmOnline(DrvUnfreezeConfirmOnlineSOARequestType requestType);

}
