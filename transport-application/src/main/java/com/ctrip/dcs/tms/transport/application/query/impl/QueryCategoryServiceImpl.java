package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.scm.merchant.interfaces.dto.ContractDTO;
import com.ctrip.dcs.scm.merchant.interfaces.dto.ContractListQueryFilterDTO;
import com.ctrip.dcs.scm.merchant.interfaces.dto.ServedScopesDTO;
import com.ctrip.dcs.scm.merchant.interfaces.message.QueryContractListRequestType;
import com.ctrip.dcs.scm.merchant.interfaces.message.QueryContractListResponseType;
import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import com.ctrip.dcs.scm.sdk.domain.CategoryRepository;
import com.ctrip.dcs.scm.sdk.domain.category.Category;
import com.ctrip.dcs.tms.transport.api.model.CategorySOADTO;
import com.ctrip.dcs.tms.transport.application.query.QueryCategoryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DcsScmMerchantServiceClientProxy;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl.EnumRepositoryHelper;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryCategoryServiceImpl implements QueryCategoryService {

  private static final Logger logger = LoggerFactory.getLogger(QueryCategoryServiceImpl.class);

  @Autowired
  private CategoryRepository categoryRepository;

  @Autowired
  private EnumRepositoryHelper helper;

  @Autowired
  private DcsScmMerchantServiceClientProxy dcsScmMerchantServiceClientProxy;

  @Override
  public List<CategorySOADTO> getContractList(Long supplierId, List<Long> cityIdList) {
    List<Category> categories = Arrays.stream(ParentCategoryEnum.values())
      .map(c -> categoryRepository.findOneByCategoryCode(c.getCode(), helper.getLocaleCode())).collect(
        Collectors.toList());
    if (supplierId == null || CollectionUtils.isEmpty(cityIdList)) {
      return getCategorySOAList(categories);
    }
    QueryContractListRequestType contractListRequestType = new QueryContractListRequestType();
    contractListRequestType.setRetrievalItems(ImmutableList.of("contract.servedscope"));
    ContractListQueryFilterDTO filterDTO = new ContractListQueryFilterDTO();
    filterDTO.setSupplierIds(ImmutableList.of(supplierId));
    contractListRequestType.setInclusionFilter(filterDTO);
    try {
      QueryContractListResponseType listResponseType = dcsScmMerchantServiceClientProxy.queryContractList(contractListRequestType);
      if (listResponseType == null || CollectionUtils.isEmpty(listResponseType.getContracts())) {
        logger.error("QueryContractInfoFail", "supplierId:{} cityIdList:{}", supplierId, cityIdList);
        return Collections.emptyList();
      }
      List<ContractDTO> contractDTOS = listResponseType.getContracts();
      if (CollectionUtils.isEmpty(contractDTOS)) {
        return getCategorySOAList(categories);
      }
      Set<String> contractLineSet = Sets.newHashSet();
      for (ContractDTO contractDTO : contractDTOS) {
        if (CollectionUtils.isEmpty(contractDTO.getServedScope())) {
          continue;
        }
        for (ServedScopesDTO scopesDTO : contractDTO.getServedScope()) {
          if (CollectionUtils.isEmpty(scopesDTO.getCityIds())) {
            continue;
          }
          for (Long cityId : scopesDTO.getCityIds()) {
            if (cityIdList.contains(cityId)) {
              contractLineSet.add(scopesDTO.getCategoryCode());
            }
          }
        }
      }
      for (int i = categories.size() - 1; i >= 0; i--) {
        if (!contractLineSet.contains(categories.get(i).getCode())) {
          categories.remove(i);
        }
      }
      return getCategorySOAList(categories);
    } catch (Exception e) {
      logger.error("QueryContractInfoError", "supplierId:{} cityIdList:{} e:{}", supplierId, cityIdList, e);
      return Collections.emptyList();
    }
  }

  protected List<CategorySOADTO> getCategorySOAList(List<Category> categories) {
    if (CollectionUtils.isEmpty(categories)) {
      return Collections.emptyList();
    }
    List<CategorySOADTO> res = Lists.newArrayListWithExpectedSize(categories.size());
    for (Category category : categories) {
      CategorySOADTO soaDto = new CategorySOADTO();
      soaDto.setId(category.getId().intValue());
      soaDto.setName(category.getName());
      res.add(soaDto);
    }
    return res;
  }
}
