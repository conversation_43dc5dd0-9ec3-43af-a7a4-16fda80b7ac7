package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.arch.distlock.*;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.config.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.constant.*;
import com.ctrip.igt.infrastructureservice.executor.contract.*;
import com.ctrip.platform.dal.dao.annotation.*;
import com.google.common.collect.*;
import credis.java.client.*;
import credis.java.client.pipeline.*;
import credis.java.client.util.CacheFactory;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.*;

@Service
public class TmsVerifyEventQueryServiceImpl implements TmsVerifyEventQueryService {

    private static final Logger logger = LoggerFactory.getLogger(TmsVerifyEventQueryServiceImpl.class);

    private static final String FACE_VERIFY_FAIL_COUNT_KEY = "face_verify_fail_count_key_";

    @Autowired
    private TmsVerifyEventRepository eventRepository;
    @Autowired
    private DrvDrvierRepository drvDrvierRepository;
    @Autowired
    private TmsTransportQconfig qconfig;
    @Autowired
    private InfrastructureServiceClientProxy infrastructureServiceClientProxy;
    @Autowired
    private VehicleRepository vehicleRepository;
    @Autowired
    private TmsVerifyEventCommandService eventCommandService;
    @Autowired
    TmsVerifyRecordRepository tmsVerifyRecordRepository;
    private CacheProvider provider = CacheFactory.getProvider("dcs_tms");
    @Autowired
    private DistributedLockConfig distributedLockConfig;

    @Autowired
    OCRQConfig ocrqConfig;

    @Override
    public Result<DrvVerifyParamsSOADTO> queryDrvVerifyParams(DrvVerifyParamsSOARequestType requestType) {
        DrvDriverPO drvDriverPO = drvDrvierRepository.queryByPk(requestType.getDriverId());
        if (Objects.isNull(drvDriverPO)) {
            return Result.Builder.<DrvVerifyParamsSOADTO>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportDrvisEmpty)).build();
        }

        GetFaceAuthSignRequestType signRequestType = new GetFaceAuthSignRequestType();
        signRequestType.setOrderNo(String.valueOf(requestType.getDriverId()));
        GetFaceAuthSignResponseType signResponseType = infrastructureServiceClientProxy.getFaceAuthSign(signRequestType);
        if (signResponseType == null || signResponseType.getResponseResult() == null || !ServiceResponseConstants.ResStatus.SUCCESS_CODE.equals(signResponseType.getResponseResult().getReturnCode())) {
            logger.warn("query public interface getFaceAuthSign","result:{}", JsonUtil.toJson(signResponseType));
            return Result.Builder.<DrvVerifyParamsSOADTO>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportRequestInterfaceFail,"【getFaceAuthSign】","")).build();
        }
        DrvVerifyParamsSOADTO soadto = new DrvVerifyParamsSOADTO();
        soadto.setDriverId(requestType.getDriverId());
        soadto.setDriverIdCardImg(drvDriverPO.getIdcardImg());
        if(judgeVerifyFailReplaceHead(requestType.getDriverId())){
            soadto.setDriverIdCardImg(drvDriverPO.getDrvHeadImg());
        }
        soadto.setSign(signResponseType.getSign());
        soadto.setAppId(signResponseType.getAppId());
        soadto.setNonce(signResponseType.getNonce());
        return Result.Builder.<DrvVerifyParamsSOADTO>newResult().success().withData(soadto).build();

    }

    @Override
    public Result<DrvVerifyResultSOADTO> queryDrvVerifyResult(DrvVerifyResultSOARequestType requestType) {
        DrvVerifyResultSOADTO soadto = new DrvVerifyResultSOADTO();
        soadto.setDriverId(requestType.getDriverId());
        //TODO 调公共查询人脸识别
        GetFaceResultRequestType resultRequestType = new GetFaceResultRequestType();
        resultRequestType.setOrderNo(requestType.getVerifyId());
        resultRequestType.setGetFile("2");
        String resultCode = "";
        String resultDesc = "";
        GetFaceResultResponseType resultResponseType = infrastructureServiceClientProxy.getFaceResult(resultRequestType);
        if (resultResponseType == null || resultResponseType.getResponseResult() == null || !ServiceResponseConstants.ResStatus.SUCCESS_CODE.equals(resultResponseType.getResponseResult().getReturnCode())) {
            logger.warn("query public interface getFaceResult","result:{}", JsonUtil.toJson(resultResponseType));
            resultCode = "99999";
            resultDesc = SharkUtils.getSharkValue(SharkKeyConstant.transportRequestInterfaceFail,"【getFaceResult】","");
            if(resultResponseType!=null && resultResponseType.getResponseResult()!=null){
                resultDesc = resultResponseType.getResponseResult().getReturnMessage();
            }
        }else{
            resultCode = resultResponseType.getCode();
            resultDesc = resultResponseType.getMsg();
        }
        soadto.setVerifyResult(resultCode);
        soadto.setVerifyResultDesc(resultDesc);
        //公共返回结果并将事件标注为已验证同事新生成一个人脸事件
        eventCommandService.insertFaceResultedEvent("",requestType.getDriverId(), resultCode, resultDesc, requestType.getModifyUser());
        //验证错误次数累计
        if(StringUtils.isNotEmpty(resultCode) && !StringUtils.equals(resultCode,"0")){
            this.verifyFailCountCumulative(requestType.getDriverId());
        }
        //认证失败的需保存司机验证时的图片
        if(qconfig.getNeedSaveResultCodeList().contains(resultCode) || qconfig.getNeedSaveResultCodeList().contains("-1")){
            if(StringUtils.isNotEmpty(resultResponseType.getPhoto())){
                resultDesc = resultDesc + "("+resultResponseType.getPhoto()+")";
            }
        }
        //插入验证记录表
        insertVerifyRecord(requestType.getDriverId(), TmsTransportConstant.VerifyTypeEnum.FACE,JsonUtil.toJson(resultRequestType),JsonUtil.toJson(resultResponseType),resultCode,resultDesc);
        return Result.Builder.<DrvVerifyResultSOADTO>newResult().success().withData(soadto).build();
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<VehicleVerifyResultSOADTO> queryVehicleVerifyResult(VehicleVerifyResultSOARequestType requestType) {
        DrvDriverPO drvDriverPO = drvDrvierRepository.queryByPk(requestType.getDriverId());
        if (Objects.isNull(drvDriverPO)) {
            return Result.Builder.<VehicleVerifyResultSOADTO>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportDrvisEmpty)).build();
        }
        Long vehicleId = getVehicleId(requestType.getVehicleId(), drvDriverPO);
        if (vehicleId == null || vehicleId <= 0) {
            return Result.Builder.<VehicleVerifyResultSOADTO>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportVehicleIsEmpty)).build();
        }
        VehVehiclePO vehVehiclePO = vehicleRepository.queryByPk(vehicleId);
        if (Objects.isNull(vehVehiclePO)) {
            return Result.Builder.<VehicleVerifyResultSOADTO>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportVehicleIsEmpty)).build();
        }
        VehicleVerifyResultSOADTO soadto = new VehicleVerifyResultSOADTO();
        soadto.setDriverId(requestType.getDriverId());
        soadto.setVehicleId(vehVehiclePO.getVehicleId());
        PlateLicenseRequestType resultRequestType = new PlateLicenseRequestType();
        resultRequestType.setImageUrl(requestType.getVehicleFullImg());
        String resultCode = "0";
        String resultDesc = "success";
        PlateLicenseResponseType resultResponseType = infrastructureServiceClientProxy.plateLicense(resultRequestType);
        if (resultResponseType == null || resultResponseType.getResponseResult() == null || !ServiceResponseConstants.ResStatus.SUCCESS_CODE.equals(resultResponseType.getResponseResult().getReturnCode())) {
            logger.warn("query public interface plateLicense","result:{}", JsonUtil.toJson(resultResponseType));
            resultCode = "99999";
            resultDesc = SharkUtils.getSharkValue(SharkKeyConstant.transportRequestInterfaceFail,"【plateLicense】","");
            if(resultResponseType!=null && resultResponseType.getResponseResult()!=null){
                resultDesc = resultResponseType.getResponseResult().getReturnMessage();
            }
        }else{
            if (Objects.isNull(resultResponseType.getPlateLicense()) || !Objects.equals(resultResponseType.getPlateLicense().getNumber(), vehVehiclePO.getVehicleLicense())) {
                resultCode = "5";
                resultDesc = SharkUtils.getSharkValue(SharkKeyConstant.transportQueryDataIsEmpty);
            }
            if (StringUtils.isNotBlank(resultResponseType.getPlateLicense().getColor()) && ocrqConfig.getOcrIllegalColorList().contains(resultResponseType.getPlateLicense().getColor())) {
                logger.warn("query public interface plateLicense, color not match","result:{}", JsonUtil.toJson(resultResponseType));
                resultCode = ErrorCodeEnum.TRANSPORT_VEHICLE_COLOR_ILLEGAL.getCode();
                resultDesc = SharkUtils.getSharkValue(ErrorCodeEnum.TRANSPORT_VEHICLE_COLOR_ILLEGAL.getMessage(), "vehicle ocr recognize color illegal");
            }
        }
        soadto.setVerifyResult(resultCode);
        soadto.setVerifyResultDesc(resultDesc);
        eventCommandService.insertVehicleResultedEvent("",vehVehiclePO.getVehicleId(), resultCode, resultDesc, drvDriverPO.getDrvName());
        //插入验证记录表
        insertVerifyRecord(vehVehiclePO.getVehicleId(), TmsTransportConstant.VerifyTypeEnum.VEHICLE,JsonUtil.toJson(resultRequestType),JsonUtil.toJson(resultResponseType),resultCode,resultDesc);
        return Result.Builder.<VehicleVerifyResultSOADTO>newResult().success().withData(soadto).build();
    }

    protected Long getVehicleId(Long vehicleId, DrvDriverPO drvDriverPO) {
        return Optional.ofNullable(vehicleId).orElse(ocrqConfig.getUseDriverBindVehicleVerify() ? drvDriverPO.getVehicleId() : null);
    }

    @Override
    public Result<DrvIsNeedVerifySOADTO> drvIsNeedVerify(DrvIsNeedVerifySOARequestType requestType) {

        DrvDriverPO drvDriverPO = drvDrvierRepository.queryByPk(requestType.getDriverId());
        if (Objects.isNull(drvDriverPO)) {
            return Result.Builder.<DrvIsNeedVerifySOADTO>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportDrvisEmpty)).build();
        }
        Long drvId = drvDriverPO.getDrvId();
        Long vehicleId = getVehicleId(requestType.getVehicleId(), drvDriverPO);
        DrvIsNeedVerifySOADTO soadto = new DrvIsNeedVerifySOADTO();
        soadto.setVerifyEventType(requestType.getVerifyEventType());
        soadto.setVerifyTimeOutThreshold(qconfig.getVerifyTimeOut());
        //人车都为false,说明关闭人车验证
        if((!qconfig.getDrvVerifySwitch() && !qconfig.getVehicleVerifySwitch()) || Objects.equals(drvDriverPO.getInternalScope(), AreaScopeTypeEnum.OVERSEAS.getCode()) || BaseUtil.getLongSet(ocrqConfig.getExemptVerifyDrvIdList()).contains(drvDriverPO.getDrvId())){
            soadto.setVerifyFlag(TmsTransportConstant.VerifyFlagEnum.NO_VERIFY.getCode());
            return Result.Builder.<DrvIsNeedVerifySOADTO>newResult().success().withData(soadto).build();
        }
        List<Long> list = Lists.newArrayList();
        Map<String, Long> sourceIdMap = Maps.newConcurrentMap();
        Map<Integer, Long> sourceTypeMap = Maps.newConcurrentMap();
        Integer verifyType = TmsTransportConstant.VerifyTypeEnum.FACE.getCode();
        if(qconfig.getDrvVerifySwitch()){
            list.add(drvId);
            sourceIdMap.put(TmsTransportConstant.DRV_SOURCE_ID_KEY, drvId);
            sourceTypeMap.put(TmsTransportConstant.VerifyTypeEnum.FACE.getCode(), drvId);
        }
        if (vehicleId != null && vehicleId > 0 && qconfig.getVehicleVerifySwitch()) {
            list.add(vehicleId);
            sourceIdMap.put(TmsTransportConstant.VEHICLE_SOURCE_ID_KEY, vehicleId);
            verifyType = TmsTransportConstant.VerifyTypeEnum.VEHICLE.getCode();
            sourceTypeMap.put(TmsTransportConstant.VerifyTypeEnum.VEHICLE.getCode(), vehicleId);
        }
        if(qconfig.getDrvVerifySwitch() && qconfig.getVehicleVerifySwitch() && vehicleId != null && vehicleId > 0){
            verifyType = null;
        }
        List<TmsVerifyEventPO> eventPOList = eventRepository.queryWaitVerifyEvent(list, verifyType, TmsTransportConstant.VerifyStatusEnum.NO_VERIFY.getCode(), requestType.getVerifyEventType(), Boolean.FALSE);
        //过滤不同验证类型，但司机车辆ID相同的数据
        for(Iterator<TmsVerifyEventPO> iterator = eventPOList.iterator();iterator.hasNext();){
            TmsVerifyEventPO verifyEventPO = iterator.next();
            Long verifySourceId = sourceTypeMap.get(verifyEventPO.getVerifySourceType());
            if(!Objects.equals(verifySourceId,verifyEventPO.getVerifySourceId())){
                iterator.remove();
            }
        }
        //判断如果验证开始时间大于当前时间，说明不需要认证
        Boolean verifyFlag = Boolean.FALSE;
        List<Long> timeOutIdList = Lists.newArrayList();
        for(TmsVerifyEventPO eventPO : eventPOList){
            if(!eventPO.getVerifyStartTime().before(new Date())){
                verifyFlag = Boolean.TRUE;
                timeOutIdList.add(eventPO.getId());
            }
        }
        //去除未到验证时间的事件
        if(CollectionUtils.isNotEmpty(timeOutIdList)){
            for(Iterator<TmsVerifyEventPO> iterator = eventPOList.iterator();iterator.hasNext();){
                TmsVerifyEventPO verifyEventPO = iterator.next();
                if(timeOutIdList.contains(verifyEventPO.getId())){
                    iterator.remove();
                }
            }
        }
        if(verifyFlag && CollectionUtils.isEmpty(eventPOList)){
            soadto.setVerifyFlag(TmsTransportConstant.VerifyFlagEnum.NO_VERIFY.getCode());
            return Result.Builder.<DrvIsNeedVerifySOADTO>newResult().success().withData(soadto).build();
        }
        //如果无事件,则自动生成两个事件
        if (CollectionUtils.isEmpty(eventPOList)) {
            soadto.setVerifyFlag(TmsTransportConstant.VerifyFlagEnum.NO_VERIFY.getCode());
            String verifyStartTime = DateUtil.calculateVerifyStartTime(qconfig.getVerifyStartTimeRadom());
            if(Objects.equals(requestType.getVerifyEventType(), TmsTransportConstant.VerifyEventTypeEnum.SPOT_CHECK_EVENT.getCode())){
                String eventId = eventCommandService.insertCheckVerifyEvent(requestType,sourceIdMap, verifyStartTime);
                if (StringUtils.isNotEmpty(eventId) && DateUtil.stringToDate(verifyStartTime,DateUtil.YYYYMMDDHHMMSS).before(new Date())) {
                    soadto.setVerifyEventId(eventId);
                }
            }
            return Result.Builder.<DrvIsNeedVerifySOADTO>newResult().success().withData(soadto).build();
        }
        //如果返回多个相当事件类型事件，则只取一条
        this.sameEventRemoveduplicate(eventPOList);
        //需要人车认证(人车都未验证过)
        if (eventPOList.size() == 2) {
            eventPOList =  eventPOList.stream().sorted(Comparator.comparing(TmsVerifyEventPO::getVerifyType)).collect(Collectors.toList());
            soadto.setVerifyFlag(TmsTransportConstant.VerifyFlagEnum.FACE_VEHICLE_VERIFY.getCode());
            soadto.setVerifyEventId(getEventId(eventPOList));
            String eventOutTime = getVerifyTimeOut(eventPOList);
            String noiceTimes = getNoticeTimes(eventPOList);
            soadto.setFaceVerifyTimeOut(eventOutTime.split("_")[0]);
            soadto.setVehicleVerifyTimeOut(eventOutTime.split("_")[1]);
            soadto.setFaceVerifyNoticeCount(Integer.parseInt(noiceTimes.split("_")[0]));
            soadto.setVerifyNoticeCount(Integer.parseInt(noiceTimes.split("_")[1]));
            soadto.setVehicleVerifyNoticeCount(Integer.parseInt(noiceTimes.split("_")[1]));
            return Result.Builder.<DrvIsNeedVerifySOADTO>newResult().success().withData(soadto).build();
        }
        if (eventPOList.size() == 1) {
            TmsVerifyEventPO tmsVerifyEventPO = eventPOList.get(0);
            Integer verifyFlaag = TmsTransportConstant.VerifyFlagEnum.FACE_VERIFY.getCode();
            String verifyTimeOut = DateUtil.timestampToString(tmsVerifyEventPO.getVerifyEndTime(), DateUtil.YYYYMMDDHHMMSS);
            Integer noticeTimes = tmsVerifyEventPO.getNoticeTimes();
            if (Objects.equals(tmsVerifyEventPO.getVerifyType(), TmsTransportConstant.VerifyTypeEnum.VEHICLE.getCode())) {
                verifyFlaag = TmsTransportConstant.VerifyFlagEnum.VEHICLE_VERIFY.getCode();
                soadto.setVehicleVerifyTimeOut(verifyTimeOut);
                soadto.setVerifyNoticeCount(noticeTimes);
                soadto.setVehicleVerifyNoticeCount(noticeTimes);
            } else {
                soadto.setFaceVerifyTimeOut(verifyTimeOut);
                soadto.setFaceVerifyNoticeCount(noticeTimes);
            }
            soadto.setVerifyFlag(verifyFlaag);
            soadto.setVerifyEventId(String.valueOf(tmsVerifyEventPO.getId()));
            return Result.Builder.<DrvIsNeedVerifySOADTO>newResult().success().withData(soadto).build();
        }
        return Result.Builder.<DrvIsNeedVerifySOADTO>newResult().success().withData(soadto).build();
    }

    @Override
    public Result<List<QueryDrvVerifyEventListSOADTO>> queryDrvVerifyEventList(QueryDrvVerifyEventListSOARequestType requestType) {
        DrvDriverPO drvDriverPO = drvDrvierRepository.queryByPk(requestType.getDriverId());
        List<QueryDrvVerifyEventListSOADTO> resultDTO = Lists.newArrayList();
        if (Objects.isNull(drvDriverPO)) {
            return Result.Builder.<List<QueryDrvVerifyEventListSOADTO>>newResult().success().withData(resultDTO).build();
        }
        Long drvId = drvDriverPO.getDrvId();
        Long vehicleId = drvDriverPO.getVehicleId();
        List<Long> list = Lists.newArrayList();
        Map<Integer, Long> sourceTypeMap = Maps.newConcurrentMap();
        sourceTypeMap.put(TmsTransportConstant.VerifyTypeEnum.FACE.getCode(), drvId);
        list.add(drvId);
        if (vehicleId != null && vehicleId > 0) {
            list.add(vehicleId);
            sourceTypeMap.put(TmsTransportConstant.VerifyTypeEnum.VEHICLE.getCode(), vehicleId);
        }
        List<TmsVerifyEventPO> eventPOList = eventRepository.queryWaitVerifyEvent(list, null, TmsTransportConstant.VerifyStatusEnum.NO_VERIFY.getCode(), TmsTransportConstant.VerifyEventTypeEnum.SPOT_CHECK_EVENT.getCode(), false);
        if (CollectionUtils.isEmpty(eventPOList)) {
            return Result.Builder.<List<QueryDrvVerifyEventListSOADTO>>newResult().success().withData(resultDTO).build();
        }

        //过滤不同验证类型，但司机车辆ID相同的数据
        for(Iterator<TmsVerifyEventPO> iterator = eventPOList.iterator();iterator.hasNext();){
            TmsVerifyEventPO verifyEventPO = iterator.next();
            Long verifySourceId = sourceTypeMap.get(verifyEventPO.getVerifySourceType());
            if(!Objects.equals(verifySourceId,verifyEventPO.getVerifySourceId())){
                iterator.remove();
            }
        }

        for (TmsVerifyEventPO eventPO : eventPOList) {
            QueryDrvVerifyEventListSOADTO eventListSOADTO = new QueryDrvVerifyEventListSOADTO();
            eventListSOADTO.setDriverId(requestType.getDriverId());
            eventListSOADTO.setDriverName(drvDriverPO.getDrvName());
            eventListSOADTO.setVerifyEventId(eventPO.getId());
            eventListSOADTO.setVerifyType(eventPO.getVerifyType());
            eventListSOADTO.setVerifyCheckTime(DateUtil.timestampToString(eventPO.getVerifyStartTime(), DateUtil.YYYYMMDDHHMMSS));
            resultDTO.add(eventListSOADTO);
        }

        return Result.Builder.<List<QueryDrvVerifyEventListSOADTO>>newResult().success().withData(resultDTO).build();
    }

    /**
     * 判断人脸事件是否已达上限
     *
     * @param verifySourceId
     * @return
     */
    @Override
    public Boolean judgeVerifyEventTimes(Long verifySourceId, TmsTransportConstant.VerifyTypeEnum verifyTypeEnum, Integer verifyEventType) {
        List<TmsVerifyEventPO> eventPOList = eventRepository.queryVerifyedEventBySourceIdOneMonth(verifySourceId, verifyTypeEnum.getCode(), verifyEventType);

        //FIXME 验证时间配置
        Date nowDate = DateUtil.dateToDate(new Date(), DateUtil.YYYYMMDD);
        List<TmsVerifyEventPO> evifyList  = Lists.newArrayList();
        for(TmsVerifyEventPO tmsVerifyEventPO : eventPOList){
            Date verifyStartDate = DateUtil.stringToDate(DateUtil.timestampToString(tmsVerifyEventPO.getVerifyStartTime(),DateUtil.YYYYMMDD),DateUtil.YYYYMMDD);
            Date configDate = DateUtil.dayDisplacement(verifyStartDate,this.getVerifyDayCOnfigValue(verifyTypeEnum));
            Boolean isVerified = Boolean.FALSE;
            if(nowDate!=null &&  verifyStartDate!=null && configDate!=null && nowDate.getTime()>= verifyStartDate.getTime() && nowDate.getTime()<= configDate.getTime()){
                evifyList.add(tmsVerifyEventPO);
                isVerified = Boolean.TRUE;
            }
            if(isVerified){continue;}
            Date verifyDate = DateUtil.stringToDate(DateUtil.timestampToString(tmsVerifyEventPO.getVerifyTime(), DateUtil.YYYYMMDD), DateUtil.YYYYMMDD);
            Date configEndDate = DateUtil.dayDisplacement(verifyDate,this.getVerifyDayCOnfigValue(verifyTypeEnum));
            if(nowDate!=null && verifyDate!=null && configEndDate!=null && nowDate.getTime()>=verifyDate.getTime() && nowDate.getTime()<=configEndDate.getTime()) {
                evifyList.add(tmsVerifyEventPO);
            }
        }
        if (evifyList.size() >= getVerifyTimes(verifyTypeEnum)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public Result<Boolean> drvFaceVerifyCountTransfinite(Long drvId) {
        //人脸验证 当天失败次数超20次（腾讯有返回失败结果的20次）若网络异常未提交或腾讯未返回结果的失败次数不包含在内，
        // 提示司机：当日失败次数已超限，请明日重试。 失败次数可配置
        Boolean flag = Boolean.FALSE;
        String failCount = provider.get(FACE_VERIFY_FAIL_COUNT_KEY + drvId);
        if(StringUtils.isNotEmpty(failCount) && Integer.parseInt(failCount) > qconfig.getFaceVerifyFailCount()){
            flag = Boolean.TRUE;
        }
        return Result.Builder.<Boolean>newResult().success().withData(flag).build();
    }

    public Integer getVerifyTimes(TmsTransportConstant.VerifyTypeEnum verifyTypeEnum){
         switch (verifyTypeEnum){
            case FACE:
                return qconfig.getVerifyTimes();
            case VEHICLE:
                return qconfig.getVehicleVerifyTimes();
        }
        return 0;
    }

    public Integer getVerifyDayCOnfigValue(TmsTransportConstant.VerifyTypeEnum verifyTypeEnum){
        return qconfig.getVerifyDayThresholdMap().get(verifyTypeEnum.getCode());
    }

    /**
     * 相同事件去复
     * @param eventPOList
     */
    public void sameEventRemoveduplicate(List<TmsVerifyEventPO> eventPOList){
        Set<Integer> eventTypeSet = Sets.newHashSet();
        for(Iterator<TmsVerifyEventPO> iterator = eventPOList.iterator();iterator.hasNext();){
            TmsVerifyEventPO tmsVerifyEventPO = iterator.next();
            if(eventTypeSet.contains(tmsVerifyEventPO.getVerifyType())){
                iterator.remove();
            }
            eventTypeSet.add(tmsVerifyEventPO.getVerifyType());
        }

    }

    public String getEventId(List<TmsVerifyEventPO> eventPOList) {
        StringBuilder eventId = new StringBuilder();
        eventId.append(eventPOList.get(0).getId());
        eventId.append("_").append(eventPOList.get(1).getId());
        return eventId.toString();
    }

    public String getVerifyTimeOut(List<TmsVerifyEventPO> eventPOList) {
        StringBuilder verifyTimeOut = new StringBuilder();
        String faceVerifyOutTime = DateUtil.timestampToString(eventPOList.get(0).getVerifyEndTime(), DateUtil.YYYYMMDDHHMMSS);
        verifyTimeOut.append(faceVerifyOutTime);
        String vehicleVerifyOutTime = DateUtil.timestampToString(eventPOList.get(1).getVerifyEndTime(), DateUtil.YYYYMMDDHHMMSS);
        verifyTimeOut.append("_").append(vehicleVerifyOutTime);
        return verifyTimeOut.toString();
    }

    public String getNoticeTimes(List<TmsVerifyEventPO> eventPOList) {
        StringBuilder verifyTimeOut = new StringBuilder();
        verifyTimeOut.append(eventPOList.get(0).getNoticeTimes());
        verifyTimeOut.append("_").append(eventPOList.get(1).getNoticeTimes());
        return verifyTimeOut.toString();
    }

    public Long insertVerifyRecord(Long verifySourceId, TmsTransportConstant.VerifyTypeEnum verifyTypeEnum,String requestContent,String responseContent,String verifyResultCode,String verifyFailReason){
        try {
            TmsVerifyRecordPO tmsVerifyRecordPO = new TmsVerifyRecordPO();
            tmsVerifyRecordPO.setVerifySourceId(verifySourceId);
            tmsVerifyRecordPO.setVerifyType(verifyTypeEnum.getCode());
            tmsVerifyRecordPO.setRequestContent(requestContent);
            tmsVerifyRecordPO.setResponseContent(responseContent);
            tmsVerifyRecordPO.setVerifyResultCode(verifyResultCode);
            tmsVerifyRecordPO.setVerifyFailReason(verifyFailReason);
            tmsVerifyRecordPO.setDatachangeCreatetime(DateUtil.getNow());
            tmsVerifyRecordPO.setDatachangeLasttime(DateUtil.getNow());
            tmsVerifyRecordPO.setCreateUser(TmsTransportConstant.TMS_DEFAULT_USERNAME);
            tmsVerifyRecordPO.setModifyUser(TmsTransportConstant.TMS_DEFAULT_USERNAME);
            return tmsVerifyRecordRepository.insert(tmsVerifyRecordPO);
        }catch (Exception e){
            return 0L;
        }
    }

    /**
     * 哪些错误码 验证失败后 再认证 换头像 认证，可配置 -1=全部都换成头像认证
     * @param verifySourceId
     * @return
     */
    public Boolean judgeVerifyFailReplaceHead(Long verifySourceId){
        //配置为空,关闭头像认证
        if(CollectionUtils.isEmpty(qconfig.getVerifyResultCodeList())){
            return Boolean.FALSE;
        }
        //-1=全量换头像认证
        if(qconfig.getVerifyResultCodeList().contains("-1")){
            return Boolean.TRUE;
        }
        List<TmsVerifyEventPO> eventPOList = eventRepository.queryWaitVerifyEvent(Arrays.asList(verifySourceId), TmsTransportConstant.VerifyTypeEnum.FACE.getCode(),TmsTransportConstant.VerifyStatusEnum.NO_VERIFY.getCode(), null, false);
        if(CollectionUtils.isEmpty(eventPOList)){
            return Boolean.FALSE;
        }

        //哪些错误码 验证失败后 再认证 换头像 认证，可配置 -1=全部都换成头像认证
        for (TmsVerifyEventPO verifyEventPO : eventPOList) {
            if (qconfig.getVerifyResultCodeList().contains(verifyEventPO.getVerifyResultCode())) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;

    }

    public Boolean verifyFailCountCumulative(Long driverId){
        String key = FACE_VERIFY_FAIL_COUNT_KEY + driverId;
        DLock lock = distributedLockConfig.getDistributedLockVerifyDrv(driverId);
        boolean locked = false;
        try {
            if (lock != null && (locked = lock.tryLock(DistributedLockConfig.PROCESSING_TIME, TimeUnit.SECONDS))) {
                String failCount = provider.get(key);
                if(StringUtils.isEmpty(failCount)){
                    provider.setex(key, RedisUtils.ONE_DAY, "1");
                }else {
                    CachePipeline cachePipeline = provider.getPipeline();
                    cachePipeline.incr(key);
                    cachePipeline.sync();
                }
            }
            return Boolean.TRUE;
        }catch (Exception e){
            logger.error("verifyFailCountCumulative","verifyFailCountCumulative Error_" + key);
            return Boolean.FALSE;
        }finally {
            if (locked) {
                lock.unlock();
            }
        }
    }
}
