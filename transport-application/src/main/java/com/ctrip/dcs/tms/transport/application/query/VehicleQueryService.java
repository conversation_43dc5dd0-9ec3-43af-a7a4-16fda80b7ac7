package com.ctrip.dcs.tms.transport.application.query;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.igt.framework.common.base.PageHolder;
import com.ctrip.igt.framework.common.result.*;

import java.util.*;

/**
 * 车辆查询类接口
 * <AUTHOR>
 * @Date 2020/3/17 15:04
 */
public interface VehicleQueryService {

    Result<Boolean> isVehicleLicenseUniqueness(Long vehicleId, String vehicleLicense);

    Result<VehicleDetailDTO> queryVehicleDetail(Long vehicleId);

    Result<Integer> queryVehicleCount(QueryVehicleSOARequestType queryVehicleRequestType);

    Result<List<VehicleListSOADTO>> queryVehicleList(QueryVehicleSOARequestType queryVehicleRequestType);

    /**
     * 查询车辆缓存集合
     * @param vehIdSet
     * @return
     */
    List<VehCacheDTO> queryVehCacheList(Set<Long> vehIdSet);

    /**
     * 查询车辆准入年限
     * @param
     * @return
     */
    Result<Integer> queryVehicleAccessYear(Long cityId,Long vehicleTypeId);

    /**
     * 根据车票号查询车辆信息
     * @param vehicleNo
     * @return
     */
    Long queryVehicleId(String vehicleNo);

    /**
     * 从司机所在的供应商下随机选择一辆合规车辆
     * @param supplierId
     * @return
     */
    VehCacheDTO queryRandomVehicle(Long supplierId,Long cityId,boolean conformanceFlag);

    /**
     * 根据车辆id查询车辆信息
     * @param vehicleId
     * @return
     */
    SimpleVehicleInfoDTO querySimpleVehicleInfo(Long vehicleId);

    /**
    　* @description: 查询车辆信息识别状态
    　* <AUTHOR>
    　* @date 2023/9/1 9:52
    */
    Result<QueryVehRecognitionStatusResponseType> queryVehRecognitionStatus(String vehicleLicense);

    /**
     * 已废弃车辆列表
     * @param drvDO
     * @return
     */
    Result<PageHolder<VehicleListSOADTO>> queryDiscardVehList(QueryDiscardVehicleListSOARequestType requestType);

}
