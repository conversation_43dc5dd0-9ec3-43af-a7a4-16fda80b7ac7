package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.platform.dal.dao.annotation.*;
import com.ctriposs.baiji.exception.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * <AUTHOR>
 * @Description  证件审核接口
 * @Date 14:42 2020/9/27
 * @Param 
 * @return 
 **/
@Service("certificateCheckCommandService")
public class CertificateCheckCommandServiceImpl implements CertificateCheckCommandService {

    private static final Logger logger = LoggerFactory.getLogger(CertificateCheckCommandServiceImpl.class);

    @Autowired
    private TmsCertificateCheckRepository checkRepository;
    @Autowired
    private TmsBackgroundChecksRepository backgroundChecksRepository;

    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    @Override
    public Long insertCheckRecordDB(Long checkId, Integer checkType, Integer certificateType,String checkKeyword, String content, List<CertificateCheckSOAInfo> checkInfo, Integer checkStatus,Integer thirdCheckStatus, String userName) {
        try{
            logger.info("insertCheckRecordDB ,params:checkId:{},checkType:{}",checkId,checkType);
            //将第三方审核记录存表
            TmsCertificateCheckPO records = new TmsCertificateCheckPO();
            records.setCheckId(checkId);
            records.setCheckType(checkType);
            records.setCertificateType(certificateType);
            records.setCreateUser(userName);
            records.setCheckContent(content);
            records.setCheckStatus(checkStatus);
            records.setCheckResult(CollectionUtils.isEmpty(checkInfo)?"":JsonUtil.toJson(checkInfo));
            records.setCheckKeyword(checkKeyword);
            records.setThirdCheckStatus(thirdCheckStatus);
            return  checkRepository.insertTmsCertificateCheck(records);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    @Override
    public Result<Boolean> insertBackGroundCheck(List<DriverBackgroundChecksSOADTO> checksDTOList) {
        try{
            if(CollectionUtils.isEmpty(checksDTOList)){
                return Result.Builder.<Boolean>newResult().success().build();
            }

            List<TmsBackgroundChecksPO> checksPOList = Lists.newArrayList();
            checksDTOList.forEach(checksSOADTO -> {
                TmsBackgroundChecksPO checksPO = new TmsBackgroundChecksPO();
                BeanUtils.copyProperties(checksSOADTO,checksPO);
                checksPO.setPersonId(TmsTransUtil.encrypt(checksSOADTO.getPersonId(), KeyType.Identity_Card));
                checksPO.setCreateUser(TmsTransportConstant.TMS_DEFAULT_USERNAME);
                checksPO.setModifyUser(TmsTransportConstant.TMS_DEFAULT_USERNAME);
                checksPO.setDatachangeCreatetime(DateUtil.getNow());
                checksPOList.add(checksPO);
            });
            backgroundChecksRepository.batchInsertCheckRecord(checksPOList);
            return Result.Builder.<Boolean>newResult().success().build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Result<Boolean> insertPropertyAddTag(PropertyAddTagSOARequestType requestType) {
        if (CollectionUtils.isEmpty(requestType.getCheckIdList()) || CollectionUtils.isEmpty(requestType.getCertificateType()) || requestType.getCheckType() == null) {
            return Result.Builder.<Boolean>newResult().fail().build();
        }
        try {
            List<TmsCertificateCheckPO> addData  = Lists.newArrayList();
            for (Long checkId : requestType.getCheckIdList()) {
                for (Integer certificateType : requestType.getCertificateType()) {
                    TmsCertificateCheckPO records = new TmsCertificateCheckPO();
                    records.setCheckId(checkId);
                    records.setCheckType(requestType.getCheckType());
                    records.setCertificateType(certificateType);
                    records.setCreateUser(StringUtils.isEmpty(requestType.getModifyUser())?TmsTransportConstant.TMS_DEFAULT_USERNAME:requestType.getModifyUser());
                    records.setModifyUser(StringUtils.isEmpty(requestType.getModifyUser())?TmsTransportConstant.TMS_DEFAULT_USERNAME:requestType.getModifyUser());
                    records.setCheckStatus(requestType.getCheckStatus());
                    records.setThirdCheckStatus(requestType.getCheckStatus());
                    addData.add(records);
                }
            }
            if(CollectionUtils.isEmpty(addData)){
                return Result.Builder.<Boolean>newResult().fail().build();
            }
            checkRepository.batchInsertCheckRecord(addData);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
        return Result.Builder.<Boolean>newResult().success().build();
    }

    @Override
    public Result<Boolean> updatePropertyTag(PropertyUpdateTagSOARequestType requestType) {
        if (CollectionUtils.isEmpty(requestType.getCheckIdList()) || requestType.getCertificateType() == null || requestType.getCheckType() == null) {
            return Result.Builder.<Boolean>newResult().fail().build();
        }
        try {
            checkRepository.updateCertificateStatus(requestType.getCheckIdList(),requestType.getCheckType(),requestType.getCertificateType(),requestType.getCheckStatus(),requestType.getModifyUser());
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
        return Result.Builder.<Boolean>newResult().success().build();
    }
}
