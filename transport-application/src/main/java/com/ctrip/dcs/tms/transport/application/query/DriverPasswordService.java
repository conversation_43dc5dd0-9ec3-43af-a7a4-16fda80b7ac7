package com.ctrip.dcs.tms.transport.application.query;

/**
 * <AUTHOR>
 * @since 2020/9/21 17:14
 */
public interface DriverPasswordService {

  /**
   * 验证密码有效性
   *
   * @param password
   * @return
   */
  boolean isPasswordValid(String password);

  /**
   * 验证司机密码一致性
   *
   * @param pwd
   * @param encPwd
   * @param hashSalt
   * @return
   */
  boolean isPasswordEqual(String pwd, String encPwd, String hashSalt);
  
  /**
   * 生成密码salt
   *
   * @return
   */
  String genPwdSalt();
  
  /**
   * 加密密码
   *
   * @param pwd
   * @param hashSalt
   * @return
   */
  String encryptPwd(String pwd, String hashSalt);
  
  /**
   * 生成重置密码
   *
   * @return
   */
  String genResetPwd();
}
