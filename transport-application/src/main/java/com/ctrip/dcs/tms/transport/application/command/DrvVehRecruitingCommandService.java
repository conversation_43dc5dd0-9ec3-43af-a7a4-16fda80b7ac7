package com.ctrip.dcs.tms.transport.application.command;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.igt.framework.common.result.*;

import java.util.*;

/**
* 司机车辆审批类接口
* <AUTHOR>
* @date 2020/4/20 15:16
*/
public interface DrvVehRecruitingCommandService {

    /**
     * 添加司机车辆审批
     * @param requestType
     * @return
     */
    Result<Boolean> addDrvVehRecruiting(DrvVehRecruitingAddSOARequestType requestType);

    /**
     * 编辑司机车辆审批
     * @param requestType
     * @return
     */
    Result<Boolean> updateDrvVehRecruiting(DrvVehRecruitingUpdateSOARequestType requestType);

    /**
     * 招募核酸标签
     * */
    void toDoNucleicAcidLabel(Long drvRecruitingId, Long cityId, String name, String idNumber, String nucleicAcidTestingTime, String ocrData);

    /**
     * 招募疫苗标签
     * */
    void toDoVaccineLabel(Long drvRecruitingId, String name, String idNumber, List<String> vaccinationTimeList, String ocrData);

    /**
     * 招募合规标签
     * */
    void toDoOcrHeadPortraitLabel(Long drvRecruitingId, boolean checkResult);

    /**
     * 合规头像标签
     * */
    void toDoComplianceHeadPortraitLabel(Long drvRecruitingId);

    /**
     * H5编辑招募信息
     * @param requestType
     * @return
     */
    Result<Boolean> updateDrvVehRecruitingFromH5(DrvVehRecruitingUpdateFromH5RequestType requestType);


    /**
     * 招募核酸标签
     * */
    Map<String,Object> toDoNucleicAcidLabelMap(Long drvRecruitingId, Long cityId, String name, String idNumber, String nucleicAcidTestingTime, String ocrData);

    Map<String,Object> toDoVaccineLabelMap(Long drvRecruitingId, String name, String idNumber, List<String> vaccinationTimeList, String ocrData,Map<String,Object> nucleicAcidMap);

    Result<Boolean> discardRecruitingDrv(Long recruitingId, List<String> permissionCodes, String modifyUser);

    Result<Boolean> discardRecruitingVeh(Long recruitingId,List<String> permissionCodes, String modifyUser);
}
