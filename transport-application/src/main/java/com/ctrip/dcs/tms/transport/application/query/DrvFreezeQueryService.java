package com.ctrip.dcs.tms.transport.application.query;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.igt.framework.common.result.*;

import java.util.*;

/**
 * 司机查询类接口
 * <AUTHOR>
 * @Date 2020/3/17 14:58
 */
public interface DrvFreezeQueryService {

    /**
     * 司机冻结详情
     * @param drvId
     * @return
     */
    Result<QueryDrvFreezeDetailDTOSOA> queryDrvFreezeDetail(Long drvId);

    /**
     * 校验供应商司机冻结权限
     * @param drvId
     * @return
     */
    Result<Boolean> checkSupplierFreezePerm(Long drvId,Integer accountType);

    /**
     * 查询满足冻结条件的司机
     * @param drvIds
     * @return
     */
    Result<List<QueryDoDrvFreezeSOADTO>> queryDoDrvFreeze(List<Long> drvIds,Integer freezeFrom);

    /**
     * 查询冻结基础信息
     */
    List<DrvFreezeInfoSOADTO> queryDrvFreezeInfoList(Set<Long> drvIdSet);

}
