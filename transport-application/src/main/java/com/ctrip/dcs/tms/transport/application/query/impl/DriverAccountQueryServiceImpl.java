package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.infrastructureservice.executor.contract.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2020/9/21 11:51
 */
@Component
public class DriverAccountQueryServiceImpl implements DriverAccountQueryService {

  private static final Logger logger = LoggerFactory.getLogger(DriverAccountQueryServiceImpl.class);

  private final static Result<DrvDriverPO> RESULT_OF_DRIVER_NOT_EXIST;
  private final static Result<DrvDriverPO> RESULT_OF_DRIVER_NOT_UNIQUE;
  private final static Result<DrvDriverPO> RESULT_OF_PWD_NOT_PASS;
  private final static Result<DrvDriverPO> RESULT_OF_PHONE_CODE_NOT_PASS;
  private static final Result<DrvDriverPO> RESULT_OF_PWD_IS_EMPTY;
  
  static {
    RESULT_OF_DRIVER_NOT_EXIST = Result.Builder.<DrvDriverPO>newResult().fail().withCode("404").build();
    RESULT_OF_DRIVER_NOT_UNIQUE = Result.Builder.<DrvDriverPO>newResult().fail().withCode("402").build();
    RESULT_OF_PWD_NOT_PASS = Result.Builder.<DrvDriverPO>newResult().fail().withCode("403").build();
    RESULT_OF_PHONE_CODE_NOT_PASS = Result.Builder.<DrvDriverPO>newResult().fail().withCode("401").build();
    RESULT_OF_PWD_IS_EMPTY = Result.Builder.<DrvDriverPO>newResult().fail().withCode("400").build();
  }

  @Autowired
  private DrvDrvierRepository repository;
  @Autowired
  private InfrastructureServiceClientProxy proxy;
  @Autowired
  private VerificationCodeQconfig config;
  @Autowired
  private DriverPasswordService passwordService;

  @Resource
  private DriverQueryService driverQueryService;

  @Override
  public Result<DrvDriverPO> login(String loginAccount, String loginPwd, String loginAreaCode, LoginType loginType) {
    if (loginType == LoginType.PHONE_CODE) {
      return loginByPhoneCode(loginAccount, loginAreaCode, loginPwd);
    }

    return loginByAccountPwd(loginAccount, loginPwd);
  }

  public Result<DrvDriverPO> loginByPhoneCode(String mobilePhone, String areaCode, String phoneCode) {
    QueryDrvDO drvQuery = new QueryDrvDO();
    drvQuery.setDrvPhone(TmsTransUtil.encrypt(mobilePhone, KeyType.Phone));
    drvQuery.setIgtCode(areaCode);
    drvQuery.setPage(1);
    drvQuery.setSize(2);
    List<DrvDriverPO> drivers = repository.queryDrvList(drvQuery);

    if (CollectionUtils.isEmpty(drivers)) {
//      return RESULT_OF_DRIVER_NOT_EXIST;
      return CtripCommonUtils.resultDrvAccountErrorInfo("404");
    }
    if (drivers.size() > 1) {
//      return RESULT_OF_DRIVER_NOT_UNIQUE;
      return CtripCommonUtils.resultDrvAccountErrorInfo("402");
    }
    if (!isPhoneCodePass(mobilePhone, areaCode, phoneCode)) {
//      return RESULT_OF_PHONE_CODE_NOT_PASS;
      return CtripCommonUtils.resultDrvAccountErrorInfo("401");
    }

    return Result.Builder.<DrvDriverPO>newResult().success().withData(drivers.get(0)).build();
  }

  public Result<DrvDriverPO> loginByAccountPwd(String hybridAccount, String loginPwd) {

    List<DrvDriverPO> drivers = driverQueryService.queryDrvDriverListByAccount(hybridAccount);

    if (CollectionUtils.isEmpty(drivers)) {
//      return RESULT_OF_DRIVER_NOT_EXIST;
      return CtripCommonUtils.resultDrvAccountErrorInfo("404");
    }
    if (drivers.size() > 1) {
//      return RESULT_OF_DRIVER_NOT_UNIQUE;
      return CtripCommonUtils.resultDrvAccountErrorInfo("402");
    }
    DrvDriverPO driver = drivers.get(0);
    if (StringUtils.isEmpty(driver.getLoginPwd())) {
//      return RESULT_OF_PWD_IS_EMPTY;
      return CtripCommonUtils.resultDrvAccountErrorInfo("400");
    }
    if (!passwordService.isPasswordEqual(loginPwd, driver.getLoginPwd(), driver.getSalt())) {
//      return RESULT_OF_PWD_NOT_PASS;
      return CtripCommonUtils.resultDrvAccountErrorInfo("403");
    }
    return Result.Builder.<DrvDriverPO>newResult().success().withData(driver).withCode("").build();
  }

  private boolean isPhoneCodePass(String mobilePhone, String areaCode, String phoneCode) {
    CheckMobilePhoneCodeRequestType requestType = new CheckMobilePhoneCodeRequestType();
    requestType.setCode(phoneCode);
    requestType.setMobilePhone(mobilePhone);
    requestType.setCountryCode(areaCode);
    requestType.setChannel(config.getChannel(VertificationCodeScene.VERIFICATION_LOGIN));
    requestType.setMessageCode(config.getMessageCode(VertificationCodeScene.VERIFICATION_LOGIN));

    CheckMobilePhoneCodeResponseType responseType = proxy.checkPhoneCode(requestType);
    return responseType != null && responseType.getResponseResult() != null && responseType.getResponseResult().isSuccess();
  }
}
