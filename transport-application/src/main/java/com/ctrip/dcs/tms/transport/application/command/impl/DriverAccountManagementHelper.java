package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.dcs.tms.transport.application.command.TmsQmqProducerCommandService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverDomainServiceProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DriverAccountRegisterResultDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TransportCommonQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.SharkUtils;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.result.Result;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.ErrorCodeEnum.TRANSPORT_DRIVER_UPDATE_ACCOUNT_FAILED;

@Component
public class DriverAccountManagementHelper {

  private static final Logger logger = LoggerFactory.getLogger(DriverAccountManagementHelper.class);


  @Autowired
  DriverDomainServiceProxy driverDomainServiceProxy;

  @Autowired
  private TmsQmqProducerCommandService tmsQmqProducerCommandService;

  @Autowired
  TransportCommonQconfig commonQconfig;

  /**
   * 注册司机用户账号
   * @param drvDriverPO
   * @return
   */
  public DriverAccountRegisterResultDTO registerDriverUserAccount(DrvDriverPO drvDriverPO) {
    if (StringUtils.isNotBlank(drvDriverPO.getUid())) {
      return new DriverAccountRegisterResultDTO(drvDriverPO.getDrvId(), null, false, null, null,false);
    }

    // 熔断开关
    DriverAccountRegisterResultDTO resultDTO  = driverDomainServiceProxy.registerNewAccount(drvDriverPO);
    if (StringUtils.isBlank(resultDTO.getUid())) {
      Cat.logEvent("driver_register_account_failed", drvDriverPO.getDrvId() + "|" + drvDriverPO.getDrvPhone() + "|" + drvDriverPO.getEmail() + "|" + resultDTO.getErrorCode() + "|" + resultDTO.getErrorMsg());
      logger.info("driver_register_account","register account failed, driverId:{}, phone:{}, email:{}", drvDriverPO.getDrvId(), drvDriverPO.getDrvPhone(), drvDriverPO.getEmail());
    }

    return resultDTO;
  }


  public void sendDriverInfoToDriverAccount(DrvDriverPO drvDriverPO) {
    if (!isNeedCallDriverDomain(drvDriverPO)) {
      return;
    }

    tmsQmqProducerCommandService.sendPhoneEmailModifyQMQ(drvDriverPO);
  }

  public boolean isNeedCallDriverDomain(DrvDriverPO drvDriverPO) {
    // 熔断开关
    if (Objects.equals("OFF", commonQconfig.getRegisterDriverAccountSwitch())) {
      logger.info("driver_update_account_info","switch closed");
      return false;
    }

    if (StringUtils.isEmpty(drvDriverPO.getUid())) {
      logger.info("driver_update_account_info","Call DriverDomain, do nothing driverId:{}, cityId:{}", drvDriverPO.getDrvId(), drvDriverPO.getCityId());
      return true;
    }
    logger.info("driver_update_account_info","Not in gray process, do nothing driverId:{}, cityId:{}", drvDriverPO.getDrvId(), drvDriverPO.getCityId());
    return false;
  }

  public boolean isInCityGrayProcess(DrvDriverPO drvDriverPO) {
    // 熔断开关
    if (Objects.equals("OFF", commonQconfig.getRegisterDriverAccountSwitch())) {
      logger.info("driver_update_account_info","switch closed");
      return false;
    }

    if (driverDomainServiceProxy.queryAccountProcess(drvDriverPO.getCityId(), drvDriverPO.getDrvId())) {
      return true;
    }
    logger.info("driver_update_account_info","Not in gray process, do nothing driverId:{}, cityId:{}", drvDriverPO.getDrvId(), drvDriverPO.getCityId());
    return false;
  }

  public List<DriverAccountRegisterResultDTO> batchRegisterDriverAccountWhenNotExist(List<DrvDriverPO> driverPOList) {
    List<DriverAccountRegisterResultDTO> resultList = Lists.newArrayList();
   for (DrvDriverPO drvDriverPO : driverPOList) {
     resultList.add(registerDriverUserAccount(drvDriverPO));
   }
   return resultList;
  }

  public void updateAccount(DrvDriverPO drvDriverPO) {
    Result<Void> result = driverDomainServiceProxy.updateAccount(drvDriverPO);
    if (!result.isSuccess()) {
      throw new BizException(TRANSPORT_DRIVER_UPDATE_ACCOUNT_FAILED.getCode(), SharkUtils.getSharkValue(TRANSPORT_DRIVER_UPDATE_ACCOUNT_FAILED.getMessage() + "." + result.getCode(), result.getMsg())  +":" + result.getMsg());
    }
  }
}
