package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.result.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 司机安全相关服务
 *
 * <AUTHOR>
 */
@Service
public class DrvSafetyServiceImpl implements DrvSafetyService {

    @Autowired
    private DrvDrvierRepository drvierRepository;

    @Autowired
    private EpidemicPreventionControlQconfig epidemicPreventionControlQconfig;

    @Autowired
    private DrvEpidemicPreventionControlInfoRepository drvEpidemicPreventionControlInfoRepository;

    @Override
    public Result<Boolean> queryUploadReportStatus(Long drvId) {
        DrvDriverPO driverPO = drvierRepository.queryByPk(drvId);
        if (driverPO == null) {
            return Result.Builder.<Boolean>newResult().fail().withData(false).build();
        }
        EpidemicPreventionControlCityInfoDTO cityInfoDTO = epidemicPreventionControlQconfig.getEpidemicPreventionControlCityMap(driverPO.getCityId());
        if (cityInfoDTO.getReportConstraintModel().intValue() == EpidemicPreventionControlEnum.AskReportStatusEnum.NOT_NEED.getCode().intValue()) {
            return Result.Builder.<Boolean>newResult().success().withData(false).build();
        }
        DrvEpidemicPreventionControlInfoPO infoPO = drvEpidemicPreventionControlInfoRepository.queryByDrvId(driverPO.getDrvId());
        if (infoPO == null) {
            return Result.Builder.<Boolean>newResult().fail().withData(false).build();
        }
        if (cityInfoDTO.getReportConstraintModel().intValue() == EpidemicPreventionControlEnum.AskReportStatusEnum.NEED_NUCLEIC_ACID_REPORT.getCode().intValue()) {
            if (checkEffectiveReportStatus(infoPO.getNucleicAcidReportStatus().intValue())) {
                return Result.Builder.<Boolean>newResult().success().withData(true).build();
            } else {
                return Result.Builder.<Boolean>newResult().fail().withData(false).build();
            }
        }
        if (cityInfoDTO.getReportConstraintModel().intValue() == EpidemicPreventionControlEnum.AskReportStatusEnum.NEED_VACCINE_REPORT.getCode().intValue()) {
            if (checkEffectiveReportStatus(infoPO.getVaccineReportStatus().intValue())) {
                return Result.Builder.<Boolean>newResult().success().withData(true).build();
            } else {
                return Result.Builder.<Boolean>newResult().fail().withData(false).build();
            }
        }
        if (cityInfoDTO.getReportConstraintModel().intValue() == EpidemicPreventionControlEnum.AskReportStatusEnum.SATISFY_ONE.getCode().intValue()) {
            if (checkEffectiveReportStatus(infoPO.getVaccineReportStatus().intValue()) || checkEffectiveReportStatus(infoPO.getNucleicAcidReportStatus().intValue())) {
                return Result.Builder.<Boolean>newResult().success().withData(true).build();
            } else {
                return Result.Builder.<Boolean>newResult().fail().withData(false).build();
            }
        }
        if (cityInfoDTO.getReportConstraintModel().intValue() == EpidemicPreventionControlEnum.AskReportStatusEnum.ALL_NEED.getCode().intValue()) {
            if (checkEffectiveReportStatus(infoPO.getVaccineReportStatus().intValue()) && checkEffectiveReportStatus(infoPO.getNucleicAcidReportStatus().intValue())) {
                return Result.Builder.<Boolean>newResult().success().withData(true).build();
            } else {
                return Result.Builder.<Boolean>newResult().fail().withData(false).build();
            }
        }
        return Result.Builder.<Boolean>newResult().fail().withData(false).build();
    }

    private boolean checkEffectiveReportStatus(int reportStatus) {
        return reportStatus == TmsTransportConstant.CheckStatusEnum.THROUGH.getCode().intValue() || reportStatus == TmsTransportConstant.CheckStatusEnum.REVIEW.getCode().intValue();
    }
}
