package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.dto.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.concurrent.threadpool.CThreadPool;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.platform.dal.dao.annotation.*;
import com.ctriposs.baiji.exception.*;
import com.dianping.cat.Cat;
import com.fasterxml.jackson.core.type.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.lang.reflect.*;
import java.sql.*;
import java.util.Date;
import java.util.*;


@Service
public class CertificateCheckQueryServiceImpl implements CertificateCheckQueryService {

    private static final Logger logger = LoggerFactory.getLogger(CertificateCheckQueryServiceImpl.class);
    @Autowired
    TmsConnectServiceClientProxy connectServiceClientProxy;
    @Autowired
    CommonCommandService commonCommandService;
    @Autowired
    TmsTransportQconfig qconfig;
    @Autowired
    CertificateCheckCommandService checkCommandService;
    @Autowired
    TmsCertificateCheckRepository checkRepository;
    @Autowired
    private TmsQmqProducerCommandService tmsQmqProducerCommandService;
    @Autowired
    private ChangeRecordAttributeNameQconfig recordAttributeNameQconfig;
    @Autowired
    DriverQueryService driverQueryService;
    @Autowired
    TmsRecruitingApproveStepRepository stepRepository;
    @Autowired
    TmsModRecordCommandService recordCommandService;

    @Override
    public Result<Boolean> asyncDrvCertificateCheck(DrvAuditDTO drvAuditDTO,Boolean operationFlag) {
        try{
            CThreadPool.pool(TransportThreadGroupConstant.certificateFutureThreadPoolName).execute(new Runnable() {
                @Override
                public void run() {
                    drvCertificateCheck(drvAuditDTO,operationFlag);
                }
            });
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
        return null;
    }

    @Override
    public Result<Boolean> asyncVehicleCertificateCheck(VehicleAuditDTO vehicleAuditDTO,Boolean operationFlag) {
        try {
            CThreadPool.pool(TransportThreadGroupConstant.certificateFutureThreadPoolName).execute(new Runnable() {
                @Override
                public void run() {
                    vehCertificateCheck(vehicleAuditDTO,operationFlag);
                }
            });
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
        return null;
    }

    @Override
    public Result<Boolean> drvCertificateCheck(DrvAuditDTO drvAuditDTO,Boolean operationFlag) {
        logger.info("drv check,params:{},operationFlag:{}",JsonUtil.toJson(drvAuditDTO),operationFlag);
        try {
            //身份证
            if(imgEquals(drvAuditDTO.getOrgIdcardImg(),drvAuditDTO.getNewIdcardImg(),drvAuditDTO.getDrvIdCard(),drvAuditDTO.getNewDrvIdCard(),operationFlag)){
                if(!judgmentIdCardIsThrough(TmsTransUtil.encrypt(drvAuditDTO.getDrvIdCard(), KeyType.Identity_Card),drvAuditDTO.getCheckId(),drvAuditDTO.getUserName(), TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV)){
                    this.idCardCheck(drvAuditDTO);
                }
            }
            //驾驶证，新增true 编辑false
            if(imgEquals(drvAuditDTO.getOrgDrvcardImg(),drvAuditDTO.getNewDrvcardImg(),drvAuditDTO.getDrvIdCard(),drvAuditDTO.getNewDrvIdCard(),operationFlag)){
                this.isCheckDrvLicense(drvAuditDTO);
            }

            if(qconfig.getNetVehicleCheckSwitch() && imgEquals(drvAuditDTO.getOrgNetVehiclePeoImg(),drvAuditDTO.getNewNetVehiclePeoImg(),drvAuditDTO.getDrvIdCard(),drvAuditDTO.getNewDrvIdCard(),operationFlag)){
                //网约车驾驶证
                this.netVehicleheck(drvAuditDTO.getCheckId(), TmsTransportConstant.CertificateTypeEnum.NETDRVCTFCT,drvAuditDTO.getDrvIdCard(),drvAuditDTO.getUserName(),drvAuditDTO.getCheckType());
            }
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
        return Result.Builder.<Boolean>newResult().success().build();
    }

    @Override
    public Result<Boolean> vehCertificateCheck(VehicleAuditDTO vehicleAuditDTO,Boolean operationFlag) {
        logger.info("vehicle check,params:{},operationFlag:{}",JsonUtil.toJson(vehicleAuditDTO),operationFlag);
        try{
            //行驶证
            if(imgEquals(vehicleAuditDTO.getOriginVehicleCertiImg(),vehicleAuditDTO.getTarVehicleCertiImg(),vehicleAuditDTO.getVehicleLicense(),vehicleAuditDTO.getNewVehicleLicense(),operationFlag)){
                this.isCheckVehicleLicense(vehicleAuditDTO);
            }
            //网约车行驶证
            if(qconfig.getNetVehicleCheckSwitch() && imgEquals(vehicleAuditDTO.getOriginNetTansCtfctUrl(),vehicleAuditDTO.getTarNetTansCtfctUrl(),vehicleAuditDTO.getVehicleLicense(),vehicleAuditDTO.getNewVehicleLicense(),operationFlag)){
                this.netVehicleheck(vehicleAuditDTO.getCheckId(),TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT,vehicleAuditDTO.getVehicleLicense(),vehicleAuditDTO.getUserName(),vehicleAuditDTO.getCheckType());
            }
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
        return Result.Builder.<Boolean>newResult().success().build();
    }

    @Override
    public Map<Integer, TmsCertificateCheckPO> queryCertificateCheckToMap(Long checkId, Integer checkType) {
        Map<Integer, TmsCertificateCheckPO> checkPOMap = Maps.newHashMap();
        if(qconfig.getQueryCertificateNewVersionSwitch()){
            return queryCertificateCheckToMap2(checkId,checkType);
        }
        List<TmsCertificateCheckPO> checkPOList = checkRepository.queryCertificateByCheckId(checkId,checkType);
        if(CollectionUtils.isEmpty(checkPOList)){
            return checkPOMap;
        }
        for(TmsCertificateCheckPO checkPO : checkPOList){
            checkPOMap.put(checkPO.getCertificateType(),checkPO);
        }
        return checkPOMap;
    }

    @Override
    public Boolean refreshDrvLicenseCheckIng(DrvAuditDTO auditDTO) {
        logger.info("schedule drvLicense ,params:" + JsonUtil.toJson(auditDTO));
        List<CertificateCheckSOAInfo> resultList = Lists.newArrayList();
        //获取第三方驾驶证核验数据
        Result<QueryDrvCardInfoSOAResponseType> drvCheckResponse = commonCommandService.queryDrvCardInfo(auditDTO.getDrvName(),auditDTO.getDrvIdCard());
        logger.info("【drv check】,The third party result :{}" , JsonUtil.toJson(auditDTO));
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.CHECKING.getCode();
        //如果是系统本身问题,定义为审批中，需跑定时刷新核验信息
        if(this.judgeSOAResult(drvCheckResponse)){
            return checkStatusErrorFlag(checkStatus);
        }
        Map<String,Object> rMap = this.getDrvLicenseCheckResult(auditDTO,drvCheckResponse.getData());
        checkStatus = (Integer) rMap.get(TmsTransportConstant.CHECKSTATUS);
        resultList = (List<CertificateCheckSOAInfo>) rMap.get(TmsTransportConstant.RESULTINFO_KEY);
        //核验结果更新入库
        TmsCertificateCheckPO certificateCheckPO = buildUpdateCheckPO(auditDTO.getId(),JsonUtil.toJson(drvCheckResponse.getData()),CollectionUtils.isEmpty(resultList)?"":JsonUtil.toJson(resultList),checkStatus,auditDTO.getCheckType(),auditDTO.getVersionFlag(),auditDTO.getCheckStatus());
        checkRepository.batchUpdateCheckRecord(Arrays.asList(certificateCheckPO));
        //添加三方标签状态记录
        recordCommandService.insertThirdCertificateRrd(auditDTO.getCheckId(),TmsTransportConstant.RecruitingTypeEnum.drv.getCode(),auditDTO.getCheckId(), TmsTransportConstant.CertificateTypeEnum.DRIVERLICENSE.getCode(),auditDTO.getCheckStatus(),certificateCheckPO.getCheckStatus(),TmsTransportConstant.TMS_DEFAULT_USERNAME);
        return checkStatusErrorFlag(checkStatus);
    }


    @Override
    public Boolean refreshVehLicenseCheckIng(VehicleAuditDTO vehicleAuditDTO) {
        logger.info("schedule vehicle license,params:" + JsonUtil.toJson(vehicleAuditDTO));
        Map<String,Object> resultMap = Maps.newHashMap();
        List<CertificateCheckSOAInfo> resultList = Lists.newArrayList();
        String drvLicenseStatusResult = "";
        //获取第三方行驶证核验数据
        Result<QueryVehicleCardInfoSOAResponseType> soaResponseTypeResult = commonCommandService.queryVehicleCardInfo(vehicleAuditDTO.getVehicleLicense(),vehicleAuditDTO.getVehicleLicenseType());
        logger.info("vehicle check,The third party result:{}" , JsonUtil.toJson(vehicleAuditDTO));
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
        if(this.judgeSOAResult(soaResponseTypeResult)){
            return Boolean.FALSE;
        }
        Map<String,Object> rMap = getVehicleLicenseCheckResult(vehicleAuditDTO,soaResponseTypeResult.getData());
        checkStatus = (Integer) rMap.get(TmsTransportConstant.CHECKSTATUS);
        resultList = (List<CertificateCheckSOAInfo>) rMap.get(TmsTransportConstant.RESULTINFO_KEY);
        //核验结果更新入库
        TmsCertificateCheckPO certificateCheckPO = buildUpdateCheckPO(vehicleAuditDTO.getId(),JsonUtil.toJson(soaResponseTypeResult.getData()),CollectionUtils.isEmpty(resultList)?"":JsonUtil.toJson(resultList),checkStatus,vehicleAuditDTO.getCheckType(),vehicleAuditDTO.getVersionFlag(),vehicleAuditDTO.getCheckStatus());
        checkRepository.batchUpdateCheckRecord(Arrays.asList(certificateCheckPO));
        //添加三方标签状态记录
        recordCommandService.insertThirdCertificateRrd(vehicleAuditDTO.getRecruitingId(),vehicleAuditDTO.getRecruitingType(),vehicleAuditDTO.getCheckId(), TmsTransportConstant.CertificateTypeEnum.CARCERTILICENSE.getCode(),vehicleAuditDTO.getCheckStatus(),certificateCheckPO.getCheckStatus(),TmsTransportConstant.TMS_DEFAULT_USERNAME);
        return Boolean.TRUE;
    }

    @Override
    public Boolean refreshNetDrvLicenseCheckIng(Long id,String idCard,Integer checkType,Long supplierId,Integer versionFlag,Integer childCheckStatus,Long checkId) {
        //网约驾驾驶证核验
        String drvLicenseStatusResult = "";
        List<CertificateCheckSOAInfo> resultList = Lists.newArrayList();
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
        Boolean flag = Boolean.TRUE;
        Result<QueryNetDrvCardInfoSOAResponseType> soaResponseTypeResult = commonCommandService.queryNetDrvCardInfo(idCard);
        if(this.judgeSOAResult(soaResponseTypeResult)){
            checkStatus = TmsTransportConstant.CheckStatusEnum.CHECKING.getCode();
        }
        QueryNetDrvCardInfoSOAResponseType netDrvCardInfoSOAResponseType = soaResponseTypeResult.getData();
        String thirdCode = netDrvCardInfoSOAResponseType.getThirdCode();
        drvLicenseStatusResult = this.drvResultByEnumCode(thirdCode);
        if(StringUtils.isEmpty(drvLicenseStatusResult) && netDrvCardInfoSOAResponseType.getData() != null){
            NetCardInfoSOADTO netCardInfoSOADTO = netDrvCardInfoSOAResponseType.getData();
            drvLicenseStatusResult = netCardInfoSOADTO.getMessage();
            checkStatus = getNetVehicleCheckStatus(netCardInfoSOADTO);
        }
        resultList.add(buildBean(TmsTransportConstant.NetDrvLicenseStatusColKey,this.getResultValue(SharkKeyConstant.netVehicleStatus),TmsTransportConstant.NetDrvLicenseStatusResultKey,drvLicenseStatusResult));
        //核验结果更新入库
        TmsCertificateCheckPO certificateCheckPO =  buildUpdateCheckPO(id,JsonUtil.toJson(soaResponseTypeResult),CollectionUtils.isEmpty(resultList)?"":JsonUtil.toJson(resultList),checkStatus,checkType,versionFlag,childCheckStatus);
        checkRepository.batchUpdateCheckRecord(Arrays.asList(certificateCheckPO));
        //添加三方标签状态记录
        recordCommandService.insertThirdCertificateRrd(checkId,TmsTransportConstant.RecruitingTypeEnum.drv.getCode(),checkId, TmsTransportConstant.CertificateTypeEnum.NETDRVCTFCT.getCode(),childCheckStatus,certificateCheckPO.getCheckStatus(),TmsTransportConstant.TMS_DEFAULT_USERNAME);
        return checkStatusErrorFlag(checkStatus);
    }

    @Override
    public Boolean refreshNetDrvLicenseCheckIng(RefreshNetDrvLicenseCheckIngParamDTO checkIngParamDTO) {
        //封装查询参数
        QueryNetDrvCardInfoParamDTO paramDTO = new QueryNetDrvCardInfoParamDTO();
        paramDTO.setDrvCardNo(checkIngParamDTO.getIdCard());
        paramDTO.setDriverName(checkIngParamDTO.getDriverName());
        paramDTO.setCityId(String.valueOf(checkIngParamDTO.getCityId()));
        //查询网约车驾驶证合规信息
        Result<QueryNetCertResultDTO> queryNetCertResultDTOResult = commonCommandService.queryNetDrvCardInfo(paramDTO);
        //封装更新数据
        TmsCertificateCheckPO tmsCertificateCheckPO = getUpdateTmsCertificateCheckPO(queryNetCertResultDTOResult,checkIngParamDTO.getId());
        //核验结果更新入库
        checkRepository.batchUpdateCheckRecord(Arrays.asList(tmsCertificateCheckPO));
        //添加三方标签状态记录
        recordCommandService.insertThirdCertificateRrd(checkIngParamDTO.getCheckId(),TmsTransportConstant.RecruitingTypeEnum.drv.getCode(),checkIngParamDTO.getCheckId(), TmsTransportConstant.CertificateTypeEnum.NETDRVCTFCT.getCode(),checkIngParamDTO.getCheckStatus(),tmsCertificateCheckPO.getCheckStatus(),TmsTransportConstant.TMS_DEFAULT_USERNAME);
        return checkStatusErrorFlag(tmsCertificateCheckPO.getCheckStatus());
    }

    /**
     * 封装更新数据
     * @param queryNetCertResultDTOResult
     * @param checkPkId
     * @return
     */
    private TmsCertificateCheckPO getUpdateTmsCertificateCheckPO(Result<QueryNetCertResultDTO> queryNetCertResultDTOResult,Long checkPkId){
        TmsCertificateCheckPO tmsCertificateCheckPO = new TmsCertificateCheckPO();
        //校验结果为合规
        if(queryNetCertResultDTOResult.isSuccess() && "00".equals(queryNetCertResultDTOResult.getData().getResponseCode())){
            //合规
            tmsCertificateCheckPO.setThirdCheckStatus(TmsTransportConstant.CheckStatusEnum.THROUGH.getCode());
            tmsCertificateCheckPO.setCheckStatus(TmsTransportConstant.CheckStatusEnum.THROUGH.getCode());
        }else if(queryNetCertResultDTOResult.isSuccess() && "01".equals(queryNetCertResultDTOResult.getData().getResponseCode())){
            //不合规
            tmsCertificateCheckPO.setThirdCheckStatus(TmsTransportConstant.CheckStatusEnum.ERROR.getCode());
            tmsCertificateCheckPO.setCheckStatus(TmsTransportConstant.CheckStatusEnum.ERROR.getCode());
        }else if(queryNetCertResultDTOResult.isSuccess() && "02".equals(queryNetCertResultDTOResult.getData().getResponseCode())){
            //复核
            tmsCertificateCheckPO.setThirdCheckStatus(TmsTransportConstant.CheckStatusEnum.REVIEW.getCode());
            tmsCertificateCheckPO.setCheckStatus(TmsTransportConstant.CheckStatusEnum.REVIEW.getCode());
        }else{
            //异常 or 未知 or 开关关闭 or其他
            tmsCertificateCheckPO.setThirdCheckStatus(TmsTransportConstant.CheckStatusEnum.CHECKING.getCode());
            tmsCertificateCheckPO.setCheckStatus(TmsTransportConstant.CheckStatusEnum.CHECKING.getCode());
        }
        //封装证件核验结果
        tmsCertificateCheckPO.setId(checkPkId);
        //29747返回的结果
        tmsCertificateCheckPO.setCheckContent(JsonUtil.toJson(queryNetCertResultDTOResult));
        //第三方返回的结果
        List<CertificateCheckSOAInfo> resultList = Lists.newArrayList();
        String netDrvLicenseStatusResult = queryNetCertResultDTOResult.getData()==null?"":queryNetCertResultDTOResult.getData().getThirdResponseMsg();
        resultList.add(buildBean(TmsTransportConstant.NetDrvLicenseStatusColKey,this.getResultValue(SharkKeyConstant.netVehicleStatus),TmsTransportConstant.NetDrvLicenseStatusResultKey,netDrvLicenseStatusResult));
        tmsCertificateCheckPO.setCheckResult(JsonUtil.toJson(resultList));
        return tmsCertificateCheckPO;
    }
    @Override
    public Boolean refreshNetVehLicenseCheckIng(Long id,String vehicleLicense,Integer checkType,Long supplierId,Integer versionFlag,Integer childCheckStatus,Long checkId,Long recruitingId,Integer reruitingType) {
        String drvLicenseStatusResult = "";
        List<CertificateCheckSOAInfo> resultList = Lists.newArrayList();
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
        Result<QueryNetVehicleCardInfoSOAResponseType> soaResponseTypeResult = commonCommandService.queryNetVehicleCardInfo(vehicleLicense);
        if(this.judgeSOAResult(soaResponseTypeResult)){
            checkStatus = TmsTransportConstant.CheckStatusEnum.CHECKING.getCode();
        }
        QueryNetVehicleCardInfoSOAResponseType netDrvCardInfoSOAResponseType = soaResponseTypeResult.getData();
        String thirdCode = netDrvCardInfoSOAResponseType.getThirdCode();
        drvLicenseStatusResult = this.drvResultByEnumCode(thirdCode);
        if(StringUtils.isEmpty(drvLicenseStatusResult) && netDrvCardInfoSOAResponseType.getData() != null){
            NetCardInfoSOADTO netCardInfoSOADTO = netDrvCardInfoSOAResponseType.getData();
            drvLicenseStatusResult = netCardInfoSOADTO.getMessage();
            checkStatus = getNetVehicleCheckStatus(netCardInfoSOADTO);
        }
        resultList.add(buildBean(TmsTransportConstant.NetDrvLicenseStatusColKey,this.getResultValue(SharkKeyConstant.netVehicleStatus),TmsTransportConstant.NetDrvLicenseStatusResultKey,drvLicenseStatusResult));
        //核验结果更新入库
        TmsCertificateCheckPO certificateCheckPO = buildUpdateCheckPO(id,JsonUtil.toJson(soaResponseTypeResult),CollectionUtils.isEmpty(resultList)?"":JsonUtil.toJson(resultList),checkStatus,checkType,versionFlag,childCheckStatus);
        checkRepository.batchUpdateCheckRecord(Arrays.asList(certificateCheckPO));
        //添加三方标签状态记录
        recordCommandService.insertThirdCertificateRrd(recruitingId,reruitingType,checkId, TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT.getCode(),childCheckStatus,certificateCheckPO.getCheckStatus(),TmsTransportConstant.TMS_DEFAULT_USERNAME);
        return checkStatusErrorFlag(checkStatus);
    }

    @Override
    public Boolean refreshNetVehLicenseCheckIng(RefreshNetVehLicenseCheckIngParamDTO paramDTO) {
        //封装查询参数 查询证件合规信息
        QueryNetVehicleCardInfoParamDTO netVehicleCardInfoParamDTO = new QueryNetVehicleCardInfoParamDTO();
        netVehicleCardInfoParamDTO.setVehicleLicense(paramDTO.getVehicleLicense());
        netVehicleCardInfoParamDTO.setCityId(paramDTO.getCityId().toString());
        //查询证件合规信息
        Result<QueryNetCertResultDTO> queryNetCertResultDTOResult = commonCommandService.queryNetVehicleCardInfo(netVehicleCardInfoParamDTO);
        //封装更新数据
        TmsCertificateCheckPO tmsCertificateCheckPO = getUpdateTmsCertificateCheckPO(queryNetCertResultDTOResult,paramDTO.getId());
        //更新数据库数据
        checkRepository.batchUpdateCheckRecord(Arrays.asList(tmsCertificateCheckPO));
        //添加三方标签状态记录
        recordCommandService.insertThirdCertificateRrd(paramDTO.getRecruitingId(),paramDTO.getRecruitingType(),paramDTO.getCheckId(), TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT.getCode(),paramDTO.getCheckStatus(),tmsCertificateCheckPO.getCheckStatus(),TmsTransportConstant.TMS_DEFAULT_USERNAME);
        return checkStatusErrorFlag(tmsCertificateCheckPO.getCheckStatus());
    }

    @Override
    public Boolean refreshIdCardCheckIng(TmsCertificateCheckPO checkPO, Map<String,TmsBackgroundChecksPO>  checksPOMap) {
        if (StringUtils.isEmpty(checkPO.getCheckKeyword())) {
            return Boolean.FALSE;
        }
        TmsBackgroundChecksPO tmsBackgroundChecksPO = checksPOMap.get(checkPO.getCheckKeyword());
        if(Objects.isNull(tmsBackgroundChecksPO)){
            return Boolean.FALSE;
        }
        //判断核验状态
        if(qconfig.getCaseTypeList().contains(tmsBackgroundChecksPO.getCaseType())){
            checkPO.setCheckStatus(TmsTransportConstant.CheckStatusEnum.ERROR.getCode());
        }else{
            checkPO.setCheckStatus(TmsTransportConstant.CheckStatusEnum.THROUGH.getCode());
        }
        //结果入核验信息表
        checkRepository.batchUpdateCheckRecord(Arrays.asList(checkPO));
        return Boolean.TRUE;
    }

    @Override
    public Boolean refreshAllDrvCertificateCheck(DrvAuditDTO drvAuditDTO) {
        try {
            //身份证
            if(qconfig.getRefreshDrvidcardSwitch()){
                this.idCardCheck(drvAuditDTO);
            }
            if(qconfig.getRefreshDrvLicenseSwitch()){
                //驾驶证
                this.isCheckDrvLicense(drvAuditDTO);
            }
            if(qconfig.getRefreshDrvNetLicenseSwitch()){
                //网约车驾驶证
                this.netDrvLicenseCertificateCheck(drvAuditDTO.getCheckId(),drvAuditDTO.getDrvIdCard(),drvAuditDTO.getUserName(),drvAuditDTO.getCheckType());
            }
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean refreshAllVehicleCertificateCheck(VehicleAuditDTO vehicleAuditDTO) {
        try {
            //行驶证
            if(qconfig.getRefreshVehicleLicenseSwitch()){
                this.isCheckVehicleLicense(vehicleAuditDTO);
            }
            //网约车行驶证
            if(qconfig.getRefreshVehicleNetLicenseSwitch()){
                this.netVehicleLicenseCertificateCheck(vehicleAuditDTO.getCheckId(),vehicleAuditDTO.getCheckType(),vehicleAuditDTO.getVehicleLicense(),vehicleAuditDTO.getUserName());
            }
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
        return Boolean.TRUE;
    }

    @Override
    public List<TmsCertificateCheckPO> approveDrvCertificateCheck(DrvAuditDTO drvAuditDTO, Boolean operationFlag) {
        List<TmsCertificateCheckPO> resultCheckList = Lists.newArrayList();
        //todo 身份证编辑审核不进三方核验，后期需求变更，直接打开
//        if(!Objects.equals(drvAuditDTO.getOrgIdcardImg(),drvAuditDTO.getNewIdcardImg())){
//            TmsCertificateCheckPO checkPO = this.approveIdCard(drvAuditDTO);
//            if(!Objects.isNull(checkPO)){
//                resultCheckList.add(checkPO);
//            }
//        }
        //驾驶证
        if(imgEquals(drvAuditDTO.getOrgDrvcardImg(),drvAuditDTO.getNewDrvcardImg())){
            TmsCertificateCheckPO checkPO = this.approveDrvLicense(drvAuditDTO);
            if(!Objects.isNull(checkPO)){
                resultCheckList.add(checkPO);
            }
        }
        if(qconfig.getNetVehicleCheckSwitch() && imgEquals(drvAuditDTO.getOrgNetVehiclePeoImg(),drvAuditDTO.getNewNetVehiclePeoImg()) && StringUtils.isNotEmpty(drvAuditDTO.getNewNetVehiclePeoImg())){
            //网约车驾驶证
            TmsCertificateCheckPO checkPO = this.approveNetVehicleheck(drvAuditDTO.getCheckId(), TmsTransportConstant.CertificateTypeEnum.NETDRVCTFCT,drvAuditDTO.getNewDrvIdCard(),drvAuditDTO.getCheckType(),drvAuditDTO.getUserName());
            if(!Objects.isNull(checkPO)){
                resultCheckList.add(checkPO);
            }
        }
        return resultCheckList;
    }

    @Override
    public List<TmsCertificateCheckPO> approveVehicleCertificateCheck(VehicleAuditDTO vehicleAuditDTO, Boolean operationFlag) {
        //行驶证
        List<TmsCertificateCheckPO> resultCheckList = Lists.newArrayList();
        if(imgEquals(vehicleAuditDTO.getOriginVehicleCertiImg(),vehicleAuditDTO.getTarVehicleCertiImg())){
            TmsCertificateCheckPO checkPO = this.approveVehicleLicense(vehicleAuditDTO);
            if(!Objects.isNull(checkPO)){
                resultCheckList.add(checkPO);
            }
        }
        //网约车行驶证
        if(qconfig.getNetVehicleCheckSwitch() && imgEquals(vehicleAuditDTO.getOriginNetTansCtfctUrl(),vehicleAuditDTO.getTarNetTansCtfctUrl()) && StringUtils.isNotEmpty(vehicleAuditDTO.getTarNetTansCtfctUrl())){
            TmsCertificateCheckPO checkPO = this.approveNetVehicleheck(vehicleAuditDTO.getCheckId(), TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT,vehicleAuditDTO.getNewVehicleLicense(),vehicleAuditDTO.getCheckType(),vehicleAuditDTO.getUserName());
            if(!Objects.isNull(checkPO)){
                resultCheckList.add(checkPO);
            }
        }
        return resultCheckList;
    }

    @Override
    public TmsCertificateCheckPO refreshApproveDrvLicenseCheckIng(DrvAuditDTO auditDTO) {
        logger.info("schedule drvLicense ,params:" + JsonUtil.toJson(auditDTO));
        List<CertificateCheckSOAInfo> resultList = Lists.newArrayList();
        //获取第三方驾驶证核验数据
        Result<QueryDrvCardInfoSOAResponseType> drvCheckResponse = commonCommandService.queryDrvCardInfo(auditDTO.getDrvName(),auditDTO.getDrvIdCard());
        logger.info("【drv check】,The third party result :{}" , JsonUtil.toJson(auditDTO));
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
        //如果是系统本身问题,定义为审批中，需跑定时刷新核验信息
        if(this.judgeSOAResult(drvCheckResponse)){
            return null;
        }
        Map<String,Object> rMap = this.getDrvLicenseCheckResult(auditDTO,drvCheckResponse.getData());
        checkStatus = (Integer) rMap.get(TmsTransportConstant.CHECKSTATUS);
        resultList = (List<CertificateCheckSOAInfo>) rMap.get(TmsTransportConstant.RESULTINFO_KEY);
        return buildTmsCheckPO(JsonUtil.toJson(drvCheckResponse),auditDTO.getDrvIdCard(), CollectionUtils.isEmpty(resultList)?"":JsonUtil.toJson(resultList),checkStatus);
    }

    @Override
    public TmsCertificateCheckPO refreshApproveVehLicenseCheckIng(VehicleAuditDTO vehicleAuditDTO) {
        logger.info("schedule vehicle license,params:" + JsonUtil.toJson(vehicleAuditDTO));
        List<CertificateCheckSOAInfo> resultList = Lists.newArrayList();
        //获取第三方行驶证核验数据
        Result<QueryVehicleCardInfoSOAResponseType> soaResponseTypeResult = commonCommandService.queryVehicleCardInfo(vehicleAuditDTO.getVehicleLicense(),vehicleAuditDTO.getVehicleLicenseType());
        logger.info("vehicle check,The third party result:{}" , JsonUtil.toJson(vehicleAuditDTO));
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
        if(this.judgeSOAResult(soaResponseTypeResult)){
            return null;
        }
        Map<String,Object> rMap = getVehicleLicenseCheckResult(vehicleAuditDTO,soaResponseTypeResult.getData());
        checkStatus = (Integer) rMap.get(TmsTransportConstant.CHECKSTATUS);
        resultList = (List<CertificateCheckSOAInfo>) rMap.get(TmsTransportConstant.RESULTINFO_KEY);
        return buildTmsCheckPO(JsonUtil.toJson(soaResponseTypeResult),vehicleAuditDTO.getVehicleLicense(), CollectionUtils.isEmpty(resultList)?"":JsonUtil.toJson(resultList),checkStatus);
    }

    @Override
    public TmsCertificateCheckPO refreshApproveNetDrvLicenseCheckIng(String idCard) {
        //网约驾驾驶证核验
        List<CertificateCheckSOAInfo> resultList = Lists.newArrayList();
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
        //查询网约车驾驶证合规结果
        Result<QueryNetDrvCardInfoSOAResponseType> soaResponseTypeResult = commonCommandService.queryNetDrvCardInfo(idCard);
        //判断是否需要重新查询 需要重新查审核状态为审核中
        if(this.judgeSOAResult(soaResponseTypeResult)){
            checkStatus = TmsTransportConstant.CheckStatusEnum.CHECKING.getCode();
        }
        //从查询结果中解析返回信息
        QueryNetDrvCardInfoSOAResponseType netDrvCardInfoSOAResponseType = soaResponseTypeResult.getData();
        String thirdCode = netDrvCardInfoSOAResponseType.getThirdCode();
        //根据29747查询返回状态码封装证件状态结果
        String drvLicenseStatusResult = this.drvResultByEnumCode(thirdCode);
        //根据第三方查询结果封装封装证件状态结果&审核状态信息
        if(StringUtils.isEmpty(drvLicenseStatusResult) && netDrvCardInfoSOAResponseType.getData() != null){
            NetCardInfoSOADTO netCardInfoSOADTO = netDrvCardInfoSOAResponseType.getData();
            //装证件状态结果
            drvLicenseStatusResult = netCardInfoSOADTO.getMessage();
            //审核状态信息
            checkStatus = getNetVehicleCheckStatus(netCardInfoSOADTO);
        }
        //封装证件核验结果
        resultList.add(buildBean(TmsTransportConstant.NetDrvLicenseStatusColKey,this.getResultValue(SharkKeyConstant.netVehicleStatus),TmsTransportConstant.NetDrvLicenseStatusResultKey,drvLicenseStatusResult));
        return buildTmsCheckPO(JsonUtil.toJson(soaResponseTypeResult),idCard, CollectionUtils.isEmpty(resultList)?"":JsonUtil.toJson(resultList),checkStatus);
    }

    @Override
    public TmsCertificateCheckPO refreshApproveNetVehLicenseCheckIng(String vehicleLicense) {
        List<CertificateCheckSOAInfo> resultList = Lists.newArrayList();
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
        //查询网约车运输证合规信息
        Result<QueryNetVehicleCardInfoSOAResponseType> soaResponseTypeResult = commonCommandService.queryNetVehicleCardInfo(vehicleLicense);
        //判断是否需要重新请求查询 需要重新查询审核状态为 审核中
        if(this.judgeSOAResult(soaResponseTypeResult)){
            checkStatus = TmsTransportConstant.CheckStatusEnum.CHECKING.getCode();
        }
        //从查询结果中解析返回信息
        QueryNetVehicleCardInfoSOAResponseType netDrvCardInfoSOAResponseType = soaResponseTypeResult.getData();
        String thirdCode = netDrvCardInfoSOAResponseType.getThirdCode();
        //根据29747查询返回状态码封装证件状态结果
        String drvLicenseStatusResult = this.drvResultByEnumCode(thirdCode);
        //根据第三方查询结果封装封装证件状态结果&审核状态信息
        if(StringUtils.isEmpty(drvLicenseStatusResult) && netDrvCardInfoSOAResponseType.getData() != null){
            NetCardInfoSOADTO netCardInfoSOADTO = netDrvCardInfoSOAResponseType.getData();
            //装证件状态结果
            drvLicenseStatusResult = netCardInfoSOADTO.getMessage();
            //审核状态信息
            checkStatus = getNetVehicleCheckStatus(netCardInfoSOADTO);
        }
        //封装证件核验结果
        resultList.add(buildBean(TmsTransportConstant.NetDrvLicenseStatusColKey,this.getResultValue(SharkKeyConstant.netVehicleStatus),TmsTransportConstant.NetDrvLicenseStatusResultKey,drvLicenseStatusResult));
        return buildTmsCheckPO(JsonUtil.toJson(soaResponseTypeResult),vehicleLicense, CollectionUtils.isEmpty(resultList)?"":JsonUtil.toJson(resultList),checkStatus);
    }

    @Override
    public TmsCertificateCheckPO refreshApproveNetDrvLicenseCheckIng(String idCard, String driverName, String cityId) {
        QueryNetDrvCardInfoParamDTO paramDTO = new QueryNetDrvCardInfoParamDTO();
        paramDTO.setCityId(cityId);
        paramDTO.setDriverName(driverName);
        paramDTO.setDrvCardNo(idCard);
        Result<QueryNetCertResultDTO> queryNetCertResultDTOResult = commonCommandService.queryNetDrvCardInfo(paramDTO);
        TmsCertificateCheckPO  tmsCertificateCheckPO = getUpdateTmsCertificateCheckPO(queryNetCertResultDTOResult,null);
        tmsCertificateCheckPO.setCheckKeyword(idCard);
        return tmsCertificateCheckPO;
    }

    @Override
    public TmsCertificateCheckPO refreshApproveNetVehLicenseCheckIng(String vehicleLicense, String cityId) {
        QueryNetVehicleCardInfoParamDTO paramDTO = new QueryNetVehicleCardInfoParamDTO();
        paramDTO.setCityId(cityId);
        paramDTO.setVehicleLicense(vehicleLicense);
        Result<QueryNetCertResultDTO> queryNetCertResultDTOResult = commonCommandService.queryNetVehicleCardInfo(paramDTO);
        TmsCertificateCheckPO  tmsCertificateCheckPO = getUpdateTmsCertificateCheckPO(queryNetCertResultDTOResult,null);
        tmsCertificateCheckPO.setCheckKeyword(vehicleLicense);
        return tmsCertificateCheckPO;
    }

    @Override
    public Map<Long, Map<Integer, Boolean>> getCertificateCheckMap(Set<Long> materialIdList, TmsTransportConstant.CertificateCheckTypeEnum checkTypeEnum, List<Integer> certificateTypeList) {
        Map<Long, Map<Integer, Boolean>> ans = Maps.newHashMapWithExpectedSize(materialIdList.size());
        for (Long materialId : materialIdList) {
            ans.put(materialId, Maps.newHashMapWithExpectedSize(certificateTypeList.size()));
        }
        List<TmsCertificateCheckPO> checkList = checkRepository.queryCertificateCheck4DrvCache(materialIdList, checkTypeEnum.getCode(), certificateTypeList);
        if (CollectionUtils.isEmpty(checkList)) {
            return ans;
        }
        Set<String> filer = Sets.newHashSetWithExpectedSize(certificateTypeList.size() * materialIdList.size());
        for (TmsCertificateCheckPO checkPO : checkList) {
            if (filer.add(checkPO.getCheckId() + "_" + checkPO.getCertificateType())) {
                Map<Integer, Boolean> certificateCheckMap = ans.get(checkPO.getCheckId());
                certificateCheckMap.put(checkPO.getCertificateType(), Objects.equals(TmsTransportConstant.CheckStatusEnum.THROUGH.getCode(), checkPO.getCheckStatus()));
            }
        }
        return ans;
    }

    /***
    　* @description: 触发调用三方接口的逻辑
                     若证件核验的入参未发生改变、且距离上次成功调用<=90天、且上次结果!=“不通过”
                     同时命中以上3项条件，则无需重新调用三方，并保留最近一次调用结果
                     若证件核验的入参发生改变（空值→A；A→B；A→空值）/距离上次成功调用>90天/上次结果＝"不通过"
                     命中3项中任意1项条件，则需重新调用三方并将新返回的证件核验结果刷新（或保存成功后入参为空值则删除结果，此情况仅适用于该证件为非必填项）
    　* <AUTHOR>
    　* @date 2021/10/8 11:52
    */
    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Map<String,Object> drvRecruitingCertificateCheck(DrvCertificateCheckParameterDTO parameterDTO) {
        Map<String,Object> resultMap = Maps.newHashMap();
        try {
            Integer checkStatus = TmsTransportConstant.CheckStatusEnum.THROUGH.getCode();
            Boolean checkFlag = Boolean.FALSE;
            Map<Integer, TmsCertificateCheckPO> drvCheckPOMap = queryCertificateCheckToMap(parameterDTO.getCheckId(), TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV.getCode());
            //身份证
            if (judgeDrvParameterChange(parameterDTO.getDrvIdCard(), parameterDTO.getNewDrvIdCard(), parameterDTO.getDrvName(), parameterDTO.getNewDrvName(), TmsTransportConstant.CertificateTypeEnum.IDCARD, drvCheckPOMap)) {
                this.idCardNewCheck(parameterDTO);
                checkFlag = Boolean.TRUE;
            }
            //驾驶证
            if (judgeDrvParameterChange(parameterDTO.getDrvIdCard(), parameterDTO.getNewDrvIdCard(), parameterDTO.getDrvName(), parameterDTO.getNewDrvName(), TmsTransportConstant.CertificateTypeEnum.DRIVERLICENSE, drvCheckPOMap)) {
                checkFlag = Boolean.TRUE;
                checkStatus = this.drvLicenseNewCheck(parameterDTO);
                resultMap.put(TmsTransportConstant.CertificateTypeEnum.DRIVERLICENSE.getCode().toString(),checkStatus);
            }
            //网约车驾驶证
            if (qconfig.getNetVehicleCheckSwitch() && (StringUtils.isNotEmpty(parameterDTO.getNetVehiclePeoImg()) || StringUtils.isNotEmpty(parameterDTO.getNetAppealMaterials())) && (!Objects.equals(parameterDTO.getDrvIdCard(), parameterDTO.getNewDrvIdCard()) || triggerDrvCheckConditions(TmsTransportConstant.CertificateTypeEnum.NETDRVCTFCT, drvCheckPOMap))) {
                checkFlag = Boolean.TRUE;
                checkStatus = this.netVehicleheck(parameterDTO.getCheckId(), TmsTransportConstant.CertificateTypeEnum.NETDRVCTFCT, parameterDTO.getNewDrvIdCard(), parameterDTO.getModifyUser(), parameterDTO.getCheckType());
            }
            resultMap.put(TmsTransportConstant.REQUEST_THIRD_INTERFACE_KEY,checkFlag);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
        return resultMap;
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Map<String,Object> vehRecruitingCertificateCheck(VehCertificateCheckParameterDTO parameterDTO) {
        Map<String,Object> resultMap = Maps.newHashMap();
        try{
            Integer checkStatus = TmsTransportConstant.CheckStatusEnum.THROUGH.getCode();
            Boolean checkFlag = Boolean.FALSE;
            Map<Integer, TmsCertificateCheckPO> vehCheckPOMap = queryCertificateCheckToMap(parameterDTO.getCheckId(), TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode());
            //行驶证
            if (judgeVehParameterChange(parameterDTO.getVehicleLicense(), parameterDTO.getNewVehicleLicense(), TmsTransportConstant.CertificateTypeEnum.CARCERTILICENSE, vehCheckPOMap)) {
                checkFlag = Boolean.TRUE;
                checkStatus = this.vehicleLicenseNewCheck(parameterDTO);
                resultMap.put(TmsTransportConstant.CertificateTypeEnum.CARCERTILICENSE.getCode().toString(),checkStatus);
            }
            //网约车行驶证
            if(qconfig.getNetVehicleCheckSwitch() && (StringUtils.isNotEmpty(parameterDTO.getNetAppealMaterials()) || StringUtils.isNotEmpty(parameterDTO.getNetTansCtfctImg())) && judgeVehParameterChange(parameterDTO.getVehicleLicense(), parameterDTO.getNewVehicleLicense(), TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT, vehCheckPOMap)){
                checkFlag = Boolean.TRUE;
                checkStatus = this.netVehicleheck(parameterDTO.getCheckId(),TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT,parameterDTO.getNewVehicleLicense(),parameterDTO.getModifyUser(),parameterDTO.getCheckType());
            }
            resultMap.put(TmsTransportConstant.REQUEST_THIRD_INTERFACE_KEY,checkFlag);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
        return resultMap;
    }

    @Override
    public Map<Long, Integer> getCertificateCheckNetMap(Set<Long> materialIdList, TmsTransportConstant.CertificateCheckTypeEnum checkTypeEnum, Integer certificateType) {
        Map<Long, Integer> ans = Maps.newHashMapWithExpectedSize(materialIdList.size());
        List<TmsCertificateCheckPO> checkList = checkRepository.queryCertificateCheck4DrvCache(materialIdList, checkTypeEnum.getCode(), Arrays.asList(certificateType));
        if (CollectionUtils.isEmpty(checkList)) {
            return ans;
        }
        Set<String> filer = Sets.newHashSet();
        for (TmsCertificateCheckPO checkPO : checkList) {
            if (filer.add(checkPO.getCheckId() + "_" + checkPO.getCertificateType())) {
                ans.put(checkPO.getCheckId(),checkPO.getCheckStatus());
            }
        }
        return ans;
    }

    @Override
    public boolean checkFromCityPlatform(String cityId) {
        try{
            String cityIds = qconfig.getCheckNetCertForCityPlatformCityIds();
            if(org.springframework.util.StringUtils.isEmpty(cityIds)){
                return false;
            }
            List<String> cityList = Arrays.asList(cityIds.split(","));
            if(CollectionUtils.isEmpty(cityList)){
                return false;
            }
            if(cityList.contains(cityId)){
                return true;
            }
            return false;
        }catch (Exception e){
            logger.info("checkFromCityPlatform_ex",e.getMessage());
            return false;
        }
    }

    public Integer getNetVehicleCheckStatus(NetCardInfoSOADTO netCardInfoSOADTO){
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
        if(Objects.equals("0",netCardInfoSOADTO.getState()) && Objects.equals("0",netCardInfoSOADTO.getCode())){
            checkStatus = TmsTransportConstant.CheckStatusEnum.THROUGH.getCode();
        }else if(Objects.equals("1",netCardInfoSOADTO.getState()) && Objects.equals("0",netCardInfoSOADTO.getCode())){
            checkStatus = TmsTransportConstant.CheckStatusEnum.REVIEW.getCode();
        }
        return checkStatus;
    }


    private TmsCertificateCheckPO buildUpdateCheckPO(Long id,String checkContent,String checkResult ,Integer checkStatus,Integer checkType,Integer versionFlag,Integer childCheckStatus){
        TmsCertificateCheckPO checkPO = new TmsCertificateCheckPO();
        checkPO.setId(id);
        checkPO.setCheckContent(checkContent);
        checkPO.setCheckResult(checkResult);
        if((Objects.equals(TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV.getCode(),checkType)||
                Objects.equals(TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode(),checkType)) && versionFlag >= 3){
            checkPO.setThirdCheckStatus(checkStatus);
            if(Objects.equals(childCheckStatus, TmsTransportConstant.CheckStatusEnum.CHECKING.getCode())){
                checkPO.setCheckStatus(checkStatus);
            }
        }else {
            checkPO.setCheckStatus(checkStatus);
        }
        return checkPO;

    }

    private Boolean idCardCheck(DrvAuditDTO drvAuditDTO) throws SQLException {
        if(!qconfig.getIdcardCheckSwitch()){
            return Boolean.FALSE;
        }
        DriverBackgroundCheckDriverVO driverVO = new DriverBackgroundCheckDriverVO();
        driverVO.setPersonId(TmsTransUtil.encrypt(drvAuditDTO.getDrvIdCard(), KeyType.Identity_Card));
        driverVO.setName(drvAuditDTO.getDrvName());
        driverVO.setPhoneNo(drvAuditDTO.getDrvPhone());
        tmsQmqProducerCommandService.sendIdcardBackGroundCheckQmq(Arrays.asList(driverVO));
        //核验结果入库
        Long id = checkRepository.insertTmsCertificateCheck(buildCheckPO(drvAuditDTO));
        //身份证48小时后返回核验结果
        tmsQmqProducerCommandService.sendIdcardApproveCheckQmq(id,1);
        return Boolean.FALSE;
    }

    private TmsCertificateCheckPO buildCheckPO(DrvAuditDTO drvAuditDTO){
        TmsCertificateCheckPO checkPO = new TmsCertificateCheckPO();
        BeanUtils.copyProperties(drvAuditDTO,checkPO);
        checkPO.setCheckKeyword(TmsTransUtil.encrypt(drvAuditDTO.getDrvIdCard(), KeyType.Identity_Card));
        checkPO.setCheckStatus(TmsTransportConstant.CheckStatusEnum.CHECKING.getCode());
        checkPO.setCertificateType(TmsTransportConstant.CertificateTypeEnum.IDCARD.getCode());
        return checkPO;
    }

    //区分新增和编辑，编辑时，如果val1/val2 相等，则跳过核验
    private Boolean imgEquals(String val1,String val2,String val3,String val4,Boolean operationFlag){
        if(StringUtils.isEmpty(val1)){
            val1 = null;
        }
        if(StringUtils.isEmpty(val2)){
            val2 = null;
        }
        if(StringUtils.isEmpty(val3)){
            val3 = null;
        }
        if(StringUtils.isEmpty(val4)){
            val4 = null;
        }
        if(operationFlag && !Objects.equals(val1,val2) && !Objects.equals(val3,val4)){
            return Boolean.TRUE;
        }
        if(!Objects.equals(val1,val2) && !Objects.equals(val3,val4)){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    //区分新增和编辑，编辑时，如果val1/val2 相等，则跳过核验
    private Boolean imgEquals(String val1,String val2){
        if(StringUtils.isEmpty(val1)){
            val1 = null;
        }
        if(StringUtils.isEmpty(val2)){
            val2 = null;
        }
        if(StringUtils.equals(val1,val2)){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private Boolean isCheckDrvLicense(DrvAuditDTO auditDTO){
        logger.info(" drv isCheckDrvLicense,params:" + JsonUtil.toJson(auditDTO));
        if(!qconfig.getDrvLicenseCheckSwitch()){
            return Boolean.FALSE;
        }
        Map<String,Object> resultMap = Maps.newHashMap();
        List<CertificateCheckSOAInfo> resultList = Lists.newArrayList();
        String drvLicenseStatusResult = "";
        //获取第三方驾驶证核验数据
        Result<QueryDrvCardInfoSOAResponseType> drvCheckResponse = commonCommandService.queryDrvCardInfo(auditDTO.getDrvName(),auditDTO.getDrvIdCard());
        logger.info("drv check,The third party result :{}" , JsonUtil.toJson(auditDTO));
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
        Boolean flag = Boolean.TRUE;
        //如果是系统本身问题,定义为审批中，需跑定时刷新核验信息
        if(this.judgeSOAResult(drvCheckResponse)){
            checkStatus = TmsTransportConstant.CheckStatusEnum.CHECKING.getCode();
            flag = Boolean.FALSE;
        }
        Map<String,Object> rMap = Maps.newHashMap();
        if(flag){
            rMap = getDrvLicenseCheckResult(auditDTO,drvCheckResponse.getData());
            checkStatus = (Integer) rMap.get(TmsTransportConstant.CHECKSTATUS);
            resultList = (List<CertificateCheckSOAInfo>) rMap.get(TmsTransportConstant.RESULTINFO_KEY);
        }
        //核验结果入库
        checkCommandService.insertCheckRecordDB(auditDTO.getCheckId(), auditDTO.getCheckType(), TmsTransportConstant.CertificateTypeEnum.DRIVERLICENSE.getCode(),"", JsonUtil.toJson(drvCheckResponse), resultList,checkStatus,checkStatus,auditDTO.getUserName());
        return isCheckResult(checkStatus);
    }

    public TmsCertificateCheckPO approveDrvLicense(DrvAuditDTO auditDTO){
        if(!qconfig.getDrvLicenseCheckSwitch()){
            return null;
        }
        List<CertificateCheckSOAInfo> resultList = Lists.newArrayList();
        //获取第三方驾驶证核验数据
        Result<QueryDrvCardInfoSOAResponseType> drvCheckResponse = commonCommandService.queryDrvCardInfo(auditDTO.getDrvName(),auditDTO.getNewDrvIdCard());
        logger.info("drv check,The third party result :{}" , JsonUtil.toJson(auditDTO));
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
        Boolean flag = Boolean.TRUE;
        if(this.judgeSOAResult(drvCheckResponse)){
            checkStatus = TmsTransportConstant.CheckStatusEnum.CHECKING.getCode();
            flag = Boolean.FALSE;
        }
        Map<String,Object> rMap = Maps.newHashMap();
        if(flag){
            rMap = getDrvLicenseCheckResult(auditDTO,drvCheckResponse.getData());
            checkStatus = (Integer) rMap.get(TmsTransportConstant.CHECKSTATUS);
            resultList = (List<CertificateCheckSOAInfo>) rMap.get(TmsTransportConstant.RESULTINFO_KEY);
        }
        return buildTmsCheckPO(auditDTO.getCheckId(), auditDTO.getCheckType(), TmsTransportConstant.CertificateTypeEnum.DRIVERLICENSE.getCode(), JsonUtil.toJson(drvCheckResponse),"" ,CollectionUtils.isEmpty(resultList)?"":JsonUtil.toJson(resultList),checkStatus,auditDTO.getUserName());
    }


    public TmsCertificateCheckPO approveVehicleLicense(VehicleAuditDTO vehicleAuditDTO){
        if(!qconfig.getVehicleLicenseCheckSwitch()){
            return null;
        }
        List<CertificateCheckSOAInfo> resultList = Lists.newArrayList();
        String drvLicenseStatusResult = "";
        //获取第三方行驶证核验数据
        Result<QueryVehicleCardInfoSOAResponseType> soaResponseTypeResult = commonCommandService.queryVehicleCardInfo(vehicleAuditDTO.getNewVehicleLicense(),vehicleAuditDTO.getVehicleLicenseType());
        logger.info("【vehicle check】,The third party result:{}" , JsonUtil.toJson(vehicleAuditDTO));
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
        Boolean flag = Boolean.TRUE;
        if(this.judgeSOAResult(soaResponseTypeResult)){
            flag = Boolean.FALSE;
            resultList.add(buildBean(TmsTransportConstant.VehLicenseStatusColKey,this.getResultValue(SharkKeyConstant.vehicleLicenseResultInfo),TmsTransportConstant.VehLicenseStatusResultKey,drvLicenseStatusResult));
            checkStatus = TmsTransportConstant.CheckStatusEnum.CHECKING.getCode();
        }
        if(flag){
            Map<String,Object> rMap = getVehicleLicenseCheckResult(vehicleAuditDTO,soaResponseTypeResult.getData());
            checkStatus = (Integer) rMap.get(TmsTransportConstant.CHECKSTATUS);
            resultList = (List<CertificateCheckSOAInfo>) rMap.get(TmsTransportConstant.RESULTINFO_KEY);
        }
        //核验结果入库
        return buildTmsCheckPO(vehicleAuditDTO.getCheckId(), vehicleAuditDTO.getCheckType(), TmsTransportConstant.CertificateTypeEnum.CARCERTILICENSE.getCode(), JsonUtil.toJson(soaResponseTypeResult.getData()),"" ,CollectionUtils.isEmpty(resultList)?"":JsonUtil.toJson(resultList),checkStatus,vehicleAuditDTO.getUserName());
    }

    /**
     * 网约车驾驶证核验
     * @param
     * @return
     */
    public TmsCertificateCheckPO approveNetVehicleheck(Long checkId,TmsTransportConstant.CertificateTypeEnum checkTypeEnum,String checkKeyword,Integer checkType,String modifyUser){
        //网约驾驾驶证核验
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.CHECKING.getCode();
        try {
            Boolean insertFlag = Boolean.TRUE;
            //网约车如果已核验过并且是通过，则取已有数据
            List<TmsCertificateCheckPO> checkPOList = checkRepository.queryCerCheckListByKeyWord(Arrays.asList(checkKeyword), TmsTransportConstant.CheckStatusEnum.THROUGH.getCode(),checkType,checkTypeEnum.getCode(),1,10000);
            if (!CollectionUtils.isEmpty(checkPOList)) {
                for (TmsCertificateCheckPO certificateCheckPO : checkPOList) {
                    if(StringUtils.isEmpty(certificateCheckPO.getCheckContent())){
                        continue;
                    }
                    NetCertificateResultDTO resultDTO = JsonUtil.fromJson(certificateCheckPO.getCheckContent(), new TypeReference<NetCertificateResultDTO>() {
                    });
                    if(!Objects.isNull(resultDTO) && !Objects.isNull(resultDTO.getData()) && !Objects.isNull(resultDTO.getData().getData()) && Objects.equals(resultDTO.getData().getData().getState(),"0")){
                        return buildTmsCheckPO(checkId, checkType, checkTypeEnum.getCode(), certificateCheckPO.getCheckContent(),certificateCheckPO.getCheckKeyword() ,certificateCheckPO.getCheckResult(),certificateCheckPO.getCheckStatus(),modifyUser);
                    }
                }
            }
            if(insertFlag){
                return buildTmsCheckPO(checkId, checkType, checkTypeEnum.getCode(), "",checkKeyword,"",checkStatus,modifyUser);
            }
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
        return null;
    }


    /**
     * 判断是否需要重新请求查询
     * @param soaResponseTypeResult
     * @param <T>
     * @return
     */
    private <T> Boolean judgeSOAResult(Result<T> soaResponseTypeResult){
        //结果为空则返回true
        if(soaResponseTypeResult == null || !soaResponseTypeResult.isSuccess() || soaResponseTypeResult.getData() == null){
            return Boolean.TRUE;
        }
        try {
            T data = soaResponseTypeResult.getData();
            Class clazz = data.getClass();
            Field field = clazz.getDeclaredField("thirdCode");
            field.setAccessible(Boolean.TRUE);
            Object fieldV = field.get(data);
            //状态码为空返回true
            if(Objects.isNull(fieldV)){
                return Boolean.TRUE;
            }
            //状态码为200 or -1 返回true
            if(systemDesultByEnumCode(fieldV.toString())){
                return Boolean.TRUE;
            }
        }catch (Exception e){
            //异常返回true
            return Boolean.TRUE;
        }
        //其他状态返回false
        return Boolean.FALSE;
    }

    public TmsCertificateCheckPO buildTmsCheckPO(Long checkId,Integer checkType,Integer certificateType,String checkContent,String checkKeyword,String checkResult,Integer checkStatus,String modifyUser){
        TmsCertificateCheckPO tmsCertificateCheckPO = new TmsCertificateCheckPO();
        tmsCertificateCheckPO.setCheckId(checkId);
        tmsCertificateCheckPO.setCheckType(checkType);
        tmsCertificateCheckPO.setCertificateType(certificateType);
        tmsCertificateCheckPO.setCreateUser(modifyUser);
        tmsCertificateCheckPO.setCheckContent(checkContent);
        tmsCertificateCheckPO.setCheckStatus(checkStatus);
        tmsCertificateCheckPO.setCheckKeyword(checkKeyword);
        tmsCertificateCheckPO.setCheckResult(checkResult);
        return tmsCertificateCheckPO;
    }

    public TmsCertificateCheckPO buildTmsCheckPO(String checkContent,String checkKeyword,String checkResult,Integer checkStatus){
        TmsCertificateCheckPO tmsCertificateCheckPO = new TmsCertificateCheckPO();
        tmsCertificateCheckPO.setCheckContent(checkContent);
        tmsCertificateCheckPO.setCheckStatus(checkStatus);
        tmsCertificateCheckPO.setCheckKeyword(checkKeyword);
        tmsCertificateCheckPO.setCheckResult(checkResult);
        return tmsCertificateCheckPO;
    }

    //司机驾驶证核验结果
    public Map<String,Object> getDrvLicenseCheckResult(DrvAuditDTO auditDTO,QueryDrvCardInfoSOAResponseType soaResponseType ){
        Map<String,Object> rMap = Maps.newHashMap();
        try {
            Integer checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
            List<CertificateCheckSOAInfo> resultList = Lists.newArrayList();
            String thirdCode = soaResponseType.getThirdCode();
            String drvLicenseStatusResult = this.drvResultByEnumCode(thirdCode);
            if(StringUtils.isNotEmpty(drvLicenseStatusResult)){
                resultList.add(buildBean(TmsTransportConstant.DrvLicenseStatusColKey,this.getResultValue(SharkKeyConstant.drvLicenseResultInfo),TmsTransportConstant.DrvLicenseStatusResultKey,drvLicenseStatusResult));
                checkStatus = TmsTransportConstant.CheckStatusEnum.REVIEW.getCode();
            }
            //驾驶证有数据情况
            if(Objects.equals(thirdCode,TmsTransportConstant.CODE_1001)){
                Map<String,Object> resultMap = checkDriverLicenseInfo(auditDTO,soaResponseType.getData());
                Boolean statusflag = Boolean.TRUE;
                if (MapUtils.isNotEmpty(resultMap) && resultMap.get(TmsTransportConstant.RESULTFLAG_KEY)!=null && !(Boolean) resultMap.get(TmsTransportConstant.RESULTFLAG_KEY)) {
                    resultMap.put(TmsTransportConstant.CHECKSTATUS_KEY, TmsTransportConstant.CheckStatusEnum.ERROR.getCode());
                    logger.info("【drv License】check,check not through ,params:" + JsonUtil.toJson(resultMap));
                    drvLicenseStatusResult = this.getResultValue(SharkKeyConstant.drvLicenseNameNoAgree);
                    resultList.add(buildBean(TmsTransportConstant.DrvLicenseStatusColKey,this.getResultValue(SharkKeyConstant.drvLicenseCheckInfo),TmsTransportConstant.DrvLicenseStatusResultKey,drvLicenseStatusResult));
                    statusflag = Boolean.FALSE;
                }
                if(statusflag){
                    checkStatus = this.checkDrvLicenseStatus(soaResponseType);
                }
                drvLicenseStatusResult = this.getResultValue(SharkKeyConstant.drvStatus)+soaResponseType.getData().getDriverStatus();
                resultList.add(buildBean(TmsTransportConstant.DrvLicenseStatusColKey,this.getResultValue(SharkKeyConstant.drvLicenseResultInfo),TmsTransportConstant.DrvLicenseStatusResultKey,drvLicenseStatusResult));
            }
            rMap.put(TmsTransportConstant.CHECKSTATUS,checkStatus);
            rMap.put(TmsTransportConstant.RESULTINFO_KEY,resultList);
            return rMap;
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    /**
     * 根据行驶证返回码判定是否有效
     * @return
     */
    private Boolean isCheckVehicleLicense(VehicleAuditDTO auditDTO){
        logger.info("isCheckVehicleLicense ,params:" + JsonUtil.toJson(auditDTO));
        Map<String,Object> resultMap = Maps.newHashMap();
        if(!qconfig.getVehicleLicenseCheckSwitch()){
            return Boolean.FALSE;
        }
        List<CertificateCheckSOAInfo> resultList = Lists.newArrayList();
        String drvLicenseStatusResult = "";
        //获取第三方行驶证核验数据
        Result<QueryVehicleCardInfoSOAResponseType> soaResponseTypeResult = commonCommandService.queryVehicleCardInfo(auditDTO.getVehicleLicense(),auditDTO.getVehicleLicenseType());
        logger.info("【vehicle check】,The third party result:{}" , JsonUtil.toJson(auditDTO));
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
        Boolean flag = Boolean.TRUE;
        if(this.judgeSOAResult(soaResponseTypeResult)){
            flag = Boolean.FALSE;
            resultList.add(buildBean(TmsTransportConstant.VehLicenseStatusColKey,this.getResultValue(SharkKeyConstant.vehicleLicenseResultInfo),TmsTransportConstant.VehLicenseStatusResultKey,drvLicenseStatusResult));
            checkStatus = TmsTransportConstant.CheckStatusEnum.CHECKING.getCode();
        }
        if(flag){
            Map<String,Object> rMap = getVehicleLicenseCheckResult(auditDTO,soaResponseTypeResult.getData());
            checkStatus = (Integer) rMap.get(TmsTransportConstant.CHECKSTATUS);
            resultList = (List<CertificateCheckSOAInfo>) rMap.get(TmsTransportConstant.RESULTINFO_KEY);
        }
        //核验结果入库
        checkCommandService.insertCheckRecordDB(auditDTO.getCheckId(), auditDTO.getCheckType(), TmsTransportConstant.CertificateTypeEnum.CARCERTILICENSE.getCode(), "",JsonUtil.toJson(soaResponseTypeResult), resultList,checkStatus,checkStatus,auditDTO.getUserName());
        return isCheckResult(checkStatus);
    }

    //车辆行驶证核验结果
    private Map<String,Object> getVehicleLicenseCheckResult(VehicleAuditDTO vehicleAuditDTO,QueryVehicleCardInfoSOAResponseType soaResponseType ){
        Map<String,Object> rMap = Maps.newHashMap();
        try {
            Integer checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
            List<CertificateCheckSOAInfo> resultList = Lists.newArrayList();
            String thirdCode = soaResponseType.getThirdCode();
            String drvLicenseStatusResult = this.drvResultByEnumCode(thirdCode);
            if(StringUtils.isNotEmpty(drvLicenseStatusResult)){
                resultList.add(buildBean(TmsTransportConstant.DrvLicenseStatusColKey,this.getResultValue(SharkKeyConstant.vehicleLicenseResultInfo),TmsTransportConstant.DrvLicenseStatusResultKey,drvLicenseStatusResult));
                checkStatus = TmsTransportConstant.CheckStatusEnum.REVIEW.getCode();
            }
            //行驶证有数据情况
            if(Objects.equals(thirdCode,TmsTransportConstant.CODE_1001)){
                Map<String,Object> resultMap = checkVehicleLicenseInfo(vehicleAuditDTO,soaResponseType);
                Boolean statusflag = Boolean.TRUE;
                if (!(Boolean) resultMap.get(TmsTransportConstant.RESULTFLAG_KEY)) {
                    resultMap.put(TmsTransportConstant.CHECKSTATUS_KEY, TmsTransportConstant.CheckStatusEnum.ERROR.getCode());
                    logger.info("【isCheckVehicleLicense】check ,check No through params:" + JsonUtil.toJson(resultMap));
                    statusflag = Boolean.FALSE;
                }

                if(statusflag && Objects.equals(resultMap.get(TmsTransportConstant.CHECKSTATUS_KEY),TmsTransportConstant.CheckStatusEnum.THROUGH.getCode())){
                    checkStatus = this.checkCarcertiStatus(soaResponseType);
                }
                drvLicenseStatusResult = this.getResultValue(SharkKeyConstant.vehicleStatus)+soaResponseType.getData().getVehicleStatus();
                resultList.add(buildBean(TmsTransportConstant.VehLicenseStatusColKey,this.getResultValue(SharkKeyConstant.vehicleStatus),TmsTransportConstant.VehLicenseStatusResultKey,drvLicenseStatusResult));
                List<CertificateCheckSOAInfo> checkresultList = (List<CertificateCheckSOAInfo>) resultMap.get(TmsTransportConstant.RESULTINFO_KEY);
                resultList.addAll(checkresultList);
            }
            rMap.put(TmsTransportConstant.CHECKSTATUS,checkStatus);
            rMap.put(TmsTransportConstant.RESULTINFO_KEY,resultList);
            return rMap;
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    private Boolean isCheckResult(Integer checkStatus){
        if(Objects.equals(checkStatus,TmsTransportConstant.CheckStatusEnum.CHECKING.getCode())){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 网约车行驶证核验
     * @param vehicleLicense
     * @return
     */
    public Boolean netVehicleLicenseCertificateCheck(Long vehicleId,Integer checkType,String vehicleLicense,String userName){
        //网约行驾驶证核验
        String drvLicenseStatusResult = "";
        List<CertificateCheckSOAInfo> resultList = Lists.newArrayList();
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
        Boolean flag = Boolean.TRUE;
        Result<QueryNetVehicleCardInfoSOAResponseType> soaResponseTypeResult = commonCommandService.queryNetVehicleCardInfo(vehicleLicense);
        if(this.judgeSOAResult(soaResponseTypeResult)){
            drvLicenseStatusResult = this.getResultValue(SharkKeyConstant.soaError);
            flag = Boolean.FALSE;
            checkStatus = TmsTransportConstant.CheckStatusEnum.CHECKING.getCode();
        }
        if(flag){
            QueryNetVehicleCardInfoSOAResponseType soaResponseType = soaResponseTypeResult.getData();
            Map<String,Object> resultMap = this.getNetVehicleResult(soaResponseType.getThirdCode(),soaResponseType.getData());
            if((Boolean) resultMap.get(TmsTransportConstant.RESULTFLAG_KEY)){
                resultList = (List<CertificateCheckSOAInfo>) resultMap.get(TmsTransportConstant.RESULTINFO_KEY);
                checkStatus = (Integer) resultMap.get(TmsTransportConstant.CHECKSTATUS_KEY);
            }
            resultList.add(buildBean(TmsTransportConstant.NetDrvLicenseStatusColKey,this.getResultValue(SharkKeyConstant.netVehicleStatus),TmsTransportConstant.NetDrvLicenseStatusResultKey,drvLicenseStatusResult));
        }
        //核验结果入库
        checkCommandService.insertCheckRecordDB(vehicleId, checkType, TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT.getCode(),StringUtils.isEmpty(vehicleLicense)?"":vehicleLicense, JsonUtil.toJson(soaResponseTypeResult), resultList,checkStatus,checkStatus,userName);
        return isCheckResult(checkStatus);
    }

    /**
     * 网约车驾驶证核验
     * @return
     */
    public Boolean netDrvLicenseCertificateCheck(Long drvId,String drvIdcard,String userName,Integer checkType){
        //网约驾驾驶证核验
        String drvLicenseStatusResult = "";
        List<CertificateCheckSOAInfo> resultList = Lists.newArrayList();
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.CHECKING.getCode();
        Boolean flag = Boolean.TRUE;
        Result<QueryNetDrvCardInfoSOAResponseType> soaResponseTypeResult = commonCommandService.queryNetDrvCardInfo(drvIdcard);
        if(soaResponseTypeResult == null || soaResponseTypeResult.getData() == null || this.systemDesultByEnumCode(soaResponseTypeResult.getData().getThirdCode())){
            drvLicenseStatusResult = this.getResultValue(SharkKeyConstant.soaError);
            checkStatus = TmsTransportConstant.CheckStatusEnum.CHECKING.getCode();
            flag = Boolean.FALSE;
        }
        if(flag){
            QueryNetDrvCardInfoSOAResponseType netDrvCardInfoSOAResponseType = soaResponseTypeResult != null ? soaResponseTypeResult.getData() : null;
            String thirdCode = "";
            if(netDrvCardInfoSOAResponseType!=null){
                 thirdCode = netDrvCardInfoSOAResponseType.getThirdCode();
            }
            drvLicenseStatusResult = this.drvResultByEnumCode(thirdCode);
            if(StringUtils.isEmpty(drvLicenseStatusResult) && netDrvCardInfoSOAResponseType!=null &&  netDrvCardInfoSOAResponseType.getData() != null){
                NetCardInfoSOADTO netCardInfoSOADTO = netDrvCardInfoSOAResponseType.getData();
                drvLicenseStatusResult = netCardInfoSOADTO.getMessage();
                checkStatus = getNetVehicleCheckStatus(netCardInfoSOADTO);
            }
            resultList.add(buildBean(TmsTransportConstant.NetDrvLicenseStatusColKey,this.getResultValue(SharkKeyConstant.netVehicleStatus),TmsTransportConstant.NetDrvLicenseStatusResultKey,drvLicenseStatusResult));
        }
        //核验结果入库
        checkCommandService.insertCheckRecordDB(drvId, checkType, TmsTransportConstant.CertificateTypeEnum.NETDRVCTFCT.getCode(), StringUtils.isEmpty(drvIdcard)?"":drvIdcard,JsonUtil.toJson(soaResponseTypeResult), resultList,checkStatus,checkStatus,userName);
        return isCheckResult(checkStatus);
    }



    private Map<String,Object> getNetVehicleResult(String thirdCode, NetCardInfoSOADTO netCardInfoSOADTO){
        Map<String,Object> resultMap = Maps.newHashMap();
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.CHECKING.getCode();
        List<CertificateCheckSOAInfo> resultList = Lists.newArrayList();
        String drvLicenseStatusResult = this.drvResultByEnumCode(thirdCode);
        if(StringUtils.isEmpty(drvLicenseStatusResult) && netCardInfoSOADTO != null){
            drvLicenseStatusResult = netCardInfoSOADTO.getMessage();
            checkStatus = getNetVehicleCheckStatus(netCardInfoSOADTO);
        }
        resultList.add(buildBean(TmsTransportConstant.NetDrvLicenseStatusColKey,this.getResultValue(SharkKeyConstant.netVehicleStatus),TmsTransportConstant.NetDrvLicenseStatusResultKey,drvLicenseStatusResult));
        resultMap.put(TmsTransportConstant.RESULTFLAG_KEY,Boolean.TRUE);
        resultMap.put(TmsTransportConstant.RESULTINFO_KEY,resultList);
        resultMap.put(TmsTransportConstant.CHECKSTATUS_KEY,checkStatus);
        return resultMap;
    }

    /**
     * 网约车驾驶证核验
     * @return
     */
    public Integer netVehicleheck(Long checkId,TmsTransportConstant.CertificateTypeEnum checkTypeEnum,String checkKeyword,String userName,Integer checkType){

        //网约驾驾驶证核验
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.CHECKING.getCode();
        try {
            Boolean insertFlag = Boolean.TRUE;
            Boolean updateStepFlag = Boolean.FALSE;
            //网约车如果已核验过并且是通过，则取已有数据
            List<TmsCertificateCheckPO> checkPOList = checkRepository.queryCerCheckListByKeyWord(Arrays.asList(checkKeyword), TmsTransportConstant.CheckStatusEnum.THROUGH.getCode(),checkType,checkTypeEnum.getCode(),1,10000);
            if (!CollectionUtils.isEmpty(checkPOList)) {
                for (TmsCertificateCheckPO certificateCheckPO : checkPOList) {
                    if(StringUtils.isEmpty(certificateCheckPO.getCheckContent())){
                        continue;
                    }
                    NetCertificateResultDTO resultDTO = JsonUtil.fromJson(certificateCheckPO.getCheckContent(), new TypeReference<NetCertificateResultDTO>() {
                    });
                    if(!Objects.isNull(resultDTO) && !Objects.isNull(resultDTO.getData()) && !Objects.isNull(resultDTO.getData().getData()) && Objects.equals(resultDTO.getData().getData().getState(),"0")){
                        checkRepository.insertTmsCertificateCheck(insertCertificateCheckPO(certificateCheckPO,checkId,userName));
                        checkStatus = TmsTransportConstant.CheckStatusEnum.THROUGH.getCode();
                        insertFlag = Boolean.FALSE;
                        updateStepFlag = Boolean.TRUE;
                        break;
                    }
                }
            }
            if(insertFlag){
                //核验结果入库
                checkCommandService.insertCheckRecordDB(checkId, checkType, checkTypeEnum.getCode(),checkKeyword, "", Lists.newArrayList(),checkStatus,checkStatus,userName);
                updateStepFlag = Boolean.TRUE;
            }
            if(updateStepFlag){
                //如果网约车人证、车证调三方，则将已通过的运营单项置为待审核
                stepRepository.updateWaitApproveStatusFromPass(checkId,chooseStepApproveType(checkType),chooseStepApproveItem(checkType), CommonEnum.ApproveFromEnum.bd.getValue(),userName,approveReason(checkType));
                stepRepository.updateSupplierFinalPassStatus(checkId,chooseStepApproveType(checkType),chooseStepApproveItem(checkType));
            }
            return checkStatus;
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }


    public TmsCertificateCheckPO insertCertificateCheckPO(TmsCertificateCheckPO checkPO,Long checkId,String modifyUser){
        TmsCertificateCheckPO certificateCheckPO = new TmsCertificateCheckPO();
        BeanUtils.copyProperties(checkPO,certificateCheckPO);
        certificateCheckPO.setDatachangeCreatetime(DateUtil.getNow());
        certificateCheckPO.setDatachangeLasttime(DateUtil.getNow());
        certificateCheckPO.setCreateUser(modifyUser);
        certificateCheckPO.setCheckId(checkId);
        return certificateCheckPO;
    }

    private String  drvResultByEnumCode(String thirdCode){
        String drvLicenseStatusResult = "";
        switch (thirdCode){
            case TmsTransportConstant.CODE_2001:
                drvLicenseStatusResult = this.getResultValue(SharkKeyConstant.querySueecessNoData);
                break;
            case TmsTransportConstant._CODE_1001:
                drvLicenseStatusResult = this.getResultValue(SharkKeyConstant.nameUnStandard);
                break;
            case TmsTransportConstant.CODE_2002:
                drvLicenseStatusResult = this.getResultValue(SharkKeyConstant.limitTheQuery);
                break;
            case TmsTransportConstant.CODE_3001:
                drvLicenseStatusResult = this.getResultValue(SharkKeyConstant.queryNoData);
                break;
            case TmsTransportConstant.CODE_3002:
                drvLicenseStatusResult = this.getResultValue(SharkKeyConstant.paramsFail);
                break;
            case TmsTransportConstant.CODE_1002:
                drvLicenseStatusResult = this.getResultValue(SharkKeyConstant.requestParamsNotStandard);
                break;
            case TmsTransportConstant.CODE_1005:
                drvLicenseStatusResult = this.getResultValue(SharkKeyConstant.paramsUnStandard);
                break;
            case TmsTransportConstant.CODE_9999:
                drvLicenseStatusResult = this.getResultValue(SharkKeyConstant.theThirdPartyServiceError);
                break;
            case TmsTransportConstant.CODE_1017:
            case TmsTransportConstant.CODE_1018:
                drvLicenseStatusResult = this.getResultValue(SharkKeyConstant.queryCeiling);
        }
        return drvLicenseStatusResult;
    }

    private  Boolean  systemDesultByEnumCode(String thirdCode){
        switch (thirdCode){
            case TmsTransportConstant.SUCCESS_CODE:
            case TmsTransportConstant.CODE_1:
                return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 驾驶证条件校验
     * @return
     */
    private Map<String, Object> checkDriverLicenseInfo(DrvAuditDTO auditDTO, DrvCardRespDTO drvCardRespDTO) {
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put(TmsTransportConstant.RESULTFLAG_KEY, Boolean.TRUE);
        try {
            if (!Objects.equals(drvCardRespDTO.getName(), auditDTO.getDrvName())) {
                resultMap.put(TmsTransportConstant.RESULTFLAG_KEY, Boolean.FALSE);
                resultMap.put(TmsTransportConstant.CERTIFICATE_CHECK_ERROR_INFO_KEY, this.getResultValue(SharkKeyConstant.drvLicenseNameNoAgree));
                return resultMap;
            }
        } catch (Exception e) {
            resultMap.put(TmsTransportConstant.RESULTFLAG_KEY, Boolean.FALSE);
        }
        return resultMap;
    }
    /**
     * 行驶证条件校验
     *
     * @param checkDTO
     * @param soaResponseType
     * @return
     */
    public Map<String, Object> checkVehicleLicenseInfo(VehicleAuditDTO checkDTO, QueryVehicleCardInfoSOAResponseType soaResponseType) {
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put(TmsTransportConstant.RESULTFLAG_KEY, Boolean.TRUE);
        List<CertificateCheckSOAInfo> soaInfoList = Lists.newArrayList();
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.THROUGH.getCode();
        Boolean checkStatusFlag = Boolean.FALSE;
        try {
            VehicleCardItemSOADTO vehicleCardItemSOADTO = soaResponseType.getData();
            if (vehicleCardItemSOADTO != null) {
                String copyStr = vehicleCardItemSOADTO.getVin() + this.getResultValue(SharkKeyConstant.vinCodeAgree);
                if (!Objects.equals(checkDTO.getVinCode(), vehicleCardItemSOADTO.getVin())) {
                    copyStr = vehicleCardItemSOADTO.getVin() + this.getResultValue(SharkKeyConstant.vinCodeNoAgree);
                    checkStatusFlag = Boolean.TRUE;
                }
                soaInfoList.add(buildBean(TmsTransportConstant.VehLicenseVinColKey, "VIN", TmsTransportConstant.VehLicenseVinColKey, copyStr));
                //当当前日期>强制报废期止时
                copyStr = this.getResultValue(SharkKeyConstant.vehicleNormalUse);
                Date RetirementDate = DateUtil.getDate(vehicleCardItemSOADTO.getRetirementDate(), DateUtil.YYYYMMDD);
                if (RetirementDate != null && RetirementDate.compareTo(new Date()) < 0) {
                    copyStr = this.getResultValue(SharkKeyConstant.vehicleScrap);
                    checkStatusFlag = Boolean.TRUE;
                }
                soaInfoList.add(buildBean(TmsTransportConstant.VehRetirementColKey, this.getResultValue(SharkKeyConstant.vehicleRetirementStatus), TmsTransportConstant.VehRetirementResultKey, copyStr));
                if (checkStatusFlag) {
                    checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
                }
            }
            resultMap.put(TmsTransportConstant.CHECKSTATUS_KEY, checkStatus);
            resultMap.put(TmsTransportConstant.RESULTINFO_KEY, soaInfoList);
            return resultMap;
        } catch (Exception e) {
            resultMap.put(TmsTransportConstant.RESULTFLAG_KEY, Boolean.FALSE);
        }
        return resultMap;
    }

    //检验驾驶证状态
    private Integer  checkDrvLicenseStatus(QueryDrvCardInfoSOAResponseType soaResponseType){
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
        try{
            DrvCardRespDTO drvCardResp = soaResponseType.getData();
            String drvierStatus = drvCardResp.getDriverStatus();
            //成功
            if (qconfig.getDrvLicenseSuccessList().contains(drvierStatus)) {
                checkStatus = TmsTransportConstant.CheckStatusEnum.THROUGH.getCode();
            }
            //复核
            if (qconfig.getDrvLicenseStatusReviewList().contains(drvierStatus)) {
                checkStatus = TmsTransportConstant.CheckStatusEnum.REVIEW.getCode();
            }
            //失败
            if (qconfig.geDrvLicenseStatusErrorList().contains(drvierStatus)) {
                checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
            }
            //状态组合，有一条是error 就返回
            for(String str:qconfig.geDrvLicenseStatusErrorList()){
                if(drvierStatus.contains(str)){
                    checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();

                }
            }
            //状态组合
            for(String str:qconfig.getDrvLicenseStatusReviewList()){
                if(drvierStatus.contains(str)){
                    checkStatus = TmsTransportConstant.CheckStatusEnum.REVIEW.getCode();

                }
            }
            return checkStatus;
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    //检验行驶证状态
    private Integer checkCarcertiStatus(QueryVehicleCardInfoSOAResponseType soaResponseType ){
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
        try{
            VehicleCardItemSOADTO vehicleCardItemSOADTO = soaResponseType.getData();
            String vehicleStatus = vehicleCardItemSOADTO.getVehicleStatus();
            //成功
            if (qconfig.getCarcertiStatusSuccessList().contains(vehicleStatus)) {
                checkStatus = TmsTransportConstant.CheckStatusEnum.THROUGH.getCode();
            }
            //复核
            if (qconfig.getCarcertiStatusReviewList().contains(vehicleStatus)) {
                checkStatus = TmsTransportConstant.CheckStatusEnum.REVIEW.getCode();
            }
            //失败
            if (qconfig.getCarcertiStatusErrorList().contains(vehicleStatus)) {
                checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
            }

            //状态组合，有一条是error 就返回
            for(String str:qconfig.getCarcertiStatusErrorList()){
                if(vehicleStatus.contains(str)){
                    checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
                }
            }

            //状态组合
            for(String str:qconfig.getCarcertiStatusReviewList()){
                if(vehicleStatus.contains(str)){
                    checkStatus = TmsTransportConstant.CheckStatusEnum.REVIEW.getCode();
                }
            }
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
        return checkStatus;
    }

    private CertificateCheckSOAInfo buildBean(String columnKey,String columnValue,String resultKey,String resultValue){
        CertificateCheckSOAInfo info = new CertificateCheckSOAInfo();
        info.setColumnKey(columnKey);
        info.setColumnValue(columnValue);
        info.setResultKey(resultKey);
        info.setResultValue(resultValue);
        return info;
    }
    
    private  String  getResultValue(String sharkKey){
        return recordAttributeNameQconfig.getCertificateCheckRecordMap().get(sharkKey);
    }

    /**
     * 如司机背景审核有【通过】记录，那么被驳回后重新进运营审核时，无需重新背景审核，直接给【通过】标签即可。
     * @param keyWords
     * @param checkTypeEnum
     * @return
     */
    public Boolean judgmentIdCardIsThrough(String keyWords,Long checkid,String modifyUser, TmsTransportConstant.CertificateCheckTypeEnum checkTypeEnum){
        List<TmsCertificateCheckPO> checkPOList = checkRepository.queryCerCheckListByKeyWordOrderById(checkid,keyWords, TmsTransportConstant.CheckStatusEnum.THROUGH.getCode(),checkTypeEnum.getCode(), TmsTransportConstant.CertificateTypeEnum.IDCARD.getCode(),1,1);
        if(CollectionUtils.isEmpty(checkPOList)){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public Boolean idCardNewCheck(DrvCertificateCheckParameterDTO parameterDTO) throws SQLException {
        if(!qconfig.getIdcardCheckSwitch()){
            return Boolean.FALSE;
        }
        DriverBackgroundCheckDriverVO driverVO = new DriverBackgroundCheckDriverVO();
        driverVO.setPersonId(parameterDTO.getNewDrvIdCard());
        driverVO.setName(parameterDTO.getNewDrvName());
        driverVO.setPhoneNo(parameterDTO.getDrvPhone());
        tmsQmqProducerCommandService.sendIdcardBackGroundCheckQmq(Arrays.asList(driverVO));
        //核验结果入库
        Long id = checkRepository.insertTmsCertificateCheck(buildIdCheckPO(parameterDTO));
        //身份证48小时后返回核验结果
        tmsQmqProducerCommandService.sendIdcardApproveCheckQmq(id,1);
        return Boolean.FALSE;
    }

    /***
    　* @description: 驾驶证新核验
    　* <AUTHOR>
    　* @date 2021/10/8 14:14
    */
    public Integer drvLicenseNewCheck(DrvCertificateCheckParameterDTO parameterDTO){
        logger.info("drvLicenseNewCheck","drvIdCard:{},drvName:{}" + parameterDTO.getNewDrvIdCard(),parameterDTO.getNewDrvName());
        if(!qconfig.getDrvLicenseCheckSwitch()){
            return TmsTransportConstant.CheckStatusEnum.THROUGH.getCode();
        }
        List<CertificateCheckSOAInfo> resultList = Lists.newArrayList();
        String drvIdCard = TmsTransUtil.decrypt(parameterDTO.getNewDrvIdCard(), KeyType.Identity_Card);
        String drvName = parameterDTO.getNewDrvName();
        //获取第三方驾驶证核验数据
        Result<QueryDrvCardInfoSOAResponseType> drvCheckResponse = commonCommandService.queryDrvCardInfo(drvName,drvIdCard);
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
        Boolean flag = Boolean.TRUE;
        //如果是系统本身问题,定义为审批中，需跑定时刷新核验信息
        if(this.judgeSOAResult(drvCheckResponse)){
            checkStatus = TmsTransportConstant.CheckStatusEnum.CHECKING.getCode();
            flag = Boolean.FALSE;
        }
        Map<String,Object> rMap = Maps.newHashMap();
        if(flag){
            rMap = drvLicenseCheckResult(drvName,drvCheckResponse.getData());
            checkStatus = (Integer) rMap.get(TmsTransportConstant.CHECKSTATUS);
            resultList = (List<CertificateCheckSOAInfo>) rMap.get(TmsTransportConstant.RESULTINFO_KEY);
        }
        Integer labelCheckStatus = getReviewCheck(checkStatus);
        //核验结果入库
        checkCommandService.insertCheckRecordDB(parameterDTO.getCheckId(), parameterDTO.getCheckType(), TmsTransportConstant.CertificateTypeEnum.DRIVERLICENSE.getCode(),parameterDTO.getNewDrvIdCard(), JsonUtil.toJson(drvCheckResponse), resultList,labelCheckStatus,checkStatus,parameterDTO.getModifyUser());
        return checkStatus;
    }

   /**
   　* @description: 行驶证新核验
   　* <AUTHOR>
   　* @date 2021/10/8 14:14
   */
    public Integer vehicleLicenseNewCheck(VehCertificateCheckParameterDTO parameterDTO){
        if(!qconfig.getVehicleLicenseCheckSwitch()){
            return TmsTransportConstant.CheckStatusEnum.THROUGH.getCode();
        }
        List<CertificateCheckSOAInfo> resultList = Lists.newArrayList();
        String drvLicenseStatusResult = "";
        //获取第三方行驶证核验数据
        Result<QueryVehicleCardInfoSOAResponseType> soaResponseTypeResult = commonCommandService.queryVehicleCardInfo(parameterDTO.getNewVehicleLicense(),"");
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
        Boolean flag = Boolean.TRUE;
        if(this.judgeSOAResult(soaResponseTypeResult)){
            flag = Boolean.FALSE;
            resultList.add(buildBean(TmsTransportConstant.VehLicenseStatusColKey,this.getResultValue(SharkKeyConstant.vehicleLicenseResultInfo),TmsTransportConstant.VehLicenseStatusResultKey,drvLicenseStatusResult));
            checkStatus = TmsTransportConstant.CheckStatusEnum.CHECKING.getCode();
        }
        if(flag){
            Map<String,Object> rMap = vehicleLicenseCheckResult(parameterDTO.getVinCode(),soaResponseTypeResult.getData());
            checkStatus = (Integer) rMap.get(TmsTransportConstant.CHECKSTATUS);
            resultList = (List<CertificateCheckSOAInfo>) rMap.get(TmsTransportConstant.RESULTINFO_KEY);
        }
        Integer labelCheckStatus = getReviewCheck(checkStatus);
        //核验结果入库
        checkCommandService.insertCheckRecordDB(parameterDTO.getCheckId(), parameterDTO.getCheckType(), TmsTransportConstant.CertificateTypeEnum.CARCERTILICENSE.getCode(), parameterDTO.getNewVehicleLicense(),JsonUtil.toJson(soaResponseTypeResult), resultList,labelCheckStatus,checkStatus,parameterDTO.getModifyUser());
        return checkStatus;
    }

    //司机驾驶证核验结果
    public Map<String, Object> drvLicenseCheckResult(String drvName, QueryDrvCardInfoSOAResponseType soaResponseType) {
        Map<String, Object> rMap = Maps.newHashMap();
        try {
            Integer checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
            List<CertificateCheckSOAInfo> resultList = Lists.newArrayList();
            String thirdCode = soaResponseType.getThirdCode();
            String drvLicenseStatusResult = this.drvResultByEnumCode(thirdCode);
            if (StringUtils.isNotEmpty(drvLicenseStatusResult)) {
                resultList.add(buildBean(TmsTransportConstant.DrvLicenseStatusColKey, this.getResultValue(SharkKeyConstant.drvLicenseResultInfo), TmsTransportConstant.DrvLicenseStatusResultKey, drvLicenseStatusResult));
                checkStatus = TmsTransportConstant.CheckStatusEnum.REVIEW.getCode();
            }
            //驾驶证有数据情况
            if (Objects.equals(thirdCode, TmsTransportConstant.CODE_1001)) {
                Map<String, Object> resultMap = checkDrvLicense(drvName, soaResponseType.getData().getName());
                Boolean statusflag = Boolean.TRUE;
                if (MapUtils.isNotEmpty(resultMap) && resultMap.get(TmsTransportConstant.RESULTFLAG_KEY)!=null && !(Boolean) resultMap.get(TmsTransportConstant.RESULTFLAG_KEY)) {
                    resultMap.put(TmsTransportConstant.CHECKSTATUS_KEY, TmsTransportConstant.CheckStatusEnum.ERROR.getCode());
                    drvLicenseStatusResult = this.getResultValue(SharkKeyConstant.drvLicenseNameNoAgree);
                    resultList.add(buildBean(TmsTransportConstant.DrvLicenseStatusColKey, this.getResultValue(SharkKeyConstant.drvLicenseCheckInfo), TmsTransportConstant.DrvLicenseStatusResultKey, drvLicenseStatusResult));
                    statusflag = Boolean.FALSE;
                }
                if (statusflag) {
                    checkStatus = this.checkDrvLicenseStatus(soaResponseType);
                }
                drvLicenseStatusResult = this.getResultValue(SharkKeyConstant.drvStatus) + soaResponseType.getData().getDriverStatus();
                resultList.add(buildBean(TmsTransportConstant.DrvLicenseStatusColKey, this.getResultValue(SharkKeyConstant.drvLicenseResultInfo), TmsTransportConstant.DrvLicenseStatusResultKey, drvLicenseStatusResult));
            }
            rMap.put(TmsTransportConstant.CHECKSTATUS, checkStatus);
            rMap.put(TmsTransportConstant.RESULTINFO_KEY, resultList);
            return rMap;
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    //车辆行驶证核验结果
    public Map<String,Object> vehicleLicenseCheckResult(String vinCode,QueryVehicleCardInfoSOAResponseType soaResponseType ){
        Map<String,Object> rMap = Maps.newHashMap();
        try {
            Integer checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
            List<CertificateCheckSOAInfo> resultList = Lists.newArrayList();
            String thirdCode = soaResponseType.getThirdCode();
            String drvLicenseStatusResult = this.drvResultByEnumCode(thirdCode);
            if(StringUtils.isNotEmpty(drvLicenseStatusResult)){
                resultList.add(buildBean(TmsTransportConstant.DrvLicenseStatusColKey,this.getResultValue(SharkKeyConstant.vehicleLicenseResultInfo),TmsTransportConstant.DrvLicenseStatusResultKey,drvLicenseStatusResult));
                checkStatus = TmsTransportConstant.CheckStatusEnum.REVIEW.getCode();
            }
            //行驶证有数据情况
            if(Objects.equals(thirdCode,TmsTransportConstant.CODE_1001)){
                Map<String,Object> resultMap = vehicleLicenseInfo(vinCode,soaResponseType);
                Boolean statusflag = Boolean.TRUE;
                if (!(Boolean) resultMap.get(TmsTransportConstant.RESULTFLAG_KEY)) {
                    resultMap.put(TmsTransportConstant.CHECKSTATUS_KEY, TmsTransportConstant.CheckStatusEnum.ERROR.getCode());
                    statusflag = Boolean.FALSE;
                }

                if(statusflag && Objects.equals(resultMap.get(TmsTransportConstant.CHECKSTATUS_KEY),TmsTransportConstant.CheckStatusEnum.THROUGH.getCode())){
                    checkStatus = this.checkCarcertiStatus(soaResponseType);
                }
                drvLicenseStatusResult = this.getResultValue(SharkKeyConstant.vehicleStatus)+soaResponseType.getData().getVehicleStatus();
                resultList.add(buildBean(TmsTransportConstant.VehLicenseStatusColKey,this.getResultValue(SharkKeyConstant.vehicleStatus),TmsTransportConstant.VehLicenseStatusResultKey,drvLicenseStatusResult));
                List<CertificateCheckSOAInfo> checkresultList = (List<CertificateCheckSOAInfo>) resultMap.get(TmsTransportConstant.RESULTINFO_KEY);
                resultList.addAll(checkresultList);
            }
            rMap.put(TmsTransportConstant.CHECKSTATUS,checkStatus);
            rMap.put(TmsTransportConstant.RESULTINFO_KEY,resultList);
            return rMap;
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    public Map<String, Object> vehicleLicenseInfo(String vinCode, QueryVehicleCardInfoSOAResponseType soaResponseType) {
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put(TmsTransportConstant.RESULTFLAG_KEY, Boolean.TRUE);
        List<CertificateCheckSOAInfo> soaInfoList = Lists.newArrayList();
        Integer checkStatus = TmsTransportConstant.CheckStatusEnum.THROUGH.getCode();
        Boolean checkStatusFlag = Boolean.FALSE;
        try {
            VehicleCardItemSOADTO vehicleCardItemSOADTO = soaResponseType.getData();
            if(vehicleCardItemSOADTO != null){
                String copyStr = vehicleCardItemSOADTO.getVin()+this.getResultValue(SharkKeyConstant.vinCodeAgree);
                if (!Objects.equals(vinCode, vehicleCardItemSOADTO.getVin())) {
                    copyStr = vehicleCardItemSOADTO.getVin()+this.getResultValue(SharkKeyConstant.vinCodeNoAgree);
                    checkStatusFlag = Boolean.TRUE;
                }
                soaInfoList.add(buildBean(TmsTransportConstant.VehLicenseVinColKey,"VIN",TmsTransportConstant.VehLicenseVinColKey,copyStr));
                //当当前日期>强制报废期止时
                copyStr = this.getResultValue(SharkKeyConstant.vehicleNormalUse);
                Date RetirementDate = DateUtil.getDate(vehicleCardItemSOADTO.getRetirementDate(), DateUtil.YYYYMMDD);
                if (RetirementDate != null && RetirementDate.compareTo(new Date()) < 0) {
                    copyStr = this.getResultValue(SharkKeyConstant.vehicleScrap);
                    checkStatusFlag = Boolean.TRUE;
                }
                soaInfoList.add(buildBean(TmsTransportConstant.VehRetirementColKey,this.getResultValue(SharkKeyConstant.vehicleRetirementStatus),TmsTransportConstant.VehRetirementResultKey,copyStr));
                if(checkStatusFlag){
                    checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
                }
            }
            resultMap.put(TmsTransportConstant.CHECKSTATUS_KEY, checkStatus);
            resultMap.put(TmsTransportConstant.RESULTINFO_KEY, soaInfoList);
            return resultMap;
        } catch (Exception e) {
            resultMap.put(TmsTransportConstant.RESULTFLAG_KEY, Boolean.FALSE);
        }
        return resultMap;
    }


    public TmsCertificateCheckPO buildIdCheckPO(DrvCertificateCheckParameterDTO parameterDTO){
        TmsCertificateCheckPO checkPO = new TmsCertificateCheckPO();
        BeanUtils.copyProperties(parameterDTO,checkPO);
        checkPO.setCheckKeyword(parameterDTO.getNewDrvIdCard());
        checkPO.setCheckStatus(TmsTransportConstant.CheckStatusEnum.CHECKING.getCode());
        checkPO.setThirdCheckStatus(TmsTransportConstant.CheckStatusEnum.CHECKING.getCode());
        checkPO.setCertificateType(TmsTransportConstant.CertificateTypeEnum.IDCARD.getCode());
        return checkPO;
    }

    public Boolean judgeDrvParameterChange(String drvIdCard,String newDrvIdCard,String drvName,String newDrvName,TmsTransportConstant.CertificateTypeEnum certificateTypeEnum,Map<Integer, TmsCertificateCheckPO> drvCheckPOMap){
        Boolean judgeFlag = Boolean.FALSE;
        if(!Objects.equals(drvIdCard,newDrvIdCard) || !Objects.equals(drvName,newDrvName)){
            return Boolean.TRUE;
        }
        judgeFlag = triggerDrvCheckConditions(certificateTypeEnum,drvCheckPOMap);
        return judgeFlag;
    }

    public Boolean judgeVehParameterChange(String vehicleLicense,String newVehicleLicense,TmsTransportConstant.CertificateTypeEnum certificateTypeEnum,Map<Integer, TmsCertificateCheckPO> drvCheckPOMap){
        Boolean judgeFlag = Boolean.FALSE;
        if(!Objects.equals(vehicleLicense,newVehicleLicense)){
            return Boolean.TRUE;
        }
        judgeFlag = triggerDrvCheckConditions(certificateTypeEnum,drvCheckPOMap);
        return judgeFlag;
    }

    /**
     * 驾驶证条件校验
     * @return
     */
    private Map<String, Object> checkDrvLicense(String drvName, String thirdDrvName) {
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put(TmsTransportConstant.RESULTFLAG_KEY, Boolean.TRUE);
        try {
            if (!Objects.equals(drvName, thirdDrvName)) {
                resultMap.put(TmsTransportConstant.RESULTFLAG_KEY, Boolean.FALSE);
                resultMap.put(TmsTransportConstant.CERTIFICATE_CHECK_ERROR_INFO_KEY, this.getResultValue(SharkKeyConstant.drvLicenseNameNoAgree));
                return resultMap;
            }
        } catch (Exception e) {
            resultMap.put(TmsTransportConstant.RESULTFLAG_KEY, Boolean.FALSE);
        }
        return resultMap;
    }

    /**
    　* @description:判断证件是否再次核验,如果上次核验不通过或者超过90天，则再次进入核验
    　* <AUTHOR>
    　* @date 2021/10/8 11:32
    */
    public Boolean triggerDrvCheckConditions(TmsTransportConstant.CertificateTypeEnum certificateType,Map<Integer, TmsCertificateCheckPO> drvCheckPOMap){
        TmsCertificateCheckPO soadto = drvCheckPOMap.get(certificateType.getCode());
        if(soadto == null){
            return Boolean.TRUE;
        }
        if(Objects.equals(soadto.getThirdCheckStatus(), TmsTransportConstant.CheckStatusEnum.ERROR.getCode())||
                Objects.equals(soadto.getThirdCheckStatus(), TmsTransportConstant.CheckStatusEnum.CHECKING.getCode())||
        DateUtil.getDatePoor(new Date(),soadto.getDatachangeLasttime()) > qconfig.getCertificateRequestDaysThreshold()){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public Boolean checkStatusErrorFlag(Integer checkStatus){
        if(Objects.equals(checkStatus,TmsTransportConstant.CheckStatusEnum.ERROR.getCode())){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public Map<Integer, TmsCertificateCheckPO> queryCertificateCheckToMap2(Long checkId, Integer checkType){
        Map<Integer, TmsCertificateCheckPO> checkPOMap = Maps.newHashMap();
        List<TmsCertificateCheckPO> checkPOList = checkRepository.queryCertificateByCheckIdOrderBy(checkId,checkType);
        if(CollectionUtils.isEmpty(checkPOList)){
            return checkPOMap;
        }
        Set<String> sortSet = Sets.newHashSet();
        for (TmsCertificateCheckPO checkPO : checkPOList) {
            if (sortSet.add(checkPO.getCheckId() + "_" + checkPO.getCertificateType())) {
                checkPOMap.put(checkPO.getCertificateType(),checkPO);
            }
        }
        return checkPOMap;
    }

    public Integer chooseStepApproveType(Integer checkType){
        switch (checkType){
            case 1:return 1;
            case 4:return 2;
        }
        return 0;
    }

    public Integer chooseStepApproveItem(Integer checkType){
        switch (checkType){
            case 1:return 4;
            case 4:return 102;
        }
        return 0;
    }

    public String approveReason(Integer checkType){
        switch (checkType){
            case 1:return SharkUtils.getSharkValue(SharkKeyConstant.rhbDriverguidemgtOnlinedrvlicenseReexamine);
            case 4:return SharkUtils.getSharkValue(SharkKeyConstant.rhbDriverguidemgtOnlinetaxipermitReexamine);
        }
        return "";
    }

    //驾驶证,行驶证如果核验为复核，则默认标签为通过
    public Integer getReviewCheck(Integer checkStatus){
        if(Objects.equals(checkStatus,TmsTransportConstant.CheckStatusEnum.REVIEW.getCode())){
            return TmsTransportConstant.CheckStatusEnum.THROUGH.getCode();
        }
        return checkStatus;
    }
}
