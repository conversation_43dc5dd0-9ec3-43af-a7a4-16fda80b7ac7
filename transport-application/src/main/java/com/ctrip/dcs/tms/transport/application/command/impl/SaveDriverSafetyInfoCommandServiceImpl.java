package com.ctrip.dcs.tms.transport.application.command.impl;

import org.springframework.stereotype.Service;

import com.ctrip.dcs.tms.transport.api.model.SaveDriverSafetyInfoSOARequestType;
import com.ctrip.dcs.tms.transport.application.command.SaveDriverSafetyInfoCommandService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;

@Service("saveDriverSafetyInfoCommandService")
public class SaveDriverSafetyInfoCommandServiceImpl implements SaveDriverSafetyInfoCommandService {
    @Override
    public Result<String> saveDriverSafetyInfo(SaveDriverSafetyInfoSOARequestType request) {
        return Result.Builder.<String>newResult().success().withCode(ServiceResponseConstants.ResStatus.SUCCESS_CODE).build();
    }
}
