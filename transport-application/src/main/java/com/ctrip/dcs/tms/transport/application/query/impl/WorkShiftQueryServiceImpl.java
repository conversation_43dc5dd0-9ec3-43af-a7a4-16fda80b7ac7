package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.result.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2020/11/26 12:01
 */
@Service
public class WorkShiftQueryServiceImpl implements WorkShiftQueryService {

    @Autowired
    private WorkShiftRepository workShiftRepository;

    /**
     * 检查班次配置
     * @param configSOATypes
     * @return
     */
    @Override
    public boolean checkConfig(List<WorkShiftDetailSOAType> configSOATypes) {
        return true;
    }

    @Override
    public Result<List<TspTransportGroupWorkShiftPO>> queryWorkShifts(Long transportGroupId, Integer active) {
        List<TspTransportGroupWorkShiftPO> tspIntoOrderConfigPOS = workShiftRepository.queryWorkShifts(transportGroupId, active);
        return Result.Builder.<List<TspTransportGroupWorkShiftPO>>newResult()
                .success()
                .withData(tspIntoOrderConfigPOS)
                .build();
    }
}
