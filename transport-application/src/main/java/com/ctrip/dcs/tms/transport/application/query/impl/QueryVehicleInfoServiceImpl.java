package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.igt.framework.common.exception.*;
import com.google.common.collect.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import org.springframework.util.*;

import java.util.*;

@Component
public class QueryVehicleInfoServiceImpl implements IQueryVehicleInfoService {
    @Autowired
    private VehicleQueryService vehicleQueryService;
    @Autowired
    private DriverQueryService driverQueryService;
    @Override
    public VehCacheDTO queryVehicleById(Long vehicleId) throws BizException {
        List<VehCacheDTO> vehCacheDTOS =  vehicleQueryService.queryVehCacheList(Sets.newHashSet(vehicleId));
        if(CollectionUtils.isEmpty(vehCacheDTOS)){
            return null;
        }
        return vehCacheDTOS.get(0);
    }

    @Override
    public VehCacheDTO queryVehicleByVehicleNo(String vehicleNo) throws BizException {
        Long vehicleId = vehicleQueryService.queryVehicleId(vehicleNo);
        if(vehicleId == null){
            return null;
        }
        List<VehCacheDTO> vehCacheDTOS =  vehicleQueryService.queryVehCacheList(Sets.newHashSet(vehicleId));
        if(CollectionUtils.isEmpty(vehCacheDTOS)){
            return null;
        }
        return vehCacheDTOS.get(0);
    }

    @Override
    public VehCacheDTO queryRandomVehicle(String driverId,boolean conformanceFlag) throws BizException {
        //查询司机所属供应商id
        List<DrvCacheDTO> drvCacheDTOS = driverQueryService.queryDrvCacheList(Sets.newHashSet(Long.valueOf(driverId)));
        if(CollectionUtils.isEmpty(drvCacheDTOS)){
            return null;
        }
        //随机选择一个供应商下的合规车辆
        VehCacheDTO vehCacheDTO = vehicleQueryService.queryRandomVehicle(drvCacheDTOS.get(0).getSupplierId(),drvCacheDTOS.get(0).getCityId(),conformanceFlag);
        if(vehCacheDTO == null){
            return null;
        }
        return vehCacheDTO;
    }
}
