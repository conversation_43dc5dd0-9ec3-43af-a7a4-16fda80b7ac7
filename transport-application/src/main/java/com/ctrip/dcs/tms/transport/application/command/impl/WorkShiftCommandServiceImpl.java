package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.constant.*;
import com.ctrip.platform.dal.dao.annotation.*;
import com.ctriposs.baiji.exception.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;
import java.util.stream.*;

/**
 * 工作班次
 * <AUTHOR>
 * @Date 2020/11/26 15:30
 */
@Service
public class WorkShiftCommandServiceImpl implements WorkShiftCommandService {

    @Autowired
    private WorkShiftRepository workShiftRepository;

    @Autowired
    private TspTransportGroupDriverRelationRepository relationRepository;

    @Autowired
    private DrvDrvierRepository drvierRepository;

    @Autowired
    private TmsQmqProducerCommandService tmsQmqProducerCommandService;

    @Autowired
    private TransportGroupRepository transportGroupRepository;

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Integer> addWorkShiftDetail(List<TspTransportGroupWorkShiftPO> workShiftPOList) {
        int count = workShiftRepository.addWorkShift(workShiftPOList);
        if(count <= 0){
            throw new BaijiRuntimeException(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgWorkShiftAddError));
        }
        return Result.Builder.<Integer>newResult()
                .success()
                .withCode(ServiceResponseConstants.ResStatus.SUCCESS_CODE)
                .withData(count)
                .build();
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Integer> updateWorkShiftDetail(List<TspTransportGroupWorkShiftPO> workShiftPOList) {
        int count = workShiftRepository.updateWorkShift(workShiftPOList);
        if(count > 0){
            if(CollectionUtils.isEmpty(workShiftPOList)){
                return Result.Builder.<Integer>newResult().success().withData(count).build();
            }
            TspTransportGroupWorkShiftPO workShiftPO = workShiftPOList.get(0);
            TspTransportGroupPO transportGroupPO = transportGroupRepository.queryTransportGroupDetail(workShiftPO.getTransportGroupId());
            if(Objects.isNull(transportGroupPO) ||  transportGroupPO.getGroupStatus() == 1){
                return Result.Builder.<Integer>newResult().success().withData(count).build();
            }
            //更新报名城市司机中的工作时段
            Map<Long,String> workShiftMap = Maps.newHashMap();
            String modifyUser = "";
            for(TspTransportGroupWorkShiftPO shiftPO : workShiftPOList){
                workShiftMap.put(shiftPO.getId(),shiftPO.getStarTime()+"~"+shiftPO.getEndTime());
                modifyUser = shiftPO.getModifyUser();
            }
            List<Long> workIds = workShiftPOList.stream().map(TspTransportGroupWorkShiftPO::getId).collect(Collectors.toList());
            List<TspTransportGroupDriverRelationPO> relationPOList = relationRepository.queryRelationListByWorkShiftIds(workIds);
            if(CollectionUtils.isNotEmpty(relationPOList)){
                Map<Long,List<TspTransportGroupDriverRelationPO>> relationMap = relationPOList.stream().collect(Collectors.groupingBy(TspTransportGroupDriverRelationPO::getWorkShiftId));
                if(MapUtils.isNotEmpty(relationMap)){
                    for(Map.Entry<Long,List<TspTransportGroupDriverRelationPO>> entry : relationMap.entrySet()){
                        String workPeriod = workShiftMap.get(entry.getKey());
                        List<Long> drvids = entry.getValue().stream().map(TspTransportGroupDriverRelationPO::getDrvId).collect(Collectors.toList());
                        if(CollectionUtils.isNotEmpty(drvids) && StringUtils.isNotEmpty(workPeriod)){
                            int updateCount = drvierRepository.updateDrvWorkPeriod(drvids,workPeriod,modifyUser);
                            if(updateCount > 0){
                                Map<Long, DrvDriverPO> drvDriverPoMap = drvierRepository.getDrvDriverPoMap(drvids);
                                for(Long drvId : drvids){
                                    //qmq
                                    tmsQmqProducerCommandService.sendDrvChangeQmqForUpdateTransport(drvId, 2,1, Optional.ofNullable(drvDriverPoMap.get(drvId)).orElse(new DrvDriverPO()).getSupplierId());
                                }
                            }
                        }
                    }
                }
            }
            return Result.Builder.<Integer>newResult().success().withData(count).build();
        }
        return Result.Builder.<Integer>newResult().fail().withData(count).build();

    }
}
