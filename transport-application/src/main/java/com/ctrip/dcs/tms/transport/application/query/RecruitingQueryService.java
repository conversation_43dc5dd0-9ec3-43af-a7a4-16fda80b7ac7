package com.ctrip.dcs.tms.transport.application.query;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.common.base.*;
import com.ctrip.igt.framework.common.result.*;

import java.util.*;

public interface RecruitingQueryService {

    Result<PageHolder<RecruitingSOAResponseDTO>> queryRecruitingSOAList(RecruitingSOARequestDTO recruitingSOARequestDTO, PaginatorDTO paginator) throws Exception;

    /**
     * 招募手机号校验
     * @param requestType
     * @return
     */
    Result<Boolean> checkRecruitingPhone(CheckRecruitingPhoneSOARequestType requestType);

    /**
     * 招募账号校验
     * @param requestType
     * @return
     */
    Result<Boolean> checkRecruitingAccount(CheckRecruitingAccountSOARequestType requestType);

    /**
     　* @description: 查询单项信息
     　* <AUTHOR>
     　* @date 2021/12/2 15:58
     */
    Result<QueryApproveStepSOAResponseType> queryApproveStep(QueryApproveStepSOARequestType requestType);


    /**
    　* @description: H5查询当前审批状态
    　* <AUTHOR>
    　* @date 2021/12/7 14:18
    */
    Result<Integer> queryRecruitingApproveStatus(QueryRecruitingApproveStatusSOARequestType requestType);

    /**
    　* @description: 查询单项审批操作记录
    　* <AUTHOR>
    　* @date 2022/4/20 10:55
    */
    Result<List<QueryApproveStepRecordSOADTO>> queryApproveStepRecord(QueryApproveStepRecordSOARequestType requestType);

    /**
    　* @description: 判罚查询招募司机的进度状态
    　* <AUTHOR>
    　* @date 2023/3/22 10:55
    */
    Result<QueryRecruitingDrvForPenaltyResponseType> queryRecruitingDrvForPenalty(QueryRecruitingDrvForPenaltyRequestType requestType);

}