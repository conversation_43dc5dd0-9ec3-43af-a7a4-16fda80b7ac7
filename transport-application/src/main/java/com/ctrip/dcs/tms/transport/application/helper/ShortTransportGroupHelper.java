package com.ctrip.dcs.tms.transport.application.helper;

import com.ctrip.dcs.tms.transport.api.model.TransportGroupListSOAType;
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupDriverRelationPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ErrorCodeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ShortTransportGroupEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.ResponseResultUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.SharkUtils;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class ShortTransportGroupHelper {

  @Autowired
  private TransportGroupQueryService transportGroupQueryService;

  /**
   * 校验运力组是否是短运力组，以及是否司机绑定了长短运力组（长短互斥）
   * @param drvIdList 要校验的司机ID列表
   * @param expectationShortTransportGroup 期望的（司机绑定的）短公里运力组标识
   * @return 结果
   */
  public Result<String> checkShortTransportGroupDriver(List<Long> drvIdList, Long transportGroupId, Integer expectationShortTransportGroup) {

    if(Objects.isNull(expectationShortTransportGroup)) {
      return ResponseResultUtil.success();
    }

    // 根据司机ID列表查询所有绑定的运力组
    List<TspTransportGroupDriverRelationPO> relationPoList = transportGroupQueryService.queryTransportGroupIdByDrvIds(drvIdList);

    if (CollectionUtils.isEmpty(relationPoList)) {
      return ResponseResultUtil.success();
    }

    // 根据运力组ID列表查询所有的运力组
    Result<List<TransportGroupListSOAType>> transportGroupList = transportGroupQueryService.queryTransportGroupListByIdList(relationPoList.stream().map(TspTransportGroupDriverRelationPO::getTransportGroupId).collect(
      Collectors.toList()));

    // 排除当前运力组
    List<TransportGroupListSOAType> filterTransportGroupList = transportGroupList.getData().stream().filter(item -> !Objects.equals(item.getTransportGroupId(), transportGroupId)).collect(
      Collectors.toList());

    //relationPOList转成运力组和司机list的map
    Map<Long, List<Long>> transportGroupDrvListMap = relationPoList.stream().collect(Collectors.groupingBy(TspTransportGroupDriverRelationPO::getTransportGroupId, Collectors.mapping(TspTransportGroupDriverRelationPO::getDrvId, Collectors.toList())));

    StringBuilder msg = new StringBuilder();

    // 构造司机ID和短公里运力组标签list的Map
    Map<Long, List<Long>> drvIdTransportGroupListMap = Maps.newHashMap();
    for (TransportGroupListSOAType groupType : filterTransportGroupList) {
      // 如果司机关联的别的短公里运力组标签跟期望的运力组标签不符
      if (!Objects.equals(groupType.getShortTransportGroup(), expectationShortTransportGroup)) {
        for (Long drvId : transportGroupDrvListMap.get(groupType.getTransportGroupId())) {
          drvIdTransportGroupListMap.computeIfAbsent(drvId, k -> new ArrayList<>())
            .add(groupType.getTransportGroupId());
        }
      }
    }

    if (drvIdTransportGroupListMap.isEmpty()) {
      return ResponseResultUtil.success();
    }

    //构造错误信息
    for (Map.Entry<Long, List<Long>> entry : drvIdTransportGroupListMap.entrySet()) {
      msg.append(entry.getKey()).append(":").append(entry.getValue()).append("\n");
    }

    // 当前是非短公里，被短公里运力组绑定
    if (Objects.equals(expectationShortTransportGroup, ShortTransportGroupEnum.NOT_LIMIT.getCode())) {
      return Result.Builder.<String>newResult().fail().withCode(ErrorCodeEnum.SHORT_TRANSPORT_GROUP_DRIVER_BIND_BY_SHORT_TRANSPORT_GROUP.getCode())
        .withMsg(SharkUtils.getSharkValue(ErrorCodeEnum.SHORT_TRANSPORT_GROUP_DRIVER_BIND_BY_SHORT_TRANSPORT_GROUP.getMessage()) + msg).build();
    }

    //当前是短公里， 被非短公里运力组绑定
    return Result.Builder.<String>newResult().fail().withCode(ErrorCodeEnum.SHORT_TRANSPORT_GROUP_DRIVER_BIND_BY_OTHER_NOT_SHORT_TRANSPORT_GROUP.getCode())
      .withMsg(SharkUtils.getSharkValue(ErrorCodeEnum.SHORT_TRANSPORT_GROUP_DRIVER_BIND_BY_OTHER_NOT_SHORT_TRANSPORT_GROUP.getMessage()) + msg).build();
  }
}
