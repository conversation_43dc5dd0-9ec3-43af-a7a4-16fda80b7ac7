package com.ctrip.dcs.tms.transport.application.convert;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.api.regulation.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.google.common.base.Strings;
import com.google.common.collect.*;
import org.apache.commons.collections.*;

import java.util.*;

public class HistoryDrvResourceConverter {

    private final static String[] RESOURCE_FILED = new String[]{"drv_id", "drv_name", "corp_id", "corp_name",
            "car_id", "car_license", "drv_phone", "drv_idcard", "drv_license", "coop_mode", "opt_qualif", "city_code",
            "city_name", "salesman", "pic_url", "drv_addr", "comments", "certi_date", "bank_name", "bank_accout", "ppm_accout",
            "qunar_accout", "freeze_hour", "freeze_reason", "freeze_time", "drv_status", "create_time", "update_time", "drv_from",
            "idcard_url", "drvcard_url", "salesman_id", "expiry_begin_date", "expiry_end_date", "op_from", "op_type", "op_user_id",
            "affiliation", "invite_code", "internal_scope", "drv_unique_name", "pwd_hash", "salt", "wechat", "email", "country_id",
            "country_name", "phone_area_code", "ctrip_account", "certificate_number", "approve_status", "quasi_driving_type",
            "first_online_time", "sex", "birth_time", "liveness_recognition_id", "image_recognition_id", "language_ids", "language_names","data_migrated"};

    public static OldDriverInfo convertOldDriver(DrvHistoryDriverPO historyDriverPO) {
        if (historyDriverPO == null) {
            return null;
        }
        OldDriverInfo res = new OldDriverInfo();
        res.setDrvId(historyDriverPO.getDrvId());
        res.setDrvName(historyDriverPO.getDrvName());
        res.setDrvPhone(historyDriverPO.getDrvPhone());
        res.setCoopMode(historyDriverPO.getCoopMode());
        res.setDrvStatus(historyDriverPO.getDrvStatus());
        res.setDrvIdcard(historyDriverPO.getDrvIdcard());
        res.setBankName(historyDriverPO.getBankName());
        res.setBankAccout(historyDriverPO.getBankAccout());
        res.setPpmAccount(historyDriverPO.getPpmAccout());
        res.setInternalScope(historyDriverPO.getInternalScope());
        if (historyDriverPO.getDataMigrated() == null) {
            historyDriverPO.setDataMigrated(2);
        }
        res.setRegulationType(historyDriverPO.getDataMigrated() == 0 ? 2 : historyDriverPO.getDataMigrated());
        return res;
    }

    public static List<OldDriverInfo> convertOldDriver(List<DrvHistoryDriverPO> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return Lists.newArrayList();
        }
        List<OldDriverInfo> resList = Lists.newArrayListWithExpectedSize(poList.size());
        for (DrvHistoryDriverPO driverPO : poList) {
            resList.add(convertOldDriver(driverPO));
        }
        return resList;
    }

    public static QueryHistoryDrvConditionDTO convertCondition(QueryHistoryDrvDataRequestType req) {
        return QueryHistoryDrvConditionDTO.newCondition()
                .withDrvIdList(req.getDrvId() == null ? null : Lists.newArrayList(req.getDrvId()))
                .withDriverPhoneList(Strings.isNullOrEmpty(req.getDrvPhone()) ? null : Lists.newArrayList(TmsTransUtil.encrypt(req.getDrvPhone(), KeyType.Phone)))
                .withFields(RESOURCE_FILED).build();
    }

    public static DrvHistoryDriverPO convertPO(AddHistoryDrvDataRequestType req) {
        DrvHistoryDriverPO po = new DrvHistoryDriverPO();
        po.setDrvId(req.getDrvId());
        po.setSex(req.getSex());
        po.setPicUrl(req.getPicUrl());
        po.setDrvName(req.getDrvName());
        po.setDrvStatus(req.getStatus());
        po.setCoopMode(req.getCoopMode());
        po.setCorpId(req.getCorpId());
        po.setCorpName(req.getCorpName());
        po.setIdcardUrl(req.getIdcardUrl());
        po.setDrvcardUrl(req.getDrvcardUrl());
        po.setLanguageIds(req.getLanguageIds());
        po.setLanguageNames(req.getLanguageNames());
        po.setDrvUniqueName(req.getDrvUniqueName());
        po.setPhoneAreaCode(req.getPhoneAreaCode());
        po.setEmail(TmsTransUtil.encrypt(req.getEmail(), KeyType.Mail));
        po.setDrvPhone(TmsTransUtil.encrypt(req.getDrvPhone(), KeyType.Phone));
        po.setDrvIdcard(TmsTransUtil.encrypt(req.getDrvIdCard(), KeyType.Identity_Card));
        po.setDrvLicense(TmsTransUtil.encrypt(req.getDrvLicense(), KeyType.Driver_License));
        po.setCertificateNumber(TmsTransUtil.encrypt(req.getCertificateNumber(), KeyType.OtherDocument));
        po.setCertiDate(DateUtil.stringToSqlDate(req.getCertiDate(), DateUtil.YYYYMMDD));
        po.setBirthTime(DateUtil.stringToSqlDate(req.getBirthTime(), DateUtil.YYYYMMDD));
        po.setExpiryEndDate(DateUtil.stringToSqlDate(req.getExpiryEndDate(), DateUtil.YYYYMMDD));
        po.setFreezeTime(DateUtil.string2Timestamp(req.getFreezeTime(), DateUtil.YYYYMMDDHHMMSS));
        po.setExpiryBeginDate(DateUtil.stringToSqlDate(req.getExpiryBeginDate(), DateUtil.YYYYMMDD));
        po.setUpdateTime(DateUtil.string2Timestamp(req.getUpdateTime(), DateUtil.YYYYMMDDHHMMSS));
        po.setFirstOnlineTime(DateUtil.string2Timestamp(req.getFirstOnlineTime(), DateUtil.YYYYMMDDHHMMSS));
        po.setCreateTime(DateUtil.string2Timestamp(req.getCreateTime(), DateUtil.YYYYMMDDHHMMSS));
        po.setDrvAddr(req.getDrvAddr());
        po.setCityCode(req.getCityCode());
        po.setCityName(req.getCityName());
        po.setCountryId(req.getCountryId());
        po.setCountryName(req.getCountryName());
        po.setInternalScope(req.getInternalScope());
        po.setSalesman(req.getSalesman());
        po.setSalesmanId(req.getSalesmanId());
        po.setDrvFrom(req.getDrvFrom());
        po.setOpFrom(req.getOpFrom());
        po.setOpType(req.getOpType());
        po.setOpUserId(req.getOpUserId());
        po.setOptQualif(req.getOptQualif());
        po.setAffiliation(req.getAffiliation());
        po.setApproveStatus(req.getApproveStatus());
        po.setCarId(req.getCarId());
        po.setCarLicense(req.getCarLicense());
        po.setQuasiDrivingType(req.getQuasiDrivingType());
        po.setFreezeHour(req.getFreezeHour());
        po.setFreezeReason(req.getFreezeReason());
        po.setSalt(req.getSalt());
        po.setPwdHash(req.getPwdHash());
        po.setWechat(req.getWechat());
        po.setBankName(req.getBankName());
        po.setBankAccout(req.getBankAccout());
        po.setPpmAccout(req.getPpmAccout());
        po.setQunarAccout(req.getQunarAccout());
        po.setCtripAccount(req.getCtripAccount());
        po.setInviteCode(req.getInviteCode());
        po.setImageRecognitionId(req.getImageRecognitionId());
        po.setLivenessRecognitionId(req.getLivenessRecognitionId());
        po.setComments(req.getComments());
        po.setDataMigrated(req.getDataMigrated());
        return po;
    }

}