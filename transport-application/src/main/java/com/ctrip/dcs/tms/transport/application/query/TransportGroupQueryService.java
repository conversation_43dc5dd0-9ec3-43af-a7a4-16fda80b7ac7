package com.ctrip.dcs.tms.transport.application.query;

import com.ctrip.dcs.price.sku.pricing.facade.dto.*;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.dto.TransportGroupInOrderConfigDTO;
import com.ctrip.dcs.tms.transport.application.dto.TransportGroupTimeSegmentConfigDTO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.common.base.*;
import com.ctrip.igt.framework.common.result.*;

import java.util.*;

/**
 * 运力组服务
 * <AUTHOR>
 * @Date 2020/3/13 17:17
 */
public interface TransportGroupQueryService {

    /**
     * 查询运力组报名统计信息
     * @param requestType
     * @return
     */
    Result<QueryTransportGroupApplyInfoSOAResponseType> queryTransportGroupApplyInfo(QueryTransportGroupApplyInfoSOARequestType requestType);

    /**
     * 查询运力组已关联/未关联司机列表
     * @param requestSOAType
     * @return
     */
    Result<PageHolder<DriverRelationSOADTO>> queryRelationDrvList(DriverRelationListRequestSOAType requestSOAType);

    /**
     * 查询可选的运力组模式枚举值
     * @param request
     * @return
     */
    Result<List<Long>> queryOptionalTransportGroupMode(QueryOptionalTransportGroupModeSOARequestType request);

    /**
     * 查询运力组配置信息
     * @param request
     * @return
     */
    Result<Map<String, String>> queryTransportGroupConfig(QueryTransportGroupConfigSOARequestType request);

    /**
     * 判断是否为调度模式
     * @param transportGroupMode
     * @return
     */
    boolean isDispatcherMode(Integer transportGroupMode);

    /**
     * 判断是否为报名制模式
     * @param transportGroupMode
     * @return
     */
    boolean isApplyMode(Integer transportGroupMode);

    List<SkuInfoSOADTO> querySkuInfoByTransportGroupId(Long transportGroupId);

    Result<PageHolder<QueryBingIngSkuSOADTO>>  queryBindSkuRelationList(QueryBingIngSkuSOARequestType queryBingIngSkuRequestType);

    /**
     * 通过id查询运力组
     * @param transportGroupId
     * @return
     */
    Result<TspTransportGroupPO> queryTransportGroupDetail(Long transportGroupId);

    /**
     * 运力组查询
     * @param transportGroupPO
     * @param soaRequestType
     * @return
     */
    Result queryTransportGroupList(TspTransportGroupPO transportGroupPO,QueryTransportGroupListSOARequestType soaRequestType);

    /**
     * 运力组查询（匹配已绑定sku、服务区域）
     * @param queryModel
     * @return
     */
    Result<List<TransportGroupDetailSOAType>> queryTransportGroups(QueryTransportGroupModel queryModel);

    /**
     * 是否可以绑定
     * */
    Result<Boolean> canBind(DrvDriverPO driverPO, Integer transportGroupMode);

    /**
     * 查询全量运力组(id,name)
     * @param queryModel
     * @return
     */
    Result<List<QueryAllTransGroupSOADTO>> queryAllTransportGroupsList(QueryAllTransGroupListDO queryModel);


    Result<List<TransportGroupListSOAType>> queryTransportGroupListByIdList(List<Long> idList);


    Result<List<SkuTransportGroupDTO>> querySkuTransportGroup(List<Long> skuIdList);

    Result<List<LocationDTOSOA>> queryLocationByGroup(Long transportGroupId);

    Map<Long,List<SimpleAreaGroupDTO>> getAreaCityIdByGroupIds(List<Long> areaGroupIdlist);

    Result<List<QueryTransportGroupsForDspInfo>> queryTransportGroupsForDsp(QueryTransportGroupModel queryModel);

    Result<List<QueryApplyTransGroupsSkuForDspInfo>> queryApplyTransGroupsSkuForDsp(QueryApplyTransGroupsSkuForDspRequestType requestType);

    Result<List<OrderTimeScope>> queryTransportGroupTakeOrderTimeScope(QueryTransportGroupCondition queryModel);

    List<TransportGroupBasePO> queryDriverGroupRelationPO(List<Long> drvIdList, List<Long> groupIdList);

    /**
     * 查询司机运力组缓存
     * @param drvIdList
     * @param groupIdList
     * @return
     */
    List<TransportGroupBasePO> queryDriverGroupRelationPOCache(List<Long> drvIdList, List<Long> groupIdList);

    Boolean grayTransportId(Long transportIds);

    /**
     * 查询运力组进单配置
     * @param transportGroupIds
     * @return
     */
    List<TransportGroupInOrderConfigDTO> queryInOrderConfig(List<Long> transportGroupIds, Date bookTime,Long cityId,String locationCode, Integer filterDispatchOnly);

    /**
     * 查询运力组通知信息
     */
    Result<QueryTransportInformResponseType> queryTransportInform(Long transportGroupId);

    /**
     * 根据运力组id查询司机id
     */
    List<Long> queryDrvIdByTransportGroups(List<Long> transportGroupIdList,Integer temporaryDispatchMark);

    /**
     　* @description: 境外运力组进单是否小于配置值
     　* <AUTHOR>
     　* @date 2023/8/9 18:50
     */
    Result<Boolean> inOrderUpperLimit(List<InOrderConfigSOAType> inOrderConfigs,Long cityId);

    /**
     * 获取已绑定该运力组的车辆
     * */
    List<Long> queryRelationDrvIdListByTransportGroupId(Long transportGroupId);

    /**
     * 司机对应的运力组ID
     * @param drvIds
     * @return
     */
    List<TspTransportGroupDriverRelationPO> queryTransportGroupIdByDrvIds(List<Long> drvIds) ;

}
