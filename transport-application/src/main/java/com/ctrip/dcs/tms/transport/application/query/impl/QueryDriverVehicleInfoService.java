package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.application.dto.DriverVehicleDTO;
import com.ctrip.dcs.tms.transport.application.query.IQueryDriverVehicleInfoService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehVehiclePO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.DrvInfoParam;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class QueryDriverVehicleInfoService implements IQueryDriverVehicleInfoService {
    private static final Logger logger = LoggerFactory.getLogger(QueryDriverVehicleInfoService.class);
    @Autowired
    private DrvDrvierRepository driverRepository;
    @Autowired
    private VehicleRepository vehicleRepository;
    @Override
    public DriverVehicleDTO query(Long driverId) {
        try{
            DrvInfoParam drvInfoParam = new DrvInfoParam();
            drvInfoParam.setDrvId(driverId);
            DrvDriverPO drvInfoPO = driverRepository.queryByPk(driverId);
            if(drvInfoPO == null){
                return null;
            }
            DriverVehicleDTO driverVehicleDTO = new DriverVehicleDTO();
            driverVehicleDTO.setDrvId(drvInfoPO.getDrvId());
            driverVehicleDTO.setDrvcardImg(drvInfoPO.getDrvcardImg());
            driverVehicleDTO.setDrvHeadImg(drvInfoPO.getDrvHeadImg());
            driverVehicleDTO.setNetVehiclePeoImg(drvInfoPO.getNetVehiclePeoImg());
            driverVehicleDTO.setPeopleVehicleImg(drvInfoPO.getPeopleVehicleImg());
            if(drvInfoPO.getVehicleId() == null){
                return driverVehicleDTO;
            }
            VehVehiclePO vehVehiclePO = vehicleRepository.queryByPk(drvInfoPO.getVehicleId());
            if(vehVehiclePO == null){
                return driverVehicleDTO;
            }
            driverVehicleDTO.setNetTansCertificateImg(vehVehiclePO.getNetTansCtfctImg());
            driverVehicleDTO.setVehicleFullImg(vehVehiclePO.getVehicleFullImg());
            driverVehicleDTO.setVehicleCertificateImg(vehVehiclePO.getVehicleCertiImg());
            return driverVehicleDTO;
        }catch (Exception e){
            Map<String,String> tag = new HashMap<>();
            tag.put("driverId",driverId.toString());
            logger.error("QueryDriverVehicleInfoService_ex",driverId.toString(),e,tag);
            throw new BizException("QueryDriverVehicleInfoService_ex");
        }
    }
}
