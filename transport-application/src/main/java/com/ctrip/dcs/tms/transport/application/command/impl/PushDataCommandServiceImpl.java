package com.ctrip.dcs.tms.transport.application.command.impl;

import cn.hutool.crypto.*;
import cn.hutool.crypto.asymmetric.*;
import cn.hutool.crypto.symmetric.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.concurrent.threadpool.CThreadPool;
import com.google.common.collect.*;
import okhttp3.*;
import org.apache.commons.io.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.*;
import org.springframework.core.io.*;
import org.springframework.http.MediaType;
import org.springframework.http.*;
import org.springframework.stereotype.*;
import org.springframework.util.*;

import java.io.*;
import java.net.*;
import java.util.*;

@Service
public class PushDataCommandServiceImpl implements PushDataCommandService {
    private static final okhttp3.MediaType JSON_MEDIA = okhttp3.MediaType.parse("application/json");

    private static final Logger logger = LoggerFactory.getLogger(PushDataCommandServiceImpl.class);

    private static final String DriverInfoUrl_Key = "DriverInfoUrl";
    private static final String DrvUpdateDataUrl_Key = "DrvUpdateDataUrl";
    private static final String DrvLocationsDataUrl_Key = "DrvLocationsDataUrl";
    private static final String DrvEventDataUrl_Key = "DrvEventDataUrl";
    private static final String DrvFileDataUrl_Key = "DrvFileDataUrl";

    @Autowired
    EnumRepository enumRepository;

    @Autowired
    TmsTransportQconfig qconfig;

    @Autowired
    private DrvDrvierRepository drvDrvierRepository;

    @Autowired
    private OkHttpClient httpClient;

    @Override
    public Boolean pushDrirverInfo(List<DriverVehicleInfoDTO> infoDTOList) {
        if(CollectionUtils.isEmpty(infoDTOList)){
            return Boolean.FALSE;
        }
        String content = JsonUtil.toJson(infoDTOList);
        pushData(qconfig.getPushDataUrlMap().get(DriverInfoUrl_Key),content);
        return Boolean.TRUE;
    }

    @Override
    public Boolean pushDrvUpdateData(List<DriverVehicleUpdateDTO> infoDTOList) {
        if(CollectionUtils.isEmpty(infoDTOList)){
            return Boolean.FALSE;
        }
        String content = JsonUtil.toJson(infoDTOList);
        pushData(qconfig.getPushDataUrlMap().get(DrvUpdateDataUrl_Key),content);
        return Boolean.TRUE;
    }


    @Override
    public Boolean pushEventsData(List<DriverEventDTO> eventDTOS) {
        if(CollectionUtils.isEmpty(eventDTOS)){
            return Boolean.FALSE;
        }
        String content = JsonUtil.toJson(eventDTOS);
        pushData(qconfig.getPushDataUrlMap().get(DrvEventDataUrl_Key),content);
        return Boolean.TRUE;
    }

    @Override
    public void prepare4pushDrvUpdateData(Long id, CommonEnum.RecordTypeEnum recordTypeEnum) {
        DriverVehicleUpdateDTO driverVehicleUpdateDTO = new DriverVehicleUpdateDTO();
        driverVehicleUpdateDTO.setOrderAmount(70);
        if (recordTypeEnum.getCode().intValue() == CommonEnum.RecordTypeEnum.VEHICLE.getCode().intValue()) {
            driverVehicleUpdateDTO.setVehicleStatus(0);
            driverVehicleUpdateDTO.setObjectType(1);
        } else {
            driverVehicleUpdateDTO.setCompanyEvaluation(4.3f);
            driverVehicleUpdateDTO.setObjectType(0);
            driverVehicleUpdateDTO.setDriverStatus(0);
        }
        driverVehicleUpdateDTO.setReportedAt(DateUtil.dateToString(new Date(),DateUtil.YYYYMMDDHHMMSS));
        driverVehicleUpdateDTO.setObjectId(String.valueOf(id));
        pushDrvUpdateData(ImmutableList.of(driverVehicleUpdateDTO));
    }

    @Override
    public void prepare4DrvIncrease(Long id) {
        List<CommunicationsDrvInfoPO> drvInfoPOList = drvDrvierRepository.queryDriverInfo4TrafficAgency(PushDataCommandService.HangZhouCityId, ImmutableList.of(id), TmsTransportConstant.DrvStatusEnum.ONLINE.getCode(),1,1);
        if (CollectionUtils.isEmpty(drvInfoPOList)) {
            return;
        }
        pushDrirverInfo(dealDTO(drvInfoPOList));
    }

    @Override
    public List<DriverVehicleInfoDTO> dealDTO(List<CommunicationsDrvInfoPO> drvIdList) {
        List<DriverVehicleInfoDTO> infoDTOList = Lists.newArrayListWithCapacity(drvIdList.size());
        for (CommunicationsDrvInfoPO infoPO : drvIdList) {
            DriverVehicleInfoDTO res = new DriverVehicleInfoDTO();
            res.setDriverId(String.valueOf(infoPO.getDrvId()));
            res.setName(infoPO.getDrvName());
            res.setIdCardNo(TmsTransUtil.decrypt(infoPO.getDrvIdcard(), com.ctrip.arch.coreinfo.enums.KeyType.Identity_Card));
            res.setAge(BaseUtil.getAgeFromIdCard(res.getIdCardNo()));
            res.setGender(dealGender(infoPO.getSex()));
            res.setDriverFirstRegisterAt(DateUtil.getTimeStr(infoPO.getCertiDate()));
            res.setHasDevice(1);
            res.setOrderAmount(50 + new Random(1).nextInt(10));
            res.setCompanyEvaluation(4.5f);
            res.setVehicleId(String.valueOf(infoPO.getVehicleId()));
            res.setPlateNo(infoPO.getVehicleLicense());
            res.setVehicleBrand(enumRepository.getBandName(infoPO.getVehicleBrandId()));
            res.setVehicleColor(enumRepository.getColorName(infoPO.getVehicleColorId()));
            res.setVehicleModel(enumRepository.getVehicleTypeName(infoPO.getVehicleTypeId()));
            res.setVehicleSn(infoPO.getVin());
//            res.setVehicleMotorSn("string");
            res.setVehicleBindingAt(DateUtil.getTimeStr(infoPO.getDatachangeCreatetime()));
            String regstDate = DateUtil.getTimeStr(infoPO.getRegstDate());
            res.setRegisterAt(regstDate);
            res.setVehicleRegisterAt(regstDate);
            if(StringUtils.equals(regstDate,"1777-01-01 00:00:00")){
                res.setRegisterAt("1971-01-01 00:00:00");
                res.setVehicleRegisterAt("1971-01-01 00:00:00");
            }
            infoDTOList.add(res);
        }
        return infoDTOList;
    }

    private Integer dealGender(String sex) {
        if ("1".equals(sex)) {
            return 1;
        } else {
            return 0;
        }
    }

    private void pushData(String url,String content){
        if(!qconfig.getPushDataSwitch()){
            return;
        }
        logger.info("PushDataStart ","url:{},content:{}",url,content);
        RSA rsa = new RSA(null, qconfig.getHangzhouDataPublicKey());
        // 随机生成aes密钥
        byte[] key = SecureUtil.generateKey(SymmetricAlgorithm.AES.getValue()).getEncoded();
        AES aes1 = new AES(Mode.CBC, Padding.PKCS5Padding, key, key);
        // aes加密正文
        byte[] encryptAES = aes1.encrypt(content);
        String body = Base64Utils.encodeToString(encryptAES);
        // rsa公钥加密
        byte[] encryptRSA = rsa.encrypt(key, KeyType.PublicKey);
        String BusinessKey = Base64Utils.encodeToString(encryptRSA);
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);
        requestHeaders.set("authorization",
                "Basic " + Base64.getEncoder().encodeToString(qconfig.getHangzhouDataAuthorization().getBytes()));
        requestHeaders.set("BusinessKey", BusinessKey);
        try{
            CThreadPool.pool(TransportThreadGroupConstant.certificateFutureThreadPoolName).execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        Request request = new Request.Builder().addHeader("BusinessKey", BusinessKey).addHeader("authorization","Basic " + Base64.getEncoder().encodeToString(qconfig.getHangzhouDataAuthorization().getBytes())).url(url).post(RequestBody.create(JSON_MEDIA, body)).build();
                        Response response = httpClient.newCall(request).execute();
                        logger.info("PushDataResultInfo","body:{}",response.body().string());
                    }catch (Exception e){
                        logger.warn("RestTemplateUtilsPostError",e);
                    }
                }
            });
        }catch (Exception e){
            logger.warn("hangzhouPushData error",e);
        }
    }

}