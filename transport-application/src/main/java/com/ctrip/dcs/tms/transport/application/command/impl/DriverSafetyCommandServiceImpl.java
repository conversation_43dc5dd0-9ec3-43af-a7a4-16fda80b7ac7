package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.result.*;
import com.google.common.base.*;
import org.apache.commons.lang.time.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * 2021-4-14 疫情防控核验
 *
 * <AUTHOR>
 */
@Service
public class DriverSafetyCommandServiceImpl implements DriverSafetyCommandService {

    private char[] maskArray = new char[]{'*', '@', '#', '$', '%', '&', 'X', 'x'};

    @Autowired
    private TmsTransportQconfig tmsTransportQconfig;

    @Autowired
    private TmsTransportApproveRepository tmsTransportApproveRepository;

    @Autowired
    private EpidemicPreventionControlQconfig epidemicPreventionControlQconfig;

    @Override
    public Result<EpidemicPreventionControlEnum.ReportResultEnum> baseVerifySameDriver(String driverName, String idNumber, String ocrDriverName, String ocrIdNumber) {
        Boolean nameMatchingResult = false;
        Boolean idNumberMatchingResult = false;
        if (!Strings.isNullOrEmpty(ocrDriverName) && !Strings.isNullOrEmpty(driverName)) {
            ocrDriverName = ocrDriverName.trim();
            char maskCode = getMaskCode(ocrDriverName);
            if (!checkMaskCodeFull(ocrDriverName, maskCode)) {
                if (String.valueOf(maskCode).trim().length() == 0) {
                    nameMatchingResult = driverName.equals(ocrDriverName);
                } else {
                    nameMatchingResult = matchingText(driverName, ocrDriverName, maskCode);
                }
            }
        }
        if (!Strings.isNullOrEmpty(ocrIdNumber) && !Strings.isNullOrEmpty(idNumber)) {
            ocrIdNumber = ocrIdNumber.trim();
            char maskCode = getMaskCode(ocrIdNumber);
            if (!checkMaskCodeFull(ocrIdNumber, maskCode) && ocrIdNumber.length() <= idNumber.length()) {
                idNumberMatchingResult = matchingText(idNumber, ocrIdNumber, maskCode);
            }
        }
        if (nameMatchingResult && idNumberMatchingResult) {
            return Result.Builder.<EpidemicPreventionControlEnum.ReportResultEnum>newResult().success().withData(EpidemicPreventionControlEnum.ReportResultEnum.PASS).build();
        }
        if (nameMatchingResult ^ idNumberMatchingResult) {
            return Result.Builder.<EpidemicPreventionControlEnum.ReportResultEnum>newResult().fail().withData(EpidemicPreventionControlEnum.ReportResultEnum.PARTIAL_INFORMATION_AVAILABLE).build();
        }
        return Result.Builder.<EpidemicPreventionControlEnum.ReportResultEnum>newResult().fail().withData(EpidemicPreventionControlEnum.ReportResultEnum.NOT_ONESELF).build();
    }

    @Override
    public Result<EpidemicPreventionControlEnum.ReportResultEnum> checkNucleicAcidTestingReport(Long cityId, Date samplingDate, String nucleicAcidTestingResult, Date ocrSamplingDate, String ocrNucleicAcidTestingResult) {
        Boolean nucleicAcidResult = false;
        Boolean effectiveDaysCheck = false;
        if (samplingDate == null) {
            samplingDate = ocrSamplingDate;
        }
        if (Strings.isNullOrEmpty(nucleicAcidTestingResult)) {
            nucleicAcidTestingResult = ocrNucleicAcidTestingResult;
        }
        if (!Strings.isNullOrEmpty(nucleicAcidTestingResult) && !Strings.isNullOrEmpty(ocrNucleicAcidTestingResult)) {
            if (nucleicAcidTestingResult.contains(EpidemicPreventionControlEnum.ResultStatusEnum.POSITIVE.getMessage()) && ocrNucleicAcidTestingResult.contains(EpidemicPreventionControlEnum.ResultStatusEnum.POSITIVE.getMessage())) {
                return Result.Builder.<EpidemicPreventionControlEnum.ReportResultEnum>newResult().fail().withData(EpidemicPreventionControlEnum.ReportResultEnum.POSITIVE).build();
            }
            if (nucleicAcidTestingResult.contains(EpidemicPreventionControlEnum.ResultStatusEnum.NEGATIVE.getMessage()) && ocrNucleicAcidTestingResult.contains(EpidemicPreventionControlEnum.ResultStatusEnum.NEGATIVE.getMessage())) {
                nucleicAcidResult = true;
            }
        }
        if (samplingDate != null && ocrSamplingDate != null) {
            EpidemicPreventionControlCityInfoDTO cityInfoDTO = epidemicPreventionControlQconfig.getEpidemicPreventionControlCityMap(cityId);
            Boolean isSameDay = DateUtils.isSameDay(samplingDate, ocrSamplingDate);
            if (isSameDay) {
                if (DateUtils.addDays(samplingDate, cityInfoDTO.getSamplingDateEffectiveDays()).before(new Date())) {
                    return Result.Builder.<EpidemicPreventionControlEnum.ReportResultEnum>newResult().fail().withData(EpidemicPreventionControlEnum.ReportResultEnum.OVERDUE).build();
                } else {
                    effectiveDaysCheck = true;
                }
            }
        }
        if (nucleicAcidResult && effectiveDaysCheck) {
            return Result.Builder.<EpidemicPreventionControlEnum.ReportResultEnum>newResult().success().withData(EpidemicPreventionControlEnum.ReportResultEnum.PASS).build();
        }
        return Result.Builder.<EpidemicPreventionControlEnum.ReportResultEnum>newResult().fail().withData(EpidemicPreventionControlEnum.ReportResultEnum.PARTIAL_INFORMATION_AVAILABLE).build();
    }

    @Override
    public Boolean checkOcrInfoComplete(SaveDriverSafetyInfoSOARequestType ocrRequest, Boolean isNucleicAcidDetectionType) {
        if (ocrRequest == null) {
            return true;
        }
        int ocrResult = 0;
        if (!Strings.isNullOrEmpty(ocrRequest.getDriverName())) {
            ocrResult++;
        }
        if (!Strings.isNullOrEmpty(ocrRequest.getDriverIdNumber())) {
            ocrResult++;
        }
        if (isNucleicAcidDetectionType) {
            if (!Strings.isNullOrEmpty(ocrRequest.getNucleicAcidTestingTime())) {
                ocrResult++;
            }
            if (!Strings.isNullOrEmpty(ocrRequest.getNucleicAcidTestingResult())) {
                ocrResult++;
            }
        } else {
            if (!Strings.isNullOrEmpty(ocrRequest.getVaccineName())) {
                ocrResult++;
            }
            if (!Strings.isNullOrEmpty(ocrRequest.getFirstVaccinationTime())) {
                ocrResult++;
            }
            if (!Strings.isNullOrEmpty(ocrRequest.getSecondVaccinationTime())) {
                ocrResult++;
            }
            if (ocrRequest.getVaccinationCount() != null) {
                ocrResult++;
            }
        }
        return ocrResult <= 1;
    }

    @Override
    public String getExternalDescribe(int detectionType, Long drvId, Long cityId, int days, EpidemicPreventionControlEnum.ReportResultEnum reportResultEnum) {
        return "";
    }

    private char getMaskCode(String maskText) {
        for (char mask : maskArray) {
            if (maskText.indexOf(mask) != -1) {
                return mask;
            }
        }
        return maskArray[0];
    }

    private Boolean isNucleicAcidDetectionType(int detectionType) {
        if (EpidemicPreventionControlEnum.DetectionTypeEnum.NUCLEIC_ACID_TEST.getValue().intValue() == detectionType) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private String fillTextMask(String text, String maskText, char maskCode) {
        int gap = text.length() - maskText.length();
        StringBuilder stringBuilder = new StringBuilder(maskText);
        for (int i = 0; i < gap; i++) {
            stringBuilder.append(maskCode);
        }
        return stringBuilder.toString().trim();
    }

    private boolean matchingText(String text, String maskText, char maskCode) {
        if (text.length() > maskText.length()) {
            maskText = fillTextMask(text, maskText, maskCode);
        } else if (maskText.length() > text.length()) {
            text = fillTextMask(maskText, text, maskCode);
        }
        for (int i = 0; i < text.length(); i++) {
            if (maskText.charAt(i) != maskCode & maskText.charAt(i) != text.charAt(i)) {
                return false;
            }
        }
        return true;
    }

    private boolean checkMaskCodeFull(String pattern, char code) {
        char[] codes = pattern.toCharArray();
        for (char c : codes) {
            if (c != code) {
                return false;
            }
        }
        return true;
    }

}