package com.ctrip.dcs.tms.transport.application.command;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;

import java.util.*;

public interface PushDataCommandService {

   Long HangZhouCityId = 17L;

   /**
    * 推送司机信息
    * @param infoDTOList
    * @return
    */
   Boolean pushDrirverInfo(List<DriverVehicleInfoDTO> infoDTOList);

   /**
    * 推送司机变更信息
    * @param infoDTOList
    * @return
    */
   Boolean pushDrvUpdateData(List<DriverVehicleUpdateDTO> infoDTOList);

   /**
    * 推送司机事件变更信息
    * @return
    */
   Boolean pushEventsData(List<DriverEventDTO> eventDTOS);

   /**
    * 预处理 - 数据变更
    * */
   void prepare4pushDrvUpdateData(Long id, CommonEnum.RecordTypeEnum recordTypeEnum);

   /**
    * 预处理 - 数据增量
    * */
   void prepare4DrvIncrease(Long id);

   List<DriverVehicleInfoDTO> dealDTO(List<CommunicationsDrvInfoPO> drvIdList);

}