package com.ctrip.dcs.tms.transport.application.query;


import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.igt.framework.common.base.PageHolder;
import com.ctrip.igt.framework.common.result.*;

import java.util.*;

/**
 * 新增变更记录
 * <AUTHOR>
 * @Date 2020/3/1 17:46
 */
public interface TmsModRecordQueryService {

    /**
     * 查询变更记录
     * @param requestType
     * @return
     */
    Result<List<ModRecordSOADTO>> queryModRecordList(ModRecordSOARequestType requestType);

    int  queryModRecordListCount(ModRecordSOARequestType requestType);

    /**
     * 变更记录查询前置校验
     * @param requestType 服务请求参数
     * @return
     */
    Result<Void> preCheck(ModRecordSOARequestType requestType);
}