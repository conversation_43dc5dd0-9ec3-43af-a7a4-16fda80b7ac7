package com.ctrip.dcs.tms.transport.application.query;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.api.regulation.*;
import com.ctrip.dcs.tms.transport.api.resource.driver.*;
import com.ctrip.dcs.tms.transport.application.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.common.base.*;
import com.ctrip.igt.framework.common.result.*;

import java.sql.Timestamp;
import java.util.*;

/**
 * 司机查询类接口
 * <AUTHOR>
 * @Date 2020/3/17 14:58
 */
public interface DriverQueryService {

    /**
     * 检查司机工作时段配置是否合法（返回true为通过合法）
     * @param workPeriod
     * @return
     */
    boolean checkWorkPeriod(String workPeriod);

    /**
     * 查询司机可接意愿车型
     * @param requestType
     * @return
     */
    Result<List<QueryIntendVehicleTypeSOAResponseDTO>> queryOptionalIntendVehicleType(QueryOptionalIntendVehicleTypeSOARequestType requestType);

    /**
     * 司机列表
     * @param drvDO
     * @return
     */
    Result<PageHolder<DrvDriverSOAResponseDTO>> queryDrvList(QueryDrvDO drvDO);

    Result<Integer> queryBundleStatusDrvCount(List<Long> drvIds, Long supplierId, DriverRelationListRequestSOAType requestSOAType);

    Result<List<DrvDriverPO>> queryBundleStatusDrvList(List<Long> drvIds, Long supplierId, DriverRelationListRequestSOAType requestSOAType);

    /**
     * 司机详情
     * @param drvId
     * @return
     */
    Result<QueryDrvDetailSOAResponseType> queryDrvDetail(Long drvId);

    /**
     * 查询司机详情
     * @param requestType
     * @return
     */
    Result<QueryDrvInfoDTOSOA> queryDrvInfo(QueryDrvInfoSOARequestType requestType);

    /**
     * 批量司机查询
     * @param requestType
     * @return
     */
    Result<List<QueryDrvInfoDTOSOA>> queryDrvInfoList(QueryDrvInfoListSOARequestType requestType);


    /**
     * 司机端查询
     * @param appDO
     * @return
     */
    Result<QueryDrvListByAppResponseType> queryDrvListByApp(QueryDrvListByAppDO appDO);

    /**
     * 供应商查询司机列表(id,name)
     * @param supplierId
     * @return
     */
    Result<List<QueryDrvBySupplierIdSOADTO>> queryDrvBySupplierIdList(Long supplierId);

    /**
     * 司机端登录时,校验密码是否可用
     * @param loginAccount
     * @return
     */
    Result<String> checkDrvPwd(String loginAccount,String loginPwd);

    /**
     * 查询司机绑定的运力组模式
     * @param drvIdList
     * @return
     */
    Result<Boolean> checkDrvTransportMode(List<Long> drvIdList);

    /**
     * 按多选条件查询司机列表
     */
    Result<QueryDrvByMuSelConditionsSOAResponseType> queryDrvByMuSelConditions(QueryDrvByMuSelConditionsSOARequestType requestType);

    /**
     * 生成一个随机code码,与司机ID对应，保证一对一
     * @param drvId
     * @return
     */
    Result<String> queryDrvCodeByDrvId(Long drvId);

    /**
     * 根据司机端请求 queryDrvCodeByDrvId 接口，生成的code码，返回对应的司机ID
     * @param drvCode
     * @return
     */
    Result<String> queryDrvIdByCode(String drvCode);

    Result<Boolean> checkVehicleTypeMatching(CheckVehicleTypeMatchingSOARequestType requestType);

    /**
     * 全职报名运力组计算司机的合作模式、是否抢单、是否传司机工作时段、司机端归类
     * @return
     */
    DrvInfoCacheDto calculateQJBMDrvCoopMode(Long drvId, List<TransportGroupBasePO> relationVOS);

    /**
     * 全职指派运力组计算司机的合作模式、是否抢单、是否传司机工作时段、司机端归类
     * @return
     */
    DrvInfoCacheDto calculatQJZPDrvCoopMode(Long drvId, List<TransportGroupBasePO> relationVOS);

    /**
     * 计算合作模式
     * */
    DrvInfoCacheDto calculateDrvCoopMode(Long id, List<TransportGroupBasePO> list);

    /**
     * 判断城市操作司机上线
     * @return
     */
    Boolean judgeDisableDrvOnlineCity(List<Long> drvCityList);


    /**
     * 判断操作人是否可用
     * @return
     */
    Boolean judgeOperatorPermission();

    Result<Boolean> judgeDrvOnlinePermission(Boolean firstFlag,List<Long> drvCityList,List<Long> vehicleList);

    /**
     * 查询判罚司机id
     * */
    Result<List<Long>> queryPenaltyOfflineDrvIdList(List<Long> drvIdList);

    /**
     * 查询仍然在判罚冻结时间内的司机id
     * */
    Result<List<Long>> queryPenaltyFreezeDrvIdList(List<Long> drvIdList);

    /**
     * 查询司机姓名
     * */
    Result<List<String>> queryDrvNameByIdList(List<Long> drvIdList);

    Result<DrvVerifyGraySOADTO> queryDrvVerifyGray(DrvVerifyGraySOARequestType requestType);

    Result<QueryDrvAddrModCountSOAResponseType> queryDrvAddrModCount(QueryDrvAddrModCountSOARequestType requestType);

    /***
    　* @description: 判断司机该城市下是否有健康打卡权限
    　* <AUTHOR>
    　* @date 2021/9/17 11:42
    */
    Result<Boolean> queryDrvHealthPunchPermissions(Long drvId);

    /**
     * 获取司机缓存列表
     * */
    List<DrvCacheDTO> queryDrvCacheList(Set<Long> drvIdSet);

    /**
     * 根据司机id查询司机信息
     * @param driverId
     * @return
     */
    SimpleDriverInfoDTO querySimpleDriver(Long driverId);

    /**
     * 查询司机资源
     * */
    List<DrvBase> queryDrvBaseResource(QueryDrvResourceConditionDTO condition);

    /**
     * 查询历史司机数据
     * */
    List<OldDriverInfo> queryHistoryDrvResource(QueryHistoryDrvConditionDTO condition);

    /**
    　* @description: 报名制司机检查时长
    　* <AUTHOR>
    　* @date 2023/1/3 16:37
    */
    Map<Long,Long> checkApplyDriverLeaveDuration(List<Long> drvList,Timestamp beginCheckDateTime,Timestamp endCheckDateTime,Map<Long,Timestamp> drvCalculateBeginTimeMap, TransportGroupDriverApplyProcessDTO applyProcessDTO);

    /**
    　* @description: 全局赛道判断司机请假记录是否满足条件
    　* <AUTHOR>
    　* @date 2023/1/6 15:19
    */
    Map<Long,Boolean> checkGlobalApplyDriverLeaveDuration(GlobalApplyDriverLeaveDurationVO driverLeaveDurationVO, TransportGroupDriverApplyProcessDTO applyProcessDTO);

    /**
     * 查询司机资源数量
     * */
    int queryDrvBaseResourceCount(QueryDrvResourceConditionDTO condition);

    /**
     　* @description: 境外司机派遣城市灰度 -1 代表全量
     　* <AUTHOR>
     　* @date 2023/2/13 14:00
     */
    Result<Boolean> drvDispatchCityGray(Long cityId);

    /**
     　* @description: 查询司机派遣供应商
     　* <AUTHOR>
     　* @date 2023/2/13 14:06
     */
    Result<List<QueryDrvDispatchListSOAVO>> queryDrvDispatchList(QueryDrvDispatchListSOARequestType requestType);

    Map<Long,List<Long>> queryDrvDispatchSupplierIds(Set<Long> drvIdSet);

    /**
     * 账号混合查询
     * */
    List<DrvDriverPO> queryDrvDriverListByAccount(String hybridAccount);

    /**
     　* @description: 图例列表
     　* <AUTHOR>
     　* @date 2023/6/8 15:21
     */
    Result<List<QueryDrvVehLegendListSOAVO>> queryDrvVehLegendList();

    /**
     　* @description: 境外新逻辑灰度
     　* <AUTHOR>
     　* @date 2023/6/8 16:08
     */
    Result<QuerySupplierConfigInfoSOAResponseType> querySupplierConfigInfo(Long supplierId, Long cityId, String useCarTime);

    /**
     　* @description: 境外OCR
     　* <AUTHOR>
     　* @date 2023/6/8 16:07
     */
    Result<OverseasOCRRecognitionSOAResponseType> overseasOCRRecognition(OverseasOCRRecognitionSOARequestType requestType);

    /**
     　* @description: 当前供应商是否在新业务中
     　* <AUTHOR>
     　* @date 2023/6/9 10:34
     */
    Boolean overseasIsBusiness(Long supplierId, Integer versionFlag, Integer areaScope, TmsTransportConstant.BusinessTypeEnum businessTypeEnum);

    /**
     　* @description: 境外供应商是否灰度
     　* <AUTHOR>
     　* @date 2023/6/13 14:11
     */
    Boolean overseasSupplierIsGray(Long supplierId,Integer areaScope);

    /**
     * 司机列表
     * @param drvDO
     * @return
     */
    Result<PageHolder<DrvDriverSOAResponseDTO>> queryDiscardDrvList(QueryDrvDO drvDO);

    /**
    　* @description: 通过供应商+城市 查询产线
    　* <AUTHOR>
    　* @date 2023/11/16 19:45
    */
    Result<Integer> queryTemporaryProLine(Long supplierId,Long cityId);

    /**
     * 检查手机号
     * @param requestType
     * @return
     */
    Result<PreCheckResultDTO> checkPhoneNumber(CheckPhoneNumberRequestType requestType);

    /**
     * 司机入驻/编辑前置校验
     * @param requestType
     * @return
     */
    Result<PreCheckResultDTO> drvPreCheck(DrvPreCheckRequestType requestType);

    /**
     * 检查出租车标签
     *
     * @param supplierId 供应商id
     * @param cityId     城市ID
     * @return {@link Result }<{@link Boolean }>
     */
    Result<Boolean> checkTaxiLabel(Long supplierId,Long cityId);

}
