package com.ctrip.dcs.tms.transport.application.command;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.igt.framework.common.result.*;

import java.util.*;

/**
 * <AUTHOR>
 * @Description  证件审核接口
 * @Date 14:34 2020/9/27
 * @Param 
 * @return 
 **/
public interface CertificateCheckCommandService {

    Long insertCheckRecordDB(Long checkId, Integer checkType, Integer certificateType,String checkKeyword, String content, List<CertificateCheckSOAInfo> checkInfo, Integer checkStatus,Integer thirdCheckStatus, String userName);

    Result<Boolean> insertBackGroundCheck(List<DriverBackgroundChecksSOADTO> checksDTOList);

    /**
    　* @description: 给某个属性添加标签
    　* <AUTHOR>
    　* @date 2023/3/31 14:06
    */
    Result<Boolean> insertPropertyAddTag(PropertyAddTagSOARequestType requestType);

    /**
    　* @description: 变更标签
    　* <AUTHOR>
    　* @date 2023/3/31 14:46
    */
    Result<Boolean> updatePropertyTag(PropertyUpdateTagSOARequestType requestType);
}