package com.ctrip.dcs.tms.transport.application.query;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.igt.framework.common.result.*;

/**
 * <AUTHOR>
 * @since 2020/9/21 11:47
 */
public interface DriverAccountQueryService {
  
  /**
   * 司机端登录时,校验密码是否可用
   *
   * @param loginAccount
   * @return
   */
  Result<DrvDriverPO> login(String loginAccount, String loginPwd, String loginAreaCode, LoginType loginType);
}
