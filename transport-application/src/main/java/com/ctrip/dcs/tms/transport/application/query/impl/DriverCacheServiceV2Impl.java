package com.ctrip.dcs.tms.transport.application.query.impl;

import cn.hutool.core.lang.Pair;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.dto.QueryDriverFromCacheParamDTO;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CommonEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TransportThreadGroupConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.monitoring.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.QueryDriverConditionDTO;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.concurrent.threadpool.CThreadPool;
import com.ctrip.igt.framework.common.exception.*;
import com.dianping.cat.Cat;
import com.dianping.cat.async.CatAsync;
import com.google.common.base.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.*;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.DRIVER_REST_APP_100027977;

/**
 * <AUTHOR>
 */
@Service
public class DriverCacheServiceV2Impl implements DriverCacheServiceV2 {

    private static final Logger logger = LoggerFactory.getLogger(DriverCacheServiceV2Impl.class);

    private static final int tryChallengeCount = 5;

    @Autowired
    private DrvDrvierRepository drvDrvierRepository;

    @Autowired
    private DriverGroupRelationRepository driverGroupRelationRepository;

    @Autowired
    private DriverQueryService driverQueryService;

    @Autowired
    private VehicleQueryService vehicleQueryService;

    @Autowired
    private TransportGroupRepository transportGroupRepository;

    @Autowired
    private TmsTransportQconfig tmsTransportQconfig;

    @Autowired
    private TransportGroupQueryService transportGroupQueryService;

    @Override
    public List<DriverInfo> packageQuery(QueryDriverFromCacheParamDTO req) {
        try {
            // 是否为只返回请参中运力组信息模式
            boolean onlyTransportMode = isQueryTransportMode(req);
            // 获取关系组
            Pair<Map<Long, Long>, Map<Long, List<TransportGroupBasePO>>> relationPair = queryRelationMap(req, onlyTransportMode);
            Map<Long, Long> drvIdVehicleIdMap = relationPair.getKey();
            Future<List<DrvCacheDTO>> drvCacheDTOListFuture = CThreadPool.pool(TransportThreadGroupConstant.driverCacheFutureThreadPool).submit(CatAsync.wrap(() -> driverQueryService.queryDrvCacheList(drvIdVehicleIdMap.keySet())));
            Future<List<VehCacheDTO>> vehCacheDTOListFuture = CThreadPool.pool(TransportThreadGroupConstant.driverCacheFutureThreadPool).submit(CatAsync.wrap(() -> vehicleQueryService.queryVehCacheList(Sets.newHashSet(drvIdVehicleIdMap.values()))));
            Future<Map<Long,List<Long>>> dispatchSupplierIdListFuture = queryDrvDispatchSupplierIds(Sets.newHashSet(drvIdVehicleIdMap.keySet()));
            Map<Long, List<TransportGroupBasePO>> drvTransportGroupRelationMap;
            // 请参运力组模式
            if (onlyTransportMode) {
                drvTransportGroupRelationMap = relationPair.getValue();
            } else {
                Future<Map<Long, List<TransportGroupBasePO>>> driverTransportRelationMapFuture = CThreadPool.pool(TransportThreadGroupConstant.driverCacheFutureThreadPool).submit(CatAsync.wrap(() -> getGroupBasePOMap(Lists.newArrayList(drvIdVehicleIdMap.keySet()), null)));
                drvTransportGroupRelationMap = driverTransportRelationMapFuture.get();
            }
            return packageData(drvCacheDTOListFuture.get(), vehCacheDTOListFuture.get().stream().collect(Collectors.toMap(VehCacheDTO::getCarId, Function.identity(), (key1, key2) -> key2)), drvTransportGroupRelationMap, drvIdVehicleIdMap,dispatchSupplierIdListFuture.get());
        } catch (BizException e) {
            TransportMetric.drvCacheReqDBNoResultCounter.inc();
            logger.warn(DriverCacheAgent.DRV_CACHE_WARN_TITLE, "No Result Case:{} Params:{}", e.getMessage(), JsonUtil.toJson(req));
            Cat.logEvent(Constant.EventType.DRIVER, DriverCacheAgent.DRV_CACHE_WARN_TITLE);
            return Collections.emptyList();
        } catch (Exception e) {
            TransportMetric.drvCacheErrorCounter.inc();
            logger.error(DriverCacheAgent.DRV_CACHE_ERROR_TITLE, "Params:{} Error:{}", JsonUtil.toJson(req), e);
            return Collections.emptyList();
        }
    }

    protected Future<Map<Long,List<Long>>> queryDrvDispatchSupplierIds(Set<Long> drvIdSet) {
        // 司机端查询不需要返回供应商派遣关系
        if (tmsTransportQconfig.queryDriverPerformance() && DRIVER_REST_APP_100027977.equals(PlatformUtil.getClientAppId())) {
            Cat.logEvent(Constant.EventType.DRIVER, DRIVER_REST_APP_100027977);
            return new CompletableFuture<Map<Long,List<Long>>>(){
                @Override
                public Map<Long,List<Long>> get() {
                    return Collections.emptyMap();
                }
            };
        }

        return CThreadPool.pool(TransportThreadGroupConstant.driverCacheFutureThreadPool).submit(CatAsync.wrap(() -> driverQueryService.queryDrvDispatchSupplierIds(drvIdSet)));
    }

    public HashMap<Long, List<TransportGroupBasePO>> getGroupBasePOMap(List<Long> drvIdList, List<Long> transportIdList) {
        List<TransportGroupBasePO> relationPOS = transportGroupQueryService.queryDriverGroupRelationPOCache(drvIdList, transportIdList);
        if (CollectionUtils.isEmpty(relationPOS)) {
            return Maps.newHashMapWithExpectedSize(2);
        }
        HashMap<Long, List<TransportGroupBasePO>> driverIdMap = CollectionUtils.isEmpty(drvIdList) ? Maps.newHashMap() : Maps.newHashMapWithExpectedSize(drvIdList.size());
        for (TransportGroupBasePO relationPO : relationPOS) {
            List<TransportGroupBasePO> list = driverIdMap.get(relationPO.getDrvId());
            if (CollectionUtils.isEmpty(list)) {
                driverIdMap.put(relationPO.getDrvId(), list = Lists.newArrayList());
            }
            list.add(relationPO);
        }
        return driverIdMap;
    }

    /**
     * 是否为只关心请参中运力组模式
     */
    private boolean isQueryTransportMode(QueryDriverFromCacheParamDTO req) {
        // 定位模式
        CommonEnum.QueryTransportModeEnum mode = CommonEnum.QueryTransportModeEnum.getInstance(tmsTransportQconfig.getAllowTransportGroupOnlyMode());
        // 全部关闭 或者 请参无运力组id
        if (mode == CommonEnum.QueryTransportModeEnum.ALL_REFUSE || Strings.isNullOrEmpty(req.getTransportGroupIds())) {
            return Boolean.FALSE;
        }
        // 全部放开
        if (mode == CommonEnum.QueryTransportModeEnum.ALL_ALLOW) {
            return Boolean.TRUE;
        }
        // 灰度
        return req.getQueryTargetTransportMode() != null && req.getQueryTargetTransportMode();
    }

    private boolean isSelectDriver(QueryDriverFromCacheParamDTO req) {
        return !Strings.isNullOrEmpty(req.getDriverIds()) || !Strings.isNullOrEmpty(req.getDriverName()) || !Strings.isNullOrEmpty(req.getDriverPhone());
    }

    /**
     * 查询司机缓存相关关系映射
     * 关系组 Pair-key:Map<drvId,vehicleId>
     *       Pair-value:drvId,List<TransportGroup>
     */
    private Pair<Map<Long, Long>, Map<Long, List<TransportGroupBasePO>>> queryRelationMap(QueryDriverFromCacheParamDTO req, Boolean onlyTransportMode) {
        // 通用模式
        if (!onlyTransportMode) {
            return new Pair<>(doDrvIdVehicleIdMap(req), null);
        }
        // 条件含运力组id请求司机缓存
        TransportMetric.queryCacheByTransportRecordInc();
        // 通过运力组id及可能存在的司机id去查询两者的关系
        HashMap<Long, List<TransportGroupBasePO>> drvTransportRelationMap = getGroupBasePOMap(Lists.newArrayList(BaseUtil.getLongSet(req.getDriverIds())), Lists.newArrayList(BaseUtil.getLongSet(req.getTransportGroupIds())));
        // 无交集或者无数据 - 跳出
        if (MapUtils.isEmpty(drvTransportRelationMap)) {
            throw new BizException(DriverCacheServiceV2.NO_RESULT_WITH_TRANSPORT_GROUP);
        }
        // 有效关系中的司机id集
        req.setDriverIds(Joiner.on(",").join(drvTransportRelationMap.keySet()));
        // 返回关系 Pair
        return new Pair<>(getDrvIdVehicleIdMap(req), drvTransportRelationMap);
    }

    private Map<Long, Long> doDrvIdVehicleIdMap(QueryDriverFromCacheParamDTO req) {
        if (!isSelectDriver(req)) {
            TransportMetric.queryCacheByTransportRecordInc();
            req.setDriverIds(Joiner.on(",").join(queryTransportDrvIdList(null, req.getTransportGroupIds())));
            return getDrvIdVehicleIdMap(req);
        }
        TransportMetric.drvCacheReqFocusDriverCounter.inc();
        Map<Long, Long> drvIdVehicleIdMap = getDrvIdVehicleIdMap(req);
        if (!Strings.isNullOrEmpty(req.getTransportGroupIds())) {
            List<Long> drvIdList = queryTransportDrvIdList(Lists.newArrayList(drvIdVehicleIdMap.keySet()), req.getTransportGroupIds());
            drvIdVehicleIdMap = Maps.filterKeys(drvIdVehicleIdMap, drvIdElement -> drvIdList.contains(drvIdElement));
            if (drvIdVehicleIdMap.isEmpty()) {
                throw new BizException(DriverCacheServiceV2.NO_RESULT_WITH_TRANSPORT_GROUP);
            }
        }
        return drvIdVehicleIdMap;
    }

    @Override
    public void clearDrvCache(Long drvId) throws InterruptedException {
        if (drvId == null || drvId <= 0) {
            return;
        }
        clearCache(DriverCacheServiceV2.DRIVER_V2_CACHE_PREFIX + drvId);
    }

    @Override
    public void clearDrvCacheImmediately(Long drvId) {
        if (drvId == null || drvId <= 0) {
            return;
        }
        clearCacheImmediately(DriverCacheServiceV2.DRIVER_V2_CACHE_PREFIX + drvId);
    }

    @Override
    public void clearVehCache(Long vehId) throws InterruptedException {
        if (vehId == null || vehId <= 0) {
            return;
        }
        clearCache(DriverCacheServiceV2.VEHICLE_V2_CACHE_PREFIX + vehId);
    }

    private void clearCache(String key) throws InterruptedException {
        for (int i = 0; i < tryChallengeCount; i++) {
            RedisUtils.remove(Sets.newHashSet(key));
            Thread.sleep(200);
            if (RedisUtils.get(key) == null) {
                return;
            }
        }

        TransportMetric.drvCacheChallengeFailureCounter.inc();
        logger.warn(DriverCacheAgent.DRV_CACHE_WARN_TITLE, "Challenge Fail key:{}", key);
    }

    private void clearCacheImmediately(String key) {
        RedisUtils.remove(Sets.newHashSet(key));
    }

    /**
     * 封装司机 车辆 运力组查询信息
     * @param driverDTOList
     * @param vehicleDTOMap
     * @param driverTransportRelationMap
     * @param drvIdVehicleIdMap
     * @return
     */
    public List<DriverInfo> packageData(List<DrvCacheDTO> driverDTOList, Map<Long, VehCacheDTO> vehicleDTOMap, Map<Long, List<TransportGroupBasePO>> driverTransportRelationMap, Map<Long, Long> drvIdVehicleIdMap,Map<Long,List<Long>> dispatchSupplierIdMap) {
        List<DriverInfo> res = Lists.newArrayListWithExpectedSize(driverDTOList.size());
        for (DrvCacheDTO drvCacheDTO : driverDTOList) {
            DriverInfo driverInfo = new DriverInfo();
            assembleDriverInfo(driverInfo, drvCacheDTO);
            assembleVehicleInfo(driverInfo, vehicleDTOMap.get(drvIdVehicleIdMap.get(drvCacheDTO.getDriverId())));
            List<TransportGroupBasePO> list = driverTransportRelationMap.get(driverInfo.getDriverId());
            assembleTransportGroupInfo(driverInfo, list);
            assemble(driverQueryService.calculateDrvCoopMode(driverInfo.getDriverId(), list), driverInfo);
            driverInfo.setDispatchSupplierIdList(MapUtils.isEmpty(dispatchSupplierIdMap)?Lists.newArrayList():dispatchSupplierIdMap.get(drvCacheDTO.getDriverId()));
            res.add(driverInfo);
        }
        return res;
    }

    protected void assemble(DrvInfoCacheDto cacheDto, DriverInfo info) {
        if (cacheDto == null) {
            return;
        }
        if (cacheDto.getBroadcast() != null) {
            info.setBroadcast(cacheDto.getBroadcast());
        }
        if (cacheDto.getCoopMode() != null) {
            info.setCoopMode(cacheDto.getCoopMode());
        }
        if (cacheDto.getIsSendWorkPeriod() != null) {
            info.setIsSendWorkPeriod(cacheDto.getIsSendWorkPeriod());
        }
        if (cacheDto.getCompatibleCoopMode() != null) {
            info.setCompatibleCoopMode(cacheDto.getCompatibleCoopMode());
        }
    }

    /**
     * 封装司机查询信息
     * @param driverInfo
     * @param drvCacheDTO
     */
    private void assembleDriverInfo(DriverInfo driverInfo, DrvCacheDTO drvCacheDTO) {
        driverInfo.setDriverId(drvCacheDTO.getDriverId());
        driverInfo.setDriverName(drvCacheDTO.getDriverName());
        driverInfo.setCarId(drvCacheDTO.getCarId());
        driverInfo.setIntendVehicleTypeId(drvCacheDTO.getIntendVehicleTypeId());
        driverInfo.setPhoneAreaCode(drvCacheDTO.getPhoneAreaCode());
        driverInfo.setDriverPhone(drvCacheDTO.getDriverPhone());
        driverInfo.setDriverLanguage(drvCacheDTO.getDriverLanguage());
        driverInfo.setWechat(drvCacheDTO.getWechat());
        driverInfo.setEmail(drvCacheDTO.getEmail());
        driverInfo.setCityId(drvCacheDTO.getCityId());
        driverInfo.setQunarCityCode(drvCacheDTO.getQunarCityCode());
        driverInfo.setCityName(drvCacheDTO.getCityName());
        driverInfo.setCountryId(drvCacheDTO.getCountryId());
        driverInfo.setCountryName(drvCacheDTO.getCountryName());
        driverInfo.setSupplierId(drvCacheDTO.getSupplierId());
        driverInfo.setSupplierName(drvCacheDTO.getSupplierName());
        driverInfo.setCoopMode(drvCacheDTO.getCoopMode());
        driverInfo.setStatus(drvCacheDTO.getStatus());
        driverInfo.setAddressLatitude(drvCacheDTO.getAddressLatitude());
        driverInfo.setAddressLongitude(drvCacheDTO.getAddressLongitude());
        driverInfo.setWorkTimes(drvCacheDTO.getWorkTimes());
        driverInfo.setCreateTime(drvCacheDTO.getCreateTime());
        driverInfo.setInternalScope(drvCacheDTO.getInternalScope());
        driverInfo.setPicUrl(drvCacheDTO.getPicUrl());
        driverInfo.setDrvProductionLineCodeList(drvCacheDTO.getDrvProductionLineCodeList());
        driverInfo.setRideHailingDrvCertValid(drvCacheDTO.getRideHailingDrvCertValid());
        driverInfo.setOnlineTime(drvCacheDTO.getOnlineTime());
        driverInfo.setVehBindTime(drvCacheDTO.getVehBindTime());
        driverInfo.setDrvIdcard(drvCacheDTO.getDrvIdcard());
        driverInfo.setCertiDate(drvCacheDTO.getCertiDate());
        driverInfo.setExpiryBeginDate(drvCacheDTO.getExpiryBeginDate());
        driverInfo.setExpiryEndDate(drvCacheDTO.getExpiryEndDate());
        driverInfo.setDrvLicenseNumber(drvCacheDTO.getDrvLicenseNumber());
        driverInfo.setDrvConnectAddress(drvCacheDTO.getDrvConnectAddress());
        driverInfo.setDrvcardImg(drvCacheDTO.getDrvcardImg());
        driverInfo.setNation(drvCacheDTO.getNation());
        driverInfo.setRaisingPickUp(drvCacheDTO.getRaisingPickUp());
        driverInfo.setChildSeat(drvCacheDTO.getChildSeat());
        driverInfo.setRealPicUrl(drvCacheDTO.getRealPicUrl());
        //网约车驾驶证号
        driverInfo.setDriverNetCertNo(drvCacheDTO.getDriverNetCertNo());
        driverInfo.setDrvTemporaryMark(drvCacheDTO.getTemporaryDispatchMark());
        driverInfo.setDrvTemporaryEndTime(drvCacheDTO.getTemporaryDispatchEndDatetime());
        driverInfo.setPaiayAccount(drvCacheDTO.getPaiayAccount());
        driverInfo.setPaiayEmail(drvCacheDTO.getPaiayEmail());
        driverInfo.setAccountType(drvCacheDTO.getAccountType());
        driverInfo.setPpmAccount(drvCacheDTO.getPpmAccount());
    }

    private void assembleVehicleInfo(DriverInfo driverInfo, VehCacheDTO vehCacheDTO) {
        if (vehCacheDTO == null) {
            driverInfo.setCarTypeId(0);
            return;
        }
        driverInfo.setCarLicense(vehCacheDTO.getCarLicense());
        driverInfo.setCarBrandId(vehCacheDTO.getCarBrandId());
        driverInfo.setCarBrandName(vehCacheDTO.getCarBrandName());
        driverInfo.setCarColorId(vehCacheDTO.getCarColorId());
        driverInfo.setCarColor(vehCacheDTO.getCarColor());
        driverInfo.setCarTypeId(vehCacheDTO.getCarTypeId());
        driverInfo.setCarTypeName(vehCacheDTO.getCarTypeName());
        driverInfo.setMaxLuggages(vehCacheDTO.getMaxLuggages());
        driverInfo.setMaxPassengers(vehCacheDTO.getMaxPassengers());
        driverInfo.setIsEnergy(vehCacheDTO.getIsEnergy());
        driverInfo.setCarSeriesId(vehCacheDTO.getCarSeriesId());
        driverInfo.setCarSeriesName(vehCacheDTO.getCarSeriesName());
        driverInfo.setVehProductionLineCodeList(vehCacheDTO.getVehProductionLineCodeList());
        driverInfo.setVehicleStatus(vehCacheDTO.getVehicleStatus());
        driverInfo.setRideHailingVehCertValid(vehCacheDTO.getRideHailingVehCertValid());
        driverInfo.setVin(vehCacheDTO.getVin());
        driverInfo.setVehRegstDate(vehCacheDTO.getVehRegstDate());
        driverInfo.setVehCreateTime(vehCacheDTO.getVehCreateTime());
        driverInfo.setVehicleFullImg(vehCacheDTO.getVehicleFullImg());
        //网约车驾驶证号
        driverInfo.setVehicleNetCertNo(vehCacheDTO.getVehicleNetCertNo());
        driverInfo.setVehTemporaryMark(vehCacheDTO.getTemporaryDispatchMark());
        driverInfo.setVehTemporaryEndTime(vehCacheDTO.getTemporaryDispatchEndDatetime());
        driverInfo.setOverAgeTime(vehCacheDTO.getOverAgeTime());
    }

    private void assembleTransportGroupInfo(DriverInfo driverInfo, List<TransportGroupBasePO> list) {
        if (CollectionUtils.isEmpty(list)) {
            driverInfo.setTransportGroups(Lists.newArrayList());
            return;
        }
        List<TransportGroup> transportGroupList = Lists.newArrayListWithCapacity(list.size());
        Set<String> filtration = Sets.newLinkedHashSetWithExpectedSize(list.size());
        for (TransportGroupBasePO relationPO : list) {
            if (!filtration.add(relationPO.getTransportGroupId() + "&" + relationPO.getDrvId())) {
                continue;
            }
            TransportGroup transportGroup = new TransportGroup();
            transportGroup.setApplyStatus(relationPO.getApplyStatus());
            transportGroup.setTransportGroupId(relationPO.getTransportGroupId());
            transportGroup.setTransportGroupMode(relationPO.getTransportGroupMode());
            transportGroup.setTransportGroupName(relationPO.getTransportGroupName());
            transportGroupList.add(transportGroup);
        }
        driverInfo.setTransportGroups(transportGroupList);
    }

    protected HashMap<Long, Long> getDrvIdVehicleIdMap(QueryDriverFromCacheParamDTO req) {
        QueryDriverConditionDTO condition = queryDriverConditionDTO(req);

        // 如果查询条件只有司机id，且手机号为空，则直接从缓存中取，然后内存过滤
        if (StringUtils.isNotEmpty(condition.getDriverIds())
          && StringUtil.isEmpty(condition.getDriverPhone())
          ) {
//            HashMap<Long, Long>  cacheResult = getFromCache(BaseUtil.getLongSet(req.getDriverIds()));
            // 仅过滤司机姓名，合作模式，状态，手机号有加解密处理(后面会变化)，遇到手机号不为空，则还走DB
            HashMap<Long, Long>  cacheResult = getFromCacheAndFilterByCondition(condition, BaseUtil.getLongSet(req.getDriverIds()));
            // 不为空则返回，为空则继续往下查询
            if (!cacheResult.isEmpty()) {
                return cacheResult;
            }
        }
        List<DrvDriverPO> poList = drvDrvierRepository.queryDrvIdAndVehicleIdByCondition(condition);
        if (CollectionUtils.isEmpty(poList)) {
            throw new BizException(DriverCacheServiceV2.NO_RESULT_WITH_DRIVER_DATA);
        }
        HashMap<Long, Long> drvIdVehicleIdMap = Maps.newHashMapWithExpectedSize(poList.size());
        for (DrvDriverPO driver : poList) {
            drvIdVehicleIdMap.put(driver.getDrvId(), driver.getVehicleId());
        }
        return drvIdVehicleIdMap;
    }

    /**
     * 仅过滤司机姓名，合作模式，状态，手机号有加解密处理(后面会变化)，遇到手机号不为空，则还走DB
     * @param req
     * @param longSet
     * @return
     */
    protected HashMap<Long, Long> getFromCacheAndFilterByCondition(QueryDriverConditionDTO req, Set<Long> longSet) {
        Set<Integer> coopModList = Optional.ofNullable(req.getCoopModes()).map(BaseUtil::getIntegerSet).orElse(Sets.newHashSet());
        return Optional.ofNullable(driverQueryService.queryDrvCacheList(longSet)).orElse(Lists.newArrayList()).stream().filter(item -> {
            if (StringUtils.isNotEmpty(req.getDriverName()) && !StringUtils.equals(item.getDriverName(), req.getDriverName())) {
                return false;
            }
            if (Objects.nonNull(req.getStatus()) && !Objects.equals(item.getStatus(), req.getStatus())) {
                return false;
            }
            if (!coopModList.isEmpty() && !coopModList.contains(item.getCoopMode())) {
              return false;
            }
            return true;
          })
          .collect(Collectors.toMap(DrvCacheDTO::getDriverId, DrvCacheDTO::getCarId,
            (existing, replacement) -> existing,
            HashMap::new));
    }

    private QueryDriverConditionDTO queryDriverConditionDTO(QueryDriverFromCacheParamDTO paramDTO){
        QueryDriverConditionDTO queryDriverConditionDTO = new QueryDriverConditionDTO();
        queryDriverConditionDTO.setDriverName(paramDTO.getDriverName());
        queryDriverConditionDTO.setDriverPhone(paramDTO.getDriverPhone());
        queryDriverConditionDTO.setDriverIds(paramDTO.getDriverIds());
        queryDriverConditionDTO.setStatus(paramDTO.getStatus());
        queryDriverConditionDTO.setCoopModes(paramDTO.getCoopModes());
        queryDriverConditionDTO.setTransportGroupIds(paramDTO.getTransportGroupIds());
        return queryDriverConditionDTO;
    }

    private List<Long> queryTransportDrvIdList(List<Long> drvIdList, String transportIdListStr) {
        return queryTransportDrvIdListNew(drvIdList,transportIdListStr);
    }

    public List<Long> queryTransportDrvIdListNew(List<Long> drvIdList, String transportIdListStr) {
        //先查出有效的运力组ID(已上线)
        List<TspTransportGroupPO> transportGroupPOS = transportGroupRepository.queryTspTransportBaseByIds(Lists.newArrayList(BaseUtil.getLongSet(transportIdListStr)));
        if(CollectionUtils.isEmpty(transportGroupPOS)){
            throw new BizException(DriverCacheServiceV2.NO_RESULT_WITH_TRANSPORT_GROUP);
        }
        List<Long> transportId = transportGroupPOS.stream().map(TspTransportGroupPO::getTransportGroupId).collect(Collectors.toList());
        List<Long> existDrvIdList = driverGroupRelationRepository.queryActiveDrvIdByTransportIdListNew(drvIdList,transportId);
        if(CollectionUtils.isEmpty(existDrvIdList)){
            throw new BizException(DriverCacheServiceV2.NO_RESULT_WITH_TRANSPORT_GROUP);
        }
        return existDrvIdList;
    }

}
