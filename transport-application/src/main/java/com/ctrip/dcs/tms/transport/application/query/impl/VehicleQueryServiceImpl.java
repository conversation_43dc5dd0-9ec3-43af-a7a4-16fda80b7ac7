package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.arch.distlock.DLock;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.dto.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.config.DistributedLockConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.monitoring.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.vehicle.domain.value.SceneVehicleModel;
import com.ctrip.igt.framework.common.base.PageHolder;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.concurrent.threadpool.CThreadPool;
import com.ctrip.igt.framework.common.math.RandomUtils;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.constant.*;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.fasterxml.jackson.core.type.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.*;

/**
 * <AUTHOR>
 * @Date 2020/3/17 15:04
 */
@Service
public class VehicleQueryServiceImpl implements VehicleQueryService {

    private static final String defaultKey = "default";

    @Autowired
    private VehicleRepository vehicleRepository;

    @Autowired
    private EnumRepository enumRepository;

    @Autowired
    private CertificateCheckQueryService checkQueryService;

    @Autowired
    private ProductionLineUtil productionLineUtil;

    @Autowired
    private TmsTransportQconfig tmsTransportQconfig;
    @Autowired
    private DrvDrvierRepository drvDrvierRepository;

    @Autowired
    private DistributedLockConfig distributedLockConfig;
    @Autowired
    DistributedLockConfig lockConfig;
    @Autowired
    private OverageQConfig overageQConfig;

    @Autowired
    private CommonConfig config;

    private static final Logger logger = LoggerFactory.getLogger(VehicleQueryServiceImpl.class);

    @Override
    public Result<Boolean> isVehicleLicenseUniqueness(Long vehicleId, String vehicleLicense) {
        String secret = SecretUtil.encrypt(vehicleLicense, CommonEnum.RecordTypeEnum.VEHICLE);
        if (vehicleRepository.isVehicleLicenseUniqueness(vehicleId, secret)) {
            return Result.Builder.<Boolean>newResult()
                    .success()
                    .withData(true)
                    .build();
        } else {
            return Result.Builder.<Boolean>newResult()
                    .fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportVehicleLicenseAlreadyExists))
                    .withData(false)
                    .build();
        }
    }

    @Override
    public Result<VehicleDetailDTO> queryVehicleDetail(Long vehicleId) {
        VehVehiclePO vehVehiclePO = vehicleRepository.queryByPk(vehicleId);
        if(vehVehiclePO == null) {
            return Result.Builder.<VehicleDetailDTO>newResult().success().withData(null).build();
        }
        //司机对应证件核验记录
        Map<Integer, TmsCertificateCheckPO> checkPOMap = checkQueryService.queryCertificateCheckToMap(vehicleId, TmsTransportConstant.CertificateCheckTypeEnum.VEHICLE.getCode());
        Map<Integer,String> vehicleStatusMap = enumRepository.getVehicleStatus();
        VehicleDetailDTO result = new VehicleDetailDTO();
        BeanUtils.copyProperties(vehVehiclePO, result);
        result.setVehicleAuditStatus(vehVehiclePO.getAuditStatus());
        result.setSupplierName(enumRepository.getSupplierName(result.getSupplierId()));
        result.setHasDrvValue(enumRepository.getHasDrvStr(vehVehiclePO.getHasDrv()));
        result.setVehicleBrandName(enumRepository.getBandName(result.getVehicleBrandId()));
        result.setVehicleSeriesName(enumRepository.getVehicleSeriesName(result.getVehicleSeries()));
        result.setVehicleTypeName(enumRepository.getVehicleTypeName(result.getVehicleTypeId()));
        result.setCityName(enumRepository.getCityName(result.getCityId()));
        result.setSupplierName(enumRepository.getSupplierName(result.getSupplierId()));
        result.setUsingNatureValue(enumRepository.getUsingNatureValue(result.getUsingNature()));
        result.setVehicleColorName(enumRepository.getColorName(result.getVehicleColorId()));
        result.setHasDrv(vehVehiclePO.getHasDrv() ? 1 : 0);
        result.setCreateUser(vehVehiclePO.getCreateUser());
        result.setVehicleEnergyType(vehVehiclePO.getVehicleEnergyType());
        result.setVehiclePowerModeValue(enumRepository.getVehicleEnergyTypeName(vehVehiclePO.getVehicleEnergyType()));
        result.setRegstDate(DateUtil.dateToString(vehVehiclePO.getRegstDate(),DateUtil.YYYYMMDD));
        result.setAreaScope(enumRepository.getAreaScope(vehVehiclePO.getCityId()));
        result.setDatachangeCreatetime(DateUtil.timestampToString(vehVehiclePO.getDatachangeCreatetime(),DateUtil.YYYYMMDDHHMMSS));
        result.setVehicleCertiData(CtripCommonUtils.resultSOADTO(checkPOMap.get(TmsTransportConstant.CertificateTypeEnum.CARCERTILICENSE.getCode())));
        CertificateResultSOADTO soadto = CtripCommonUtils.resultSOADTO(checkPOMap.get(TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT.getCode()));
        result.setNetTansCtfctData(soadto);
        result.setVehicleStatusName(vehicleStatusMap.get(result.getVehicleStatus()));
        result.setProLineList(productionLineUtil.getShowProductionLineList(vehVehiclePO.getCategorySynthesizeCode()));
        if (soadto != null) {
            result.setNetVehicleCheckStatus(soadto.getCheckStatus());
        }
        //车辆网约车证为空，默认核验不通过标识
        if(StringUtils.isEmpty(vehVehiclePO.getNetTansCtfctImg())){
            result.setNetVehicleCheckStatus(TmsTransportConstant.CheckStatusEnum.ERROR.getCode());
        }
        result.setVehicleLicenseCityName(enumRepository.getCityName(vehVehiclePO.getVehicleLicenseCityId()));
        if(StringUtils.isEmpty(vehVehiclePO.getVehicleCertiImg())){
            result.setVehicleCertiData(null);
        }
        if(StringUtils.isEmpty(vehVehiclePO.getNetTansCtfctImg()) && StringUtils.isEmpty(vehVehiclePO.getNetAppealMaterials())){
            result.setNetTansCtfctData(null);
        }
        result.setCertificateConfigStr(vehVehiclePO.getCertificateConfig());
        result.setBindingDrvId(queryBindingDrvId(vehicleId));
        //对于新增司机车辆：车辆入驻时候，判断“城市=上海”和“使用性质=租赁 或者 预约出租客运”两个字段，如果命中的话，按照现有流程，在司机上线前通知司机端加300合规
        if(verdictNetTansDefaultPass(vehVehiclePO.getCityId(),vehVehiclePO.getUsingNature())){
            result.setNetTansCtfctData(CtripCommonUtils.resultPassData());
        }
        //只显示境外车辆的标签
        if(enumRepository.getAreaScope(vehVehiclePO.getCityId()) == AreaScopeTypeEnum.OVERSEAS.getCode().intValue()){
            result.setCheckDataList(CtripCommonUtils.assembleCheckData(checkPOMap));
        }
        //返回车辆OCR结果
        if(StringUtils.isNotEmpty(vehVehiclePO.getOcrPassStatusJson())){
            result.setOcrPassStatusList(JsonUtil.fromJson(vehVehiclePO.getOcrPassStatusJson(), new TypeReference<List<OcrPassStatusModelSOA>>() {}));
        }
        result.setTemporaryDispatchEndDatetime(DateUtil.timestampToString(vehVehiclePO.getTemporaryDispatchEndDatetime(), DateUtil.YYYYMMDDHHMMSS));
        processoverageTime(result, vehVehiclePO);
        return Result.Builder.<VehicleDetailDTO>newResult().success().withData(result).build();
    }

    private void processoverageTime(VehicleDetailDTO result, VehVehiclePO vehVehiclePO) {
        try {
            if (result.getAreaScope() == 0) {
                List<Long> cityIdList = config.getCityIdList();
                if (!org.springframework.util.CollectionUtils.isEmpty(cityIdList) && BooleanUtils.isTrue(config.getOverageGraySwitch()) && !cityIdList.contains(vehVehiclePO.getCityId())) {
                    result.setOverAgeTime(null);
                    return;
                }
                LocalDateTime registDate;
                if (Objects.isNull(vehVehiclePO.getRegstDate()) || StringUtils.equals(Constant.DEFAULT_YEAR, vehVehiclePO.getRegstDate().toString())) {
                    Timestamp vehCreateTime = vehVehiclePO.getDatachangeCreatetime();
                    registDate = vehCreateTime.toLocalDateTime();
                } else {
                    LocalDate localDate = DateUtil.convertStringToDate(vehVehiclePO.getRegstDate().toString(), DateUtil.YYYYMMDD);
                    LocalTime localTime = LocalTime.of(23, 59, 59);  // 这里设置为12点30分0秒，可以按需修改时间
                    registDate = LocalDateTime.of(localDate, localTime);
                }
                OverageDTO overageMap = overageQConfig.getOverageMap(result.getCityId(), result.getVehicleTypeId()); // 配置的超龄信息
                LocalDateTime localDateTime = registDate.plusMonths((BigDecimal.valueOf(12L).multiply(BigDecimal.valueOf(overageMap.getOverage())).longValue()));
                result.setOverAgeTime(DateUtil.convertDateToString(localDateTime, DateUtil.YYYYMMDDHHMMSS));
            }
        } catch (Exception e) {
            logger.error("processoverageTime error", e);
        }
    }

    @Override
    public Result<Integer> queryVehicleCount(QueryVehicleSOARequestType queryVehicleRequestType) {
        return Result.Builder.<Integer>newResult()
                .success()
                .withData(vehicleRepository.queryVehicleCount(queryVehicleRequestType, productionLineUtil.getIncludeProductionLineList(queryVehicleRequestType.getProLineList())))
                .build();
    }

    @Override
    public Result<List<VehicleListSOADTO>> queryVehicleList(QueryVehicleSOARequestType queryVehicleRequestType) {
        return Result.Builder.<List<VehicleListSOADTO>>newResult()
                .success()
                .withData(vehicleRepository.queryVehicleList(queryVehicleRequestType, productionLineUtil.getIncludeProductionLineList(queryVehicleRequestType.getProLineList())))
                .build();
    }

    @Override
    public List<VehCacheDTO> queryVehCacheList(Set<Long> vehIdSet) {
        List<VehCacheDTO> vehicleCacheEntityList;
            vehIdSet.remove(0L);
        if (CollectionUtils.isEmpty(vehIdSet)) {
            return Collections.emptyList();
        }
        vehicleCacheEntityList = RedisUtils.mGet(BaseUtil.toCacheKeyList(DriverCacheServiceV2.VEHICLE_V2_CACHE_PREFIX, vehIdSet));

        if (vehicleCacheEntityList == null) {
            vehicleCacheEntityList = Lists.newArrayListWithExpectedSize(vehIdSet.size());
        }
        if (vehIdSet.size() == vehicleCacheEntityList.size()) {
            TransportMetric.cacheTimeHitCounter.inc();
            TransportMetric.drvCacheVehicleHitCounter.inc();
            logger.info("CacheRecordTitle","vehIdSet params:{} cacheValue:{}", JsonUtil.toJson(vehIdSet), JsonUtil.toJson(vehicleCacheEntityList));
            processOverAgeTime(vehicleCacheEntityList);
            return vehicleCacheEntityList;
        }
        TransportMetric.cacheTimeMissCounter.inc();
        TransportMetric.drvCacheVehicleMissCounter.inc();
        Set<Long> vehIdCacheList = vehicleCacheEntityList.stream().map(VehCacheDTO::getCarId).collect(Collectors.toSet());
        Set<Long> diffVehIdSet = Sets.filter(vehIdSet, vehIdElement -> !vehIdCacheList.contains(vehIdElement));
        List<VehVehiclePO> vehicleDBEntityList = vehicleRepository.queryVehInfo4Cache(diffVehIdSet);
        Map<Long, Map<Integer, Boolean>> certificateCheckMap = checkQueryService.getCertificateCheckMap(diffVehIdSet, TmsTransportConstant.CertificateCheckTypeEnum.VEHICLE, Lists.newArrayList(TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT.getCode()));
        List<VehCacheDTO> fromDBList = Lists.newArrayListWithExpectedSize(vehicleDBEntityList.size());
        for (VehVehiclePO vehiclePO : vehicleDBEntityList) {
            VehCacheDTO info = new VehCacheDTO();
            info.setCarId(vehiclePO.getVehicleId());
            info.setCarLicense(vehiclePO.getVehicleLicense());
            info.setCarBrandId(vehiclePO.getVehicleBrandId());
            info.setCarBrandName(enumRepository.getBandName(vehiclePO.getVehicleBrandId()));
            info.setCarColorId(vehiclePO.getVehicleColorId());
            info.setCarColor(enumRepository.getColorName(vehiclePO.getVehicleColorId()));
            info.setCarTypeId(vehiclePO.getVehicleTypeId() == null ? null : vehiclePO.getVehicleTypeId().intValue());
            info.setCarTypeName("");
            SceneVehicleModel vehicleModel = enumRepository.getVehicleType(vehiclePO.getVehicleTypeId());
            if (vehicleModel != null) {
                info.setCarTypeName(vehicleModel.getTranslationName());
                info.setMaxLuggages(vehicleModel.getLargeBaggageCount());
                info.setMaxPassengers(vehicleModel.getPassengerCount());
            }
            if (vehiclePO.getVehicleEnergyType() != null && vehiclePO.getVehicleEnergyType().intValue() == CommonEnum.EnergyEnum.CELL.getValue().intValue()) {
                info.setIsEnergy(CommonEnum.NewEnergyEnum.YES.getValue());
            } else {
                info.setIsEnergy(CommonEnum.NewEnergyEnum.NO.getValue());
            }
            info.setCarSeriesId(vehiclePO.getVehicleSeries());
            info.setCarSeriesName(enumRepository.getVehicleSeriesName(vehiclePO.getVehicleSeries()));
            info.setVehProductionLineCodeList(productionLineUtil.getShowProductionLineList(vehiclePO.getCategorySynthesizeCode()));
            info.setVehicleStatus(vehiclePO.getVehicleStatus() == null ? 0 : vehiclePO.getVehicleStatus());
            info.setVin(vehiclePO.getVin());
            info.setVehCreateTime(DateUtil.dateToString(vehiclePO.getDatachangeCreatetime(), DateUtil.YYYYMMDDHHMMSS));
            info.setVehRegstDate(DateUtil.dateToString(vehiclePO.getRegstDate(), DateUtil.YYYYMMDDHHMMSS));
            info.setRideHailingVehCertValid(certificateCheckMap.get(vehiclePO.getVehicleId()).getOrDefault(TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT.getCode(), false));
            info.setVehicleFullImg(vehiclePO.getVehicleFullImg());
            //网约车运输证号
            info.setVehicleNetCertNo(vehiclePO.getVehicleNetCertNo());
            info.setTemporaryDispatchMark(vehiclePO.getTemporaryDispatchMark());
            info.setTemporaryDispatchEndDatetime(DateUtil.timestampToString(vehiclePO.getTemporaryDispatchEndDatetime(), DateUtil.YYYYMMDDHHMMSS));
            info.setSupplierId(vehiclePO.getSupplierId());
            info.setCityId(vehiclePO.getCityId());
            info.setVehicleEnergyType(vehiclePO.getVehicleEnergyType());
            fromDBList.add(info);
        }
        logger.info("CacheRecordTitle","vehIdSet params:{} cacheValue:{} dbValue:{}", JsonUtil.toJson(vehIdSet), JsonUtil.toJson(vehicleCacheEntityList), JsonUtil.toJson(fromDBList));
        vehicleCacheEntityList.addAll(fromDBList);
        CThreadPool.pool(TransportThreadGroupConstant.minorFutureThreadPoolName).execute(() -> {supplementCache(fromDBList);});
        processOverAgeTime(vehicleCacheEntityList);
        return vehicleCacheEntityList;
    }

    protected void processOverAgeTime(List<VehCacheDTO> vehicleCacheEntityList) {
        try {
            if (CollectionUtils.isEmpty(vehicleCacheEntityList)) {
                return;
            }
            List<VehCacheDTO> vehCacheDTOS = vehicleCacheEntityList.stream().filter(Objects::nonNull).filter(item -> Objects.nonNull(item.getCityId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(vehCacheDTOS)) {
                return;
            }
            if (config.getOverageGraySwitch()) {
                vehCacheDTOS = vehCacheDTOS.stream().filter(item -> CollectionUtils.isNotEmpty(config.getCityIdList()) && config.getCityIdList().contains(item.getCityId())).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(vehCacheDTOS)) {
                return;
            }
            List<Long> cityIdList = vehCacheDTOS.stream().map(VehCacheDTO::getCityId).distinct().collect(Collectors.toList());

            List<VehicleAge> vehicleAges = getVehicleAges(vehicleCacheEntityList, cityIdList);
            if (vehicleAges == null) return;

            Map<Long, VehicleAge> collect = vehicleAges.stream().collect(Collectors.toMap(VehicleAge::getVehicleId, vehicleAge -> vehicleAge, (k1, k2) -> k1));

            vehicleCacheEntityList.forEach(item ->{
                if (collect.containsKey(item.getCarId())) {
                    item.setOverAgeTime(collect.get(item.getCarId()).getOverAgeTime());
                }
            });
        } catch (Exception e) {
            logger.error("processOverAgeTime error", e);
        }
    }

    private List<VehicleAge> getVehicleAges(List<VehCacheDTO> vehicleCacheEntityList, List<Long> cityIdList) {

        List<VehicleAge> vehicleAges = getVehicleAges(vehicleCacheEntityList);
        extracted(cityIdList, vehicleAges);
        return vehicleAges;
    }

    private static List<VehicleAge> getVehicleAges(List<VehCacheDTO> vehicleCacheEntityList) {
        List<VehicleAge> vehicleAges = vehicleCacheEntityList.stream().map(vehCacheDTO -> {
            VehicleAge vehicleAge = new VehicleAge();
            vehicleAge.setVehicleId(vehCacheDTO.getCarId());
            vehicleAge.setCityId(vehCacheDTO.getCityId());
            vehicleAge.setCarTypeId(vehCacheDTO.getCarTypeId());
            vehicleAge.setVehCreateTime(vehCacheDTO.getVehCreateTime());
            LocalDateTime localDateTime = DateUtil.convertStringToDateTime(vehCacheDTO.getVehRegstDate(), DateUtil.YYYYMMDDHHMMSS);
            vehicleAge.setVehRegstDate(localDateTime.toLocalDate().toString());
            return vehicleAge;
        }).collect(Collectors.toList());
        return vehicleAges;
    }

    private void extracted(List<Long> cityIdList, List<VehicleAge> vehicleAges) {
        List<City> cities = enumRepository.queryByCityIds(cityIdList);
        if (CollectionUtils.isEmpty(cities)) {
            return;
        }
        Map<Long, City> cityMap = cities.stream().collect(Collectors.toMap(City::getId, a -> a, (k1, k2) -> k1));
        for (VehicleAge vehCacheDTO : vehicleAges) {
            if (cityMap.containsKey(vehCacheDTO.getCityId())) {
                City city = cityMap.get(vehCacheDTO.getCityId());
                if (city.isChineseMainland()) {
                    OverageDTO overageMap = overageQConfig.getOverageMap(city.getId(), Long.valueOf(vehCacheDTO.getCarTypeId()));
                    String vehRegstDate = vehCacheDTO.getVehRegstDate();
                    LocalDateTime registDate;
                     // 魔法值
                    if (StringUtils.equals(Constant.DEFAULT_YEAR, vehRegstDate)) {
                        String vehCreateTime = vehCacheDTO.getVehCreateTime();
                        registDate = DateUtil.convertStringToDateTime(vehCreateTime, DateUtil.YYYYMMDDHHMMSS);
                    } else {
                        LocalDate localDate = DateUtil.convertStringToDate(vehRegstDate, DateUtil.YYYYMMDD);
                        LocalTime localTime = LocalTime.of(23, 59, 59);
                        registDate = LocalDateTime.of(localDate, localTime);
                    }
                    BigDecimal multiply = BigDecimal.valueOf(overageMap.getOverage()).multiply(BigDecimal.valueOf(12L));
                    LocalDateTime localDateTime = registDate.plusMonths(multiply.intValue());
                    vehCacheDTO.setOverAgeTime(DateUtil.convertDateToString(localDateTime, DateUtil.YYYYMMDDHHMMSS));
                }
            }
        }
    }

    private void supplementCache(List<VehCacheDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }
        Random random = new Random(1);
        for (VehCacheDTO cacheDTO : dtoList) {
            String key = DriverCacheServiceV2.VEHICLE_V2_CACHE_PREFIX + cacheDTO.getCarId();
            //redis存储
            DLock lock  = lockConfig.getDistributedLockVehCache(cacheDTO.getCarId());
            boolean locked = false;
            try {
                if (lock != null && (locked = lock.tryLock(DistributedLockConfig.PROCESSING_TIME, TimeUnit.SECONDS))) {
                    RedisUtils.set(key,RedisUtils.SIX_HOUR + random.nextInt(100),cacheDTO);
                }
            }catch (Exception e){

            }finally {
                if (locked) {
                    lock.unlock();
                }
            }
        }
    }

    @Override
    public Result<Integer> queryVehicleAccessYear(Long cityId,Long vehicleTypeId) {

        String vehicleAccessYear =  tmsTransportQconfig.getVehicleAccessYearThreshold();
        //新增车型查询配置
        if(vehicleTypeId!=null && vehicleTypeId > 0){
            return queryVehicleAccessYearByVehicleType(cityId,vehicleTypeId);
        }
        if(StringUtils.isEmpty(vehicleAccessYear) || cityId == null || cityId <= 0){
            Result.Builder.<Integer>newResult().success().withData(5).build();
        }
        Map<String,Integer> vehicleAccessYearMap =  JsonUtil.fromJson(vehicleAccessYear, new TypeReference<Map<String,Integer>>() { });
        if(MapUtils.isEmpty(vehicleAccessYearMap)){
            Result.Builder.<Integer>newResult().success().withData(5).build();
        }
        Integer resultData = vehicleAccessYearMap.get("default");
        for(Map.Entry<String,Integer> entry : vehicleAccessYearMap.entrySet()){
            String entryKey = entry.getKey();
            Integer entryV = entry.getValue();
            if(Objects.equals(entryKey,"default")){
                continue;
            }
            List<Long> cityIdList = Stream.of(entryKey.split(",")).map(Long::valueOf).collect(Collectors.toList());
            if(cityIdList.contains(cityId)){
                resultData = entryV;
                break;
            }
        }
        return Result.Builder.<Integer>newResult().success().withData(resultData).build();
    }

    public Result<Integer> queryVehicleAccessYearByVehicleType(Long cityId,Long vehicleTypeId) {
        //{"121":{"default":5,"1,2,3,4":8},"default":{"default":5},"117":{"default":5,"1,2,3,4":8}}
        try {
            String vehicleAccessYear =  tmsTransportQconfig.getVehicleAccessYearNewThreshold();
            if(StringUtils.isEmpty(vehicleAccessYear) || cityId == null || cityId <= 0){
                Result.Builder.<Integer>newResult().success().withData(5).build();
            }

            Map<String,Map<String,Integer>> vehicleAccessYearMap =  JsonUtil.fromJson(vehicleAccessYear, new TypeReference<Map<String,Map<String,Integer>>>() { });
            if(MapUtils.isEmpty(vehicleAccessYearMap)){
                Result.Builder.<Integer>newResult().success().withData(5).build();
            }

            //获取默认配置值
            Map<String,Integer> resultMap = vehicleAccessYearMap.get(String.valueOf(vehicleTypeId));
            if(MapUtils.isEmpty(resultMap)){
                resultMap = vehicleAccessYearMap.get(defaultKey);
                return Result.Builder.<Integer>newResult().success().withData(resultMap.get(defaultKey)).build();
            }

            //遍历车型中配置的值{"default":5,"1,2,3,4":8}
            Integer resultData = resultMap.get(defaultKey);
            for(Map.Entry<String,Integer> entry : resultMap.entrySet()){
                String entryKey = entry.getKey();
                Integer entryV = entry.getValue();
                //设置一个默认的值，如果对应的车型中没有配置的城市，则取车型中默认值
                if(Objects.equals(entryKey,defaultKey)){
                    continue;
                }
                List<Long> cityIdList = Stream.of(entryKey.split(",")).map(Long::valueOf).collect(Collectors.toList());
                if(cityIdList.contains(cityId)){
                    return Result.Builder.<Integer>newResult().success().withData(entryV).build();
                }
                return Result.Builder.<Integer>newResult().success().withData(resultData).build();
            }
        }catch (Exception e){
            logger.error("queryVehicleAccessYearByVehicleTypeError","cityId:{},vehicleTypeId:{},e:{}",cityId,vehicleTypeId,e);
        }
        return Result.Builder.<Integer>newResult().success().withData(5).build();
    }

    @Override
    public Long queryVehicleId(String vehicleNo) {
        return vehicleRepository.queryOnlineVehicleId(vehicleNo);
    }

    @Override
    public VehCacheDTO queryRandomVehicle(Long supplierId,Long cityId,boolean conformanceFlag) {
        //查询司机数据
        int sum = vehicleRepository.queryRandomVehicleIdCount(supplierId,cityId);
        if(sum == 0){
            return null;
        }
        int pageSize = 1000;
        int pageCount = sum%pageSize>0?sum/pageSize+1:sum/pageSize;
        for(int i=0;i<pageCount;i++){
            int page = i+1;
            VehCacheDTO vehCacheDTO = queryRandomVehicle(supplierId,cityId,conformanceFlag,page,pageSize);
            if(vehCacheDTO != null){
                return vehCacheDTO;
            }
        }
        return null;
    }

    @Override
    public SimpleVehicleInfoDTO querySimpleVehicleInfo(Long vehicleId) {
        VehVehiclePO vehVehiclePO = vehicleRepository.queryByPk(vehicleId);
        if(vehVehiclePO == null){
            return null;
        }
        SimpleVehicleInfoDTO simpleVehicleInfoDTO = new SimpleVehicleInfoDTO();
        simpleVehicleInfoDTO.setCityId(vehVehiclePO.getCityId());
        simpleVehicleInfoDTO.setVehicleId(vehVehiclePO.getVehicleId());
        simpleVehicleInfoDTO.setVehicleLicense(vehVehiclePO.getVehicleLicense());
        return simpleVehicleInfoDTO;
    }

    @Override
    public Result<QueryVehRecognitionStatusResponseType> queryVehRecognitionStatus(String vehicleLicense) {
        try {
            QueryVehRecognitionStatusResponseType responseType = new QueryVehRecognitionStatusResponseType();
            List<VehVehiclePO> vehVehiclePOList = vehicleRepository.queryVehByVehicleLicense(vehicleLicense);
            if(CollectionUtils.isEmpty(vehVehiclePOList)){
                return Result.Builder.<QueryVehRecognitionStatusResponseType>newResult().success().withData(responseType).build();
            }
            VehVehiclePO vehVehiclePO = vehVehiclePOList.get(0);
            if(Objects.isNull(vehVehiclePO)){
                return Result.Builder.<QueryVehRecognitionStatusResponseType>newResult().success().withData(responseType).build();
            }
            responseType.setVehicleFullImg(vehVehiclePO.getVehicleFullImg());
            //查询当前车辆的标签结果值
            Map<Integer, TmsCertificateCheckPO> checkPOMap = checkQueryService.queryCertificateCheckToMap(vehVehiclePO.getVehicleId(), TmsTransportConstant.CertificateCheckTypeEnum.VEHICLE.getCode());
            String ocrPassStatusJson = vehVehiclePO.getOcrPassStatusJson();
            //如果无系统OCR结果，则查询人工标签
            if(StringUtils.isEmpty(ocrPassStatusJson)){
                if(MapUtils.isNotEmpty(checkPOMap)){
                    TmsCertificateCheckPO vehLicensecheck =  checkPOMap.get(placeCertificateType(TmsTransportConstant.ApproveItemEnum.vehicle_number.getCode()));
                    TmsCertificateCheckPO vehicleColorcheck =  checkPOMap.get(placeCertificateType(TmsTransportConstant.ApproveItemEnum.vehicle_color.getCode()));
                    responseType.setVehLicenseRecognitionStatus(Objects.isNull(vehLicensecheck) ? null : Objects.equals(TmsTransportConstant.CheckStatusEnum.THROUGH.getCode(), vehLicensecheck.getCheckStatus()) ? Boolean.TRUE : Boolean.FALSE);
                    // todo 颜色的审核项就是没有的
                    responseType.setVehColorRecognitionStatus(Objects.isNull(vehicleColorcheck) ? null : Objects.equals(TmsTransportConstant.CheckStatusEnum.THROUGH.getCode(), vehicleColorcheck.getCheckStatus()) ? Boolean.TRUE : Boolean.FALSE);
                }
                return Result.Builder.<QueryVehRecognitionStatusResponseType>newResult().success().withData(responseType).build();
            }

            //[{"ocrId":6,"ocrItem":104,"passStatus":0},{"ocrId":6,"ocrItem":105,"passStatus":0}]
            List<OcrPassStatusModelSOA> ocrPassStatusModelSOAList = JsonUtil.fromJson(ocrPassStatusJson, new TypeReference<List<OcrPassStatusModelSOA>>() {
            });
            if(CollectionUtils.isNotEmpty(ocrPassStatusModelSOAList)){
                for(OcrPassStatusModelSOA ocrPass : ocrPassStatusModelSOAList){
                    if(Objects.isNull(ocrPass)){
                        continue;
                    }
                    //判断车辆系统OCR识别结果
                    Boolean passFlag = Objects.equals(ocrPass.getPassStatus(), TmsTransportConstant.OverseasOCRPassStatusEnum.pass.getCode()) ? Boolean.TRUE : Boolean.FALSE;
                    //系统OCR结果为不通过，则判断人工打标签是否为通过
                    if(!passFlag && MapUtils.isNotEmpty(checkPOMap)){
                        TmsCertificateCheckPO checkPO =  checkPOMap.get(placeCertificateType(ocrPass.getOcrItem()));
                        //如果人工标签为空，则说明人工没有打过标签，则直接返回不通过值,如果人工标签为通过，则返回人工审核状态
                        passFlag = checkPO == null ? Boolean.FALSE : Objects.equals(TmsTransportConstant.CheckStatusEnum.THROUGH.getCode(), checkPO.getCheckStatus()) ? Boolean.TRUE : Boolean.FALSE;
                    }
                    if(Objects.equals(ocrPass.getOcrItem(), TmsTransportConstant.ApproveItemEnum.vehicle_number.getCode())){
                        responseType.setVehLicenseRecognitionStatus(passFlag);
                    }
                    if(Objects.equals(ocrPass.getOcrItem(), TmsTransportConstant.ApproveItemEnum.vehicle_color.getCode())){
                        responseType.setVehColorRecognitionStatus(passFlag);
                    }
                }
            }
            return Result.Builder.<QueryVehRecognitionStatusResponseType>newResult().success().withData(responseType).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Result<PageHolder<VehicleListSOADTO>> queryDiscardVehList(QueryDiscardVehicleListSOARequestType requestType) {
        PageHolder pageHolder = null;
        try {
            int count = vehicleRepository.countDiscardVeh(requestType);
            if (count == 0) {
                pageHolder = PageHolder.of(new ArrayList<>()).pageIndex(requestType.getPaginator().getPageNo()).pageSize(requestType.getPaginator().getPageSize()).totalSize(count).build();
                return Result.Builder.<PageHolder<VehicleListSOADTO>>newResult().success().withData(pageHolder).build();
            }
            List<VehVehiclePO> vehVehiclePOList = vehicleRepository.queryDiscardVeh(requestType);
            if (CollectionUtils.isEmpty(vehVehiclePOList)) {
                pageHolder = PageHolder.of(new ArrayList<>()).pageIndex(requestType.getPaginator().getPageNo()).pageSize(requestType.getPaginator().getPageSize()).totalSize(count).build();
                return Result.Builder.<PageHolder<VehicleListSOADTO>>newResult().success().withData(pageHolder).build();
            }

            Map<Integer,String> vehicleStatusMap = enumRepository.getVehicleStatus();
            List<VehicleListSOADTO> result = Lists.newArrayList();
            for (VehVehiclePO vehiclePO : vehVehiclePOList) {
                VehicleListSOADTO tmp = new VehicleListSOADTO();
                BeanUtils.copyProperties(vehiclePO, tmp);
                tmp.setVehicleLicense(SecretUtil.decode(tmp.getVehicleLicense(), CommonEnum.RecordTypeEnum.VEHICLE));
                tmp.setHasDrv(vehiclePO.getHasDrv() ? 1 : 0);
                tmp.setHasDrvValue(enumRepository.getHasDrvStr(vehiclePO.getHasDrv()));
                tmp.setVehicleSeriesId(vehiclePO.getVehicleSeries());
                tmp.setCityName(enumRepository.getCityName(tmp.getCityId()));
                tmp.setCreateUser(vehiclePO.getCreateUser());
                tmp.setModifyUser(vehiclePO.getModifyUser());
                tmp.setSupplierName(enumRepository.getSupplierName(tmp.getSupplierId()));
                tmp.setVehicleTypeName(enumRepository.getVehicleTypeName(tmp.getVehicleTypeId()));
                tmp.setVehicleBrandName(enumRepository.getBandName(tmp.getVehicleBrandId()));
                tmp.setVehicleColorName(enumRepository.getColorName(tmp.getVehicleColorId()));
                tmp.setVehicleSeriesName(enumRepository.getVehicleSeriesName(tmp.getVehicleSeriesId()));
                tmp.setVehicleStatus(vehiclePO.getVehicleStatus());
                tmp.setVehicleStatusName(vehicleStatusMap.get(vehiclePO.getVehicleStatus()));
                tmp.setProLineName(productionLineUtil.getProductionLineNames(vehiclePO.getCategorySynthesizeCode()));
                tmp.setAreaScope(enumRepository.getAreaScope(tmp.getCityId()));
                result.add(tmp);
            }
            pageHolder = PageHolder.of(result).pageIndex(requestType.getPaginator().getPageNo()).pageSize(requestType.getPaginator().getPageSize()).totalSize(count).build();
            return Result.Builder.<PageHolder<DrvLeaveDetailPO>>newResult().success().withData(pageHolder).build();
        } catch (Exception e) {
            logger.error("queryDiscardVehListERROR","params:{},e:{}", JsonUtil.toJson(requestType),e);
        }
        return Result.Builder.<PageHolder<DrvLeaveDetailPO>>newResult().fail().withData(pageHolder).build();
    }

    public Integer placeCertificateType(Integer approveItemEnum){
        switch (approveItemEnum){
            case 104: return TmsTransportConstant.CertificateTypeEnum.VEHICLELICENSE.getCode();
            case 105:return TmsTransportConstant.CertificateTypeEnum.VEHICLECOLORID.getCode();
        }
        return approveItemEnum;
    }

    /**
     * 查询分页数据
     * @param supplierId
     * @param cityId
     * @param conformanceFlag
     * @param page
     * @param pageSize
     * @return
     */
    private VehCacheDTO queryRandomVehicle(Long supplierId,Long cityId,boolean conformanceFlag,int page,int pageSize){
        List<VehVehiclePO> vehVehiclePOS = vehicleRepository.queryRandomVehicleId(supplierId,cityId,page,pageSize);
        if(org.springframework.util.CollectionUtils.isEmpty(vehVehiclePOS)){
            return null;
        }
        //有合规要求选择一个合规车辆
        if(conformanceFlag){
            return queryConformanceVehicle(vehVehiclePOS);
        }
        //没有合规要求 随机选择一个车辆
        int randomIndex = RandomUtils.nextInt(0,vehVehiclePOS.size()-1);
        VehVehiclePO vehVehiclePO =  vehVehiclePOS.get(randomIndex);
        List<VehCacheDTO> vehCacheDTOS = queryVehCacheList(Sets.newHashSet(vehVehiclePO.getVehicleId()));
        if(org.springframework.util.CollectionUtils.isEmpty(vehCacheDTOS)){
            return null;
        }
        return vehCacheDTOS.get(0);
    }

    /**
     * 随机选择一个合规的车辆
     * @param vehVehiclePOS
     * @return
     */
    private VehCacheDTO queryConformanceVehicle(List<VehVehiclePO> vehVehiclePOS){
        //查询合规车辆数据
        List<Long> vehicleIds = vehVehiclePOS.stream().map(vehVehiclePO->vehVehiclePO.getVehicleId()).collect(Collectors.toList());
        List<VehCacheDTO> vehCacheDTOS = queryVehCacheList(Sets.newHashSet(vehicleIds));
        //过滤合规数据
        List<VehCacheDTO> vehCacheDTOList = new ArrayList<>();
        for (VehCacheDTO vehCacheDTO : vehCacheDTOS) {
            //是否合规需要一个参数
            if(vehCacheDTO.getRideHailingVehCertValid()){
                vehCacheDTOList.add(vehCacheDTO);
                continue;
            }
        }
        if(org.springframework.util.CollectionUtils.isEmpty(vehCacheDTOList)){
            return null;
        }
        //随机选择一个车辆
        int randomIndex = RandomUtils.nextInt(0,vehCacheDTOList.size()-1);
        return vehCacheDTOList.get(randomIndex);
    }

    public Long queryBindingDrvId(Long vehicleId){
        if(vehicleId == null || vehicleId <= 0){
            return 0L;
        }
        List<DrvDriverPO> driverPOS = drvDrvierRepository.queryDrvByVehicleIds(Arrays.asList(vehicleId));
        if(CollectionUtils.isEmpty(driverPOS)){
            return 0L;
        }
        return driverPOS.get(0).getDrvId();

    }

    //判断该车是否命中城市+使用性质
    public Boolean verdictNetTansDefaultPass(Long cityId,Integer usingNature){
        if(cityId == null || usingNature == null){
            return Boolean.FALSE;
        }
        try {
        //{"3":"1,2,3,-1","5":"1,2,3,-1"}
            String config =  tmsTransportQconfig.getVehicleDefaultPassNetData();
            if(StringUtils.isEmpty(config)){
                return Boolean.FALSE;
            }
            Map<String,String> configMap = JsonUtil.fromJson(config, new TypeReference<Map<String, String>>() {
            });
            if(MapUtils.isEmpty(configMap)){
                return Boolean.FALSE;
            }
            for(Map.Entry<String,String> entry:configMap.entrySet()){
                Integer usingNatureKey = Integer.parseInt(entry.getKey());
                String cityV = entry.getValue();
                //如果该车辆命中性质并且在配置城市中(-1：表示所有城市)，则返回通过
                if(Objects.equals(usingNature,usingNatureKey) && (BaseUtil.getLongSet(cityV).contains(cityId) || BaseUtil.getLongSet(cityV).contains(TmsTransportConstant.allData))){
                    return Boolean.TRUE;
                }
            }
            return Boolean.FALSE;
        }catch (Exception e){
            logger.error("verdictNetTansDefaultPassError","params:cityId:{},usingNature:{},e:{}",cityId,usingNature,e);
        }
        return Boolean.FALSE;
    }
}
