package com.ctrip.dcs.tms.transport.application.dto;

import java.util.List;

public class TransportGroupInOrderConfigDTO {
    /**
     * 国家码
     */
    private Long countryId;

    /**
     * 城市Id
     */
    private Long cityId;

    /**
     * 点位code
     */
    private String locationCode;

    /**
     * 运力组ID
     */
    private Long transportGroupId;

    /**
     * 点位类型(1.机场，2.火车站)
     */
    private Integer locationType;

    /**
     * 产线（商品类别分组1接送机站2打车4包车8其他）
     */
    private Integer productLine;
    /**
     * 供应商id
     */
    private Long supplierId;
    /**
     * 时段配置
     */
    List<TransportGroupTimeSegmentConfigDTO> timeSegmentConfigDTOList;
    /**
     * 全天库存上限包含未命中时段和已命中时段
     */
    private Integer totalOrderCount;
    /**
     * 运力组进单时间
     */
    private Integer takeOrderLimitTime;

    public Long getCountryId() {
        return countryId;
    }

    public void setCountryId(Long countryId) {
        this.countryId = countryId;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public String getLocationCode() {
        return locationCode;
    }

    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }

    public Long getTransportGroupId() {
        return transportGroupId;
    }

    public void setTransportGroupId(Long transportGroupId) {
        this.transportGroupId = transportGroupId;
    }

    public Integer getLocationType() {
        return locationType;
    }

    public void setLocationType(Integer locationType) {
        this.locationType = locationType;
    }

    public List<TransportGroupTimeSegmentConfigDTO> getTimeSegmentConfigDTOList() {
        return timeSegmentConfigDTOList;
    }

    public void setTimeSegmentConfigDTOList(List<TransportGroupTimeSegmentConfigDTO> timeSegmentConfigDTOList) {
        this.timeSegmentConfigDTOList = timeSegmentConfigDTOList;
    }

    public Integer getProductLine() {
        return productLine;
    }

    public void setProductLine(Integer productLine) {
        this.productLine = productLine;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Integer getTotalOrderCount() {
        return totalOrderCount;
    }

    public void setTotalOrderCount(Integer totalOrderCount) {
        this.totalOrderCount = totalOrderCount;
    }

    public Integer getTakeOrderLimitTime() {
        return takeOrderLimitTime;
    }

    public void setTakeOrderLimitTime(Integer takeOrderLimitTime) {
        this.takeOrderLimitTime = takeOrderLimitTime;
    }
}
