package com.ctrip.dcs.tms.transport.application.command;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.common.result.*;

/**
 * 司机请假命令类接口
 * <AUTHOR>
 * @Date 2020/3/17 15:06
 */
public interface DriverLeaveCommandService {

    /**
     * 新增司机请假
     * @param driverLeavePO
     * @return
     */
    Result<Boolean> addDrvLeave(DrvDriverLeavePO driverLeavePO);

    /**
     * 司机销假
     * @param driverLeavePO
     * @return
     */
    Result<Boolean> closeDrvLeave(DrvDriverLeavePO driverLeavePO);
}
