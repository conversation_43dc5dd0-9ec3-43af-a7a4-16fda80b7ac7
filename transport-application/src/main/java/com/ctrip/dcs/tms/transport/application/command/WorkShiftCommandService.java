package com.ctrip.dcs.tms.transport.application.command;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.common.result.*;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2020/11/26 15:28
 */
public interface WorkShiftCommandService {

    /**
     * 新增工作班次
     * @param workShiftPOList
     * @return
     */
    Result<Integer> addWorkShiftDetail(List<TspTransportGroupWorkShiftPO> workShiftPOList);

    /**
     * 更新工作班次信息
     * @param workShiftPOList
     * @return
     */
    Result<Integer> updateWorkShiftDetail(List<TspTransportGroupWorkShiftPO> workShiftPOList);

}
