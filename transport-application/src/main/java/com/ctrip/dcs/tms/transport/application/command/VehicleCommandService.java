package com.ctrip.dcs.tms.transport.application.command;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehVehiclePO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.VehicleUpdateStatusDTO;
import com.ctrip.igt.framework.common.result.*;

/**
 * 车辆命令类接口
 * <AUTHOR>
 * @Date 2020/3/17 15:05
 */
public interface VehicleCommandService {

    Result<Integer> addVehicle(VehicleAddSOARequestType vehicleAddSOARequestType, boolean hasDrv);

    Result<Boolean> updateVehicle(VehicleUpdateSOARequestType vehicleUpdateSOARequestType);

    Result<Boolean> updateVehicleStatus(VehicleUpdateStatusSOARequestType vehicleUpdateStatusSOARequestType);

    Result<Long> temporaryDispatchVehicleAdd(TemporaryDispatchVehicleAddSOARequestType requestType);

    Boolean  temToOfficialSendQmq(Long vehicleId,String vehicleLicense,String modifyUser);
    Result<Long>  generateGlobalId(String vehicleLicense, String source);
    void offlineVehicles(VehicleUpdateStatusDTO requestType);

    void updateVehicleAgeType(VehVehiclePO vehicle, String code, String system);
}
