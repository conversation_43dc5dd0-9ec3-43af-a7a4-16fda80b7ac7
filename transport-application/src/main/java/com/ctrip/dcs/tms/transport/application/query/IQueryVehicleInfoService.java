package com.ctrip.dcs.tms.transport.application.query;

import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.igt.framework.common.exception.*;

public interface IQueryVehicleInfoService {
    public VehCacheDTO queryVehicleById(Long vehicleId)throws BizException;
    public VehCacheDTO queryVehicleByVehicleNo(String vehicleNo)throws BizException;
    public VehCacheDTO queryRandomVehicle(String driverId,boolean conformanceFlag)throws BizException;
}
