package com.ctrip.dcs.tms.transport.task.application.type;

import com.ctrip.dcs.tms.transport.api.model.IsNeedCreateTaskRequestType;
import com.ctrip.dcs.tms.transport.api.model.SaveIsNeedCreateTaskRequestType;
import com.ctrip.dcs.tms.transport.task.infrastructure.common.enums.NeedCreateTaskEnum;
import com.ctrip.dcs.tms.transport.task.infrastructure.common.enums.TaskTypeEnum;
import com.ctrip.igt.framework.common.result.Result;

/**
 * <AUTHOR>
 */
public interface TaskService {

  Result<String> queryTaskConfig(String taskConfigParam);

  Result<NeedCreateTaskEnum> isNeedCreateTask(IsNeedCreateTaskRequestType requestType);

  Result<Boolean> saveIsNeedCreateTask(SaveIsNeedCreateTaskRequestType requestType);

  boolean match(TaskTypeEnum taskTypeEnum);

}
