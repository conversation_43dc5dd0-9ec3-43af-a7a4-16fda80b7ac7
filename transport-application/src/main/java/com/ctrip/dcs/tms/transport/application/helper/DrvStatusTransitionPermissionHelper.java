package com.ctrip.dcs.tms.transport.application.helper;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TmsDrvFreezePO;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CommonEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ErrorCodeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.DriverStatusTransitionPermissionQConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.SharkUtils;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsDrvFreezeRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.result.Result;
import com.dianping.cat.Cat;
import com.dianping.cat.utils.StringUtils;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 司机状态流转规则校验
 * http://conf.ctripcorp.com/pages/viewpage.action?pageId=**********
 * <AUTHOR>
 * @date 2025/4/29
 */
@Service
public class DrvStatusTransitionPermissionHelper {

  private static final Logger logger = LoggerFactory.getLogger(DrvStatusTransitionPermissionHelper.class);

  @Autowired
  TmsDrvFreezeRepository freezeRepository;

  @Autowired
  DrvDrvierRepository drvDrvierRepository;

  @Autowired
  DriverStatusTransitionPermissionQConfig drvStatusPConfig;

  //验证司机状态流转权限
  public Result<Boolean> checkPermission(String targetStatus, Long drvId, Integer accountType) {
    if (isBackendRequest(accountType)) {
      return createSuccessResult();
    }
    DrvDriverPO drvDriverPo = drvDrvierRepository.queryByPk(drvId);
    if (isOnlineOrUnactivated(drvDriverPo)) {
      return createSuccessResult();
    }
    TmsDrvFreezePO drvFreezePo = freezeRepository.queryByPk(drvId);
    List<String> opFromList = determineOpFromList(drvDriverPo, drvFreezePo);
    if (opFromList.isEmpty()) {
      Cat.logEvent("driver_op_from", "empty");
      return createSuccessResult();
    }
    Cat.logEvent("driver_op_from", opFromList.toString());
    if (isSuperAdmin(accountType)) {
      Cat.logEvent("driver_op_from", SessionHolder.getRestSessionAccountName());
      return createSuccessResult();
    }
    return evaluatePermissions(opFromList, drvDriverPo, drvFreezePo, targetStatus, accountType);
  }

  //判断是否是后台（系统）请求
  private boolean isBackendRequest(Integer accountType) {
    return accountType == null;
  }

  //判断是否是上线或未激活
  private boolean isOnlineOrUnactivated(DrvDriverPO drvDriverPo) {
    return drvDriverPo == null ||
        Objects.equals(drvDriverPo.getDrvStatus(), TmsTransportConstant.DrvStatusEnum.UNACT.getCode()) ||
        Objects.equals(drvDriverPo.getDrvStatus(), TmsTransportConstant.DrvStatusEnum.ONLINE.getCode());
  }

  //判断操作来源
  private List<String> determineOpFromList(DrvDriverPO drvDriverPo, TmsDrvFreezePO drvFreezePo) {
    if (Objects.equals(drvDriverPo.getDrvStatus(), TmsTransportConstant.DrvStatusEnum.FREEZE.getCode())) {
      if (drvFreezePo != null && StringUtils.isNotEmpty(drvFreezePo.getTotalFreezeFrom())) {
        return Arrays.asList(drvFreezePo.getTotalFreezeFrom().split(TmsTransportConstant.SPLIT));
      }
    } else if (drvDriverPo.getOpFrom() != null) {
      return Lists.newArrayList(String.valueOf(drvDriverPo.getOpFrom()));
    }
    return Lists.newArrayList();
  }

  //判断是否是超级管理员
  private boolean isSuperAdmin(Integer accountType) {
    return Objects.equals(accountType, TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue()) &&
        drvStatusPConfig.getSuperAdminAccountList().contains(SessionHolder.getRestSessionAccountName());
  }

  //判断是否有权限
  private Result<Boolean> evaluatePermissions(List<String> opFromList, DrvDriverPO drvDriverPo, TmsDrvFreezePO drvFreezePo, String targetStatus, Integer accountType) {
    logger.info("evaluatePermissions", opFromList.toString(), drvDriverPo.getDrvStatus(), targetStatus, accountType);
    for (Integer operatorFrom : parseOperatorFromList(opFromList)) {
      if (!hasPermission(drvDriverPo, targetStatus, accountType, operatorFrom)) {
        if (canOperateInSpecialCases(drvFreezePo, accountType, operatorFrom)) {
          Cat.logEvent("driver_op_from", "compatible");
          continue;
        }
        return logPermissionErrorAndCreateResult(accountType);
      }
    }
    return createSuccessResult();
  }
  private List<Integer> parseOperatorFromList(List<String> opFromList) {
    return opFromList.stream().map(Integer::parseInt).collect(Collectors.toList());
  }
  private boolean hasPermission(DrvDriverPO drvDriverPo, String targetStatus, Integer role, Integer operatorFrom) {
    return drvStatusPConfig.getPermissionRule(String.valueOf(drvDriverPo.getDrvStatus()), targetStatus, String.valueOf(role)).contains(operatorFrom);
  }
  private boolean canOperateInSpecialCases(TmsDrvFreezePO drvFreezePo, Integer accountType, Integer operatorFrom) {
    return Objects.equals(accountType, TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue()) &&
        drvFreezePo != null &&
        (drvStatusPConfig.getDispatchOpFromList().contains(drvFreezePo.getModifyUser()) ||
            drvStatusPConfig.getSettleMentOpFromList().contains(drvFreezePo.getModifyUser()));
  }
  private Result<Boolean> logPermissionErrorAndCreateResult(Integer accountType) {
    if (Objects.equals(accountType, TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue())) {
      Cat.logEvent("driver_op_from", "bd_no_permission");
      return createErrorResult(ErrorCodeEnum.BD_NO_PERMISSION);
    }
    Cat.logEvent("driver_op_from", "supplier_no_permission");
    return createErrorResult(ErrorCodeEnum.SUPPLIER_NO_PERMISSION);
  }
  public String convertModifyUser(String modifyUser) {
    return drvStatusPConfig.getFreezeFromUserMap().getOrDefault(modifyUser, modifyUser);
  }

  /**
   * 对判罚来源进行细化映射
   * @param opFrom
   * @param modifyUser
   * @return
   */
  public Integer mappingOpFrom(Integer opFrom, String modifyUser) {
    if (!Objects.equals(CommonEnum.FreezeOPFromEnum.PUNISH.getValue(), opFrom)) {
      return opFrom;
    }
    if (drvStatusPConfig.getDispatchOpFromList().contains(modifyUser)) {
      return CommonEnum.FreezeOPFromEnum.DISPATCH.getValue();
    }
    if (drvStatusPConfig.getSettleMentOpFromList().contains(modifyUser)) {
      return CommonEnum.FreezeOPFromEnum.SETTLEMENT.getValue();
    }
    if (drvStatusPConfig.getDriverRiskOpFromList().contains(modifyUser)) {
      return CommonEnum.FreezeOPFromEnum.DRIVER_RISK_CONTROL.getValue();
    }
    return opFrom;
  }

  /**
   * 判罚下线>其他下线>判罚冻结>其他冻结
   * @param drvFreezePo 司机冻结信息
   * @return 最高优先级的操作来源
   */
  public Integer getFreezeOpFrom(TmsDrvFreezePO drvFreezePo) {
    if (drvFreezePo == null || StringUtils.isEmpty(drvFreezePo.getTotalFreezeFrom())) {
      return null;
    }
    return Arrays.stream(drvFreezePo.getTotalFreezeFrom().split(TmsTransportConstant.SPLIT))
        .map(String::trim)
        .map(Integer::parseInt)
        .sorted(CommonEnum.FreezeOPFromEnum::getPriority)
        .findFirst()
        .orElse(null);
  }
  private Result<Boolean> createSuccessResult() {
    return Result.Builder.<Boolean>newResult().success().withData(true).build();
  }
  private Result<Boolean> createErrorResult(ErrorCodeEnum errorCodeEnum) {
    return Result.Builder.<Boolean>newResult().fail().withCode(errorCodeEnum.getCode())
        .withMsg(SharkUtils.getSharkValue(errorCodeEnum.getMessage()))
        .withData(false).build();
  }
}
