package com.ctrip.dcs.tms.transport.task.application.command.impl;

import com.ctrip.dcs.tms.transport.api.model.UploadVehicleDispatchPhotoListRequestType;
import com.ctrip.dcs.tms.transport.task.application.command.VehicleDispatchPhotoCommandService;
import com.ctrip.dcs.tms.transport.task.application.type.VehicleDispatchPhotoTaskService;
import com.ctrip.igt.framework.common.result.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class VehicleDispatchPhotoCommandServiceImpl implements VehicleDispatchPhotoCommandService {

  @Autowired
  VehicleDispatchPhotoTaskService vehicleDispatchPhotoTaskService;

  @Override
  public Result<Boolean> uploadVehicleDispatchPhotoList(UploadVehicleDispatchPhotoListRequestType requestType) {
    return vehicleDispatchPhotoTaskService.uploadVehicleDispatchPhotoList(requestType);
  }
}
