package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.application.dto.DriverLeaveDTO;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.gateway.DrvDriverLeaveGateway;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.common.base.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.result.*;
import com.ctriposs.baiji.exception.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang.time.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.time.*;
import java.util.*;
import java.util.stream.*;

/**
 * <AUTHOR>
 * @Date 2020/3/17 15:03
 */
@Service
public class DriverLeaveQueryServiceImpl implements DriverLeaveQueryService {

  private static final Logger logger = LoggerFactory.getLogger(DriverLeaveQueryServiceImpl.class);

  @Autowired
  private DrvDriverLeaveRepository drvDriverLeaveRepository;
  @Autowired
  private DrvDrvierRepository drvierRepository;
  @Autowired
  private DriverQueryService driverQueryService;
  @Autowired
  private TmsTransportQconfig qconfig;
  @Autowired
  private VehicleRepository vehicleRepository;

  @Autowired
  private DrvDriverLeaveGateway drvDriverLeaveGateway;

  @Override
  public Result queryDrvLeaveDetail(DrvDriverLeavePO driverLeavePO, PaginatorDTO pageInfo) {
    List<DrvLeaveDetailPO> leavePOList = drvDriverLeaveRepository.queryDrvLeaveDetail(driverLeavePO, pageInfo);
    PageHolder pageHolder;
    if (pageInfo != null) {
      int total = drvDriverLeaveRepository.countDrvLeaveDetail(driverLeavePO);
      pageHolder =
          PageHolder.of(leavePOList).pageIndex(pageInfo.getPageNo()).pageSize(pageInfo.getPageSize()).totalSize(total).build();
    } else {
      pageHolder = PageHolder.of(leavePOList).pageIndex(1).pageSize(leavePOList.size() == 0 ? 10 : leavePOList.size())
          .totalSize(leavePOList.size()).build();
    }
    return Result.Builder.<PageHolder<DrvLeaveDetailPO>>newResult()
        .success()
        .withData(pageHolder)
        .build();
  }

  @Override
  public Result<List<DrvLeaveDetailPO>> queryDrvLeaveDetailForDsp(List<Long> drvIds, Boolean useCache) {
    logger.info("queryDrvLeaveDetailForDsp", JsonUtil.toJson(drvIds));
    if(CollectionUtils.isEmpty(drvIds)){
      return Result.Builder.<List<DrvLeaveDetailPO>>newResult()
              .success()
              .withData(Lists.newArrayList())
              .build();
    }
//    List<DrvLeaveDetailPO> leavePOList = drvDriverLeaveRepository.queryDrvLeaveDetailForDsp(drvIds);
    List<DrvLeaveDetailPO> leavePOList = drvDriverLeaveGateway.queryDrvLeaveDetail(drvIds, useCache);

    return Result.Builder.<List<DrvLeaveDetailPO>>newResult()
        .success()
        .withData(leavePOList)
        .build();
  }

  @Override
  public Result<String> checkDrvLeave(Long driverId, LocalDateTime checkTime) {
    Optional<DrvLeaveDetailPO> leaveDetail = getDrvLeaveDetail(driverId, checkTime);
    
    if (leaveDetail.isPresent()){
      return Result.Builder.<String>newResult().success().withData(leaveDetail.get().getLeaveReason()).build();
    }

    return Result.Builder.<String>newResult().fail().build();
  }

  @Override
  public Optional<DrvLeaveDetailPO> getDrvLeaveDetail(Long driverId, LocalDateTime checkTime) {
    List<DrvLeaveDetailPO> pos = drvDriverLeaveRepository.queryDrvLeaveDetailForDsp(Lists.newArrayList(driverId));
    return pos.stream().filter(m->isInLeaveRange(checkTime, m)).findFirst();
  }

  @Override
  public DrvLeaveDetailPO getDrvLeaveLately(Long drvId) {
    List<DrvLeaveDetailPO> pos = drvDriverLeaveRepository.queryDrvLeaveDetailForDsp(Lists.newArrayList(drvId));
    if (CollectionUtils.isEmpty(pos)) {
      return null;
    }
    Collections.sort(pos, (o1, o2) -> {
      if (o1.getLeaveBeginTime().getTime() > o2.getLeaveBeginTime().getTime()) {
        return 1;
      } else if (o1.getLeaveBeginTime().getTime() < o2.getLeaveBeginTime().getTime()) {
        return -1;
      } else {
        return 0;
      }
    });
    DrvLeaveDetailPO res = pos.get(0);
    for (int i = 1; i < pos.size(); i++) {
      Date leaveEndTime = DateUtils.addSeconds(res.getLeaveEndTime(), 1);
      if (leaveEndTime.compareTo(pos.get(i).getLeaveBeginTime()) == 0) {
        res.setLeaveEndTime(pos.get(i).getLeaveEndTime());
      } else {
        break;
      }
    }
    if (isInLeaveRange(LocalDateTime.now(), res)) {
      return res;
    } else {
      return null;
    }
  }

  @Override
  public DriverLeaveDTO queryDriverLeaveById(Long drvId, Long id) {
    try{
      DrvDriverLeavePO driverLeavePO = drvDriverLeaveRepository.queryDriverLeaveById(drvId,id);
      if(driverLeavePO == null){
        return null;
      }
      DriverLeaveDTO driverLeaveDTO = new DriverLeaveDTO();
      driverLeaveDTO.setDriverId(driverLeavePO.getDrvId());
      driverLeaveDTO.setLeaveStatus(driverLeavePO.getLeaveStatus());
      driverLeaveDTO.setActive(driverLeavePO.getActive());
      driverLeaveDTO.setEndTime(driverLeavePO.getLeaveEndTime());
      driverLeaveDTO.setStartTime(driverLeavePO.getLeaveBeginTime());
      driverLeaveDTO.setId(driverLeavePO.getId());
      return driverLeaveDTO;
    }catch (BizException be){
      throw be;
    }catch (Exception e){
      Map<String,String> tag = new HashMap<>();
      tag.put("driverId",drvId.toString());
      tag.put("driverLeaveId",id.toString());
      logger.error("DriverLeaveQueryServiceImpl_queryDriverLeaveById_ex",e,tag);
      throw new BizException("DriverLeaveQueryServiceImpl_queryDriverLeaveById_ex");
    }
  }

  private boolean isInLeaveRange(LocalDateTime checkTime, DrvLeaveDetailPO detail) {
    long checkTimeMilli = checkTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    
    return detail.getLeaveBeginTime().getTime() <= checkTimeMilli && detail.getLeaveEndTime().getTime() >= checkTimeMilli;
  }

  private boolean isLessThenLeaveRange(LocalDateTime checkTime, DrvLeaveDetailPO detail) {
    long checkTimeMilli = checkTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

    return detail.getLeaveEndTime().getTime() < checkTimeMilli;
  }


}
