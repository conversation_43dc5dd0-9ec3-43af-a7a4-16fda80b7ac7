package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.pms.product.api.*;
import com.ctrip.dcs.pms.product.api.dto.*;
import com.ctrip.dcs.scm.merchant.interfaces.dto.*;
import com.ctrip.dcs.scm.merchant.interfaces.message.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;
import java.util.stream.*;

/**
 * <AUTHOR>
 * @Date 2020/3/13 15:29
 */
@Service("tmsPmsproductQueryService")
public class TmsPmsproductQueryServiceImpl implements TmsPmsproductQueryService {

    @Autowired
    private PmsProductServiceClientProxy pmsProductServiceClientProxy;

    @Autowired
    private DcsScmMerchantServiceClientProxy dcsScmMerchantServiceClientProxy;

    /**
     * 判断供应商是否已完成sku创建
     * @param supplierId
     * @return
     */
    @Override
    public boolean checkSkuIsExist(Long supplierId) {
        try {
            //TODO 去掉服务商必须创建SKU判断
            if(true){
                return Boolean.TRUE;
            }
            //1、获取供应商下的服务商
            QueryServiceProviderListRequestType request = new QueryServiceProviderListRequestType();
            ServiceProviderListQueryFilterDTO inclusionFilter = new ServiceProviderListQueryFilterDTO();
            inclusionFilter.setSupplierIds(Lists.newArrayList(supplierId));
            request.setInclusionFilter(inclusionFilter);
            QueryServiceProviderListResponseType queryServiceProviderListResponseType = dcsScmMerchantServiceClientProxy.queryServiceProviderList(request);
            if (CollectionUtils.isEmpty(queryServiceProviderListResponseType.getServiceProviders())) {
                return false;
            }
            List<Long> collect = queryServiceProviderListResponseType.getServiceProviders()
                    .stream()
                    .map(serviceProviderDTO -> serviceProviderDTO.getBase().getId())
                    .collect(Collectors.toList());
            //2、判断所有服务商是否完成sku创建(非api对接sku)
            QueryProductSkuListRequestType skuRequest = new QueryProductSkuListRequestType();
            SkuListSearchFilterDTO filterDTO = new SkuListSearchFilterDTO();
            filterDTO.setServiceProviderIds(collect);
            filterDTO.setConnectModes(Lists.newArrayList(TmsTransportConstant.SkuConnectModeEnum.VBK.getCode()));
            skuRequest.setInclusionFilter(filterDTO);
            skuRequest.setPagingSetting(ServiceExecutorUtils.getOrDefaultPaginator(null));
            QueryProductSkuListResponseType queryProductSkuListResponseType = pmsProductServiceClientProxy.queryProductSkuList(skuRequest);
            return CollectionUtils.isNotEmpty(queryProductSkuListResponseType.getSkuList());
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }
}
