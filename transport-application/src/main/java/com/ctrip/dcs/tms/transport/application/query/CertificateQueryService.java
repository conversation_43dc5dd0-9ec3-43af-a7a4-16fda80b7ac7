package com.ctrip.dcs.tms.transport.application.query;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.igt.framework.common.base.*;
import com.ctrip.igt.framework.common.result.*;

import java.util.*;

/**
 * <AUTHOR>
 * @Description  证件接口
 * @Date 14:07 2020/9/11
 * @Param
 * @return
 **/
public interface CertificateQueryService {


    /**
     * 证件列表
     * @param requestType
     * @return
     */
    Result<PageHolder<QueryCertificateConfigListSOADTO>> queryCertificateConfigList(QueryCertificateConfigListSOARequestType requestType);

    /**
     * 证件信息
     * @param requestType
     * @return
     */
    Result<List<QueryCertificateConfigListSOADTO>> queryCertificateConfigInfo(QueryCertificateConfigInfoSOARequestType requestType);

    /**
     * 证件详情
     * @param requestType
     * @return
     */
    Result<QueryCertificateConfigListSOADTO> queryCertificateConfigDetail(QueryCertificateConfigDetailSOARequestType requestType);


    /**
     * 证件配置白名单
     * @param requestType
     * @return
     */
    Result<Boolean> certificateConfigIsWhite(CertificateConfigIsWhiteSOARequestType requestType);


}
