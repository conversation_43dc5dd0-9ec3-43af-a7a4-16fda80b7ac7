package com.ctrip.dcs.tms.transport.application.dto;


import java.util.List;

public class SaasDriverDTO {

    public Long driverId;

    public Long supplierId;

    public Integer driverStatus;

    public String driverName;

    public Long cityId;

    public String cityName;

    public String driverPhone;

    public String driverPhoneAreaCode;

    public List<SaasTransportGroupDTO> transportGroupList;

    public Integer driverCategory;

    public Long vehicleId;

    public String driverLanguage;

    public Long getDriverId() {
        return driverId;
    }

    public void setDriverId(Long driverId) {
        this.driverId = driverId;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Integer getDriverStatus() {
        return driverStatus;
    }

    public void setDriverStatus(Integer driverStatus) {
        this.driverStatus = driverStatus;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getDriverPhone() {
        return driverPhone;
    }

    public void setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone;
    }

    public String getDriverPhoneAreaCode() {
        return driverPhoneAreaCode;
    }

    public void setDriverPhoneAreaCode(String driverPhoneAreaCode) {
        this.driverPhoneAreaCode = driverPhoneAreaCode;
    }

    public Integer getDriverCategory() {
        return driverCategory;
    }

    public void setDriverCategory(Integer driverCategory) {
        this.driverCategory = driverCategory;
    }

    public Long getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(Long vehicleId) {
        this.vehicleId = vehicleId;
    }

    public List<SaasTransportGroupDTO> getTransportGroupList() {
        return transportGroupList;
    }

    public void setTransportGroupList(List<SaasTransportGroupDTO> transportGroupList) {
        this.transportGroupList = transportGroupList;
    }

    public String getDriverLanguage() {
        return driverLanguage;
    }

    public void setDriverLanguage(String driverLanguage) {
        this.driverLanguage = driverLanguage;
    }
}
