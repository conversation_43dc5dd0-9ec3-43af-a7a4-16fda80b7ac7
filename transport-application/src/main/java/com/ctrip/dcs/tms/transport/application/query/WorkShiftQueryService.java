package com.ctrip.dcs.tms.transport.application.query;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.common.result.*;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2020/11/26 12:00
 */
public interface WorkShiftQueryService {

    /**
     * 检查工作班次配置
     * @param configSOATypes
     * @return
     */
    boolean checkConfig(List<WorkShiftDetailSOAType> configSOATypes);

    /**
     * 查询工作班次信息
     * @param transportGroupId
     * @param active
     * @return
     */
    Result<List<TspTransportGroupWorkShiftPO>> queryWorkShifts(Long transportGroupId, Integer active);

}
