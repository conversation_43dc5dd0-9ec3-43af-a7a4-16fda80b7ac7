package com.ctrip.dcs.tms.transport.application.event.impl;

import com.ctrip.dcs.tms.transport.application.event.ITemporaryDispatchDriverDiscardEventHandler;
import com.ctrip.dcs.tms.transport.application.command.TmsQmqProducerCommandService;
import com.ctrip.dcs.tms.transport.application.command.TransportGroupCommandService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.SelfDispatchOrderServiceClientProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.StringUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsTransportApproveRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.platform.dal.dao.annotation.DalTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.HashMap;

/**
 * 临时派遣司机废弃
 */
@Component
public class TemporaryDispatchDriverDiscardEventHandler implements ITemporaryDispatchDriverDiscardEventHandler {
    private static final Logger logger = LoggerFactory.getLogger(TemporaryDispatchDriverDiscardEventHandler.class);
    @Autowired
    private TmsTransportApproveRepository approveRepository;
    @Autowired
    private SelfDispatchOrderServiceClientProxy selfDispatchOrderServiceClientProxy;
    @Autowired
    private DrvDrvierRepository driverRepository;
    @Autowired
    private TransportGroupCommandService transportGroupCommandService;
    @Autowired
    private TmsQmqProducerCommandService tmsQmqProducerCommandService;

    @Override
    public boolean discard(Long driverId) {
        try{
            DrvDriverPO drvDriverPO = driverRepository.queryByPk(driverId);
            //未查到司机信息
            if(drvDriverPO == null){
                logger.info("temporary_dispatch_driver_null",driverId.toString());
                return false;
            }
            //不是临时派遣司机
            if(drvDriverPO.getTemporaryDispatchMark().intValue() != 1){
                logger.info("not_temporary_dispatch_driver",driverId.toString());
                return false;
            }
            //当前时间小于临时派遣结束时间  未到废弃时间
            if(new Timestamp(System.currentTimeMillis()).before(drvDriverPO.getTemporaryDispatchEndDatetime())){
                logger.info("lt_endTime",driverId.toString());
                return false;
            }
            //驾驶证图片不为空 不废弃
            if(!StringUtil.isEmpty(drvDriverPO.getDrvcardImg())){
                logger.info("DrvCardImg_notNull",driverId.toString());
                return false;
            }
            //检查临时派遣编辑审核记录数 有审核记录不废弃
            int count = approveRepository.queryTemporaryDispatchApproveCount(drvDriverPO.getDrvId(),TmsTransportConstant.ApproveSourceTypeEnum.DRV.getCode(),TmsTransportConstant.EnentTypeEnum.TEMPORARYDISPATCH.getCode());
            if(count > 0){
                logger.info("ApproveCount_gt0",driverId.toString());
                return false;
            }
            //是否有待服务订单
            boolean hasWaitingServiceOrder = selfDispatchOrderServiceClientProxy.hasWaitingServiceOrderForDriver(driverId);
            if(hasWaitingServiceOrder){
                logger.info("hasWaitingServiceOrder",driverId.toString());
                return false;
            }
            //废弃司机操作db
            discardDriver(driverId);
            //发送司机车辆关系变更消息
            tmsQmqProducerCommandService.sendDrvVehicleChangeQmq(drvDriverPO.getDrvId(), 0L);
            //发送司机下线消息
            tmsQmqProducerCommandService.sendDrvOfflineQmq(Arrays.asList(driverId),0);
            //发送司机废弃消息
            tmsQmqProducerCommandService.sendDiscardDriverQmq(driverId);
            return true;
        }catch (Exception e){
            logger.error("TemporaryDispatch_discard_ex",driverId.toString(),e,new HashMap<>());
            return false;
        }
    }

    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Boolean discardDriver(Long driverId){
        //下线
        int offlineResult = driverRepository.updateDrvStatus(Arrays.asList(driverId), TmsTransportConstant.DrvStatusEnum.OFFLINE.getCode(),"system");
        if(offlineResult < 1){
            logger.info("updateDrvStatus_db_false",driverId.toString());
            throw new BizException("updateDrvStatus_db_false");
        }
        //司机下线解绑车辆
        int unbindResult = driverRepository.unbindCarforDrv(Arrays.asList(driverId),"system");
        if(unbindResult < 1){
            logger.info("unbindCarforDrv_db_false",driverId.toString());
            throw new BizException("unbindCarforDrv_db_false");
        }
        //解绑运力组
        transportGroupCommandService.unBoundTransport(Arrays.asList(driverId), "system",true);
        //废弃
        int discardResult = driverRepository.discardDrv(driverId,false,"system");
        if(discardResult < 1){
            logger.info("discardDrv_db_false",driverId.toString());
            throw new BizException("discardDrv_db_false");
        }
        return true;
    }
}
