package com.ctrip.dcs.tms.transport.task.application.type.impl;

import com.ctrip.dcs.tms.transport.api.model.IsNeedCreateTaskRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryTaskConfigRequestType;
import com.ctrip.dcs.tms.transport.api.model.SaveIsNeedCreateTaskRequestType;
import com.ctrip.dcs.tms.transport.task.application.type.TaskManageService;
import com.ctrip.dcs.tms.transport.task.application.type.TaskService;
import com.ctrip.dcs.tms.transport.task.infrastructure.common.enums.NeedCreateTaskEnum;
import com.ctrip.dcs.tms.transport.task.infrastructure.common.enums.TaskTypeEnum;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.result.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class TaskManageServiceImpl implements TaskManageService {

  @Autowired
  List<TaskService> taskServiceList;
  
  @Override
  public Result<String> queryTaskConfig(QueryTaskConfigRequestType requestType) {
    return getTaskService(TaskTypeEnum.getByCode(requestType.getTaskType())).queryTaskConfig(requestType.getTaskConfigParam());
  }

  @Override
  public Result<NeedCreateTaskEnum> isNeedCreateTask(IsNeedCreateTaskRequestType requestType) {
    return getTaskService(TaskTypeEnum.getByCode(requestType.getTaskType())).isNeedCreateTask(requestType);
  }

  @Override
  public Result<Boolean> saveIsNeedCreateTask(SaveIsNeedCreateTaskRequestType requestType) {
    return getTaskService(TaskTypeEnum.getByCode(requestType.getTaskType())).saveIsNeedCreateTask(requestType);
  }

  public TaskService getTaskService(TaskTypeEnum taskType) {
    return taskServiceList.stream().filter(taskService -> taskService.match(taskType)).findFirst().orElseThrow(() -> new BizException("task not found"));
  }
}
