package com.ctrip.dcs.tms.transport.application.query.impl;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import com.ctrip.dcs.tms.transport.application.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ComplianceStrategyEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.VehicleAuditStatusEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.LocalSnowFlakeUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.ctrip.basebiz.ai.aiplatform.contract.api.AiPlatformServiceClient;
import com.ctrip.basebiz.ai.aiplatform.contract.common.AiPlatformRequest;
import com.ctrip.basebiz.ai.aiplatform.contract.common.AiPlatformResponse;
import com.ctrip.dcs.tms.transport.application.query.InternationalEntryService;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.InternationalEntryConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.NewOcrFiledConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.result.Result;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;

@Service
public class InternationalEntryServiceImpl implements InternationalEntryService {
    private Logger logger = LoggerFactory.getLogger(InternationalEntryServiceImpl.class);
    @Autowired
    private InternationalEntryConfig internationalEntryConfig;

    @Autowired
    private NewOcrFiledConfig newOcrFiledConfig;

    @Autowired
    private EnumRepository enumRepository;

    @Autowired
    @Qualifier("aiPlatformRequest")
    private AiPlatformRequest aiPlatformRequest;

    private LocalSnowFlakeUtil localSnowFlakeUtil = new LocalSnowFlakeUtil();

    /**
     * 是否在安全合规的范围内
     *
     * @param cityId 城市ID
     * @param supplierId 供应商id
     * @return {@link Result }<{@link Boolean }>
     */
    @Override
    public Result<Boolean> isInComplianceRuleGary(Long cityId, Long supplierId) {
        List<Long> complianceGaryCountry = internationalEntryConfig.getComplianceGaryCountry();
        List<Long> complianceGaryCity = internationalEntryConfig.getComplianceGaryCity();

        if (complianceGaryCity.contains(cityId)) {
            return Result.Builder.<Boolean>newResult().success().withData(true).build();
        }
        Long countryId = enumRepository.getCountryId(cityId);
        if (complianceGaryCountry.contains(countryId)) {
            return Result.Builder.<Boolean>newResult().success().withData(true).build();
        }
        return Result.Builder.<Boolean>newResult().success().withData(false).build();
    }

    /**
     * 查询所需字段列表
     *
     * @param cityId     城市ID
     * @param supplierId 供应商id
     * @param sceneList
     * @return {@link Result }<{@link List }<{@link String }>>
     */
    @Override
    public Result<List<RequiredFieldDTO>> queryRequiredFiledList(Long cityId, Long supplierId, List<String> sceneList) {
        List<RequiredFieldDTO> result = Lists.newArrayList();
        List<RequiredFieldDTO> fityFieldList = newOcrFiledConfig.getFieldList(cityId,"city", sceneList);
        Long countryId = enumRepository.getCountryId(cityId);
        List<RequiredFieldDTO> fityFieldList1 = newOcrFiledConfig.getFieldList(countryId, "country", sceneList);

        if (CollectionUtils.isNotEmpty(fityFieldList)) {
            result.addAll(fityFieldList);
        }
        if (CollectionUtils.isNotEmpty(fityFieldList1)) {
            result.addAll(fityFieldList1);
        }

        if (CollectionUtils.isNotEmpty(result)) {
            return Result.Builder.<List<RequiredFieldDTO>>newResult().success().withData(result).build();
        }
        return Result.Builder.<List<RequiredFieldDTO>>newResult().success().withData(null).build();
    }

    @Override
    public Result<List<OcrResultDTO>> ocrRecognition(OcrReqDTO ocrReqDTO) {

        List<OcrResultDTO> resultDTOS = new ArrayList<>();
        Long cityId = ocrReqDTO.getCityId();
        String imgType = ocrReqDTO.getImgType();
        String imgUrl = ocrReqDTO.getImgUrl();

        OcrRecognitionResultDTO recognitionResultDTO = newOcrFiledConfig.getOcrRecognitionResultList(cityId, "city", imgType);
        if (Objects.isNull(recognitionResultDTO)) {
            Long countryId = enumRepository.getCountryId(cityId);
            recognitionResultDTO = newOcrFiledConfig.getOcrRecognitionResultList(countryId, "country", imgType);
            if (Objects.isNull(recognitionResultDTO)) {
                return Result.Builder.<List<OcrResultDTO>>newResult().success().withData(null).build();
            }
        }
        long imageId = localSnowFlakeUtil.nextId();
        Map<String, String> extData = new HashMap<>(16);
        extData.put("imageId", String.valueOf(imageId));
        extData.put("image", imgUrl);
        extData.put("inputType", "url");
        extData.put("mode", "extract");
        extData.put("imageLabel", recognitionResultDTO.getReqType());

        aiPlatformRequest.setBiztype("voucherImageRec");
        aiPlatformRequest.setAppId("100025330");
        aiPlatformRequest.setService("imageService");
        aiPlatformRequest.setRequestData(JsonUtil.toJson(extData));

        List<OcrRespDTO> response = recognitionResultDTO.getResponse();

        AiPlatformServiceClient aiPlatformServiceClient = AiPlatformServiceClient.getInstance();
        aiPlatformServiceClient.setCurrentSocketTimeout(30);
        try {
            AiPlatformResponse resp = aiPlatformServiceClient.execute(aiPlatformRequest);
            Integer returnCode = resp.getReturnCode();
            if (Objects.equals(returnCode, 200)) {
                String responseData = resp.getResponseData();
                OcrRecognitionDTO ocrRecognitionDTO = JsonUtil.fromJson(responseData, new TypeReference<OcrRecognitionDTO>() {});
                String returnCode1 = ocrRecognitionDTO.getReturn_code();
                if (StringUtils.equals(returnCode1, "200")) {
                    List<ExtractResult> resExtract = ocrRecognitionDTO.getRes_extract();
                    Map<String, String> collect = Optional.ofNullable(resExtract).orElse(Lists.newArrayList()).stream().collect(Collectors.toMap(ExtractResult::getKey, ExtractResult::getValue));
                    if (CollectionUtils.isNotEmpty(response)) {
                        response.forEach(ocrRespDTO -> {
                            String field = ocrRespDTO.getField();
                            String value = collect.get(field);
                            OcrResultDTO ocrResultDTO = new OcrResultDTO();
                            ocrResultDTO.setFieldName(field);
                            ocrResultDTO.setBackFillField(ocrRespDTO.getBackFillField());
                            ocrResultDTO.setValue(value);
                            ocrResultDTO.setBackfill(ocrRespDTO.getRequired());
                            ocrResultDTO.setOcrId(imageId);
                            resultDTOS.add(ocrResultDTO);
                        });
                    }
                }
            }
        } catch (Exception e) {
            logger.error("ocrRecognition error:", e);
        }
        return Result.Builder.<List<OcrResultDTO>>newResult().success().withData(resultDTOS).build();
    }

    /**
     * 合规性验证 1,合规  2不合规  3转人工
     *
     * @param inComplianceRule
     * @param vehicleLicense   车辆牌照
     * @param newOcrFieldValue 新ocr字段值
     * @return int
     */
    @Override
    public int complianceVerification(Integer inComplianceRule, String vehicleLicense, String newOcrFieldValue) {
        if (ComplianceStrategyEnum.FIRST_STRATEGY.getCode().equals(inComplianceRule)) {
            return japanCompliance(vehicleLicense, newOcrFieldValue);
        }else if (ComplianceStrategyEnum.SECOND_STRATEGY.getCode().equals(inComplianceRule)) {
            return singaporeCompliance(vehicleLicense, newOcrFieldValue);
        }else if (ComplianceStrategyEnum.THIRD_STRATEGY.getCode().equals(inComplianceRule)) {
            return koreaCompliance(vehicleLicense);
        }
        return VehicleAuditStatusEnum.UNDISPOSED.getCode();
    }

    private int koreaCompliance(String vehicleLicense) {
        AtomicReference<Boolean> koreaVehivleApprove = new AtomicReference<>(false);
        if (StringUtils.isNotBlank(vehicleLicense)) {
            List<String> approveVehicleList = internationalEntryConfig.getKoreaApproveVehicleList();
            vehicleLicense.chars().forEach(c -> {
                if (approveVehicleList.contains(c + "")) {
                    koreaVehivleApprove.set(true);
                }
            });
        }

        if (BooleanUtils.isTrue(koreaVehivleApprove.get())) {
            return VehicleAuditStatusEnum.COMPLIANCE.getCode();
        }
        return VehicleAuditStatusEnum.UNDISPOSED.getCode();
    }

    private static int singaporeCompliance(String vehicleLicense, String newOcrFieldValue) {
        Map<String,  Map<String,String>> stringStringMap = JsonUtil.fromJson(newOcrFieldValue, new TypeReference<Map<String, Map<String,String>>>() {});
        Map<String, String> stringStringMap2 = stringStringMap.get("vehicleCertificate");
        if (MapUtils.isEmpty(stringStringMap2)) {
            return 3;
        }
        String vehicleNature = stringStringMap2.get("VehicleNature");
        String licensePlateNo = stringStringMap2.get("LicensePlateNo");

        boolean startsWithP = vehicleLicense.startsWith("P");
        boolean startsWithSH = vehicleLicense.startsWith("SH");
        boolean privateHire = vehicleNature.toLowerCase().contains("private hire");
        boolean publicServiceVehicle = vehicleNature.toLowerCase().contains("public service vehicle");
        boolean equals = StringUtils.equals(licensePlateNo.toLowerCase(), licensePlateNo.toLowerCase());

        if (startsWithSH) {
            return VehicleAuditStatusEnum.COMPLIANCE.getCode();
        }
        if (startsWithP) {
            if (publicServiceVehicle && equals) {
                return VehicleAuditStatusEnum.COMPLIANCE.getCode();
            }
            if (!equals && !publicServiceVehicle) {
                return VehicleAuditStatusEnum.DISQUALIFICATION.getCode();
            }
            return VehicleAuditStatusEnum.UNDISPOSED.getCode();
        }
        if (privateHire) {
            return VehicleAuditStatusEnum.COMPLIANCE.getCode();
        }
        return VehicleAuditStatusEnum.UNDISPOSED.getCode();
    }

    @Nullable
    private Integer japanCompliance(String vehicleLicense, String newOcrFieldValue) {
        if (StringUtils.isNotBlank(newOcrFieldValue)) {
            Map<String,  Map<String,String>> stringStringMap = JsonUtil.fromJson(newOcrFieldValue, new TypeReference<Map<String, Map<String,String>>>() {});
            Map<String, String> stringStringMap1 = stringStringMap.get("vehicleCertificate");
            String useType = stringStringMap1.get("useType");
            Boolean useAge = false;
            AtomicReference<Boolean> vehivleApprove = new AtomicReference<>(false);

            if (StringUtils.isNotBlank(useType) && useType.equals("事业用")) {
                useAge = true;
            }
            if (StringUtils.isNotBlank(vehicleLicense)) {
                List<String> approveVehicleList = internationalEntryConfig.getJapanApproveVehicleList();
                vehicleLicense.chars().forEach(c -> {
                    if (approveVehicleList.contains(c + "")) {
                        vehivleApprove.set(true);
                    }
                });
            }
            if (BooleanUtils.isTrue(useAge) && BooleanUtils.isTrue(vehivleApprove.get())) {
                return VehicleAuditStatusEnum.COMPLIANCE.getCode();
            }
            if (BooleanUtils.isFalse(useAge) && BooleanUtils.isFalse(vehivleApprove.get())) {
                return VehicleAuditStatusEnum.DISQUALIFICATION.getCode();
            }
            return VehicleAuditStatusEnum.UNDISPOSED.getCode();

        }
        return VehicleAuditStatusEnum.UNDISPOSED.getCode();
    }

    @Override
    public OcrComplianceDTO isInComplianceRuleGary(Long cityId) {
        OcrComplianceDTO city = newOcrFiledConfig.getOcrComplianceList(cityId, "city");
        if (Objects.nonNull(city)) {
            return city;
        }
        Long countryId = enumRepository.getCountryId(cityId);
        OcrComplianceDTO countryCompliance = newOcrFiledConfig.getOcrComplianceList(countryId, "country");
        if (Objects.nonNull(countryCompliance)) {
            return countryCompliance;
        }
        return null;
    }

    @Override
    public InternatSettleOCRDTO useNewOCR(Long cityId, Long supplierId) {
        InternatSettleOCRDTO internatSettleOCRDTO = new InternatSettleOCRDTO();
        NewOCRDTO newOcr = newOcrFiledConfig.getNewOcr(cityId, "city");
        if (Objects.nonNull(newOcr)) {
            internatSettleOCRDTO.setUseNewOCR(true);
            internatSettleOCRDTO.setNewOCRFieldList(newOcr.getOcrFieldList());
            return internatSettleOCRDTO;
        }
        Long countryId = enumRepository.getCountryId(cityId);
        newOcr = newOcrFiledConfig.getNewOcr(countryId, "country");
        if (Objects.nonNull(newOcr)) {
            internatSettleOCRDTO.setUseNewOCR(true);
            internatSettleOCRDTO.setNewOCRFieldList(newOcr.getOcrFieldList());
            return internatSettleOCRDTO;
        }
        internatSettleOCRDTO.setUseNewOCR(false);
        internatSettleOCRDTO.setNewOCRFieldList(new ArrayList<>());
        return internatSettleOCRDTO;
    }

}
