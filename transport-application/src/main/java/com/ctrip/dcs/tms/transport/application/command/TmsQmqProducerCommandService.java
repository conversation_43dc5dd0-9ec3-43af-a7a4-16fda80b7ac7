package com.ctrip.dcs.tms.transport.application.command;

import com.ctrip.dcs.async.task.message.DataCompareMessage;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;

import java.util.*;

/**
 * QMQ生产者
 * <AUTHOR>
 * @Date 2020/2/27 16:32
 */
public interface TmsQmqProducerCommandService {

    /**
     * 司机冻结QMQ
     * @param drvId
     * @param freezeHour
     * @param isToSend 是否改派 1.不改派,2.自动改派
     * @param accountType  1.供应商,2.BD 3.派发
     */
    void sendDrvFreezeQmq(long drvId, int freezeHour,Integer isToSend,Integer accountType);

    /**
     * 司机延时解冻QMQ
     * @param drvId
     * @param freezeHour
     */
    void sendDrvUnfreezeQmq(long drvId, int freezeHour);


    /**
     * 司机冻结QMQ
     * @param drvIds
     * @param freezeHour
     */
    void sendDrvFreezeQmq(List<Long> drvIds, int freezeHour,Integer isToSend,Integer accountType);

    /**
     * 司机请假
     * @param drvId
     * @param leaveBeginTime
     * @param leaveEndTime
     */
    void sendDrvLeaveQmq(long drvId, String leaveBeginTime, String leaveEndTime);

    /**
     * 司机销假
     * @param drvId
     */
    void sendDrvCloseLeaveQmq(long drvId);

    /**
     * 司机下线
     * @param drvIds
     */
    void sendDrvOfflineQmq(List<Long> drvIds,Integer isFromPenalty);

    /**
     * 司机上线
     * @param drvIds
     */
    void sendDrvOnlineQmq(List<Long> drvIds);

    /**
     * 运力组下线
     * @param transportGroupId
     */
    void sendTransportGroupOfflineQmq(Long transportGroupId);

    /**
     * 发送车辆表更
     * */
    void sendVehicleModifyQmq(Long vehicleId, Long drvId);

    /**
     * 运力组司机绑定
     * */
    void sendTransportGroupBoundDrvQmq(Long transportGroupId, List<Long> drvId);

    /**
     * 运力组司机解绑
     * */
    void sendTransportGroupUnbondDrvQmq(Long transportGroupId, List<Long> drvId);

    /**
     * 运力组上线
     * @param transportGroupId
     */
    void sendTransportGroupOnlineQmq(Long transportGroupId);

    /**
     * 运力组基础信息变更
     * @param transportGroupId
     */
    void sendTransportGroupModifyQmq(Long transportGroupId);

    /**
     * 运力组SKU绑定、解绑
     * */
    void sendTransportGroupBoundSkuQmq(Long transportGroupId, List<Long> skuInfoList,Integer bindStatus);

    /**
     * 司机变更
     * changeType 1.新增,2.编辑
     * isToSendType 司机解绑车辆后，司机的待服务订单会通知派发侧进行改派操作 1.不改派,2.改派  (该参数 TTS改派用)
     * */
    void sendDrvChangeQmq(Long drvId,Integer changeType,Integer isToSendType);

    /**
     * 编辑后审核通过，发送消息
     * changeType 1.新增,2.编辑
     * isToSendType 司机解绑车辆后，司机的待服务订单会通知派发侧进行改派操作 1.不改派,2.改派  (该参数 TTS改派用)
     * @param drvId
     * @param changeType
     * @param isToSendType
     */
    void sendDrvChangeQmqAfterApprove(Long drvId,Integer changeType,Integer isToSendType);

    void sendDrvChangeQmqForUpdateTransport(Long drvId,Integer changeType,Integer isToSendType, Long supplierId);

    /**
     * 司机换车
     * drvId 司机ID
     * vehicleId 车辆ID
     * */
    void sendDrvVehicleChangeQmq(Long drvId,Long vehicleId);

    /**
     * 司机换供应商
     * drvId 司机ID
     * supplierId 供应商ID
     * */
    void sendDrvSupplierIdChangeQmq(Long drvId,Long supplierId);

    /**
     * 司机换供城市
     * drvId 司机ID
     * cityId 城市ID
     * */
    void sendDrvCityIdChangeQmq(Long drvId,Long cityId, Long supplierId);

    /**
     * 发送车辆车型表更
     * */
    void sendVehicleTypeModifyQmq(Long vehicleId, Long vehicleTypeId,Long drvId);

    /**
     * 司机换供城市
     * drvId 司机ID
     * loginAccount 账号
     * */
    void sendDrvLoginAccountChangeQmq(Long drvId,String loginAccount);


    /**
     * 批量司机变更
     * changeType 1.新增,2.编辑
     * isToSendType 司机解绑车辆后，司机的待服务订单会通知派发侧进行改派操作 1.不改派,2.改派  (该参数 TTS改派用)
     * */
    void sendDrvBatchChangeQmq(List<Long> drvIds,Integer changeType,Integer isSendToType);

    /**
     * 请求C侧身份证背调qmq
     * @param list
     */
    void sendIdcardBackGroundCheckQmq(List<DriverBackgroundCheckDriverVO> list);

    /**
     * 请求C侧身份证背调qmq
     * @param list
     */
    void sendCalculateDrvCoopModeQmq(List<Long> list,String modifyUser);

    /**
     * 身份证核验审批
     * @param sourceId 核验ID
     * @param sourceType 1.新入注司机,2.编辑后审批
     */
    void sendIdcardApproveCheckQmq(Long sourceId,Integer sourceType);


    /**
     * 审批流的证件核验qmq
     */
    void sendApproveCertificateCheckQmq(Long approveId,Integer approveType,String orgDrvInfo,String newDrvInfo,String orgVehicleInfo,String newVehicleInfo);

    /**
     * 发送派发司机变更Qmq
     * */
    void sendDrvDspStatusChangeQmq(Long drvId, TmsTransportConstant.DrvDspStatusEnum changeType);
    /**
     * 发送派发司机变更Qmq
     * */
    void sendDrvDspStatusChangeQmq(String operateType,Long id,Long drvId, TmsTransportConstant.DrvDspStatusEnum changeType);
    /**
     * 推送数据到携程大学qmq
     * @param drvId
     */
    void sendPushDataToCtripUniversityQmq(Long drvId);

    /***
    　* @description: 发送招募审批时间
    　* <AUTHOR>
    　* @date 2021/8/20 10:27
    */
    void sendRecruitingApproveAgingQMQ(Long recruitingId,Integer recruitingType,Integer approveStatus);

    /***
    　* @description: 招募司机、车辆证件核验
    　* <AUTHOR>
    　* @date 2021/9/30 14:20
    */
    void sendRecruitingCertificateCheckQMQ(Long recruitingId,Integer recruitingType,String drvRequestInfo,String vehRequestInfo,String modfyUser);

    /**
    　* @description: 招募司机、车辆核验状态
    　* <AUTHOR>
    　* @date 2021/10/12 9:28
    */
    void sendRecruitingCheckStatusQMQ(Long recruitingId,Integer recruitingType,String modfyUser);

    /**
    　* @description: 系统自动驳回招募信息
    　* <AUTHOR>
    　* @date 2021/12/17 16:44
    */
    void sendSystemAutoRejectedRecruitingQMQ(Long recruitingId,Integer recruitingType);

    /**
    　* @description: 招募司机、车辆审核进度
    　* <AUTHOR>
    　* @date 2022/1/5 9:50
      * @params accountType = 当前操作角色(1.供应商,2.运营)
    */
    void sendRecruitingApproveScheduleQMQ(Long recruitingId,Integer recruitingType,Integer accountType);


    /**
    　* @description: 废弃司机，通知司机端
    　* <AUTHOR>
    　* @date 2023/4/3 15:23
    */
    void sendDiscardDriverQmq(Long drvId);

    /**
    　* @description: 招募司机审核通过成为正式司机，发消息注册成功消息
    　* <AUTHOR>
    　* @date 2023/5/26 11:35
    */
    void sendDriverRegisterQmq(Long drvId,Long cityId,List<Integer> proLineList);

    /**
    　* @description: 临派司机转为正式
    　* <AUTHOR>
    　* @date 2023/10/18 14:18
    */
    void sendTemporaryToOfficialQmq(List<Long> officialIds,Integer type);

    /**
    　* @description: 编辑审核后，判断司机、车辆是否转正
    　* <AUTHOR>
    　* @date 2023/11/6 16:06
    */
    void sendApproveToOfficial(Long sourceId,Integer sourceType);

    /**
    　* @description: 临时派遣司机、车辆补充信息提醒
    　* <AUTHOR>
    　* @date 2023/11/6 16:05
    */
    void sendTemporaryReplenishInfo(Long replenishId,Integer type,Long vehicleId);


    void sendDataCompareNotice(DataCompareMessage dataCompareMessage);

    void sendOverseasDrvVehBatchOCR(Long id,Integer type);
    void sendPhoneEmailModifyQMQ(DrvDriverPO drvDriverPO);

    void sendDriverAppElistmateMessge(TransportGroupDriverApplyFailedReasonDTO applyFailedReasonDTO);

    void sendDriverApplySuccessMessage(Map<Long, String> applySuccessDriverWorkPeriodMap);

    void sendDriverApplyFailedMessage(List<TransportGroupDriverApplyFailedReasonDTO> failedDriverList);

    void sendDrvOutCheck(Long insert);

    void sendRealNameAuth(List<Long> drvId);
}
