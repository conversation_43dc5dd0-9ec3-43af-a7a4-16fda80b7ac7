package com.ctrip.dcs.tms.transport.application.command;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.common.result.*;

import java.util.*;

/**
 * 运力组命令类接口
 * <AUTHOR>
 * @Date 2020/3/13 17:17
 */
public interface TransportGroupCommandService {

    /**
     * 更新运力组的报名状态
     */
    Result<UpdateTransportGroupApplyStatusSOADTO> updateTransportGroupApplyStatus(UpdateTransportGroupApplyStatusSOARequestType soaRequestType);

    /**
     * 处理运力组绑定/解绑司机
     * @param requestSOAType
     * @return
     */
    Result<Boolean> bindDriverRelation(DriverRelationBindRequestSOAType requestSOAType);

    /**
     * 新增运力组
     * @param transportGroupPO
     * @return
     */
    Result<Long> addTransportGroup(TspTransportGroupPO transportGroupPO);

    /**
     * 批量解绑SKU关系
     * */
    Result<Boolean> updateTransportGroupSkuRelationStatus(Long transportGroupId, List<Long> skuInfoList,String operator);

    /**
     * 批量绑定SKU关系
     * */
    Result<Boolean> insertTransportGroupSkuRelationStatus(Long transportGroupId, List<Long> skuInfoList,String operator);

    /**
     * 更新运力组信息
     * @param transportGroupPO
     * @return
     */
    Result<Boolean> updateTransportGroup(TspTransportGroupPO transportGroupPO);

    /**
     * 更新运力组状态
     * @param transportGroupPO
     * @return
     */
    Result<Boolean> updateTransportGroupStatus(TspTransportGroupPO transportGroupPO);

    /**
     * 解绑运力组
     * @param drvid
     * @param modifyUser
     */
    void unBoundTransport(List<Long> drvids,String modifyUser,boolean allIn);

    /**
     * 点击基础信息-[保存]按钮后，判断“该运力组-所有赛道-已关联司机列表”是否存在等同于被删除城市的司机
     * 存在：该运力组直接解绑对应城市所有司机，更新司机合作模式，向下游发通知
     * 不存在：无需解绑任何司机
     * 注意：当所删除运力区域城市=点位城市时，直接删除成功无提示，且已关联同城市司机不解绑，不变更全职报名状态
     *  在“基础信息-运力区域”增加城市后，已关联司机列表不自动增加对应城市司机
     * 特指重新增加“删除过的城市”，无需加回自动解绑的同城市司机
     * @return
     */
    Boolean unBingTransportAssociated(Long transportGroupId,Long pointCityId,Integer originAreaType,Integer targetAreaType,Long originAreaGroupId,Long targetAreaGroupId,String modifyUser);

    /**
     　* @description: 司机派遣-解绑同供应同下的运力组
     　* <AUTHOR>
     　* @date 2023/2/15 13:10
     */
    Boolean drvDispatchunBoundTransport(Long drvId,String modifyUser,Long supplierId);

}
