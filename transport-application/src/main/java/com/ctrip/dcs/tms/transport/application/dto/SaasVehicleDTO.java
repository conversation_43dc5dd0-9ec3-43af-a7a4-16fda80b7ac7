package com.ctrip.dcs.tms.transport.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class SaasVehicleDTO {

    public Long vehicleId;

    public Long supplerId;

    public Integer vehicleStatus;

    public String vehicleLicense;

    public Long vehicleBrandId;

    public String vehicleBrandName;

    public Long vehicleSeriesId;

    public String vehicleSeriesName;

    public Long vehicleTypeId;

    public String vehicleTypeName;

    public Integer vehicleCategory;

    public Long vehicleColorId;

    public String vehicleColorName;

    public Long getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(Long vehicleId) {
        this.vehicleId = vehicleId;
    }

    public Long getSupplerId() {
        return supplerId;
    }

    public void setSupplerId(Long supplerId) {
        this.supplerId = supplerId;
    }

    public Integer getVehicleStatus() {
        return vehicleStatus;
    }

    public void setVehicleStatus(Integer vehicleStatus) {
        this.vehicleStatus = vehicleStatus;
    }

    public String getVehicleLicense() {
        return vehicleLicense;
    }

    public void setVehicleLicense(String vehicleLicense) {
        this.vehicleLicense = vehicleLicense;
    }

    public Long getVehicleBrandId() {
        return vehicleBrandId;
    }

    public void setVehicleBrandId(Long vehicleBrandId) {
        this.vehicleBrandId = vehicleBrandId;
    }

    public String getVehicleBrandName() {
        return vehicleBrandName;
    }

    public void setVehicleBrandName(String vehicleBrandName) {
        this.vehicleBrandName = vehicleBrandName;
    }

    public Long getVehicleSeriesId() {
        return vehicleSeriesId;
    }

    public void setVehicleSeriesId(Long vehicleSeriesId) {
        this.vehicleSeriesId = vehicleSeriesId;
    }

    public String getVehicleSeriesName() {
        return vehicleSeriesName;
    }

    public void setVehicleSeriesName(String vehicleSeriesName) {
        this.vehicleSeriesName = vehicleSeriesName;
    }

    public Long getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(Long vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getVehicleTypeName() {
        return vehicleTypeName;
    }

    public void setVehicleTypeName(String vehicleTypeName) {
        this.vehicleTypeName = vehicleTypeName;
    }

    public Integer getVehicleCategory() {
        return vehicleCategory;
    }

    public void setVehicleCategory(Integer vehicleCategory) {
        this.vehicleCategory = vehicleCategory;
    }

    public Long getVehicleColorId() {
        return vehicleColorId;
    }

    public void setVehicleColorId(Long vehicleColorId) {
        this.vehicleColorId = vehicleColorId;
    }

    public String getVehicleColorName() {
        return vehicleColorName;
    }

    public void setVehicleColorName(String vehicleColorName) {
        this.vehicleColorName = vehicleColorName;
    }
}
