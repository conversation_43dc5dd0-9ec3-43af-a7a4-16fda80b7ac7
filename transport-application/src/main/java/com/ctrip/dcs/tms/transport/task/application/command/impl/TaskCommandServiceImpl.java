package com.ctrip.dcs.tms.transport.task.application.command.impl;

import com.ctrip.dcs.tms.transport.api.model.SaveIsNeedCreateTaskRequestType;
import com.ctrip.dcs.tms.transport.task.application.command.TaskCommandService;
import com.ctrip.dcs.tms.transport.task.application.type.TaskManageService;
import com.ctrip.igt.framework.common.result.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TaskCommandServiceImpl implements TaskCommandService {

  @Autowired
  private TaskManageService taskManageService;

  @Override
  public Result<Boolean> saveIsNeedCreateTask(SaveIsNeedCreateTaskRequestType requestType) {
    return taskManageService.saveIsNeedCreateTask(requestType);
  }
}
