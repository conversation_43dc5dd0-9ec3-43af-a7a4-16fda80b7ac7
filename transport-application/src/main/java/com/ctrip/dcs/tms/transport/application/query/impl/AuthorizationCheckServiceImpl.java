package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.application.command.CommonCommandService;
import com.ctrip.dcs.tms.transport.application.query.AuthorizationCheckService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.SelfServiceProviderModel;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.SelfServiceProviderConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.SharkUtils;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.result.Result;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class AuthorizationCheckServiceImpl implements AuthorizationCheckService {

    @Autowired
    private VehicleRecruitingRepository vehicleRecruitingRepository;

    @Autowired
    private TransportGroupRepository transportGroupRepository;

    @Autowired
    private DrvRecruitingRepository drvRecruitingRepository;

    @Autowired
    private DrvDrvierRepository drvDrvierRepository;

    @Autowired
    private VehicleRepository vehicleRepository;

    @Autowired
    private CommonCommandService commandService;

    @Autowired
    SelfServiceProviderConfig selfServiceProviderConfig;

    @Override
    public boolean checkAuthorizationDriver(List<Long> idList, Long supplierId) {
        if (CollectionUtils.isNotEmpty(idList)) {
            List<DrvDriverPO> driverPOList = drvDrvierRepository.queryDrvBySupplierIdAndId(idList, supplierId);
            return idList.size() == driverPOList.size();
        }
        return true;
    }

    @Override
    public boolean checkAuthorizationDrvVehRecruiting(List<Long> idList, Long supplierId) {
        if (CollectionUtils.isNotEmpty(idList)) {
            List<DrvRecruitingPO> drvRecruitingPOList = drvRecruitingRepository.queryDrvRecruitingBySupplierIdAndId(idList, supplierId);
            return idList.size() == drvRecruitingPOList.size();
        }
        return true;
    }

    @Override
    public boolean checkAuthorizationTransportGroup(List<Long> idList, Long supplierId) {
        if (CollectionUtils.isNotEmpty(idList)) {
            List<TspTransportGroupPO> transportGroupPOList = transportGroupRepository.queryTransportGroupBySupplierIdAndId(idList, null);
//            return idList.size() == transportGroupPOList.size();
            Map<Long,List<TspTransportGroupPO>> longListMap = transportGroupPOList.stream().collect(Collectors.groupingBy(TspTransportGroupPO::getSupplierId));
            List<TspTransportGroupPO> tranListV = longListMap.get(supplierId);
            if(MapUtils.isNotEmpty(longListMap) && CollectionUtils.isNotEmpty(tranListV) && longListMap.size() == 1 && idList.size() == tranListV.size()){
                return Boolean.TRUE;
            }
            Set<Long> supplierIdSet = transportGroupPOList.stream().map(TspTransportGroupPO::getSupplierId).collect(Collectors.toSet());
            List<Long> salfSupplierLis = selfServiceProviderConfig.getSelfServiceProviders().stream().map(SelfServiceProviderModel::getSelfSupplierId).collect(Collectors.toList());
            salfSupplierLis.add(supplierId);
            //如果是自宫供应商 可以操作报名制运力组
            if(commandService.judgeSupplierIsZY(supplierId)){
                for(Long supplier :  supplierIdSet){
                    if(!salfSupplierLis.contains(supplier)){
                        return Boolean.FALSE;
                    }
                }
                    return Boolean.TRUE;
            }
            return Boolean.FALSE;

        }
        return true;
    }

    @Override
    public boolean checkAuthorizationVehicle(List<Long> idList, Long supplierId) {
        if (CollectionUtils.isNotEmpty(idList)) {
            List<VehVehiclePO> vehiclePOList = vehicleRepository.queryVehVehicleBySupplierIdAndId(idList, supplierId);
            return vehiclePOList.size() == idList.size();
        }
        return true;
    }

    @Override
    public boolean checkAuthorizationVehicleRecruiting(List<Long> idList, Long supplierId) {
        if (CollectionUtils.isNotEmpty(idList)) {
            List<VehicleRecruitingPO> vehicleRecruitingPOList = vehicleRecruitingRepository.queryVehicleRecruitingBySupplierIdAndId(idList, supplierId);
            return vehicleRecruitingPOList.size() == idList.size();
        }
        return true;
    }

    @Override
    public boolean decisionRoute(Integer materialTypeId, List<Long> idList, Long supplierId) {
        //CommonEnum.RecordTypeEnum
        switch (materialTypeId) {
            case 1:
                return checkAuthorizationTransportGroup(idList, supplierId);
            case 2:
                return checkAuthorizationDriver(idList, supplierId);
            case 3:
                return checkAuthorizationVehicle(idList, supplierId);
            case 4:
                return checkAuthorizationDrvVehRecruiting(idList, supplierId);
            case 7:
                return checkAuthorizationVehicleRecruiting(idList, supplierId);
            default:
                return false;
        }
    }

    @Override
    public Result<Boolean> operationAuthJudge(List<String> permissionCodes,String code) {
        //获取当前操作角色,如果未登录，提示权限有问题
        Map<String, String> map = SessionHolder.getSessionSource();
        if(MapUtils.isEmpty(map) || map.get("accountType") == null){
            return Result.Builder.<Boolean>newResult().fail().withCode("403").build();
        }
        //接口只对BD开放
        Integer accountType = Integer.parseInt(map.get("accountType"));
        if(Objects.equals(TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue(),accountType)){
            return Result.Builder.<Boolean>newResult().fail().withCode("403").build();
        }
        //当前用户是否有废弃权限操作
        if(CollectionUtils.isEmpty(permissionCodes) || !permissionCodes.contains(code)){
            return Result.Builder.<Boolean>newResult().fail().withCode("400").withMsg(SharkUtils.getSharkValue(SharkKeyConstant.driverlistpageDiscardtipWrongNoncapacity)).build();
        }
        return Result.Builder.<Boolean>newResult().success().build();
    }
}