package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.EffectiveCapacityQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.EdwPrdTrhInventoryCapacityPredictHPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.StringUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EdwPrdTrhInventoryCapacityPredictHRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.igt.PaginationDTO;
import com.ctrip.igt.framework.common.result.Result;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class EffectiveCapacityQueryServiceImpl implements EffectiveCapacityQueryService {

    @Autowired
    TmsTransportQconfig qconfig;
    @Autowired
    EnumRepository enumRepository;
    @Autowired
    EdwPrdTrhInventoryCapacityPredictHRepository repository;

    @Override
    public Result<List<VehicleTypeSOADTO>> queryEffCapacityVehicleType() {
        if(!isBD()){
            return Result.Builder.<List<VehicleTypeSOADTO>>newResult().fail().withCode("403").build();
        }
        List<Long> vehicleTypeList = qconfig.getValidTransportCarTypeConfigList();
        List<VehicleTypeSOADTO> resultData = Lists.newArrayList();
        for (Long vehicleType : vehicleTypeList) {
            VehicleTypeSOADTO vehicleTypeSOADTO = new VehicleTypeSOADTO();
            vehicleTypeSOADTO.setCarTypeId(vehicleType);
            vehicleTypeSOADTO.setCarTypeName(enumRepository.getVehicleTypeName(vehicleType));
            resultData.add(vehicleTypeSOADTO);
        }
        return Result.Builder.<List<VehicleTypeSOADTO>>newResult().success().withData(resultData).build();
    }

    @Override
    public Result<QueryResidualInventorySOAResponseType> queryResidualInventory(QueryResidualInventorySOARequestType requestType) {
        try {
            if(!isBD()){
                return Result.Builder.<QueryResidualInventorySOAResponseType>newResult().fail().withCode("403").build();
            }
            QueryResidualInventorySOAResponseType responseType = new QueryResidualInventorySOAResponseType();
            List<QueryEffCapacitySOAResponseVO> data = assembleData(queryDB(requestType.getData()));
            if (CollectionUtils.isEmpty(data)) {
                responseType.setData(Lists.newArrayList());
                responseType.setDefaultCityIds(Lists.newArrayList());
                responseType.setPagination(responsePage(0,0,0));
                return Result.Builder.<QueryResidualInventorySOAResponseType>newResult().success().withData(responseType).build();
            }
            //剩余库存升序排
            data = data.stream().sorted(Comparator.comparing(QueryEffCapacitySOAResponseVO::getUnusedCapacity)).collect(Collectors.toList());
            int pageSize = requestType.getPaginator().getPageSize();
            int pageIndex = (requestType.getPaginator().getPageNo() - 1) * pageSize;
            List<QueryEffCapacitySOAResponseVO> resultData = data.stream().skip(pageIndex).limit(pageSize).collect(Collectors.toList());
            responseType.setData(resultData);
            responseType.setDefaultCityIds(getDefaultCityIds(data));
            responseType.setPagination(responsePage(requestType.getPaginator().getPageNo(),pageSize,data.size()));
            return Result.Builder.<QueryResidualInventorySOAResponseType>newResult().success().withData(responseType).build();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Result<QueryInventoryCapacitySOAResponseType> queryInventoryCapacity(QueryInventoryCapacitySOARequestType requestType) {
        try {
            if(!isBD()){
                return Result.Builder.<QueryInventoryCapacitySOAResponseType>newResult().fail().withCode("403").build();
            }
            QueryInventoryCapacitySOAResponseType responseType = new QueryInventoryCapacitySOAResponseType();
            List<QueryEffCapacitySOAResponseVO> data = assembleData(queryDB(requestType.getData()));
            if (CollectionUtils.isEmpty(data)) {
                responseType.setData(Lists.newArrayList());
                responseType.setPagination(responsePage(0,0,0));
                return Result.Builder.<QueryInventoryCapacitySOAResponseType>newResult().success().withData(responseType).build();
            }
            //运力数降序
            data = data.stream().sorted(Comparator.comparing(QueryEffCapacitySOAResponseVO::getInventoryCapacity).reversed()).collect(Collectors.toList());
            int pageSize = requestType.getPaginator().getPageSize();
            int pageIndex = (requestType.getPaginator().getPageNo() - 1) * pageSize;
            List<QueryEffCapacitySOAResponseVO> resultData = data.stream().skip(pageIndex).limit(pageSize).collect(Collectors.toList());
            responseType.setData(resultData);
            responseType.setPagination(responsePage(requestType.getPaginator().getPageNo(),pageSize,data.size()));
            return Result.Builder.<QueryInventoryCapacitySOAResponseType>newResult().success().withData(responseType).build();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Result<QueryCapacityAnalyseSOAResponseType> queryCapacityAnalyse(QueryCapacityAnalyseSOARequestType requestType) {
        try {
            if(!isBD()){
                return Result.Builder.<QueryCapacityAnalyseSOAResponseType>newResult().fail().withCode("403").build();
            }
            QueryCapacityAnalyseSOAResponseType responseType = new QueryCapacityAnalyseSOAResponseType();
            List<EdwPrdTrhInventoryCapacityPredictHPO> dbList = queryDB(requestType.getData());
            List<QueryCapacityAnalyseSOADTO> data = assembleUseTimeData((dbList));
            if (CollectionUtils.isEmpty(data)) {
                responseType.setData(Lists.newArrayList());
                return Result.Builder.<QueryCapacityAnalyseSOAResponseType>newResult().success().withData(responseType).build();
            }
            responseType.setData(data);
            responseType.setSuggestList(capacitySuggestSort(requestType.getData().getCityIds(),assembleSuggestData(dbList)));
            responseType.setTotalData(capacityAnalyseSort(requestType.getData().getCityIds(),assembleData(dbList)));
            return Result.Builder.<QueryCapacityAnalyseSOAResponseType>newResult().success().withData(responseType).build();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    //组装有效动力数据
    public List<QueryEffCapacitySOAResponseVO> assembleData(List<EdwPrdTrhInventoryCapacityPredictHPO> dbList) {
        if (CollectionUtils.isEmpty(dbList)) {
            return Collections.emptyList();
        }
        Map<Long, List<EdwPrdTrhInventoryCapacityPredictHPO>> collMap = dbList.stream().collect(Collectors.groupingBy(EdwPrdTrhInventoryCapacityPredictHPO::getCityId));
        List<QueryEffCapacitySOAResponseVO> data = Lists.newArrayList();
        for (Map.Entry<Long, List<EdwPrdTrhInventoryCapacityPredictHPO>> entry : collMap.entrySet()) {
            QueryEffCapacitySOAResponseVO vo = new QueryEffCapacitySOAResponseVO();
            vo.setCityId(entry.getKey());
            List<EdwPrdTrhInventoryCapacityPredictHPO> entryValue = entry.getValue();
            EdwPrdTrhInventoryCapacityPredictHPO predictHPO = entryValue.get(0);
            vo.setCityName(predictHPO.getCityName());
            vo.setProvinceId(predictHPO.getProvinceId());
            vo.setProvinceName(predictHPO.getProvinceName());
            //剩余库存数
            Double unusedCapacity = 0D;
            //运力数
            Double inventoryCapacity = 0D;
            //运力需求
            Double capacityNeed = 0D;
            //已支付单量
            Double payOrderCnt = 0D;
            //人效
            Double avgHumanEffect = 0D;

            for (EdwPrdTrhInventoryCapacityPredictHPO predictH : entryValue) {
                unusedCapacity += predictH.getUnusedCapacity();
                inventoryCapacity += predictH.getInventoryCapacity();
                capacityNeed += predictH.getCapacityNeed();
                payOrderCnt += predictH.getPayOrderCnt();
                avgHumanEffect += predictH.getAvgHumanEffect();
            }
            vo.setUnusedCapacity(unusedCapacity.intValue());
            vo.setInventoryCapacity(inventoryCapacity.intValue());
            vo.setCapacityNeed((int) Math.ceil(capacityNeed));
            vo.setPayOrderCnt(payOrderCnt.intValue());
            vo.setAvgHumanEffect(new BigDecimal(String.valueOf(avgHumanEffect)).setScale(1,BigDecimal.ROUND_HALF_UP).doubleValue());
            vo.setUnusedCapacityRate(StringUtil.percentData(unusedCapacity, inventoryCapacity, 0) + "%");
            data.add(vo);
        }
        return data;
    }

    //运力分析建议，当城市{运力剩余库存}＜ 0 ,取值
    public List<CapacitySuggestSOADTO> assembleSuggestData(List<EdwPrdTrhInventoryCapacityPredictHPO> dbList) {
        if (CollectionUtils.isEmpty(dbList)) {
            return Collections.emptyList();
        }
        Map<Long, List<EdwPrdTrhInventoryCapacityPredictHPO>> proMap =  dbList.stream().collect(Collectors.groupingBy(EdwPrdTrhInventoryCapacityPredictHPO::getCityId));
        List<CapacitySuggestSOADTO> resultData = Lists.newArrayList();
        for (Map.Entry<Long, List<EdwPrdTrhInventoryCapacityPredictHPO>> entry : proMap.entrySet()) {
            List<EdwPrdTrhInventoryCapacityPredictHPO> entryValue = entry.getValue();
            EdwPrdTrhInventoryCapacityPredictHPO predictHPO = entryValue.get(0);
            CapacitySuggestSOADTO soaDTO = new CapacitySuggestSOADTO();
            soaDTO.setCityId(predictHPO.getCityId());
            soaDTO.setCityName(predictHPO.getCityName());
            soaDTO.setUseDate(predictHPO.getUseDate());
            List<CapacitySuggestTimesSOADTO> suggestTimesList = Lists.newArrayList();
            for (EdwPrdTrhInventoryCapacityPredictHPO capacityPredictHPO : entryValue) {
                //城市的时段剩余库存小于0，则组装数据，返回给前端展示
                if (capacityPredictHPO.getUnusedCapacity() < 0) {
                    CapacitySuggestTimesSOADTO soadto = new CapacitySuggestTimesSOADTO();
                    soadto.setCarTypeName(capacityPredictHPO.getCarTypeName());
                    soadto.setUseTime(capacityPredictHPO.getUseTime());
                    soadto.setSupportNum((int) Math.abs(capacityPredictHPO.getUnusedCapacity()));
                    suggestTimesList.add(soadto);
                }
            }
            if(CollectionUtils.isEmpty(suggestTimesList)){
                continue;
            }
            soaDTO.setSuggestTimesList(suggestTimesList);
            resultData.add(soaDTO);
        }
        return resultData;
    }

    //接条件查询DB，按时间段分组
    public List<EdwPrdTrhInventoryCapacityPredictHPO> queryDB(QueryEffCapacitySOARequestVO requestVO) {
        List<EdwPrdTrhInventoryCapacityPredictHPO> queryDataList = repository.queryCapacityPredictH(requestVO);
        if (CollectionUtils.isEmpty(queryDataList)) {
            return Collections.emptyList();
        }
        //判断是否是当天
        Boolean isToday = DateUtil.isToday(requestVO.getUseDate());
        //当前小时
        int nowHour = DateUtil.nowHour();
        //当天只查询当前时间段后的数据
        for(Iterator<EdwPrdTrhInventoryCapacityPredictHPO> iterator = queryDataList.iterator();iterator.hasNext();){
            EdwPrdTrhInventoryCapacityPredictHPO predictHPO = iterator.next();
            //如果入参是当前天，过滤小于当前小时的数据
            if(isToday && nowHour > Integer.parseInt(predictHPO.getUseTimeLabel())){
                iterator.remove();
            }
        }
        return queryDataList;
    }

    public PaginationDTO responsePage(int pageNo, int pageSize, int total) {
        PaginationDTO paginationDTO = new PaginationDTO();
        paginationDTO.setPageNo(pageNo);
        paginationDTO.setPageSize(pageSize);
        paginationDTO.setTotalSize(total);
        return paginationDTO;
    }

    //获取剩余库存数最低的三个城市
    public List<DefaultCityIdsSOAVO> getDefaultCityIds(List<QueryEffCapacitySOAResponseVO> data){
        if(CollectionUtils.isEmpty(data)){
            return Collections.emptyList();
        }
        //查出剩余库比最低的三个城市ID
        List<Long> lowGradeCitys = Lists.newArrayList();
        //如果查询大于3个城市，则存前3个，否则全取
        if (data.size() > 3) {
            for (int i = 0; i < 3; i++) {
                lowGradeCitys.add(data.get(i).getCityId());
            }
        } else {
            lowGradeCitys = data.stream().map(QueryEffCapacitySOAResponseVO::getCityId).collect(Collectors.toList());
        }
        List<DefaultCityIdsSOAVO> resultData = Lists.newArrayList();
        for(Long cityId : lowGradeCitys){
            DefaultCityIdsSOAVO soavo = new DefaultCityIdsSOAVO();
            soavo.setCityId(cityId);
            soavo.setCityName(enumRepository.getCityName(cityId));
            resultData.add(soavo);
        }
        return resultData;
    }

    //组装有效动力数据
    public List<QueryCapacityAnalyseSOADTO> assembleUseTimeData(List<EdwPrdTrhInventoryCapacityPredictHPO> dbList) {
        if (CollectionUtils.isEmpty(dbList)) {
            return Collections.emptyList();
        }

        Map<String, List<EdwPrdTrhInventoryCapacityPredictHPO>> proMap =  dbList.stream().collect(Collectors.groupingBy(EdwPrdTrhInventoryCapacityPredictHPO::getUseTime));
        List<QueryCapacityAnalyseSOADTO> resultData = Lists.newArrayList();
        for (Map.Entry<String, List<EdwPrdTrhInventoryCapacityPredictHPO>> entry : proMap.entrySet()) {
            QueryCapacityAnalyseSOADTO vo = new QueryCapacityAnalyseSOADTO();
            vo.setUseTime(entry.getKey());
            List<QueryEffCapacitySOAResponseVO> data = assembleData(entry.getValue());
            vo.setData(data);
            resultData.add(vo);
        }
        return resultData;
    }

    //按传入城市ID顺序排
    public List<QueryEffCapacitySOAResponseVO> capacityAnalyseSort(List<Long> cityIds, List<QueryEffCapacitySOAResponseVO> data) {
        if (CollectionUtils.isEmpty(cityIds) || CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }
        Map<Long, QueryEffCapacitySOAResponseVO> proMap = data.stream().collect(Collectors.toMap(QueryEffCapacitySOAResponseVO::getCityId, p -> p));
        List<QueryEffCapacitySOAResponseVO> resultData = Lists.newArrayList();
        for (Long cityId : cityIds) {
            if(proMap.get(cityId) == null){
                continue;
            }
            resultData.add(proMap.get(cityId));
        }
        return resultData;
    }

    //运力分析意见排序
    public List<CapacitySuggestSOADTO> capacitySuggestSort(List<Long> cityIds, List<CapacitySuggestSOADTO> data) {
        if (CollectionUtils.isEmpty(cityIds) || CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }
        Map<Long, CapacitySuggestSOADTO> proMap = data.stream().collect(Collectors.toMap(CapacitySuggestSOADTO::getCityId, p -> p));
        List<CapacitySuggestSOADTO> resultData = Lists.newArrayList();
        for (Long cityId : cityIds) {
            if(proMap.get(cityId) == null){
                continue;
            }
            resultData.add(proMap.get(cityId));
        }
        return resultData;
    }

    public Boolean isBD(){
        //获取当前操作角色,如果未登录，提示权限有问题
        Map<String, String> map = SessionHolder.getSessionSource();
        if(MapUtils.isEmpty(map) || map.get("accountType") == null){
            return Boolean.FALSE;
        }
        //接口只对BD开放
        Integer accountType = Integer.parseInt(map.get("accountType"));
        if(Objects.equals(TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue(),accountType)){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }
}
