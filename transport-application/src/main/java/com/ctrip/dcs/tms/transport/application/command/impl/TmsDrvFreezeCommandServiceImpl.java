package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.CommonCommandService;
import com.ctrip.dcs.tms.transport.application.command.TmsDrvFreezeCommandService;
import com.ctrip.dcs.tms.transport.application.command.TmsQmqProducerCommandService;
import com.ctrip.dcs.tms.transport.application.command.TransportGroupCommandService;
import com.ctrip.dcs.tms.transport.application.helper.DrvStatusTransitionPermissionHelper;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvFreezeRecordPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TmsDrvFreezePO;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CommonEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.EpidemicPreventionControlEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ErrorCodeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.ApprovalProcessAuthQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvFreezeRecordRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsDrvFreezeRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TspTransportGroupDriverRelationRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.DrvFreezeReasonDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.ctrip.platform.dal.dao.annotation.DalTransactional;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant.drvFreezeStatusError;

/**
 * 司机冻结接口
 */
@Service
public class TmsDrvFreezeCommandServiceImpl implements TmsDrvFreezeCommandService {

    private static final Logger logger = LoggerFactory.getLogger(TmsDrvFreezeCommandServiceImpl.class);

    private static final String  supplierMismatching = "100002";//冻结确认上线操作-供应商不匹配
    private static final String  radomMismatching = "100001";//冻结确认上线操作-鉴权随机码不匹配
    private static final String  freezeStatusMismatching = "100003";//冻结确认上线操作-冻结状态不匹配

    @Autowired
    private DrvDrvierRepository drvDrvierRepository;
    @Autowired
    private TmsQmqProducerCommandService tmsQmqProducerCommandService;
    @Autowired
    private TmsDrvFreezeRepository tmsDrvFreezeRepository;
    @Autowired
    private CommonCommandService commonCommandService;
    @Autowired
    private ApprovalProcessAuthQconfig approvalProcessAuthQconfig;
    @Autowired
    private TransportGroupCommandService transportGroupCommandService;
    @Autowired
    private TmsTransportQconfig tmsTransportQconfig;
    @Autowired
    private DriverQueryService driverQueryService;
    @Autowired
    private DrvFreezeRecordRepository drvFreezeRecordRepository;
    @Autowired
    TspTransportGroupDriverRelationRepository relationRepository;

    @Autowired
    private DrvStatusTransitionPermissionHelper drvStatusTransitionPermissionHelper;

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> addDrvFreeze(TmsDrvFreezeAddSOARequestType requestType, boolean isNucleicAcidVaccine) {
        try {
            requestType.setTotalHours(Math.abs(requestType.getTotalHours()));
            if (StringUtils.isEmpty(requestType.getFreezeReason())) {
                return resultError(SharkUtils.getSharkValue(SharkKeyConstant.freezeReasonNullError));
            }
            DrvDriverPO drvDriverPO = drvDrvierRepository.queryByPk(requestType.getDrvId());
            if (drvDriverPO == null) {
                return resultError(SharkUtils.getSharkValue(SharkKeyConstant.transportDrvisEmpty));
            }
            Integer drvStatus = drvDriverPO.getDrvStatus();
            if (!checkOlAndFrz(drvStatus)) {
                if(Objects.equals(requestType.getFreezeFrom(), CommonEnum.FreezeOPFromEnum.PUNISH.getValue())){
                    return  Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
                }else {
                    return resultError(ErrorCodeEnum.TRANSPORT_DRIVER_STATUS_NOT_SUPPORT_FREEZE.getCode(),SharkUtils.getSharkValue(ErrorCodeEnum.TRANSPORT_DRIVER_STATUS_NOT_SUPPORT_FREEZE.getMessage(), drvFreezeStatusError));
                }
            }
            if (requestType.getTotalHours() == 0 || requestType.getTotalHours() > tmsTransportQconfig.getFreezeMaxHours()) {
                return resultError(SharkUtils.getSharkValue(SharkKeyConstant.freezeTimeScopeNotMatching, String.valueOf(0), String.valueOf(tmsTransportQconfig.getFreezeMaxHours() / DateUtil.DAY_INTERVAL)));
            }

            //检查操作人是否有权限
            Result<Boolean> unfreezePermissionCheck = drvStatusTransitionPermissionHelper.checkPermission(
                String.valueOf(TmsTransportConstant.DrvStatusEnum.ONLINE.getCode()), drvDriverPO.getDrvId(),
                StringUtils.isEmpty(SessionHolder.getRestSessionAccountType()) ? null : Integer.valueOf(SessionHolder.getRestSessionAccountType()));
            if (!unfreezePermissionCheck.isSuccess()) {
                return unfreezePermissionCheck;
            }

            requestType.setModifyUser(drvStatusTransitionPermissionHelper.convertModifyUser(requestType.getModifyUser()));
            requestType.setFreezeFrom(drvStatusTransitionPermissionHelper.mappingOpFrom(requestType.getFreezeFrom(), requestType.getModifyUser()));;
            requestType.setSupplierId(drvDriverPO.getSupplierId());
            requestType.setDrvStatus(drvStatus);
            Integer freezeHour;
            Integer count = 0;
            TmsDrvFreezePO tmsDrvFreezePO;
            TmsDrvFreezePO tarFreezePO = null;
            long delayMins = 0;
            Timestamp now = DateUtil.getNow();
            //1司机状态为上线 2 或者下线状态（由系统过来的判罚），执行首次冻结
            if (Objects.equals(drvStatus, TmsTransportConstant.DrvStatusEnum.ONLINE.getCode())
                || Objects.equals(drvStatus, TmsTransportConstant.DrvStatusEnum.OFFLINE.getCode())) {
                tmsDrvFreezePO = tmsDrvFreezeRepository.queryByPk(requestType.getDrvId());
                requestType.setFreezeHour(requestType.getTotalHours());
                tarFreezePO = buildDrvFreezePO(requestType, null,now);
                if (isNucleicAcidVaccine) {
                    tarFreezePO.setReportFreezeStatus(EpidemicPreventionControlEnum.ReportFreezeStatusEnum.FROZE.getCode());
                }
                delayMins = tarFreezePO.getFreezeHour() * DateUtil.MINUTE_INTERVAL;
                //未插入过记录
                if (tmsDrvFreezePO == null) {
                    count = tmsDrvFreezeRepository.addDrvFreeze(tarFreezePO);
                } else {
                    count = tmsDrvFreezeRepository.updateDrvFreeze(tarFreezePO);
                }
                //保存冻结记录
                if(count > 0 ){
                    this.saveFreezeRecord(requestType.getDrvId(),requestType.getFreezeType(),now,requestType.getFreezeFrom().toString(),
                            TmsTransportConstant.FreezeStatusEnum.FREEZE.getValue(),requestType.getTotalHours(),requestType.getFreezeReason(),
                            requestType.getUnfreezeAction(),requestType.getFreezeOrderSet(),requestType.getModifyUser());
                }
            }
            //司机状态为冻结，连续冻结
            if (Objects.equals(drvStatus, TmsTransportConstant.DrvStatusEnum.FREEZE.getCode())) {
                tmsDrvFreezePO = tmsDrvFreezeRepository.queryByPk(requestType.getDrvId());
                //未插入过记录
                if (tmsDrvFreezePO == null) {
                    return resultError(SharkUtils.getSharkValue(SharkKeyConstant.drvFreezeNullError));
                }
                // 计算冻结小时数
                int freezeHours = tmsDrvFreezePO.getFreezeHour() == null ? 0 : tmsDrvFreezePO.getFreezeHour();
                if (TmsTransportConstant.FrzTypeEnum.MINUS.getValue().equals(requestType.getFreezeType())) {
                    /**
                     * 有过处罚
                     * 在当前冻结周期中，若来源”判罚系统“的冻结到期时间为Y，则可减少到的时间需>Y
                     * 判断减少后冻结到期时间<=Y，toast提示“当前司机因判罚被冻结至{YYYY-MM-DD}，不可被消除，减少后冻结到期时间不可早于该时间”，需要重新输入
                     * */
                    if (tmsTransportQconfig.getPenaltySwitch() && tmsDrvFreezePO.getPenalizeFreezeHour() != null && tmsDrvFreezePO.getPenalizeFreezeHour() != 0 && requestType.getTotalHours() > (tmsDrvFreezePO.getFreezeHour() - tmsDrvFreezePO.getPenalizeFreezeHour())) {
                        return resultError(SharkUtils.getSharkValue(SharkKeyConstant.capacitygroupmgtJudgmentreduceHint,(Object) DateUtil.dateToString(DateUtils.addHours(tmsDrvFreezePO.getFirstFreezeTime(),tmsDrvFreezePO.getPenalizeFreezeHour()),DateUtil.YYYYMMDD)));
                    }
                    //连续冻结做减法，累积冻结时长大于1
                    if (DateUtil.getRemainingMins(tmsDrvFreezePO.getFirstFreezeTime(), freezeHours) <= requestType.getTotalHours() * DateUtil.MINUTE_INTERVAL) {
                        return resultError(SharkUtils.getSharkValue(SharkKeyConstant.freezeHoursCompareError));
                    }
                    freezeHours = freezeHours - requestType.getTotalHours();
                } else {
                    freezeHours = freezeHours + requestType.getTotalHours();
                }
                freezeHour = freezeHours;
                // 判断 1小时＜=累计冻结时长＜=90天
                if (this.checkFreezeTime(freezeHours, null, 1)) {
                    String msg = SharkUtils.getSharkValue(SharkKeyConstant.freezeTimeScopeNotMatching, String.valueOf(0), String.valueOf(tmsTransportQconfig.getFreezeMaxHours() / DateUtil.DAY_INTERVAL));
                    if(Objects.equals(requestType.getFreezeFrom(), CommonEnum.FreezeOPFromEnum.PUNISH.getValue())){
                        return  Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
                    }else {
                        return resultError(msg);
                    }
                }
                //冻结时间是否小于当前时间
                long subMins;
                subMins = DateUtil.getMinOfDate(tmsDrvFreezePO.getFirstFreezeTime(), new java.util.Date());
                delayMins = freezeHours * DateUtil.MINUTE_INTERVAL - subMins;
                if (delayMins <= 0) {
                    return resultError(SharkUtils.getSharkValue(SharkKeyConstant.freezeHoursCompareError));
                }

                requestType.setFreezeHour(freezeHour);
                tarFreezePO = buildDrvFreezePO(requestType, tmsDrvFreezePO,now);
                if (isNucleicAcidVaccine) {
                    tarFreezePO.setReportFreezeStatus(EpidemicPreventionControlEnum.ReportFreezeStatusEnum.FROZE.getCode());
                }
                //连续冻结，将确认标识置为 未确认
                tarFreezePO.setConfirmOnlineStatus(Boolean.FALSE);
                count = tmsDrvFreezeRepository.updateDrvFreeze(tarFreezePO);
                if(count > 0){
                    //保存冻结记录
                    this.saveFreezeRecord(requestType.getDrvId(),requestType.getFreezeType(),tmsDrvFreezePO.getFirstFreezeTime(),requestType.getFreezeFrom().toString(),
                            TmsTransportConstant.FreezeStatusEnum.FREEZE.getValue(),requestType.getTotalHours(),requestType.getFreezeReason(),
                            requestType.getUnfreezeAction(),requestType.getFreezeOrderSet(),requestType.getModifyUser());
                }
            }
            if (count > 0) {
                doFreezeAction(delayMins, drvDriverPO, requestType, tarFreezePO);
                doUnfreezeDelRedisKey(tarFreezePO.getDrvId());
            }
            return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
        } catch (Exception e) {
            throw new BaijiRuntimeException("addDrvFreeze error", e);
        }
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> drvUnFreeze(DrvUnFreezeSOARequestType requestType) {
        try {
            if (StringUtils.isEmpty(requestType.getUnfreezeReason())) {
                return resultError(SharkUtils.getSharkValue(SharkKeyConstant.unFreezeReasonNullError));
            }

            TmsDrvFreezePO drvFreezePO = tmsDrvFreezeRepository.queryByPk(requestType.getDrvId());
            if (drvFreezePO == null || !Objects.equals(TmsTransportConstant.FreezeStatusEnum.FREEZE.getValue(), drvFreezePO.getFreezeStatus())) {
                return resultError(ErrorCodeEnum.TRANSPORT_DRIVER_IN_UNFREEZE_STATUS.getCode(), SharkUtils.getSharkValue(SharkKeyConstant.drvUnfreezeAndStatusError));
            }

            if (tmsTransportQconfig.getPenaltySwitch() && drvFreezePO.getPenalizeFreezeHour() != null && drvFreezePO.getPenalizeFreezeHour() != 0 && System.currentTimeMillis() < DateUtils.addHours(drvFreezePO.getFirstFreezeTime(),drvFreezePO.getPenalizeFreezeHour()).getTime()) {
                return resultError(SharkUtils.getSharkValue(SharkKeyConstant.capacitygroupmgtJudgmentFreezeHint));
            }

            //检查操作人是否有权限
            Result<Boolean> unfreezePermissionCheck = drvStatusTransitionPermissionHelper.checkPermission(
                String.valueOf(TmsTransportConstant.DrvStatusEnum.ONLINE.getCode()), requestType.getDrvId(),
                StringUtils.isEmpty(SessionHolder.getRestSessionAccountType()) ? null : Integer.valueOf(SessionHolder.getRestSessionAccountType()));
            if (!unfreezePermissionCheck.isSuccess()) {
                return unfreezePermissionCheck;
            }

            DrvDriverPO drvDriverPO = drvDrvierRepository.queryByPk(requestType.getDrvId());
            if (drvDriverPO == null) {
                return resultError(ErrorCodeEnum.TRANSPORT_DRIVER_NOT_FOUND.getCode(), SharkUtils.getSharkValue(SharkKeyConstant.transportDrvisEmpty));
            }
            List<Long> vehcileIds = Lists.newArrayList();
            if (drvDriverPO.getVehicleId() != null && drvDriverPO.getVehicleId() > 0) {
                vehcileIds.add(drvDriverPO.getVehicleId());
            }
            //新增按城市限制操作
            Result<Boolean> disableFlagResult = driverQueryService.judgeDrvOnlinePermission(Boolean.TRUE, Arrays.asList(drvDriverPO.getCityId()), vehcileIds);
            if (!disableFlagResult.isSuccess()) {
                return disableFlagResult;
            }
            int count = tmsDrvFreezeRepository.drvUnFreeze(requestType.getDrvId(), requestType.getUnfreezeReason(), requestType.getModifyUser());
            if (count > 0) {
                //变更司机状态
                drvDrvierRepository.updateDrvStatus(Arrays.asList(requestType.getDrvId()), TmsTransportConstant.DrvStatusEnum.ONLINE.getCode(), requestType.getModifyUser());
                //发送司机解绑QMQ
                tmsQmqProducerCommandService.sendDrvOnlineQmq(Arrays.asList(requestType.getDrvId()));
                Map<String, String> params = Maps.newHashMap();
                params.put("DriverName", drvDriverPO.getDrvName());
                //发送短信
                commonCommandService.sendMessageByPhone(drvDriverPO.getIgtCode(), TmsTransUtil.decrypt(drvDriverPO.getDrvPhone(), KeyType.Phone), approvalProcessAuthQconfig.getDrvManualUnfreezeOnLineCode(), params);
                //司机入驻携程大学
                tmsQmqProducerCommandService.sendPushDataToCtripUniversityQmq(drvDriverPO.getDrvId());
                //解冻清空redis标签
                doUnfreezeDelRedisKey(drvDriverPO.getDrvId());
                //保存冻结记录
                this.saveFreezeRecord(requestType.getDrvId(),TmsTransportConstant.FrzTypeEnum.NORMAL.getValue(),drvFreezePO.getFirstFreezeTime(),drvFreezePO.getTotalFreezeFrom(),
                        TmsTransportConstant.FreezeStatusEnum.UNFRREZE.getValue(),drvFreezePO.getFreezeHour(),drvFreezePO.getFreezeReason(),
                        drvFreezePO.getUnfreezeAction(),drvFreezePO.getFreezeOrderSet(),requestType.getModifyUser());
                //报名制司机-报名状态已剔除置为已报名
                relationRepository.updatEeliminateStatusByDrvId(Arrays.asList(requestType.getDrvId()),requestType.getModifyUser());
            }
            return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
        } catch (Exception e) {
            throw new BaijiRuntimeException("drvUnFreeze error", e);
        }
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<List<QueryDoDrvFreezeSOADTO>> batchDrvFreeze(DrvFreezeBatchSOARequestType requestType) {
        try {
            List<Long> drvIdsList = requestType.getDrvIds();
            if (CollectionUtils.isEmpty(drvIdsList) || StringUtils.isEmpty(requestType.getFreezeReason())) {
                return Result.Builder.<List<QueryDoDrvFreezeSOADTO>>newResult()
                        .fail()
                        .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.freezeReasonNullError))
                        .withData(Lists.newArrayList())
                        .build();
            }

            List<DrvDriverPO> drvList = drvDrvierRepository.queryDrvList(drvIdsList);
            //请求司机数和查询司机数不符
            if (drvList.size() != drvIdsList.size()) {
                return Result.Builder.<List<QueryDoDrvFreezeSOADTO>>newResult()
                        .fail()
                        .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportFreezeError))
                        .withData(Lists.newArrayList())
                        .build();
            }
            if (this.checkFreezeTime(requestType.getTotalHours(), null, 1)) {
                return Result.Builder.<List<QueryDoDrvFreezeSOADTO>>newResult()
                        .fail()
                        .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.freezeTimeScopeNotMatching, String.valueOf(0), String.valueOf(tmsTransportQconfig.getFreezeMaxHours() / DateUtil.DAY_INTERVAL)))
                        .withData(Lists.newArrayList())
                        .build();
            }

            Map<Long, DrvDriverPO> drvMap = drvList.stream().collect(Collectors.toMap(DrvDriverPO::getDrvId, Function.identity(), (key1, key2) -> key2));

            Set<Long> onLineDrvList = Sets.newHashSet();
            Set<Long> freezeDrvList = Sets.newHashSet();
            for (DrvDriverPO drvDriverPO : drvList) {
                if (Objects.equals(TmsTransportConstant.DrvStatusEnum.ONLINE.getCode(), drvDriverPO.getDrvStatus())) {
                    onLineDrvList.add(drvDriverPO.getDrvId());
                }
                if (Objects.equals(TmsTransportConstant.DrvStatusEnum.FREEZE.getCode(), drvDriverPO.getDrvStatus())) {
                    freezeDrvList.add(drvDriverPO.getDrvId());
                }
            }
            //初次冻结
            this.initFreeze(onLineDrvList, requestType, drvMap);
            //连续冻结
            List<Long> screeningDrvList = continuousFreeze(freezeDrvList, requestType, drvMap);
            List<QueryDoDrvFreezeSOADTO> resultList = Lists.newArrayList();
            Iterator<Long> iterator = screeningDrvList.iterator();
            while (iterator.hasNext()) {
                Long drvId = iterator.next();
                QueryDoDrvFreezeSOADTO freezeDTO = new QueryDoDrvFreezeSOADTO();
                freezeDTO.setDrvId(drvId);
                freezeDTO.setDrvName(drvMap.get(drvId).getDrvName());
                resultList.add(freezeDTO);
            }

            return Result.Builder.<List<QueryDoDrvFreezeSOADTO>>newResult().success().withData(resultList).build();
        } catch (Exception e) {
            throw new BaijiRuntimeException("batchDrvFreeze error", e);
        }
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> execUnfreezeDrv(TmsDrvFreezePO drvFreezePO, DrvDriverPO drvDriverPO, Integer hours, boolean dateCheck) {
        try {
            // 是否需要时间检查
            if (dateCheck) {
                Date unFreezeTime = DateUtil.getDayShift(drvFreezePO.getFirstFreezeTime(), hours);
                // 判断司机解冻时间大于当前时间则返回
                if (unFreezeTime.compareTo(new Date()) == 1) {
                    return Result.Builder.<Boolean>newResult().success().withData(Boolean.FALSE).build();
                }
            }
            //到期解冻后置操作（1.解冻后自动上线,2.解冻后自动下线）
            Integer unfreezeAction = drvFreezePO.getUnfreezeAction();
            Integer count = tmsDrvFreezeRepository.drvUnFreeze(drvFreezePO.getDrvId(), SharkUtils.getSharkValue(SharkKeyConstant.transportAutoUnFreezeInfo), TmsTransportConstant.TMS_DEFAULT_USERNAME);
            logger.info("unfreezeDB,drvid:{},resultCount:{}", drvFreezePO.getDrvId(), count);
            if (count > 0) {
                Integer drvStatus = TmsTransportConstant.DrvStatusEnum.ONLINE.getCode();
                String messageCode = approvalProcessAuthQconfig.getDrvAutoUnfreezeOnLineCode();
                if (unfreezeAction == 2) {
                    drvStatus = TmsTransportConstant.DrvStatusEnum.OFFLINE.getCode();
                    messageCode = approvalProcessAuthQconfig.getDrvAutoUnfreezeOffLineCode();
                }
                logger.info("unfrreze-updateDrvStatus,drvid:{},drvStatus:{}", drvFreezePO.getDrvId(), drvStatus);
                //变更司机状态
                drvDrvierRepository.updateDrvStatus(Arrays.asList(drvFreezePO.getDrvId()), drvStatusTransitionPermissionHelper.getFreezeOpFrom(drvFreezePO), drvStatus, TmsTransportConstant.TMS_DEFAULT_USERNAME);

                if (TmsTransportConstant.DrvStatusEnum.OFFLINE.getCode().equals(drvStatus)) {
                    //司机下线解绑车辆
                    drvDrvierRepository.unbindCarforDrv(Arrays.asList(drvFreezePO.getDrvId()), TmsTransportConstant.TMS_DEFAULT_USERNAME);
                    //司机下线解绑运力组
                    transportGroupCommandService.unBoundTransport(Arrays.asList(drvFreezePO.getDrvId()), TmsTransportConstant.TMS_DEFAULT_USERNAME, false);
                    //司机自动解冻下线赋值角色
                    this.drvUnfreezeAaccountType(drvFreezePO.getTotalFreezeFrom());
                    //发送司机下线消息
                    tmsQmqProducerCommandService.sendDrvOfflineQmq(Arrays.asList(drvFreezePO.getDrvId()),judgeFreezeFromIsPunish(drvFreezePO.getTotalFreezeFrom())?1:0);
                } else if (drvStatus!=null && TmsTransportConstant.DrvStatusEnum.ONLINE.getCode().equals(drvStatus)) {
                    tmsQmqProducerCommandService.sendDrvOnlineQmq(Arrays.asList(drvFreezePO.getDrvId()));
                }
                //发送短信
                Map<String, String> params = Maps.newHashMap();
                params.put("DriverName", drvDriverPO.getDrvName());
                commonCommandService.sendMessageByPhone(drvDriverPO.getIgtCode(), TmsTransUtil.encrypt(drvDriverPO.getDrvPhone(), KeyType.Phone), messageCode, params);
                //携程大学
                tmsQmqProducerCommandService.sendPushDataToCtripUniversityQmq(drvDriverPO.getDrvId());
                //解冻清空redis标签
                doUnfreezeDelRedisKey(drvDriverPO.getDrvId());
            }
            return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
        } catch (Exception e) {
            throw new BaijiRuntimeException("execUnfreezeDrv error", e);
        }
    }

    @Override
    public boolean checkReportFreezeStatus(Long drvId) {
        TmsDrvFreezePO drvFreezePO = tmsDrvFreezeRepository.queryByPk(drvId);
        if (drvFreezePO == null || drvFreezePO.getFreezeStatus() == null || drvFreezePO.getFreezeStatus().intValue() == TmsTransportConstant.FreezeStatusEnum.UNFRREZE.getValue().intValue() || drvFreezePO.getReportFreezeStatus() == null) {
            return false;
        }
        return drvFreezePO.getReportFreezeStatus().intValue() == EpidemicPreventionControlEnum.ReportFreezeStatusEnum.FROZE.getCode().intValue();
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> penaltyDrvFreeze(List<Long> drvIdList, String modifyUser, String reason) {
        if (CollectionUtils.isEmpty(drvIdList)) {
            return resultError(SharkUtils.getSharkValue(SharkKeyConstant.transportDrvisEmpty));
        }
        DrvDriverPO drvDriverPO = drvDrvierRepository.queryByPk(drvIdList.get(0));
        if (drvDriverPO == null) {
            return resultError(SharkUtils.getSharkValue(SharkKeyConstant.transportDrvisEmpty));
        }

        long delayMinutes = tmsTransportQconfig.getPenaltyDefaultHours() * DateUtil.MINUTE_INTERVAL;
        int updateCount = 0;

        TmsDrvFreezePO tmsDrvFreezePO = tmsDrvFreezeRepository.queryByPk(drvDriverPO.getDrvId());
        TmsDrvFreezeAddSOARequestType requestType = new TmsDrvFreezeAddSOARequestType();
        requestType.setFreezeReason(reason);
        requestType.setModifyUser(modifyUser);
        requestType.setDrvId(drvDriverPO.getDrvId());
        requestType.setDrvStatus(drvDriverPO.getDrvStatus());
        requestType.setSupplierId(drvDriverPO.getSupplierId());
        requestType.setFreezeFrom(CommonEnum.FreezeOPFromEnum.PUNISH_OFFLINE.getValue());
        requestType.setFreezeHour(tmsTransportQconfig.getPenaltyDefaultHours());
        requestType.setTotalHours(tmsTransportQconfig.getPenaltyDefaultHours());
        requestType.setFreezeOrderSet(TmsTransportConstant.FreezeOrderSetEnum.FREEZEORDERCHG.getValue());
        requestType.setUnfreezeAction(TmsTransportConstant.UnfreezeActionEnum.UNFREEZEOFFLINE.getValue());
        requestType.setModifyUser(drvStatusTransitionPermissionHelper.convertModifyUser(requestType.getModifyUser()));
        requestType.setFreezeFrom(drvStatusTransitionPermissionHelper.mappingOpFrom(requestType.getFreezeFrom(), requestType.getModifyUser()));;
        TmsDrvFreezePO freezePO = buildDrvFreezePO(requestType, tmsDrvFreezePO,new Timestamp(System.currentTimeMillis()));
        freezePO.setFirstFreezeTime(new Timestamp(System.currentTimeMillis()));
        freezePO.setUnfreezeAction(TmsTransportConstant.UnfreezeActionEnum.UNFREEZEOFFLINE.getValue());
        switch (TmsTransportConstant.DrvStatusEnum.getEnum(drvDriverPO.getDrvStatus())) {
            case UNACT:
            case ONLINE:
                //未插入过记录
                if (tmsDrvFreezePO == null) {
                    updateCount = tmsDrvFreezeRepository.addDrvFreeze(freezePO);
                } else {
                    updateCount = tmsDrvFreezeRepository.updateDrvFreeze(freezePO);
                }
                break;
            case FREEZE:
                if (tmsDrvFreezePO != null) {
                    updateCount = tmsDrvFreezeRepository.updateDrvFreeze(freezePO);
                }
                break;
        }
        if (updateCount > 0) {
            doFreezeAction(delayMinutes, drvDriverPO, requestType, freezePO);
        }
        return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> drvUnfreezeConfirmOnline(DrvUnfreezeConfirmOnlineSOARequestType requestType) {
        try {
            Long drvId = requestType.getDrvId();
            DrvDriverPO drvDriverPO = drvDrvierRepository.queryByPk(drvId);
            if (Objects.isNull(drvDriverPO)) {
                return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportQueryDataIsEmpty)).build();
            }

            //当前供应商与操作的供应商不符
            if (!Objects.equals(drvDriverPO.getSupplierId(), requestType.getSupplierId())) {
                return Result.Builder.<Boolean>newResult().fail().withCode(supplierMismatching).build();
            }
            //随机数鉴权，判断链接的随机码和缓存的随机码是否一致，不一定则链接无效
            String randomStr = RedisUtils.get(TmsTransportConstant.DRV_UNFREEZE_CONFIRM_KEY + drvId);
            if (tmsTransportQconfig.getDrvUnfreezeConfirmAuthSwitch() && !Objects.equals(requestType.getRandomStr(), randomStr)) {
                return Result.Builder.<Boolean>newResult().fail().withCode(radomMismatching).build();
            }
            TmsDrvFreezePO drvFreezePO = tmsDrvFreezeRepository.queryByPk(drvId);
            if (Objects.isNull(drvFreezePO) || Objects.equals(drvFreezePO.getFreezeStatus(), TmsTransportConstant.FreezeStatusEnum.UNFRREZE.getValue())) {
                return Result.Builder.<Boolean>newResult().fail().withCode(freezeStatusMismatching).build();
            }
            tmsDrvFreezeRepository.drvUnfreezeConfirmOnline(Arrays.asList(drvId), requestType.getModifyUser(), Boolean.TRUE);
            return Result.Builder.<Boolean>newResult().success().build();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    private void doFreezeAction(long delayMinutes, DrvDriverPO drvDriverPO, TmsDrvFreezeAddSOARequestType requestType, TmsDrvFreezePO tarFreezePO) {
        //修改司机冻结状态
        if (TmsTransportConstant.DrvStatusEnum.ONLINE.getCode().equals(drvDriverPO.getDrvStatus())) {
            drvDrvierRepository.updateDrvStatus(Arrays.asList(drvDriverPO.getDrvId()), TmsTransportConstant.DrvStatusEnum.FREEZE.getCode(), requestType.getModifyUser());
        }
        //发送司机冻结QMQ
        tmsQmqProducerCommandService.sendDrvFreezeQmq(requestType.getDrvId(), (int) delayMinutes / DateUtil.MINUTE_INTERVAL, requestType.getFreezeOrderSet(), getAccountType(requestType.getFreezeFrom()));

        //司机冻结 发送短信
        this.drvFreezeSendMessage(tarFreezePO, drvDriverPO.getDrvStatus(), drvDriverPO, requestType.getFreezeReason());

        //司机入驻携程大学
        tmsQmqProducerCommandService.sendPushDataToCtripUniversityQmq(drvDriverPO.getDrvId());
    }

    /**
     * 初始冻结
     * */
    private Integer initFreeze(Set<Long> onLineDrvList, DrvFreezeBatchSOARequestType requestType, Map<Long, DrvDriverPO> driverPOMap) {
        try {
            //初次冻结
            Integer count = 0;
            Timestamp now = DateUtil.getNow();
            if (!CollectionUtils.isEmpty(onLineDrvList)) {
                Map<Long, TmsDrvFreezePO> freezePOMap = Maps.newHashMap();
                List<TmsDrvFreezePO> tarFreezePOList = Lists.newArrayList();
                List<TmsDrvFreezePO> drvFreezePOS = tmsDrvFreezeRepository.queryDrvFreezeByDrvIds(onLineDrvList);
                if (!CollectionUtils.isEmpty(drvFreezePOS)) {
                    freezePOMap = drvFreezePOS.stream().collect(Collectors.toMap(TmsDrvFreezePO::getDrvId, Function.identity(), (key1, key2) -> key2));
                }
                if (MapUtils.isEmpty(freezePOMap)) {
                    tarFreezePOList = buildBatchDrvFreezePO(requestType, new ArrayList(onLineDrvList), driverPOMap,now);
                    count = tmsDrvFreezeRepository.batchInsertDrvFreeze(tarFreezePOList);
                } else {
                    List<Long> newDrvId = Lists.newArrayList();
                    List<Long> oldDrvId = Lists.newArrayList();
                    for (Long drvId : onLineDrvList) {
                        if (freezePOMap.get(drvId) == null) {
                            newDrvId.add(drvId);
                        } else {
                            oldDrvId.add(drvId);
                        }
                    }
                    if (!CollectionUtils.isEmpty(newDrvId)) {
                        tarFreezePOList = buildBatchDrvFreezePO(requestType, newDrvId, driverPOMap,now);
                        count = tmsDrvFreezeRepository.batchInsertDrvFreeze(tarFreezePOList);
                    }
                    if (!CollectionUtils.isEmpty(oldDrvId)) {
                        tarFreezePOList = buildBatchDrvFreezePO(requestType, oldDrvId, driverPOMap,now);
                        count = tmsDrvFreezeRepository.batchUpdateDrvFreeze(tarFreezePOList);
                    }
                }
                if (count > 0) {
                    //变更司机状态为冻结
                    drvDrvierRepository.updateDrvStatus(new ArrayList(onLineDrvList), TmsTransportConstant.DrvStatusEnum.FREEZE.getCode(), requestType.getModifyUser());
                    //发送QMQ
                    tmsQmqProducerCommandService.sendDrvFreezeQmq(new ArrayList(onLineDrvList), requestType.getTotalHours(), requestType.getFreezeOrderSet(), requestType.getFreezeFrom());
                    List<Long> chgOrderIds = Lists.newArrayList();
                    //是否改派 1.不改派,2.自动改派
                    tarFreezePOList.forEach(drvFreezePO -> {
                        if (Objects.equals(drvFreezePO.getFreezeOrderSet(), TmsTransportConstant.FreezeOrderSetEnum.FREEZEORDERCHG.getValue())) {
                            chgOrderIds.add(drvFreezePO.getDrvId());
                        }
                    });
                    //发送短信
                    this.batchDrvFreezeSendMessage(tarFreezePOList, driverPOMap, requestType.getFreezeReason());
                    //司机入驻携程大学
                    for (Long drvId : onLineDrvList) {
                        tmsQmqProducerCommandService.sendPushDataToCtripUniversityQmq(drvId);
                        //保存冻结记录
                        this.saveFreezeRecord(drvId,TmsTransportConstant.FrzTypeEnum.NORMAL.getValue(),now,requestType.getFreezeFrom().toString(),
                                TmsTransportConstant.FreezeStatusEnum.FREEZE.getValue(),requestType.getTotalHours(),requestType.getFreezeReason(),
                                requestType.getUnfreezeAction(),requestType.getFreezeOrderSet(),requestType.getModifyUser());
                    }
                }
            }
            return count;
        } catch (Exception e) {
            throw new BaijiRuntimeException("initFreeze error", e);
        }
    }

    //连续冻结
    private List<Long> continuousFreeze(Set<Long> freezeDrvList, DrvFreezeBatchSOARequestType requestType, Map<Long, DrvDriverPO> drvMap) {
        try {
            //记录不满足条件的司机ID
            List<Long> screeningDrvList = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(freezeDrvList)) {
                List<TmsDrvFreezePO> drvFreezePOS = tmsDrvFreezeRepository.queryDrvFreezeByDrvIds(freezeDrvList);
                for (TmsDrvFreezePO drvFreezePO : drvFreezePOS) {
                    // 计算冻结小时数
                    int freezeHours = drvFreezePO.getFreezeHour() == null ? 0 : drvFreezePO.getFreezeHour();
                    int operationFreezeHour = requestType.getTotalHours();
                    freezeHours = freezeHours + requestType.getTotalHours();
                    // 判断 1小时＜=累计冻结时长＜=90天
                    //累积冻结时间必须大于1小时并且小于等于90天
                    if (this.checkFreezeTime(freezeHours, null, 1)) {
                        screeningDrvList.add(drvFreezePO.getDrvId());
                        continue;
                    }

                    // 新冻结截止时间不能小于等于当前时间
                    long subMins = 0;
                    subMins = DateUtil.getMinOfDate(drvFreezePO.getFirstFreezeTime(), new java.util.Date());
                    long delayMins = freezeHours * DateUtil.MINUTE_INTERVAL - subMins;
                    if (delayMins <= 0) {
                        screeningDrvList.add(drvFreezePO.getDrvId());
                        continue;
                    }
                    //供应商权限下,校验供应商是否有冻结权限
                    Boolean flag = Boolean.TRUE;
                    if (StringUtils.isNotEmpty(drvFreezePO.getTotalFreezeFrom()) &&
                            Objects.equals(TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue(), requestType.getFreezeFrom())) {
                        List<Integer> fromLists = Arrays.stream(drvFreezePO.getTotalFreezeFrom().split(TmsTransportConstant.SPLIT))
                                .map(s -> Integer.parseInt(s.trim()))
                                .collect(Collectors.toList());
                        for (Integer integer : fromLists) {
                            if (Objects.equals(integer, TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue())) {
                                flag = Boolean.FALSE;
                                break;
                            }
                        }
                    }
                    //false:BD操作过冻结,供应商禁止操作
                    if (!flag) {
                        screeningDrvList.add(drvFreezePO.getDrvId());
                        continue;
                    }
                    requestType.setTotalHours(freezeHours);
                    TmsDrvFreezePO updateFreezePO = buildBatchUpdateDrvFreezePO(requestType, drvFreezePO);
                    int count = tmsDrvFreezeRepository.updateDrvFreeze(updateFreezePO);
                    if (count > 0) {
                        //发送QMQ
                        tmsQmqProducerCommandService.sendDrvFreezeQmq(Arrays.asList(drvFreezePO.getDrvId()), (int) delayMins / DateUtil.MINUTE_INTERVAL, updateFreezePO.getFreezeOrderSet(), requestType.getFreezeFrom());
                        //冻结短信
                        this.drvFreezeSendMessage(updateFreezePO, TmsTransportConstant.DrvStatusEnum.FREEZE.getCode(), drvMap.get(updateFreezePO.getDrvId()), requestType.getFreezeReason());
                        doUnfreezeDelRedisKey(updateFreezePO.getDrvId());
                        //保存冻结记录
                        this.saveFreezeRecord(drvFreezePO.getDrvId(),requestType.getFreezeType(),drvFreezePO.getFirstFreezeTime(),requestType.getFreezeFrom().toString(),
                                TmsTransportConstant.FreezeStatusEnum.FREEZE.getValue(),operationFreezeHour,requestType.getFreezeReason(),
                                requestType.getUnfreezeAction(),requestType.getFreezeOrderSet(),requestType.getModifyUser());
                    }
                }
            }
            return screeningDrvList;
        } catch (Exception e) {
            throw new BaijiRuntimeException("continuousFreeze error", e);
        }
    }

    public static boolean checkOlAndFrz(Integer status) {
        if (TmsTransportConstant.DrvStatusEnum.ONLINE.getCode().equals(status) || TmsTransportConstant.DrvStatusEnum.FREEZE.getCode().equals(status)) {
            return true;
        }
        return false;
    }

    private Integer getFreezeFrom(Integer freezeFrom) {
        return freezeFrom == null ? CommonEnum.FreezeOPFromEnum.SUPPLIER.getValue() : freezeFrom;
    }

    public TmsDrvFreezePO buildDrvFreezePO(TmsDrvFreezeAddSOARequestType requestType, TmsDrvFreezePO freezePO,Timestamp now) {
        TmsDrvFreezePO tmsDrvFreezePO = new TmsDrvFreezePO();
        BeanUtils.copyProperties(requestType, tmsDrvFreezePO);
        Integer reTotalFreezeFrom = getFreezeFrom(requestType.getFreezeFrom());
        if (TmsTransportConstant.DrvStatusEnum.ONLINE.getCode().equals(requestType.getDrvStatus())) {
            tmsDrvFreezePO.setFirstFreezeTime(now);
            tmsDrvFreezePO.setTotalFreezeFrom(String.valueOf(reTotalFreezeFrom));
            tmsDrvFreezePO.setFreezeReason(dealFreezeReason(null, now, requestType.getFreezeReason(), requestType.getRemark()));
            tmsDrvFreezePO.setFreezeCount(1);
        } else {
            tmsDrvFreezePO.setFreezeReason(dealFreezeReason(freezePO == null ? null : freezePO.getFreezeReason(), now, requestType.getFreezeReason(), requestType.getRemark()));
            String totalFreezeFrom = freezePO.getTotalFreezeFrom();
            if (StringUtils.isEmpty(totalFreezeFrom)) {
                tmsDrvFreezePO.setTotalFreezeFrom(String.valueOf(reTotalFreezeFrom));
            } else {
                tmsDrvFreezePO.setTotalFreezeFrom(totalFreezeFrom + TmsTransportConstant.SPLIT + reTotalFreezeFrom);
            }
            tmsDrvFreezePO.setFreezeCount(freezePO.getFreezeCount() == null ? 1 : freezePO.getFreezeCount() + 1);
        }
        // 来源为判罚
        if (Objects.equals(requestType.getFreezeFrom(), CommonEnum.FreezeOPFromEnum.PUNISH.getValue())
            || Objects.equals(requestType.getFreezeFrom(), CommonEnum.FreezeOPFromEnum.PUNISH_OFFLINE.getValue())) {
            // 增加冻结操作
            if (TmsTransportConstant.FrzTypeEnum.PLUS.getValue().equals(requestType.getFreezeType())) {
                //判罚时间等于当前冻结时间
                tmsDrvFreezePO.setPenalizeFreezeHour(requestType.getFreezeHour());
                Integer penalizeAccumulatedHour = freezePO == null ? 0 : (freezePO.getPenalizeAccumulatedHour() == null ? 0 : freezePO.getPenalizeAccumulatedHour());
                tmsDrvFreezePO.setPenalizeAccumulatedHour(penalizeAccumulatedHour + requestType.getTotalHours());
            }
            if (freezePO == null) {
                //判罚系统：都默认为解冻后上线
                tmsDrvFreezePO.setUnfreezeAction(TmsTransportConstant.UnfreezeActionEnum.UNFREEZEONLINE.getValue());
            } else {
                //当两个系统不一致时（运力系统为解冻后下线、判罚系统为解冻后上线），则综合为解冻后下线（无论两个系统配置先后）
                if (freezePO.getUnfreezeAction().intValue() == TmsTransportConstant.UnfreezeActionEnum.UNFREEZEOFFLINE.getValue()) {
                    tmsDrvFreezePO.setUnfreezeAction(TmsTransportConstant.UnfreezeActionEnum.UNFREEZEOFFLINE.getValue());
                }
            }
        }
        tmsDrvFreezePO.setFreezeStatus(TmsTransportConstant.FreezeStatusEnum.FREEZE.getValue());
        return tmsDrvFreezePO;
    }

    private String dealFreezeReason(String srcFreezeReason, Timestamp now, String freezeReason, String remark) {
        List<DrvFreezeReasonDTO> list;
        if (StringUtils.isNotEmpty(srcFreezeReason)) {
            List<DrvFreezeReasonDTO> reasonDTOS = JsonUtil.fromJson(srcFreezeReason, new TypeReference<List<DrvFreezeReasonDTO>>() {
            });
            list = Lists.newArrayList(reasonDTOS);
        } else {
            list = Lists.newArrayListWithCapacity(1);
        }
        list.add(DrvFreezeReasonDTO.buildDTO(DateUtil.dateToString(now, DateUtil.YYYYMMDD), freezeReason, remark));
        return JsonUtil.toJson(list);
    }

    private Result<Boolean> resultError(String errorInfo) {
        return Result.Builder.<Boolean>newResult()
                .fail()
                .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                .withMsg(errorInfo)
                .withData(Boolean.TRUE)
                .build();
    }

    protected Result<Boolean> resultError(String errorCode, String errorInfo) {
        return Result.Builder.<Boolean>newResult()
          .fail()
          .withCode(errorCode)
          .withMsg(errorInfo)
          .withData(Boolean.TRUE)
          .build();
    }

    private List<TmsDrvFreezePO> buildBatchDrvFreezePO(DrvFreezeBatchSOARequestType requestType, List<Long> onLineDrvList, Map<Long, DrvDriverPO> driverPOMap,Timestamp now) {
        List<TmsDrvFreezePO> insertFreezeList = Lists.newArrayList();
        for (Long drvId : onLineDrvList) {
            TmsDrvFreezePO tmsDrvFreezePO = new TmsDrvFreezePO();
            BeanUtils.copyProperties(requestType, tmsDrvFreezePO);
            tmsDrvFreezePO.setDrvId(drvId);
            tmsDrvFreezePO.setSupplierId(driverPOMap.get(drvId).getSupplierId());
            tmsDrvFreezePO.setFirstFreezeTime(now);
            tmsDrvFreezePO.setFreezeHour(requestType.getTotalHours());
            List<DrvFreezeReasonDTO> reasonList = Arrays.asList(DrvFreezeReasonDTO.buildDTO(DateUtil.dateToString(now, DateUtil.YYYYMMDD), requestType.getFreezeReason(), requestType.getRemark()));
            tmsDrvFreezePO.setFreezeReason(JsonUtil.toJson(reasonList));
            tmsDrvFreezePO.setTotalFreezeFrom(String.valueOf(requestType.getFreezeFrom() == null ? 1 : requestType.getFreezeFrom()));
            tmsDrvFreezePO.setFreezeStatus(TmsTransportConstant.FreezeStatusEnum.FREEZE.getValue());
            tmsDrvFreezePO.setFreezeCount(1);
            insertFreezeList.add(tmsDrvFreezePO);
        }
        return insertFreezeList;
    }

    public TmsDrvFreezePO buildBatchUpdateDrvFreezePO(DrvFreezeBatchSOARequestType requestType, TmsDrvFreezePO drvFreezePO) {
        TmsDrvFreezePO tmsDrvFreezePO = new TmsDrvFreezePO();
        BeanUtils.copyProperties(requestType, tmsDrvFreezePO);
        if (drvFreezePO != null) {
            tmsDrvFreezePO.setDrvId(drvFreezePO.getDrvId());
        }
        tmsDrvFreezePO.setFreezeHour(requestType.getTotalHours());
        Integer reTotalFreezeFrom = getFreezeFrom(requestType.getFreezeFrom());
        Timestamp now = DateUtil.getNow();
        tmsDrvFreezePO.setFreezeReason(dealFreezeReason(drvFreezePO.getFreezeReason(), now, requestType.getFreezeReason(), requestType.getRemark()));
        String totalFreezeFrom = drvFreezePO.getTotalFreezeFrom();
        if (StringUtils.isEmpty(drvFreezePO.getTotalFreezeFrom())) {
            tmsDrvFreezePO.setTotalFreezeFrom(String.valueOf(reTotalFreezeFrom));
        } else {
            tmsDrvFreezePO.setTotalFreezeFrom(totalFreezeFrom + TmsTransportConstant.SPLIT + reTotalFreezeFrom);
        }
        tmsDrvFreezePO.setFreezeStatus(TmsTransportConstant.FreezeStatusEnum.FREEZE.getValue());
        tmsDrvFreezePO.setFreezeCount(drvFreezePO.getFreezeCount() == null ? 1 : drvFreezePO.getFreezeCount() + 1);
        tmsDrvFreezePO.setConfirmOnlineStatus(Boolean.FALSE);
        return tmsDrvFreezePO;
    }

    //冻结司机发送短信
    private void drvFreezeSendMessage(TmsDrvFreezePO tarFreezePO, Integer drvStatus, DrvDriverPO drvDriverPO, String freezeReason) {
        String messageCode = "";
        String blockDay = String.valueOf(tarFreezePO.getFreezeHour() / DateUtil.DAY_INTERVAL);
        String blockhour = String.valueOf(tarFreezePO.getFreezeHour() % DateUtil.DAY_INTERVAL);
        String orderList = "";
        //是否改派 1.不改派,2.自动改派
        if (tarFreezePO.getFreezeOrderSet() == 2) {
            //司机状态为上线，表示首次冻结
            if (Objects.equals(drvStatus, TmsTransportConstant.DrvStatusEnum.ONLINE.getCode())) {
                //司机首次冻结改派,发送短信
                messageCode = approvalProcessAuthQconfig.getDrvInitFreezeSendCode();
            } else {
                //司机累计冻结改派,发送短信
                messageCode = approvalProcessAuthQconfig.getDrvCumulativeFreezeSendCode();
            }

        } else {
            //司机状态为上线，表示首次冻结
            if (Objects.equals(drvStatus, TmsTransportConstant.DrvStatusEnum.ONLINE.getCode())) {
                //司机首次冻结不改派,发送短信
                messageCode = approvalProcessAuthQconfig.getDrvInitFreezeNoSendCode();
            } else {
                //司机累积冻结不改派,发送短信
                messageCode = approvalProcessAuthQconfig.getDrvCumulativeFreezeNoSendCode();
            }
        }
        //发送短信
        Map<String, String> params = Maps.newHashMap();
        params.put("DriverName", drvDriverPO.getDrvName());
        params.put("blockday", blockDay);
        params.put("blockhour", blockhour);
        params.put("cumulativetimes", String.valueOf(tarFreezePO.getFreezeCount()));//累积冻结次数
        params.put("blockReason", freezeReason);
        params.put("OrderID", StringUtils.isEmpty(orderList) ? SharkUtils.getSharkValue(SharkKeyConstant.transportNoToSendOrder) : orderList);
        commonCommandService.sendMessageByPhone(drvDriverPO.getIgtCode(), TmsTransUtil.decrypt(drvDriverPO.getDrvPhone(), KeyType.Phone), messageCode, params);
    }

    //批量冻结司机发送短信
    public void batchDrvFreezeSendMessage(List<TmsDrvFreezePO> tarFreezePO, Map<Long, DrvDriverPO> drvDriverMap, String freezeReason) {
        for (TmsDrvFreezePO freezePO : tarFreezePO) {
            String messageCode = "";
            String blockDay = String.valueOf(freezePO.getFreezeHour() / DateUtil.DAY_INTERVAL);
            String blockhour = String.valueOf(freezePO.getFreezeHour() % DateUtil.DAY_INTERVAL);
            Integer drvStatus = drvDriverMap.get(freezePO.getDrvId()).getDrvStatus();
            //是否改派 1.不改派,2.自动改派
            if (freezePO.getFreezeOrderSet() == 2) {
                //司机状态为上线，表示首次冻结
                if (Objects.equals(drvStatus, TmsTransportConstant.DrvStatusEnum.ONLINE.getCode())) {
                    //司机首次冻结改派,发送短信
                    messageCode = approvalProcessAuthQconfig.getDrvInitFreezeSendCode();
                } else {
                    //司机累计冻结改派,发送短信
                    messageCode = approvalProcessAuthQconfig.getDrvCumulativeFreezeSendCode();
                }

            } else {
                //司机状态为上线，表示首次冻结
                if (Objects.equals(drvStatus, TmsTransportConstant.DrvStatusEnum.ONLINE.getCode())) {
                    //司机首次冻结不改派,发送短信
                    messageCode = approvalProcessAuthQconfig.getDrvInitFreezeNoSendCode();
                } else {
                    //司机累积冻结不改派,发送短信
                    messageCode = approvalProcessAuthQconfig.getDrvCumulativeFreezeNoSendCode();
                }
            }
            //发送短信
            DrvDriverPO drvDriverPO = drvDriverMap.get(freezePO.getDrvId());
            Map<String, String> params = Maps.newHashMap();
            params.put("DriverName", drvDriverPO.getDrvName());
            params.put("blockday", blockDay);
            params.put("blockhour", blockhour);
            params.put("cumulativetimes", String.valueOf(freezePO.getFreezeCount()));//累积冻结次数
            params.put("blockReason", freezeReason);
            params.put("OrderID", "");
            commonCommandService.sendMessageByPhone(drvDriverPO.getIgtCode(), TmsTransUtil.decrypt(drvDriverPO.getDrvPhone(), KeyType.Phone), messageCode, params);
        }

    }

    /**
     * 冻结时间 大于1小时 小于90天
     * checkType 1.判断冻结时间大于1小时 小于90天，2.冻结时间是否小于当前时间
     * */
    private Boolean checkFreezeTime(Integer freezeHours, Timestamp firstFreezeTime, Integer checkType) {

        if (checkType == 1 && (freezeHours < 1 || freezeHours > tmsTransportQconfig.getFreezeMaxHours())) {
            return Boolean.TRUE;
        }
        if (checkType == 2 && firstFreezeTime != null) {
            long subMins;
            subMins = DateUtil.getMinOfDate(firstFreezeTime, new java.util.Date());
            long delayMins = freezeHours * DateUtil.MINUTE_INTERVAL - subMins;
            if (delayMins <= 0) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 司机自动下线场景，包括但不限于冻结操作关联的“解冻后自动下线”（如有其它场景辛苦一起识别下），
     * 补充accounttype，对于冻结操作涉及多个操作角色的，按最后一次操作人返回
     *
     * @param drvFrom
     */
    public void drvUnfreezeAaccountType(String drvFrom) {
        Integer accountType = TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue();
        if (StringUtils.isNotEmpty(drvFrom)) {
            String[] drvFroms = drvFrom.split(TmsTransportConstant.SPLIT);
            String drvFromEnd = drvFroms[drvFroms.length - 1];
            if (Objects.equals(drvFromEnd, String.valueOf(TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue()))) {
                accountType = TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue();
            }
        }
        HashMap<String, String> sessionHolder = Maps.newHashMapWithExpectedSize(4);
        sessionHolder.put("accountType", String.valueOf(accountType));
        SessionHolder.setSessionSource(sessionHolder);
    }

    public Integer getAccountType(Integer freezeFrom) {
        if (freezeFrom == null) {
            return CommonEnum.FreezeOPFromEnum.SUPPLIER.getValue();
        }
        if (Objects.equals(freezeFrom,CommonEnum.FreezeOPFromEnum.PUNISH.getValue())) {
            return CommonEnum.FreezeOPFromEnum.BD.getValue();
        }
        return freezeFrom;
    }

    /**
    　* @description: 判断冻结操作来源中是否存在判罚
    　* <AUTHOR>
    　* @date 2021/11/3 10:57
    */
    public Boolean judgeFreezeFromIsPunish(String freezeFrom) {
        if (StringUtils.isEmpty(freezeFrom)) {
            return Boolean.FALSE;
        }
        List<Integer> fromLists = Arrays.stream(freezeFrom.split(TmsTransportConstant.SPLIT))
                .map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fromLists)) {
            return Boolean.FALSE;
        }
        if (fromLists.contains(CommonEnum.FreezeOPFromEnum.PUNISH_OFFLINE.getValue())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    //解冻/连续冻结后redis标识清空
    public Boolean doUnfreezeDelRedisKey(Long drvId){
        RedisUtils.del(TmsTransportConstant.DRV_UNFREEZE_24SENDEMAILKEY + drvId);
        RedisUtils.del(TmsTransportConstant.DRV_UNFREEZE_CONFIRM_KEY + drvId);
        RedisUtils.del(TmsTransportConstant.DRV_UNFREEZE_24SENDMESSAGEKEY + drvId);
        return Boolean.TRUE;
    }

    public Boolean saveFreezeRecord(Long drvId, Integer freezeType, Timestamp fristFreezeTime, String freezeFrom,
                                    Integer freezeStatus, Integer freezeHour, String freezeReason, Integer unfreezeAction,
                                    Integer freezeOrderSet, String modifyUser) {
        try {
            DrvFreezeRecordPO record = new DrvFreezeRecordPO();
            record.setDrvId(drvId);
            record.setFirstFreezeTime(fristFreezeTime == null ? DateUtil.getNow() : fristFreezeTime);
            record.setFreezeFrom(freezeFrom);
            record.setFreezeStatus(freezeStatus);
            record.setFreezeHour(freezeHour);
            //如果操作冻结时减小时数，时间置为负数
            if (TmsTransportConstant.FrzTypeEnum.MINUS.getValue().equals(freezeType)) {
                record.setFreezeHour(-freezeHour);
            }
            record.setFreezeReason(freezeReason);
            record.setUnfreezeAction(unfreezeAction);
            record.setFreezeOrderSet(freezeOrderSet);
            if (Objects.equals(freezeStatus, TmsTransportConstant.FreezeStatusEnum.UNFRREZE.getValue())) {
                record.setUnfreezeTime(DateUtil.getNow());
            }
            record.setCreateUser(modifyUser);
            record.setModifyUser(modifyUser);
            drvFreezeRecordRepository.insert(record);
            return Boolean.TRUE;
        } catch (SQLException e) {
            logger.error("saveFreezeRecordError", "drvId:{},fristFreezeTime:{},e:{}", drvId, fristFreezeTime, e);
            return Boolean.FALSE;
        }
    }

}
