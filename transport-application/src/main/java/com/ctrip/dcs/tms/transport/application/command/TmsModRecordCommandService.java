package com.ctrip.dcs.tms.transport.application.command;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.common.result.*;

import java.util.*;

/**
 * 变更记录命令类接口
 * <AUTHOR>
 * @Date 2020/3/1 17:46
 */
public interface TmsModRecordCommandService {

    void drvStatusUpdateModRrd(Long drvId, int originalStatus, int changedStatus,String modifyUser);

    void drvIntendVehicleTypeUpdateModRrd(Long drvId, String originalIntendVehicleType, String changedIntendVehicleType,String modifyUser);

    void transportGroupAddModRrd(Long transportGroupId,String modifyUser);

    void transportGroupUpdateModRrd(TspTransportGroupPO transportGroupPO);

    void transportGroupStatusUpdateModRrd(Long transportGroupId, int originalStatus, int changedStatus,String modifyUser);

    /**
     * 添加审批记录
     * @param modRrdDTO
     * @return
     */
    Long drvVehRecruitingInsertModRrd(DrvVehRecruitingInsertModRrdDTO modRrdDTO);
    /**
     * 添加审批记录集合
     * @return
     */
    void drvVehRecruitingInsertModRrdList(List<Long> recruitingIdList, int approveStatus, String operator, String remark,Integer recruitingType,Integer approverAperation);

    /**
     * 冻结司机变更记录
     * @param freezeList
     */
    void tmsinitDrvFreezeModRrd(List<TmsDrvFreezePO> freezeList);

    void tmsDrvFreezeModRrd(TmsDrvFreezePO origin,TmsDrvFreezePO tarPO);

    void tmsDrvUnFreezeModRrd(Long drvid,String unfreezeReason,String modifyUser);

    /**
     * 添加证件审批记录集合
     * @return
     */
    void drvVehRecruitingInsertModRrdList(List<Long> recruitingIdList, CommonEnum.RecordTypeEnum recordTypeEnum, Integer checkStatus, String modifyUser, Integer orgCheckStatus,Integer certificateType);

    int tmsUpdateVerifyEventModRrd(Long verifySourceId,String orgVerifyStartTime,String tarVerifyStartTime,String modifyUser);

    Result<Boolean> saveRecruitingOperationLog(Long recruitingId, Integer recruitingType, String modifyUser,Integer approverStatus);

    Boolean saveRecruitingSingleRrd(Long recruitingId, String  singleMod, Integer recruitingType,String modifyUser);

    Boolean saveSingleAndChildRrd(List<StepModRecordParams> recordParams, List<StepChildModRecordParams> childRecordParams, String modifyUser,Long recruitingId,Integer recruitingType);

    /**
     * 添加审批记录集合
     * @return
     */
    Long drvVehRecruitingInsertModRrdList(Long recruitingId, int approveStatus, String operator, String remark,Integer recruitingType,Integer approverAperation);

    /**
    　* @description: 单项记录
    　* <AUTHOR>
    　* @date 2022/4/24 19:41
    */
    Long insertAutoPassStepRrd(Long recruitingId,Integer recruitingType, int approveStatus,Long stepId,Long recruitingRrdId);

    /**
    　* @description: 三方状态变更时，记录变更记录
    　* <AUTHOR>
    　* @date 2022/4/24 10:56
    */
    Long insertThirdCertificateRrd(Long recruitingId,Integer recruitingType,Long checkId,Integer certificateType, Integer orgCheckStatus,Integer newCheckStatus,String modifyUser);

    /**
    　* @description: 子项记录
    　* <AUTHOR>
    　* @date 2022/4/24 19:06
    */
    Boolean saveChildSingleRrd(List<StepChildModRecordParams> childRecordParams, String modifyUser,Long recruitingRecordId,Long recruitingId,Integer recordType);

    /**
     　* @description: 单项记录
     　* <AUTHOR>
     　* @date 2022/4/24 19:41
     */
    Long insertAutoTurnDownStepRrd(Long recruitingId,Integer recruitingType,int approveStatus,Long stepId);

    /**
    　* @description: 插入招募废弃记录
    　* <AUTHOR>
    　* @date 2022/8/22 11:05
    */
    Result<Boolean> insertRecruitingActiveRrd(Long recruitingId, Integer recruitingType, String modifyUser);


}