package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.common.base.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.constant.*;
import com.ctriposs.baiji.exception.*;
import com.fasterxml.jackson.core.type.*;
import com.google.common.base.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.Objects;
import java.util.*;
import java.util.stream.*;


/**
*  司机车辆审批
* <AUTHOR>
* @date 2020/4/20 15:55
*/
@Service
public class DrvVehRecruitingQueryServiceImpl implements DrvVehRecruitingQueryService {

    private static final Logger logger = LoggerFactory.getLogger(DrvVehRecruitingQueryServiceImpl.class);

    private static final String offlineUrl_key = "offlineUrl";
    private static final String onlineUrl_key = "onlineUrl";
    private static final String appUrl_key = "appUrl";

    @Autowired
    private VehicleRecruitingRepository vehicleRecruitingRepository;
    @Autowired
    private DrvRecruitingRepository drvRecruitingRepository;
    @Autowired
    private EnumRepository enumRepository;
    @Autowired
    private ModRecordRespository modRecordRespository;
    @Autowired
    private VehicleRepository vehicleRepository;
    @Autowired
    private CertificateCheckQueryService checkQueryService;
    @Autowired
    private TmsTransportQconfig qconfig;
    @Autowired
    private ProductionLineUtil productionLineUtil;
    @Autowired
    private SensitiveDataControl control;

    @Override
    public Result<DrvVehRecruitingDetailSOADTO> queryDrvVehRecruitingDetail(Long drvRecruitingId,Integer recruitingType) {
        DrvVehRecruitingDetailSOADTO soadto = new DrvVehRecruitingDetailSOADTO();
        if(drvRecruitingId == null || drvRecruitingId <= 0){
            logger.info("drvRecruiting ID is not allow empty,drvRecruitingId:"+ drvRecruitingId);
            return Result.Builder.<DrvVehRecruitingDetailSOADTO>newResult().fail() .withData(null) .build();
        }

        //招募类型必传,并且只能为1or2
        if(recruitingType == null){
            recruitingType = TmsTransportConstant.RecruitingTypeEnum.drv.getCode();
        }
        //招募详情兼容车辆招募,为了区分司机和车辆，查询条件中固定RecruitingType 便于查询
        if(Objects.equals(recruitingType, TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode())){
            return this.getVehResult(drvRecruitingId,recruitingType);
        }
        DrvRecruitingPO drvRecruitingPO = drvRecruitingRepository.queryByPK(drvRecruitingId);
        if(drvRecruitingPO == null){
            logger.info("DrvRecruitingPO is not existing,drvRecruitingId:"+ drvRecruitingId);
            return Result.Builder.<DrvVehRecruitingDetailSOADTO>newResult().success() .withData(null) .build();
        }
        Long vehicleId = drvRecruitingPO.getVehicleId();
        //司机审批详情，如果司机来源是自助注册，司机关联的车辆为车辆审批表信息，
        // 工作台注册则查询正式车辆
        String regstDate = "";
        List<OcrPassStatusModelSOA> ocrPassStatusList = Lists.newArrayList();
        if(vehicleId != null && vehicleId > 0){
            if(drvRecruitingPO.getDrvFrom().intValue() == TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode().intValue() ){
                    VehicleRecruitingPO vehicleRecruitingPO =  vehicleRecruitingRepository.queryByPK(vehicleId);
                    if(vehicleRecruitingPO != null){
                        BeanUtils.copyProperties(vehicleRecruitingPO,soadto);
                        soadto.setVehProLineList(productionLineUtil.getShowProductionLineList(vehicleRecruitingPO.getCategorySynthesizeCode()));
                        soadto.setVehNetAppealMaterials(vehicleRecruitingPO.getNetAppealMaterials());
                        soadto.setVehicleLicenseCityName(enumRepository.getCityName(vehicleRecruitingPO.getVehicleLicenseCityId()));
                        regstDate = DateUtil.timestampToString(vehicleRecruitingPO.getRegstDate(),DateUtil.YYYYMMDD);
                        if(StringUtils.isNotEmpty(vehicleRecruitingPO.getOcrPassStatusJson())){
                            ocrPassStatusList =  JsonUtil.fromJson(vehicleRecruitingPO.getOcrPassStatusJson(), new TypeReference<List<OcrPassStatusModelSOA>>() {});
                        }
                    }
                }else{
                VehVehiclePO vehVehiclePO = vehicleRepository.queryByPk(vehicleId);
                if(vehVehiclePO != null){
                    BeanUtils.copyProperties(vehVehiclePO,soadto);
                    soadto.setVehProLineList(productionLineUtil.getShowProductionLineList(vehVehiclePO.getCategorySynthesizeCode()));
                    if(vehVehiclePO.getVehicleColorId()!=null && vehVehiclePO.getVehicleColorId() >0 ){
                        soadto.setVehicleColorId(vehVehiclePO.getVehicleColorId().intValue());
                    }
                    regstDate = DateUtil.dateToString(vehVehiclePO.getRegstDate(),DateUtil.YYYYMMDD);
                    soadto.setVehNetAppealMaterials(vehVehiclePO.getNetAppealMaterials());
                    soadto.setVehicleLicenseCityName(enumRepository.getCityName(vehVehiclePO.getVehicleLicenseCityId()));
                }
            }
        }
        BeanUtils.copyProperties(drvRecruitingPO,soadto);
        soadto.setDrvProLineList(productionLineUtil.getShowProductionLineList(drvRecruitingPO.getCategorySynthesizeCode()));
        Map<String,String> languageMap = enumRepository.getDrvLanguageMap();
        Map<Integer,String> approverStatusMap = enumRepository.getRecruitingApproverStatus();
        Map<Integer,String> coopModeMap = enumRepository.getDrvCoopMode();
        if(soadto.getVehicleId()!=null && soadto.getVehicleId() > 0){
            Map<Integer, TmsCertificateCheckPO> vehCheckPOMap = checkQueryService.queryCertificateCheckToMap(drvRecruitingPO.getVehicleId(), TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode());
            soadto.setVehicleCertiData(CtripCommonUtils.resultSOADTO(vehCheckPOMap.get(TmsTransportConstant.CertificateTypeEnum.CARCERTILICENSE.getCode())));
            soadto.setNetTansCtfctData(CtripCommonUtils.resultSOADTO(vehCheckPOMap.get(TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT.getCode())));
            if(StringUtils.isEmpty(soadto.getVehicleCertiImg())){
                soadto.setVehicleCertiData(null);
            }
            if(StringUtils.isEmpty(soadto.getNetTansCtfctImg()) && StringUtils.isEmpty(soadto.getVehNetAppealMaterials())){
                soadto.setNetTansCtfctData(null);
            }
        }
        soadto.setApproverStatus(drvRecruitingPO.getApproverStatus());
        soadto.setSupplierName(enumRepository.getSupplierName(soadto.getSupplierId()));
        soadto.setCityName(enumRepository.getCityName(soadto.getCityId()));
        soadto.setDrvLanguageName(enumRepository.getDrvLanguageName(soadto.getDrvLanguage(),languageMap));
        soadto.setDrvFromName(enumRepository.getDrvFrom().get(soadto.getDrvFrom()));
        soadto.setIntendVehicleTypeName(enumRepository.getIntendVehicleTypeName(soadto.getIntendVehicleTypeId()));
        soadto.setCertiDate(DateUtil.dateToString(drvRecruitingPO.getCertiDate(), DateUtil.YYYYMMDD));
        soadto.setExpiryBeginDate(DateUtil.dateToString(drvRecruitingPO.getExpiryBeginDate(), DateUtil.YYYYMMDD));
        soadto.setExpiryEndDate(DateUtil.dateToString(drvRecruitingPO.getExpiryEndDate(), DateUtil.YYYYMMDD));
        soadto.setDrvPhone(control.getSensitiveData(drvRecruitingPO.getDrvPhone(), KeyType.Phone));
        soadto.setDrvIdcard(control.getSensitiveData(drvRecruitingPO.getDrvIdcard(), KeyType.Identity_Card));
        soadto.setDrvLicenseNumber(control.getSensitiveData(drvRecruitingPO.getDrvLicenseNumber(), KeyType.Identity_Card));
        soadto.setEmail(control.getSensitiveData(drvRecruitingPO.getEmail(), KeyType.Mail));
        soadto.setApproverStatusName(approverStatusMap.get(drvRecruitingPO.getApproverStatus())==null?"":approverStatusMap.get(drvRecruitingPO.getApproverStatus()));
        soadto.setVehicleTypeName(enumRepository.getVehicleTypeName(drvRecruitingPO.getVehicleTypeId()));
        soadto.setDatachangeCreatetime(DateUtil.timestampToString(drvRecruitingPO.getDatachangeCreatetime(),DateUtil.YYYYMMDDHHMMSS));
        soadto.setDatachangeLasttime(DateUtil.timestampToString(drvRecruitingPO.getDatachangeLasttime(),DateUtil.YYYYMMDDHHMMSS));
        soadto.setVehicleColorName(enumRepository.getColorName(soadto.getVehicleColorId() == null?0:Long.parseLong(soadto.getVehicleColorId()+"")));
        soadto.setUsingNatureName(enumRepository.getUsingNatureValue(soadto.getUsingNature()));
        soadto.setVehicleSeriesName(enumRepository.getVehicleSeriesName(soadto.getVehicleSeries()));
        soadto.setVehicleBrandName(enumRepository.getBandName(soadto.getVehicleBrandId()));
        soadto.setCoopModeName(coopModeMap.get(soadto.getCoopMode()));
        soadto.setVehicleCityId(drvRecruitingPO.getCityId());
        soadto.setVehicleCityName(enumRepository.getCityName(drvRecruitingPO.getCityId()));
        Map<Integer, TmsCertificateCheckPO> drvCheckPOMap = checkQueryService.queryCertificateCheckToMap(drvRecruitingPO.getDrvRecruitingId(), TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV.getCode());
        soadto.setDrvLicenseData(CtripCommonUtils.resultSOADTO(drvCheckPOMap.get(TmsTransportConstant.CertificateTypeEnum.DRIVERLICENSE.getCode())));
        soadto.setNetDrvLicenseData(CtripCommonUtils.resultSOADTO(drvCheckPOMap.get(TmsTransportConstant.CertificateTypeEnum.NETDRVCTFCT.getCode())));
        soadto.setIdCardData(CtripCommonUtils.resultSOADTO(drvCheckPOMap.get(TmsTransportConstant.CertificateTypeEnum.IDCARD.getCode())));

        // 招募核酸疫苗新增 start
        if (!Strings.isNullOrEmpty(drvRecruitingPO.getVaccinationTimeList())) {
            soadto.setVaccinationTimeList(Arrays.asList(drvRecruitingPO.getVaccinationTimeList().split(",")));
        }
        soadto.setNucleicAcidTestingTime(BaseUtil.dealWithDateToStr(drvRecruitingPO.getNucleicAcidTestingTime()));
        soadto.setVaccineReportImg(drvRecruitingPO.getVaccineReportImg());
        soadto.setNucleicAcidReportImg(drvRecruitingPO.getNucleicAcidReportImg());
        soadto.setVaccineCtfctData(CtripCommonUtils.resultSOADTO(drvCheckPOMap.get(TmsTransportConstant.CertificateTypeEnum.VACCINE.getCode())));
        soadto.setNucleicAcidCtfctData(CtripCommonUtils.resultSOADTO(drvCheckPOMap.get(TmsTransportConstant.CertificateTypeEnum.NUCLEIC_ACID.getCode())));
        soadto.setHeadPortraitCertiData(CtripCommonUtils.resultSOADTO(drvCheckPOMap.get(TmsTransportConstant.CertificateTypeEnum.HEAD_PORTRAIT_COMPLIANCE.getCode())));

        TmsCertificateCheckPO ocrCheckPO = drvCheckPOMap.get(TmsTransportConstant.CertificateTypeEnum.OCR_HEAD_PORTRAIT.getCode());
        if (ocrCheckPO != null) {
            soadto.setOcrheadPortraitResult(Objects.equals(ocrCheckPO.getCheckStatus(), TmsTransportConstant.CheckStatusEnum.THROUGH.getCode()));
        }
        // 招募核酸疫苗新增 end

        soadto.setRegstDate(regstDate);
        soadto.setRecruitingType(recruitingType);
        soadto.setDrvNetAppealMaterials(drvRecruitingPO.getNetAppealMaterials());
        //删除证件时，对应的标签需要一并隐藏，结果为页面不展示、下游（司机端）识别人证车证的标识为不再加分
        if(StringUtils.isEmpty(drvRecruitingPO.getIdcardImg())){
            soadto.setIdCardData(null);
        }
        if(StringUtils.isEmpty(drvRecruitingPO.getDrvcardImg())){
            soadto.setDrvLicenseData(null);
        }
        if(StringUtils.isEmpty(drvRecruitingPO.getNetVehiclePeoImg()) && StringUtils.isEmpty(drvRecruitingPO.getNetAppealMaterials())){
            soadto.setNetDrvLicenseData(null);
        }
        if(StringUtils.isEmpty(drvRecruitingPO.getNucleicAcidReportImg())){
            soadto.setNucleicAcidCtfctData(null);
        }
        if(StringUtils.isEmpty(drvRecruitingPO.getVaccineReportImg())){
            soadto.setVaccineCtfctData(null);
        }

        soadto.setCertificateConfigStr(drvRecruitingPO.getCertificateConfig());
        soadto.setAppealMaterialsShowSwitch(drvRecruitingPO.getBdTurnDownCount().intValue() >= qconfig.getBdTurnDownCountThreshold().intValue());
        soadto.setActive(drvRecruitingPO.getActive());
        if(StringUtils.isNotEmpty(drvRecruitingPO.getOcrPassStatusJson())){
            List<OcrPassStatusModelSOA> modelSOAS =  JsonUtil.fromJson(drvRecruitingPO.getOcrPassStatusJson(), new TypeReference<List<OcrPassStatusModelSOA>>() {
            });
            ocrPassStatusList = (List<OcrPassStatusModelSOA>) CollectionUtils.union(ocrPassStatusList,modelSOAS);
        }
        soadto.setOcrPassStatusList(ocrPassStatusList);
        soadto.setPaiayEmail(control.getSensitiveData(drvRecruitingPO.getPaiayEmail(), KeyType.Mail));
        return Result.Builder.<DrvVehRecruitingDetailSOADTO>newResult().success() .withData(soadto) .build();
    }

    /**
     * 获取工作台创建车辆招募信息
     * @param vehicleRecruitingId
     * @return
     */
    private Result<DrvVehRecruitingDetailSOADTO> getVehResult(Long vehicleRecruitingId,Integer recruitingType){
        VehicleRecruitingPO vehicleRecruitingPO = vehicleRecruitingRepository.queryByPK(vehicleRecruitingId);
        if(vehicleRecruitingPO == null){
            logger.info("vehicleRecruitingPO is not existing,vehicleRecruitingPO:"+ vehicleRecruitingId);
            return Result.Builder.<DrvVehRecruitingDetailSOADTO>newResult().success() .withData(null) .build();
        }
        Map<Integer,String> approverStatusMap = enumRepository.getRecruitingApproverStatus();
        Map<Integer,String> vehicleStatusMap = enumRepository.getVehicleStatus();
        DrvVehRecruitingDetailSOADTO soadto = new DrvVehRecruitingDetailSOADTO();
        BeanUtils.copyProperties(vehicleRecruitingPO,soadto);
        soadto.setDrvRecruitingId(vehicleRecruitingPO.getVehicleId());
        soadto.setRecruitingType(recruitingType);
        soadto.setVehicleCityId(vehicleRecruitingPO.getCityId());
        soadto.setVehicleCityName(enumRepository.getCityName(vehicleRecruitingPO.getCityId()));
        soadto.setSupplierName(enumRepository.getSupplierName(soadto.getSupplierId()));
        soadto.setApproverStatusName(approverStatusMap.get(vehicleRecruitingPO.getApproverStatus())==null?"":approverStatusMap.get(vehicleRecruitingPO.getApproverStatus()));
        soadto.setVehicleTypeName(enumRepository.getVehicleTypeName(vehicleRecruitingPO.getVehicleTypeId()));
        soadto.setDatachangeCreatetime(DateUtil.timestampToString(vehicleRecruitingPO.getDatachangeCreatetime(),DateUtil.YYYYMMDDHHMMSS));
        soadto.setDatachangeLasttime(DateUtil.timestampToString(vehicleRecruitingPO.getDatachangeLasttime(),DateUtil.YYYYMMDDHHMMSS));
        soadto.setVehicleColorName(enumRepository.getColorName(vehicleRecruitingPO.getVehicleColorId() == null?0:Long.parseLong(vehicleRecruitingPO.getVehicleColorId()+"")));
        soadto.setUsingNatureName(enumRepository.getUsingNatureValue(vehicleRecruitingPO.getUsingNature()));
        soadto.setVehicleSeriesName(enumRepository.getVehicleSeriesName(vehicleRecruitingPO.getVehicleSeries()));
        soadto.setVehicleBrandName(enumRepository.getBandName(vehicleRecruitingPO.getVehicleBrandId()));
        soadto.setRegstDate(DateUtil.dateToString(vehicleRecruitingPO.getRegstDate(),DateUtil.YYYYMMDD));
        soadto.setVehicleFromName(vehicleStatusMap.get(vehicleRecruitingPO.getVehicleFrom()));
        soadto.setInternalScope(enumRepository.getAreaScope(vehicleRecruitingPO.getCityId()));
        Map<Integer, TmsCertificateCheckPO> vehCheckPOMap = checkQueryService.queryCertificateCheckToMap(vehicleRecruitingPO.getVehicleId(), TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode());
        soadto.setVehicleCertiData(CtripCommonUtils.resultSOADTO(vehCheckPOMap.get(TmsTransportConstant.CertificateTypeEnum.CARCERTILICENSE.getCode())));
        soadto.setNetTansCtfctData(CtripCommonUtils.resultSOADTO(vehCheckPOMap.get(TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT.getCode())));
        soadto.setVehProLineList(productionLineUtil.getShowProductionLineList(vehicleRecruitingPO.getCategorySynthesizeCode()));
        soadto.setVehNetAppealMaterials(vehicleRecruitingPO.getNetAppealMaterials());
        soadto.setVehicleLicenseCityName(enumRepository.getCityName(vehicleRecruitingPO.getVehicleLicenseCityId()));
        if(StringUtils.isEmpty(soadto.getVehicleCertiImg())){
            soadto.setVehicleCertiData(null);
        }
        if(StringUtils.isEmpty(soadto.getNetTansCtfctImg()) && StringUtils.isEmpty(soadto.getVehNetAppealMaterials())){
            soadto.setNetTansCtfctData(null);
        }
        soadto.setCertificateConfigStr(vehicleRecruitingPO.getCertificateConfig());
        soadto.setAppealMaterialsShowSwitch(vehicleRecruitingPO.getBdTurnDownCount().intValue() >= qconfig.getBdTurnDownCountThreshold().intValue());
        if(StringUtils.isNotEmpty(vehicleRecruitingPO.getOcrPassStatusJson())){
            soadto.setOcrPassStatusList(JsonUtil.fromJson(vehicleRecruitingPO.getOcrPassStatusJson(), new TypeReference<List<OcrPassStatusModelSOA>>() {}));
        }
        return Result.Builder.<DrvVehRecruitingDetailSOADTO>newResult().success() .withData(soadto) .build();

    }

    @Override
    public Result<List<QueryDrvVehRecruitingModRrdSOADTO>> queryDrvVehRecruitingModRrd(Long drvRecruitingId,Integer recruitingType) {
        return queryDrvVehRecruitingModRrd(Lists.newArrayList(drvRecruitingId), recruitingType);
    }

    @Override
    public Result<List<QueryDrvVehRecruitingModRrdSOADTO>> queryDrvVehRecruitingModRrd(List<Long> drvRecruitingIdList,
      Integer recruitingType) {
        if(CollectionUtils.isEmpty(drvRecruitingIdList)){
            return Result.Builder.<List<QueryDrvVehRecruitingModRrdSOADTO>>newResult().success() .withData(Lists.newArrayList()) .build();
        }
        //招募类型必传,并且只能为1or2
        if(recruitingType == null){
            recruitingType = TmsTransportConstant.RecruitingTypeEnum.drv.getCode();
        }
        Integer recordType = CommonEnum.RecordTypeEnum.CHECK.getCode();
        //招募详情兼容车辆招募,为了区分司机和车辆，查询条件中固定RecruitingType 便于查询
        if(Objects.equals(recruitingType, TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode())){
            recordType = CommonEnum.RecordTypeEnum.VEH_CHECK.getCode();
        }

        List<TmsModRecordPO> poList = modRecordRespository.queryModRecordList(drvRecruitingIdList, recordType);
        if(CollectionUtils.isEmpty(poList)){
            return Result.Builder.<List<QueryDrvVehRecruitingModRrdSOADTO>>newResult().success() .withData(Lists.newArrayList()) .build();
        }
        Map<Integer, String> approverStatusMap = enumRepository.getRecruitingApproverStatus();
        List<QueryDrvVehRecruitingModRrdSOADTO> soadtoList = Lists.newArrayList();
        poList.forEach(tmsModRecordPO -> {
            QueryDrvVehRecruitingModRrdSOADTO soadto = new QueryDrvVehRecruitingModRrdSOADTO();
            soadto.setId(tmsModRecordPO.getId());
            soadto.setDrvRecruitingId(tmsModRecordPO.getRrdId());
            String recordCount = tmsModRecordPO.getModContent();
            Integer approverStatus = -1;
            Integer checkStatusType = null;
            String approverStatusName = "";
            if(StringUtils.isNotEmpty(recordCount)){
                List<TmsModContent> contentList = JsonUtil.fromJson(recordCount,new TypeReference<List<TmsModContent>>(){});
                for(TmsModContent modContent : contentList){
                    if(Objects.equals(modContent.getAttributeKey(), Constant.Fields.approver_status)){
                        soadto.setAttributeKey(modContent.getAttributeKey());
                        soadto.setApproverStatus(Integer.parseInt(modContent.getChangeValue()));
                        approverStatus = Integer.parseInt(modContent.getChangeValue());
                    }
                    if(Objects.equals(modContent.getAttributeKey(), Constant.Fields.approver_remark)){
                        soadto.setRemark(modContent.getChangeValue());
                    }
                    if(Objects.equals(modContent.getAttributeKey(), Constant.Fields.certificate_check_status)){
                        soadto.setAttributeKey(modContent.getAttributeKey());
                        checkStatusType = 1;
                        approverStatusName = modContent.getChangeValue();
                    }
                    if(Objects.equals(modContent.getAttributeKey(), Constant.Fields.approver_operation)){
                        soadto.setApproverAperationName(TmsTransportConstant.ApproverAperationEnum.getText(Integer.parseInt(modContent.getChangeValue())));
                    }
                }
            }
            soadto.setModifyUser(tmsModRecordPO.getModifyUser());
            if(checkStatusType == null){
                soadto.setApproverStatusName(approverStatusMap.get(approverStatus)==null ?"" : approverStatusMap.get(approverStatus));
            }else{
                soadto.setApproverStatusName(approverStatusName);
            }
            soadto.setModifyDate(DateUtil.timestampToString(tmsModRecordPO.getDatachangeCreatetime(),DateUtil.YYYYMMDDHHMMSS));
            soadtoList.add(soadto);
        });
        return Result.Builder.<List<QueryDrvVehRecruitingModRrdSOADTO>>newResult().success() .withData(soadtoList) .build();
    }

    @Override
    public Result<String> checkSupplierQualification(Long supplierId) {
        //查询供应商下服务商
        List<Long> serviceProIds =  enumRepository.queryServiceProviderIds(supplierId);
        if(CollectionUtils.isEmpty(serviceProIds)){
            return Result.Builder.<String>newResult().success() .withData(TmsTransportConstant.SupplierQualificationEnum.none.getCode()) .build();
        }

        //服务商下是否有sku TODO 去掉服务商必须创建SKU判断
       /* List<SkuDTO> skuDTOList = enumRepository.querySkuList(serviceProIds);
        if(CollectionUtils.isEmpty(skuDTOList)){
            return Result.Builder.<String>newResult().success() .withData(TmsTransportConstant.SupplierQualificationEnum.none.getCode()) .build();
        }*/
        //查询服务商所关联的城市
        List<Long> cityIdList = enumRepository.queryCityListByServiceProviderIds(serviceProIds,supplierId);
        if(CollectionUtils.isEmpty(cityIdList)){
            return Result.Builder.<String>newResult().success() .withData(TmsTransportConstant.SupplierQualificationEnum.none.getCode()) .build();
        }
        List<City> cityList = enumRepository.queryByCityIds(cityIdList);
        if(CollectionUtils.isEmpty(cityList)){
            return Result.Builder.<String>newResult().success() .withData(TmsTransportConstant.SupplierQualificationEnum.none.getCode()) .build();
        }
        //遍历城市列表,if 将境内外标识写入set,判断set值，为0表示无满足条件,1.城市中只包含境内或境外,2.境内外都包含
        Set<String> setStatus = Sets.newHashSet();
        cityList.forEach(city -> {
            if(!city.isChineseMainland()){
                setStatus.add(TmsTransportConstant.SupplierQualificationEnum.overseas.getCode());
            }else{
                setStatus.add(TmsTransportConstant.SupplierQualificationEnum.domestic.getCode());
            }
        });
        int setSize = setStatus.size();
        if(setSize == 0){
            return Result.Builder.<String>newResult().success() .withData(TmsTransportConstant.SupplierQualificationEnum.none.getCode()) .build();
        }else if (setSize == 2){
            return Result.Builder.<String>newResult().success() .withData(TmsTransportConstant.SupplierQualificationEnum.all.getCode()) .build();
        }else if(setSize == 1){
            if(setStatus.contains(TmsTransportConstant.SupplierQualificationEnum.overseas.getCode())){
                return Result.Builder.<String>newResult().success() .withData(TmsTransportConstant.SupplierQualificationEnum.overseas.getCode()) .build();
            }else{
                return Result.Builder.<String>newResult().success() .withData(TmsTransportConstant.SupplierQualificationEnum.domestic.getCode()) .build();
            }
        }
        return Result.Builder.<String>newResult().success() .withData(TmsTransportConstant.SupplierQualificationEnum.none.getCode()) .build();
    }

    @Override
    public Result<List<TodoListTypeSOA>> todoListCount(TodoListCountSOARequestType requestType) {
        List<TodoListCountPO> reslutList = Lists.newArrayList();
        List<TodoListTypeSOA> todoListTypes = Lists.newArrayList();
        String todoCode = StringUtils.isEmpty(requestType.getTodoCode())?"":requestType.getTodoCode();
        Map<String,String> drviverMap = qconfig.getTodoListDriverUrlMap();
        Map<String,String> vehicleMap = qconfig.getTodoListVehicleUrlMap();
        try {
            switch (todoCode){
                case "vbk_home_todolist_car_driver":
                    reslutList =  drvRecruitingRepository.todoListCount(requestType.getProviderId());
                    todoListTypes = getToduList(reslutList,requestType.getTodoCode(),drviverMap.get(offlineUrl_key),drviverMap.get(onlineUrl_key),drviverMap.get(appUrl_key));
                    break;
                case "vbk_home_todolist_car_vehicle":
                    reslutList =  vehicleRecruitingRepository.todoListCount(requestType.getProviderId());
                    todoListTypes = getToduList(reslutList,requestType.getTodoCode(),vehicleMap.get(offlineUrl_key),vehicleMap.get(onlineUrl_key),vehicleMap.get(appUrl_key));
                    break;
                default:
                    reslutList =  drvRecruitingRepository.todoListCount(requestType.getProviderId());
                    todoListTypes = getToduList(reslutList,"vbk_home_todolist_car_driver",drviverMap.get(offlineUrl_key),drviverMap.get(onlineUrl_key),drviverMap.get(appUrl_key));
                    reslutList =  vehicleRecruitingRepository.todoListCount(requestType.getProviderId());
                    List<TodoListTypeSOA> todoListTypess = getToduList(reslutList,"vbk_home_todolist_car_vehicle",vehicleMap.get(offlineUrl_key),vehicleMap.get(onlineUrl_key),vehicleMap.get(appUrl_key));
                    todoListTypes = (List<TodoListTypeSOA>) CollectionUtils.union(todoListTypes,todoListTypess);
            }
            if(CollectionUtils.isEmpty(todoListTypes)){
                return Result.Builder.<List<TodoListTypeSOA>>newResult().success().withData(Lists.newArrayList()).build();
            }
            return Result.Builder.<List<TodoListTypeSOA>>newResult().success().withData(todoListTypes).build();
        }catch (Exception e){
            logger.error("todoListCountError","params:{}",requestType.toString());
        }
        return Result.Builder.<List<TodoListTypeSOA>>newResult().success().withData(todoListTypes).build();
    }

    @Override
    public Result<Boolean> checkRecruitingDrvOnly(String drvPhone, String drvIdCard) {
        try {
            //判断手机号唯一
            if (drvRecruitingRepository.checkRecruitingDrvOnly(TmsTransUtil.encrypt(drvPhone, KeyType.Phone), TmsTransportConstant.DrvOnlyTypeEnum.PHONE.getCode())) {
               return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportPhoneAlreadyExists)).withData(false).build();
            }
            //身份证唯一
            if (drvRecruitingRepository.checkRecruitingDrvOnly(TmsTransUtil.encrypt(drvIdCard, KeyType.Identity_Card), TmsTransportConstant.DrvOnlyTypeEnum.IDCARD.getCode())) {
                return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportIdcardAlreadyExists)).withData(false).build();
            }
            return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Result<Boolean> checkRecruitingVehicleOnly(String vehicleLicense, String vin) {
        try {
            //判断车牌号唯一
            if (vehicleRecruitingRepository.checkRecruitingVehOnly(vehicleLicense, TmsTransportConstant.VehOnlyTypeEnum.vehicle_license.getCode())) {
                return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportVehicleLicenseAlreadyExists)).withData(false).build();
            }
            //vin码唯一
            if (vehicleRecruitingRepository.checkRecruitingVehOnly(vin, TmsTransportConstant.VehOnlyTypeEnum.vehicle_vin.getCode())) {
                return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportVinAlreadyExists)).withData(false).build();
            }
            return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Result<DrvVehRecruitingDetailSOADTO> queryDrvVehRecruitingDetailFromH5(DrvVehRecruitingDetailFromH5RequestType requestType) {
        DrvVehRecruitingDetailSOADTO soadto = new DrvVehRecruitingDetailSOADTO();
        Long drvRecruitingId = requestType.getDrvRecruitingId();
        String drvPhone = requestType.getDrvPhone();//手机号必传
        //手机号和验证码必传
        if (StringUtils.isEmpty(drvPhone) || StringUtils.isEmpty(requestType.getVerificationCode())) {
            return Result.Builder.<DrvVehRecruitingDetailSOADTO>newResult().fail().withData(null).build();
        }
        try {
            //鉴权-当前手机号和验证码是否有效
            if(!h5VerificationPermissions(drvPhone,requestType.getVerificationCode())){
                return Result.Builder.<DrvVehRecruitingDetailSOADTO>newResult().fail().withData(null).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.capacitymgtAuditmgtCurrentoperationNopermit)).build();
            }
            DrvRecruitingPO drvRecruitingPO = null;
            if(drvRecruitingId !=null && drvRecruitingId > 0 ){
                drvRecruitingPO = drvRecruitingRepository.queryByPK(drvRecruitingId);
            }
            if(drvRecruitingId == null && StringUtils.isNotEmpty(drvPhone)){
                List<DrvRecruitingPO> recruitingPOList = drvRecruitingRepository.queryDomesticRecruitingDrvByPhone(TmsTransUtil.encrypt(drvPhone,KeyType.Phone));
                if(CollectionUtils.isNotEmpty(recruitingPOList)){
                    drvRecruitingPO = recruitingPOList.get(0);
                }
            }
            //H5司机只能查询自己注册的信息，无权限查询工作台创建司机
            if (drvRecruitingPO == null || !Objects.equals(drvRecruitingPO.getDrvFrom(), TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode())) {
                return Result.Builder.<DrvVehRecruitingDetailSOADTO>newResult().fail().withData(null).build();
            }

            Long vehicleId = drvRecruitingPO.getVehicleId();
            String regstDate = "";
            if (vehicleId != null && vehicleId > 0) {
                VehicleRecruitingPO vehicleRecruitingPO = vehicleRecruitingRepository.queryByPK(vehicleId);
                if (vehicleRecruitingPO != null) {
                    BeanUtils.copyProperties(vehicleRecruitingPO, soadto);
                    soadto.setVehProLineList(productionLineUtil.getShowProductionLineList(vehicleRecruitingPO.getCategorySynthesizeCode()));
                    soadto.setVehNetAppealMaterials(vehicleRecruitingPO.getNetAppealMaterials());
                    soadto.setVehicleLicenseCityName(enumRepository.getCityName(vehicleRecruitingPO.getVehicleLicenseCityId()));
                    regstDate = DateUtil.timestampToString(vehicleRecruitingPO.getRegstDate(), DateUtil.YYYYMMDD);
                }
            }
            BeanUtils.copyProperties(drvRecruitingPO, soadto);
            soadto.setDrvProLineList(productionLineUtil.getShowProductionLineList(drvRecruitingPO.getCategorySynthesizeCode()));
            Map<String, String> languageMap = enumRepository.getDrvLanguageMap();
            Map<Integer, String> approverStatusMap = enumRepository.getRecruitingApproverStatus();
            Map<Integer, String> coopModeMap = enumRepository.getDrvCoopMode();
            soadto.setApproverStatus(drvRecruitingPO.getApproverStatus());
            soadto.setSupplierName(enumRepository.getSupplierName(soadto.getSupplierId()));
            soadto.setCityName(enumRepository.getCityName(soadto.getCityId()));
            soadto.setDrvLanguageName(enumRepository.getDrvLanguageName(soadto.getDrvLanguage(), languageMap));
            soadto.setDrvFromName(enumRepository.getDrvFrom().get(soadto.getDrvFrom()));
            soadto.setIntendVehicleTypeName(enumRepository.getIntendVehicleTypeName(soadto.getIntendVehicleTypeId()));
            soadto.setCertiDate(DateUtil.dateToString(drvRecruitingPO.getCertiDate(), DateUtil.YYYYMMDD));
            soadto.setExpiryBeginDate(DateUtil.dateToString(drvRecruitingPO.getExpiryBeginDate(), DateUtil.YYYYMMDD));
            soadto.setExpiryEndDate(DateUtil.dateToString(drvRecruitingPO.getExpiryEndDate(), DateUtil.YYYYMMDD));
            soadto.setDrvPhone(control.getSensitiveData(drvRecruitingPO.getDrvPhone(), KeyType.Phone));
            soadto.setDrvIdcard(control.getSensitiveData(drvRecruitingPO.getDrvIdcard(), KeyType.Identity_Card));
            soadto.setDrvLicenseNumber(control.getSensitiveData(drvRecruitingPO.getDrvLicenseNumber(), KeyType.Identity_Card));
            soadto.setEmail(control.getSensitiveData(drvRecruitingPO.getEmail(), KeyType.Mail));
            soadto.setApproverStatusName(approverStatusMap.get(drvRecruitingPO.getApproverStatus()) == null ? "" : approverStatusMap.get(drvRecruitingPO.getApproverStatus()));
            soadto.setVehicleTypeName(enumRepository.getVehicleTypeName(drvRecruitingPO.getVehicleTypeId()));
            soadto.setDatachangeCreatetime(DateUtil.timestampToString(drvRecruitingPO.getDatachangeCreatetime(), DateUtil.YYYYMMDDHHMMSS));
            soadto.setDatachangeLasttime(DateUtil.timestampToString(drvRecruitingPO.getDatachangeLasttime(), DateUtil.YYYYMMDDHHMMSS));
            soadto.setVehicleColorName(enumRepository.getColorName(soadto.getVehicleColorId() == null ? 0 : Long.parseLong(soadto.getVehicleColorId() + "")));
            soadto.setUsingNatureName(enumRepository.getUsingNatureValue(soadto.getUsingNature()));
            soadto.setVehicleSeriesName(enumRepository.getVehicleSeriesName(soadto.getVehicleSeries()));
            soadto.setVehicleBrandName(enumRepository.getBandName(soadto.getVehicleBrandId()));
            soadto.setCoopModeName(coopModeMap.get(soadto.getCoopMode()));
            soadto.setVehicleCityId(drvRecruitingPO.getCityId());
            soadto.setVehicleCityName(enumRepository.getCityName(drvRecruitingPO.getCityId()));
            // 招募核酸疫苗新增 start
            if (!Strings.isNullOrEmpty(drvRecruitingPO.getVaccinationTimeList())) {
                soadto.setVaccinationTimeList(Arrays.asList(drvRecruitingPO.getVaccinationTimeList().split(",")));
            }
            soadto.setNucleicAcidTestingTime(BaseUtil.dealWithDateToStr(drvRecruitingPO.getNucleicAcidTestingTime()));
            soadto.setVaccineReportImg(drvRecruitingPO.getVaccineReportImg());
            soadto.setNucleicAcidReportImg(drvRecruitingPO.getNucleicAcidReportImg());
            // 招募核酸疫苗新增 end
            soadto.setRegstDate(regstDate);
            soadto.setDrvNetAppealMaterials(drvRecruitingPO.getNetAppealMaterials());
            soadto.setCertificateConfigStr(drvRecruitingPO.getCertificateConfig());
            return Result.Builder.<DrvVehRecruitingDetailSOADTO>newResult().success().withData(soadto).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Boolean h5VerificationPermissions(String drvPhone, String verificationCode) {
        if(StringUtils.isEmpty(drvPhone) || StringUtils.isEmpty(verificationCode)){
            return Boolean.FALSE;
        }
        String key = Constant.H5_VALIDATION_PERMISSIONS_KEY + drvPhone+ "_"+ verificationCode;
        Integer value =  RedisUtils.get(key);
        if(value == null){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @Override
    public Result<PageHolder<QueryRecruitingDrvForCrawlerDTO>> queryRecruitingDrvForCrawler(QueryRecruitingDrvForCrawlerRequestType request) {
        PaginatorDTO paginatorDTO = request.getPaginator();
        List<QueryRecruitingDrvForCrawlerDTO> resultList = Lists.newArrayList();
        PageHolder pageHolder = null;
        if(paginatorDTO == null){
            pageHolder = PageHolder.of(resultList).pageIndex(1).pageSize(10).totalSize(0).build();
            return Result.Builder.<PageHolder<QueryRecruitingDrvForCrawlerDTO>>newResult().success().withData(pageHolder).build();
        }
        int count = drvRecruitingRepository.countWaitApproveRecruitingDrv();
        if(count == 0){
            pageHolder = PageHolder.of(resultList).pageIndex(paginatorDTO.getPageNo()).pageSize(paginatorDTO.getPageSize()).totalSize(0).build();
            return Result.Builder.<PageHolder<QueryRecruitingDrvForCrawlerDTO>>newResult().success().withData(pageHolder).build();
        }
        List<DrvRecruitingPO> drvRecruitingPOList = drvRecruitingRepository.queryWaitApproveRecruitingDrvByPage(paginatorDTO.getPageNo(),paginatorDTO.getPageSize());
        if(CollectionUtils.isEmpty(drvRecruitingPOList)){
            pageHolder = PageHolder.of(resultList).pageIndex(paginatorDTO.getPageNo()).pageSize(paginatorDTO.getPageSize()).totalSize(count).build();
            return Result.Builder.<PageHolder<QueryRecruitingDrvForCrawlerDTO>>newResult().success().withData(pageHolder).build();
        }
        List<String> identityCardList = drvRecruitingPOList.stream().map(DrvRecruitingPO::getDrvIdcard).collect(Collectors.toList());
        List<String> PhoneList = drvRecruitingPOList.stream().map(DrvRecruitingPO::getDrvPhone).collect(Collectors.toList());
        Map<String,String> identityCardMap = TmsTransUtil.batchDecrypt(identityCardList,KeyType.Identity_Card);
        Map<String,String> phoneMap = TmsTransUtil.batchDecrypt(PhoneList,KeyType.Phone);
        Set<Long> drvRecruitingIds = drvRecruitingPOList.stream().map(DrvRecruitingPO::getDrvRecruitingId).collect(Collectors.toSet());
        Map<Long, Integer> checkMap =  checkQueryService.getCertificateCheckNetMap(drvRecruitingIds, TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV,TmsTransportConstant.CertificateTypeEnum.NETDRVCTFCT.getCode());
        for(DrvRecruitingPO drvRecruitingPO : drvRecruitingPOList){
            QueryRecruitingDrvForCrawlerDTO crawlerDTO = new QueryRecruitingDrvForCrawlerDTO();
            BeanUtils.copyProperties(drvRecruitingPO,crawlerDTO);
            crawlerDTO.setDrvId(drvRecruitingPO.getDrvRecruitingId());
            crawlerDTO.setCheckStatus(checkMap.get(drvRecruitingPO.getDrvRecruitingId()));
            String drvIdcard = identityCardMap.get(drvRecruitingPO.getDrvIdcard());
            String drvPhone = phoneMap.get(drvRecruitingPO.getDrvPhone());
//            crawlerDTO.setDrvIdcard(control.getSensitiveData(drvRecruitingPO.getDrvIdcard(),KeyType.Identity_Card));
//            crawlerDTO.setDrvPhone(control.getSensitiveData(drvRecruitingPO.getDrvPhone(),KeyType.Phone));
            crawlerDTO.setDrvIdcard(StringUtils.isEmpty(drvIdcard) ? drvRecruitingPO.getDrvIdcard() : drvIdcard);
            crawlerDTO.setDrvPhone(StringUtils.isEmpty(drvPhone)?drvRecruitingPO.getDrvPhone():drvPhone);
            crawlerDTO.setDatachangeCreatetime(DateUtil.timestampToString(drvRecruitingPO.getDatachangeCreatetime(),DateUtil.YYYYMMDDHHMMSS));
            resultList.add(crawlerDTO);
        }
        pageHolder = PageHolder.of(resultList).pageIndex(paginatorDTO.getPageNo()).pageSize(paginatorDTO.getPageSize()).totalSize(count).build();
        return Result.Builder.<PageHolder<QueryRecruitingDrvForCrawlerDTO>>newResult().success().withData(pageHolder).build();
    }

    @Override
    public Result<PageHolder<QueryRecruitingVehForCrawlerDTO>> queryRecruitingVehForCrawler(QueryRecruitingVehForCrawlerRequestType request) {
        PaginatorDTO paginatorDTO = request.getPaginator();
        PageHolder pageHolder = null;
        List<QueryRecruitingVehForCrawlerDTO> resultList = Lists.newArrayList();
        if(paginatorDTO == null){
            pageHolder = PageHolder.of(resultList).pageIndex(1).pageSize(10).totalSize(0).build();
            return Result.Builder.<PageHolder<QueryRecruitingVehForCrawlerDTO>>newResult().success().withData(pageHolder).build();
        }
        int count = vehicleRecruitingRepository.queryvCountWaitApproveRecruiting();
        if(count == 0){
            pageHolder = PageHolder.of(resultList).pageIndex(paginatorDTO.getPageNo()).pageSize(paginatorDTO.getPageSize()).totalSize(0).build();
            return Result.Builder.<PageHolder<QueryRecruitingVehForCrawlerDTO>>newResult().success().withData(pageHolder).build();
        }
        List<VehicleRecruitingPO> vehicleRecruitingPOList = vehicleRecruitingRepository.queryvWaitApproveRecruitingByPage(paginatorDTO.getPageNo(),paginatorDTO.getPageSize());
        if(CollectionUtils.isEmpty(vehicleRecruitingPOList)){
            pageHolder = PageHolder.of(resultList).pageIndex(paginatorDTO.getPageNo()).pageSize(paginatorDTO.getPageSize()).totalSize(0).build();
            return Result.Builder.<PageHolder<QueryRecruitingVehForCrawlerDTO>>newResult().success().withData(pageHolder).build();
        }
        Set<Long> vehicleIds = vehicleRecruitingPOList.stream().map(VehicleRecruitingPO::getVehicleId).collect(Collectors.toSet());
        Map<Long, Integer> checkMap =  checkQueryService.getCertificateCheckNetMap(vehicleIds, TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE,TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT.getCode());
        for(VehicleRecruitingPO vehicleRecruitingPO : vehicleRecruitingPOList){
            QueryRecruitingVehForCrawlerDTO crawlerDTO = new QueryRecruitingVehForCrawlerDTO();
            BeanUtils.copyProperties(vehicleRecruitingPO,crawlerDTO);
            crawlerDTO.setCheckStatus(checkMap.get(vehicleRecruitingPO.getVehicleId()));
            crawlerDTO.setDatachangeCreatetime(DateUtil.timestampToString(vehicleRecruitingPO.getDatachangeCreatetime(),DateUtil.YYYYMMDDHHMMSS));
            resultList.add(crawlerDTO);
        }
        pageHolder = PageHolder.of(resultList).pageIndex(paginatorDTO.getPageNo()).pageSize(paginatorDTO.getPageSize()).totalSize(count).build();
        return Result.Builder.<PageHolder<QueryRecruitingVehForCrawlerDTO>>newResult().success().withData(pageHolder).build();
    }

    public List<TodoListTypeSOA> getToduList(List<TodoListCountPO> reslutList,String todoCode,String offlineUrl,String onlineUrl,String appUrl){
        List<TodoListTypeSOA> todoListTypes = Lists.newArrayList();
        if(CollectionUtils.isEmpty(reslutList)){
            return todoListTypes;
        }
        reslutList.forEach(todoListCountPO -> {
            TodoListTypeSOA todoListType = new TodoListTypeSOA();
            todoListType.setProviderId(todoListCountPO.getSupplierId());
            todoListType.setCount(todoListCountPO.getCount());
            todoListType.setTodoCode(todoCode);
            todoListType.setOfflineUrl(offlineUrl);
            todoListType.setOnlineUrl(onlineUrl);
            todoListType.setAppUrl(appUrl);
            todoListTypes.add(todoListType);
        });
        return todoListTypes;
    }
}
