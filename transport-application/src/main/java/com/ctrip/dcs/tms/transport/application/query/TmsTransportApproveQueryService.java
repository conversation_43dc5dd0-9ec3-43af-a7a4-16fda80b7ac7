package com.ctrip.dcs.tms.transport.application.query;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.igt.framework.common.base.*;
import com.ctrip.igt.framework.common.result.*;

import java.util.*;

/**
 * <AUTHOR>
 * @Description  审批
 * @Date 14:07 2020/9/11
 * @Param
 * @return
 **/
public interface TmsTransportApproveQueryService {

    /**
     * 审批列表
     * @param request
     * @return
     */
    Result<PageHolder<QueryTransportApproveListSOADTO>> queryApproveList(QueryTransportApproveListSOARequestType request);

    /**
     * 审批详情
     * @param request
     * @return
     */
    Result<QueryTransportApproveDetailSOADTO> queryDrvDetail(QueryTransportApproveDetailSOARequestType request);

    /**
     * 查询审批id所属的供应商
     * @param approveIds
     * @return
     */
    Map<String,String> querySupplierByApproveId(List<Long> approveIds);

}
