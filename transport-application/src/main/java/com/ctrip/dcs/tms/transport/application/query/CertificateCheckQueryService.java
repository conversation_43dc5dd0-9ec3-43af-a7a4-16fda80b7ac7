package com.ctrip.dcs.tms.transport.application.query;

import com.ctrip.dcs.tms.transport.application.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.igt.framework.common.result.*;

import java.util.*;

/**
 * <AUTHOR>
 * @Description  证件核验接口
 * @Date 14:49 2020/9/25
 * @Param 
 * @return 
 **/
public interface CertificateCheckQueryService {

    /**
     * 司机异步核验（驾驶证，网约车驾驶证）
     * @param drvAuditDTO
     * @return
     */
    Result<Boolean> asyncDrvCertificateCheck(DrvAuditDTO drvAuditDTO,Boolean operationFlag);

    /**
     * 车辆异步核验（行驶证，网约车行驶证）
     * @param vehicleAuditDTO
     * @return
     */
    Result<Boolean> asyncVehicleCertificateCheck(VehicleAuditDTO vehicleAuditDTO,Boolean operationFlag);

    /**
     * 司机证件审核
     * @param drvAuditDTO
     * @param operationFlag true 新增,false 编辑
     * @return
     */
    Result<Boolean> drvCertificateCheck(DrvAuditDTO drvAuditDTO,Boolean operationFlag);

    /**
     * 车辆证件审核
     * @param drvAuditDTO
     * @return
     */
    Result<Boolean> vehCertificateCheck(VehicleAuditDTO drvAuditDTO,Boolean operationFlag);

    Map<Integer, TmsCertificateCheckPO> queryCertificateCheckToMap(Long checkId,Integer checkType);


    /**
     * 刷新审核中的驾驶证
     * @param auditDTO
     * @return
     */
    Boolean refreshDrvLicenseCheckIng(DrvAuditDTO auditDTO);

    /**
     * 刷新审核中的行驶证
     * @param vehicleAuditDTO
     * @return
     */
    Boolean refreshVehLicenseCheckIng(VehicleAuditDTO vehicleAuditDTO);

    /**
     * 刷新审核中的网约车驾驶证
     * @param idCard
     * @return
     */
    Boolean refreshNetDrvLicenseCheckIng(Long id,String idCard,Integer checkType,Long supplierId,Integer versionFlag,Integer checkStatus,Long checkId);
    /**
     * 刷新审核中的网约车驾驶证
     * @param paramDTO
     * @return
     */
    Boolean refreshNetDrvLicenseCheckIng(RefreshNetDrvLicenseCheckIngParamDTO paramDTO);
    /**
     * 刷新审核中的网约车行驶证
     * @param vehicleLicense
     * @return
     */
    Boolean refreshNetVehLicenseCheckIng(Long id,String vehicleLicense,Integer checkType,Long supplierId,Integer versionFlag,Integer checkStatus,Long checkId,Long recruitingId,Integer rcruitingType);
    /**
     * 刷新审核中的网约车行驶证
     * @param
     * @return
     */
    Boolean refreshNetVehLicenseCheckIng(RefreshNetVehLicenseCheckIngParamDTO paramDTO);
    /**
     * 刷新审核中的身份证信息
     * @param
     * @return
     */
    Boolean refreshIdCardCheckIng(TmsCertificateCheckPO checkPO,Map<String,TmsBackgroundChecksPO>  checksPOMap);

    /**
     * 刷新全量司机核验数据
     * @param auditDTO
     * @return
     */
    Boolean refreshAllDrvCertificateCheck(DrvAuditDTO auditDTO);

    /**
     * 刷新全量车辆核验数据
     * @param vehicleAuditDTO
     * @return
     */
    Boolean refreshAllVehicleCertificateCheck(VehicleAuditDTO vehicleAuditDTO);


    /**
     * 审批司机证件审核
     * @param drvAuditDTO
     * @param operationFlag true 新增,false 编辑
     * @return
     */
    List<TmsCertificateCheckPO> approveDrvCertificateCheck(DrvAuditDTO drvAuditDTO, Boolean operationFlag);

    /**
     * 审批车辆证件审核
     * @param vehicleAuditDTO
     * @param operationFlag true 新增,false 编辑
     * @return
     */
    List<TmsCertificateCheckPO> approveVehicleCertificateCheck(VehicleAuditDTO vehicleAuditDTO,Boolean operationFlag);

    /**
     * 刷新审核中的驾驶证
     * @param auditDTO
     * @return
     */
    TmsCertificateCheckPO refreshApproveDrvLicenseCheckIng(DrvAuditDTO auditDTO);

    TmsCertificateCheckPO refreshApproveVehLicenseCheckIng(VehicleAuditDTO vehicleAuditDTO);

    TmsCertificateCheckPO refreshApproveNetDrvLicenseCheckIng(String idCard);

    TmsCertificateCheckPO refreshApproveNetVehLicenseCheckIng(String vehicleLicense);

    TmsCertificateCheckPO refreshApproveNetDrvLicenseCheckIng(String idCard,String driverName,String cityId);

    TmsCertificateCheckPO refreshApproveNetVehLicenseCheckIng(String vehicleLicense,String cityId);

    Map<Long, Map<Integer, Boolean>> getCertificateCheckMap(Set<Long> materialIdList, TmsTransportConstant.CertificateCheckTypeEnum checkTypeEnum, List<Integer> certificateTypeList);
    /***
     　* @description: 司机证件核验
     　* <AUTHOR>
     　* @date 2021/9/30 15:57
     */
    Map<String,Object> drvRecruitingCertificateCheck(DrvCertificateCheckParameterDTO parameterDTO);


    /***
     　* @description: 车辆证件核验
     　* <AUTHOR>
     　* @date 2021/10/8 10:07
     */
    Map<String,Object> vehRecruitingCertificateCheck(VehCertificateCheckParameterDTO parameterDTO);

    Map<Long, Integer> getCertificateCheckNetMap(Set<Long> materialIdList, TmsTransportConstant.CertificateCheckTypeEnum checkTypeEnum, Integer certificateType);

    /**
     * 车辆司机合规校验使用城市自己的平台校验，不使用国家平台校验
     * @param cityId
     * @return
     */
    boolean checkFromCityPlatform(String cityId);

}
