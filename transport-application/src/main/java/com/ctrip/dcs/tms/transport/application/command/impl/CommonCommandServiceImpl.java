package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.dto.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl.EnumRepositoryHelper;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.http.util.*;
import com.ctrip.igt.framework.infrastructure.constant.*;
import com.dianping.cat.Cat;
import com.ctrip.igt.infrastructureservice.executor.contract.*;
import com.ctrip.igt.infrastructureservice.executor.contract.ClientInfoDTO;
import com.ctrip.model.CreatePhoneCheckRequestType;
import com.ctrip.model.NotifyVerificationCodeByCallPhoneRequestType;
import com.ctrip.model.NotifyVerificationCodeByCallPhoneResponseType;
import com.ctrip.soa.platform.account.accountsmessageservice.v1.GetCodeByPhoneRequestType;
import com.ctrip.soa.platform.account.accountsmessageservice.v1.GetCodeByPhoneResponseType;
import com.ctriposs.baiji.exception.*;
import com.fasterxml.jackson.core.type.*;
import com.google.common.base.*;
import com.google.common.collect.*;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.core.convert.support.*;
import org.springframework.stereotype.*;
import org.springframework.util.CollectionUtils;

import java.sql.Date;
import java.util.Objects;
import java.util.*;

@Service
public class CommonCommandServiceImpl implements CommonCommandService {

    private static final Logger logger = LoggerFactory.getLogger(CommonCommandServiceImpl.class);

    private final int tryCountLimit = 2;

    private static final String HMAC_PATTERN="signKey={0}&version={1}&busiTypeId={2}&merchantCode={3}&userId={4}&loginName={5}&mobile={6}&identitytype={7}&identitycode={8}";

    @Autowired
    EnumRepository enumRepository;
    @Autowired
    private InfrastructureServiceClientProxy infrastructureServiceClientProxy;
    @Autowired
    private ApprovalProcessAuthQconfig approvalProcessAuthQconfig;
    @Autowired
    private ExernalCallUrlQconfig exernalCallUrlQconfig;
    @Autowired
    private HttpClientUtil httpClientUtil;
    @Autowired
    TmsConnectServiceClientProxy connectServiceClientProxy;

    @Autowired
    SelfServiceProviderConfig selfServiceProviderConfig;
    @Autowired
    TmsTransportQconfig qconfig;
    @Autowired
    TmsCityNetConfigRepository tmsCityNetConfigRepository;

    @Autowired
    private TmsTransportQconfig tmsTransportQconfig;

    @Autowired
    private CommonCommandService commonCommandService;

    @Autowired
    private CertificateCheckQueryService certificateCheckQueryService;

    @Autowired
    private EnumRepositoryHelper enumRepositoryHelper;

    @Autowired
    private DriverDomainService driverDomainService;

    @Autowired
    private AccountsMessageServiceProxy accountsMessageServiceProxy;

    private static final String VOICE_CODE_COUNT_KEY_PREFIX = "voice_code_count_";
    private static final String MESSAGE_COUNT_KEY_PREFIX = "message_count_";

    @Override
    public Result<String> sendMessageByPhone4China(String mobilePhone,String site) {
        SendMesByPhoneRequestType requestType = new SendMesByPhoneRequestType();
        requestType.setMessageCode(getMessageCode(site));
        requestType.setChannel(approvalProcessAuthQconfig.getChannelId());
        requestType.setCountryCode(Constant.CHINA_COUNTRY_CODE);
        requestType.setMobilePhone(mobilePhone);
        try {
            SendMesByPhoneResponseType responseType = infrastructureServiceClientProxy.sendMessageByPhone(requestType);
            if (responseType == null || !Objects.equals(responseType.responseResult.getReturnCode(), TmsTransportConstant.ResultStatusTypeEnum.SUCCESS_CODE.getCode())) {
                return Result.Builder.<String>newResult()
                        .fail()
                        .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withData(responseType == null ? ServiceResponseConstants.ResStatus.EXCEPTION_CODE : responseType.responseResult.getReturnCode())
                        .build();
            }
        } catch (Exception e) {
            return Result.Builder.<String>newResult()
                    .fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportSendMsgError))
                    .withData("-1")
                    .build();

        }
        return Result.Builder.<String>newResult()
                .success()
                .build();
    }

    @Override
    public Result<String> sendMessageByPhone4China(SendVerificationPhoneSOARequestType request) {
        Cat.logEvent(CatEventType.SMS_MESSAGE, "method_entry");


        Integer todayCount = 0;
        String countKey = MESSAGE_COUNT_KEY_PREFIX + request.getMobilePhone();
        if (StringUtils.equals(AreaScopeTypeEnum.OVERSEAS.name(), request.getSite())) {
            Cat.logEvent(CatEventType.SMS_MESSAGE, "check_rate_limit");
            todayCount = RedisUtils.get(countKey);

            if (todayCount != null && todayCount >= tmsTransportQconfig.getMaxVoiceCodePerDay()) {
                Cat.logEvent(CatEventType.SMS_MESSAGE, "rate_limit_exceeded");
                return Result.Builder.<String>newResult()
                        .fail()
                        .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withMsg(SharkUtils.getSharkValue(ErrorCodeEnum.SEND_SMS_OVER_LIMIT.getMessage()))
                        .withData(ErrorCodeEnum.SEND_SMS_OVER_LIMIT.getCode())
                        .build();
            }
        }

        Cat.logEvent(CatEventType.SMS_MESSAGE, "prepare_request");
        SendMesByPhoneRequestType requestType = new SendMesByPhoneRequestType();
        requestType.setMessageCode(getMessageCode(request.getSite()));
        requestType.setChannel(approvalProcessAuthQconfig.getChannelId());
        requestType.setCountryCode(getCountryCode(request.getCountryCode()));
        requestType.setMobilePhone(request.getMobilePhone());
        requestType.setPlatform(request.getPlatform());
        requestType.setClientInfo(convert(request.getClientInfo()));
        requestType.setSite(request.getSite());
        requestType.setSubscene(request.getSubscene());
        try {
            Cat.logEvent(CatEventType.SMS_MESSAGE, "send_message");
            SendMesByPhoneResponseType responseType = infrastructureServiceClientProxy.sendMessageByPhone(requestType);
            if (responseType == null || !Objects.equals(responseType.responseResult.getReturnCode(), TmsTransportConstant.ResultStatusTypeEnum.SUCCESS_CODE.getCode())) {
                Cat.logEvent(CatEventType.SMS_MESSAGE, "send_message_failed", "ERROR", responseType == null ? "null_response" : "ReturnCode:" + responseType.responseResult.getReturnCode());
                return Result.Builder.<String>newResult()
                  .fail()
                  .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                  .withMsg(responseType == null ? ServiceResponseConstants.ResStatus.EXCEPTION_CODE : responseType.responseResult.getReturnMessage())
                  .withData(responseType == null ? ServiceResponseConstants.ResStatus.EXCEPTION_CODE : responseType.responseResult.getReturnCode())
                  .build();
            }

            if (StringUtils.equals(AreaScopeTypeEnum.OVERSEAS.name(), request.getSite())) {
                // 更新计数器
                Cat.logEvent(CatEventType.SMS_MESSAGE, "update_counter");
                if (todayCount == null) {
                    RedisUtils.set(countKey, RedisUtils.ONE_DAY, 1);
                } else {
                    RedisUtils.set(countKey, RedisUtils.ONE_DAY, todayCount + 1);
                }
            }

        } catch (Exception e) {
            Cat.logError("sendMessageByPhone4China error", e);
            return Result.Builder.<String>newResult()
              .fail()
              .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
              .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportSendMsgError))
              .withData("-1")
              .build();

        }
        Cat.logEvent(CatEventType.SMS_MESSAGE, "success");
        return Result.Builder.<String>newResult()
          .success()
          .build();
    }

    private String getCountryCode(String countryCode) {
        if (StringUtils.isBlank(countryCode)) {
            return Constant.CHINA_COUNTRY_CODE;
        }
        return countryCode;
    }

    private String getMessageCode(String site) {
        if (StringUtils.equals(AreaScopeTypeEnum.OVERSEAS.name(), site)) {
            return approvalProcessAuthQconfig.getMessageCodeEn();
        }
        return approvalProcessAuthQconfig.getMessageCode();
    }

    private ClientInfoDTO convert(com.ctrip.dcs.tms.transport.api.model.ClientInfoDTO clientInfo) {
        if (clientInfo == null) {
            return null;
        }
        ClientInfoDTO clientInfoDTO = new ClientInfoDTO();
        clientInfoDTO.setClientIp(clientInfo.getClientIp());
        clientInfoDTO.setClientId(clientInfo.getClientId());
        clientInfoDTO.setRefer(clientInfo.getRefer());
        clientInfoDTO.setUserAgent(clientInfo.getUserAgent());
        clientInfoDTO.setRmsToken(clientInfo.getRmsToken());
        return clientInfoDTO;
    }

    @Override
    public Result<String> checkPoneCode4China(String code, String mobilePhone, String countryCode, String site) {
        CheckMobilePhoneCodeRequestType requestType = new CheckMobilePhoneCodeRequestType();
        requestType.setCode(code);
        requestType.setMobilePhone(mobilePhone);
        requestType.setCountryCode(getCountryCode(countryCode));
        requestType.setChannel(approvalProcessAuthQconfig.getChannelId());
        requestType.setMessageCode(getMessageCode(site));
        try {
            CheckMobilePhoneCodeResponseType responseType = infrastructureServiceClientProxy.checkPhoneCode(requestType);
            if (responseType != null && !Objects.equals(responseType.responseResult.getReturnCode(), TmsTransportConstant.ResultStatusTypeEnum.SUCCESS_CODE.getCode())) {
                return Result.Builder.<String>newResult()
                        .fail()
                        .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withData(responseType.responseResult.getReturnCode())
                        .build();
            }
        } catch (Exception e) {
            return Result.Builder.<String>newResult()
                    .fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportVerifyError))
                    .withData("-1")
                    .build();
        }
        String key = Constant.H5_VALIDATION_PERMISSIONS_KEY + mobilePhone+ "_"+ code;
        RedisUtils.set(key,RedisUtils.ONE_DAY,1);
        createPhoneCheckLog(code, mobilePhone);
        return Result.Builder.<String>newResult()
                .success()
                .build();
    }

    protected void createPhoneCheckLog(String code, String mobilePhone) {
        CreatePhoneCheckRequestType createPhoneCheckRequestType = new CreatePhoneCheckRequestType();
        createPhoneCheckRequestType.setCountryCode(code);
        createPhoneCheckRequestType.setPhoneNumber(TmsTransUtil.encrypt(mobilePhone, KeyType.Phone));
        createPhoneCheckRequestType.setCheckType(1);
        createPhoneCheckRequestType.setCheckState(1);
        driverDomainService.createPhoneCheck(createPhoneCheckRequestType);
    }

    @Override
    public Map<String, Integer> getJurisdictionMap(String roleCode, Integer areaScope, Integer drvFrom, Integer approveStatus) {
        DefaultConversionService defaultConversionService = new DefaultConversionService();
        Map<String, Map<String, String>> authMap = approvalProcessAuthQconfig.getAuthMap();
        Map<String, String> jurisdictionCodeListMap = authMap.get(Constant.FlowJurisdiction);
        if (jurisdictionCodeListMap == null) {
            return Maps.newHashMap();
        }
        Object scopeMapObj = jurisdictionCodeListMap.get(getAreaScopeKey(areaScope));
        if (scopeMapObj == null) {
            return Maps.newHashMap();
        }
//        HashMap scopeMap = (HashMap) scopeMapObj;
        HashMap scopeMap = defaultConversionService.convert(scopeMapObj,HashMap.class);
        if (scopeMap == null) {
            return Maps.newHashMap();
        }
        Object fromMapObj = scopeMap.get(getFromScopeKey(drvFrom));
        if (fromMapObj == null) {
            return Maps.newHashMap();
        }
//        HashMap fromMap = (HashMap) fromMapObj ;
        HashMap fromMap = defaultConversionService.convert(fromMapObj,HashMap.class) ;
        String[] codeList = roleCode.split(",");
        Object roleMapObj = null;
        for (String str : codeList) {
            if (!Strings.isNullOrEmpty(str)) {
                roleMapObj = fromMap.get(str);
                if (roleMapObj != null) {
                    break;
                }
            }
        }
        if (roleMapObj == null) {
            return Maps.newHashMap();
        }
        HashMap roleMap = (HashMap) roleMapObj;
        Object codeMapObj = roleMap.get(approveStatus + "");
        if (codeMapObj == null) {
            return Maps.newHashMap();
        }
        HashMap codeMap = (HashMap) codeMapObj;
        return codeMap;
    }

    protected String getAreaScopeKey(Integer areaScope) {
        if (areaScope.intValue() == AreaScopeTypeEnum.DOMESTIC.getCode().intValue()) {
            return Constant.cisborder;
        }
        if (areaScope.intValue() == AreaScopeTypeEnum.OVERSEAS.getCode().intValue()) {
            return Constant.overseas;
        }
        return "";
    }

    protected String getFromScopeKey(Integer drvFrom) {
        if (drvFrom.intValue() == TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode().intValue()) {
            return Constant.drvAuto;
        }
        if (drvFrom.intValue() == TmsTransportConstant.DrvFromEnum.DRV_MANUAL.getCode().intValue()) {
            return Constant.workbench;
        }
        return "";
    }

    /**
     * 1:供应商通过 2:供应商驳回 3:运营驳回 4:运营通过并激活 5:运营通过未激活
     */
    @Override
    public List<Integer> getAuthCode(List<Integer> sourceCodeList) {
        if (CollectionUtils.isEmpty(sourceCodeList)) {
            return Lists.newArrayList();
        }
        List<Integer> actionCodeList = Lists.newArrayListWithCapacity(sourceCodeList.size() + 1);
        for (Integer approveStatus : sourceCodeList) {
            if (approveStatus.intValue() == TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode().intValue()) {
                actionCodeList.add(TmsTransportConstant.RecruitingApproverActionEnum.supplier_pass.getCode());
            } else if (approveStatus.intValue() == TmsTransportConstant.RecruitingApproverStatusEnum.supplier_turnDown.getCode().intValue()) {
                actionCodeList.add(TmsTransportConstant.RecruitingApproverActionEnum.supplier_refuse.getCode());
            } else if (approveStatus.intValue() == TmsTransportConstant.RecruitingApproverStatusEnum.operating_turnDown.getCode().intValue()) {
                actionCodeList.add(TmsTransportConstant.RecruitingApproverActionEnum.operating_refuse.getCode());
            } else if (approveStatus.intValue() == TmsTransportConstant.RecruitingApproverStatusEnum.finish.getCode().intValue()) {
                ///运营通过并激活按钮开关true:开,false:关
                if(approvalProcessAuthQconfig.getOperatingFinishOnlineSwitch()){
                    actionCodeList.add(TmsTransportConstant.RecruitingApproverActionEnum.operating_finish_online.getCode());
                }
                actionCodeList.add(TmsTransportConstant.RecruitingApproverActionEnum.operating_finish_offline.getCode());
            }
        }
        return actionCodeList;
    }

    private void protectSet(DrvDriverPO driverPO) {
        Map<Integer, TmsCertificateCheckPO> checkPOMap = certificateCheckQueryService.queryCertificateCheckToMap(driverPO.getDrvId(), TmsTransportConstant.CertificateCheckTypeEnum.DRV.getCode());
        TmsCertificateCheckPO headPortraitCheckPO = checkPOMap.get(TmsTransportConstant.CertificateTypeEnum.HEAD_PORTRAIT_COMPLIANCE.getCode());
        if (headPortraitCheckPO == null ||
                headPortraitCheckPO.getCheckStatus() == null ||
                !Objects.equals(headPortraitCheckPO.getCheckStatus().intValue(), TmsTransportConstant.CheckStatusEnum.THROUGH.getCode().intValue())) {
            driverPO.setDrvHeadImg(tmsTransportQconfig.getDefaultDriverPic());
        }
        driverPO.setDrvName(infrastructureServiceClientProxy.nameTranslate(driverPO.getDrvName()));
    }

    @Override
    public String[] createQunarAccount(DrvDriverPO driverPO) {
        logger.info("start creat QunarAccount driverPO "); // anchor
        if (!Arrays.asList(exernalCallUrlQconfig.getQunarAccountSwitch().split(",|，")).contains(driverPO.getInternalScope().toString())) {
            return new String[]{"",""};
        }
        QunarUserDto qunarAccountDto = getQIdByDriverId(driverPO.getDrvId());
        if (qunarAccountDto == null) {
            return new String[]{"",""};
        }
        String[] result = new String[2];
        result[0] = qunarAccountDto.getUserid();
        if (!Arrays.asList(exernalCallUrlQconfig.getPpmAccountSwitch().split(",|，")).contains(driverPO.getInternalScope().toString())) {
            result[1] = "";
            return result;
        }
        protectSet(driverPO);
        String ppmAccount = "";
        // 不调用支付中心
        if(exernalCallUrlQconfig.getRequestCreateAccountSwitch()){
            result[1] = qunarAccountDto.getUserid();
            return result;
        }
        result[1] = ppmAccount;
        return result;
    }

    @Override
    public String createCtripAccount(DrvDriverPO driverPO) {
        if (!Arrays.asList(exernalCallUrlQconfig.getCtripAccountSwitch().split(",|，")).contains(driverPO.getInternalScope().toString())) {
            return "";
        }
        protectSet(driverPO);
        return createCtripIMAccount(driverPO);
    }

    @Override
    public void updateaCtripAccount(DrvDriverPO driverPO) {
        protectSet(driverPO);
        updateCtripIMAccount(driverPO);
    }

    @Override
    public Result<Boolean> sendMessageByPhone(String igtCode,String mobilePhone, String messageCode, Map<String,String> content) {
        try{
            Map<String,String> channelInfo = Maps.newHashMap();
            channelInfo.put("MobilePhone",mobilePhone);
            SendMessageRequestType requestType = new SendMessageRequestType();
            requestType.setChannel(TmsTransportConstant.CTRIP_MESSAGE_CHANNEL);
            requestType.setMessageCode(Integer.parseInt(messageCode));
            MessageBody messageBody  = new MessageBody();
            messageBody.setChannelInfo(channelInfo);
            messageBody.setContent(content);
            requestType.setMsgBody(messageBody);
            SendMessageResponseType responseType =  infrastructureServiceClientProxy.sendMessage(requestType);
            if (responseType == null || !Objects.equals(responseType.responseResult.getReturnCode(), TmsTransportConstant.ResultStatusTypeEnum.SUCCESS_CODE.getCode())) {
                return Result.Builder.<Boolean>newResult()
                        .fail()
                        .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withData(Boolean.FALSE)
                        .build();
            }
        }catch (Exception e){
            return Result.Builder.<Boolean>newResult()
                    .fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgSendEmailError))
                    .withData(Boolean.FALSE)
                    .build();
        }

        return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
    }

    @Override
    public Result<Boolean> sendEmail(String subject,String receiverEmail, String content) {
        try {
            SendEmailRequestType requestType = new SendEmailRequestType();
            if(StringUtils.isEmpty(receiverEmail) || StringUtils.isEmpty(subject)){
               return Result.Builder.<Boolean>newResult()
                        .fail()
                        .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withData(Boolean.FALSE)
                        .build();
            }
            //发件人
            Producer producer = new Producer();
            producer.setEmail(TmsTransportConstant.DEFAULTFROM);
            requestType.setProducer(producer);
            //收件人
            List<Receiver> receiverList = Lists.newArrayList();
            List<String> emailList = Arrays.asList(StringUtils.split(receiverEmail,TmsTransportConstant.SPLIT));
            emailList.forEach(email -> {
                Receiver receiver = new Receiver();
                receiver.setEmail(email);
                receiverList.add(receiver);
            });
            requestType.setSubject(subject);
            requestType.setReceiver(receiverList);
            requestType.setChannel(TmsTransportConstant.CTRIP_EMAIL_CHANNEL);
            StringBuilder sb = new StringBuilder();
            if (content.contains("</body>")) {
                sb.append("<entry><content><![CDATA[").append(content).append("]]></content></entry>");
            } else {
                sb.append("<entry><content><html><![CDATA[").append(content).append("]]></html></content></entry>");
            }
            requestType.setContent(sb.toString());
            requestType.setContentType(TmsTransportConstant.CONTENTTYPE);
            requestType.setHasAttach(Boolean.FALSE);
            SendEmailResponseType responseType =  infrastructureServiceClientProxy.sendEmail(requestType);
            if (responseType == null || !Objects.equals(responseType.getReturnCode(), TmsTransportConstant.ResultStatusTypeEnum.SUCCESS_CODE.getCode())) {
                return Result.Builder.<Boolean>newResult()
                        .fail()
                        .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withData(Boolean.FALSE)
                        .build();
            }

        }catch (Exception e){
            return Result.Builder.<Boolean>newResult()
                    .fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgSendEmailError))
                    .withData(Boolean.FALSE)
                    .build();
        }
        return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
    }

    @Override
    public Result<Boolean> sendJoinMessageByPhone(Long drvId,String igtCode, String mobilePhone) {
        logger.info("sendJoinMessageByPhone,drvId:{}",drvId);
        try{
            Map<String,String> channelInfo = Maps.newLinkedHashMapWithExpectedSize(2);
            Map<String, String> content = Maps.newLinkedHashMapWithExpectedSize(2);
            channelInfo.put("MobilePhone",TmsTransUtil.decrypt(mobilePhone, KeyType.Phone));
            channelInfo.put("UID",String.valueOf(drvId));
            content.put("msgContent","");
            SendMessageRequestType requestType = new SendMessageRequestType();
            requestType.setChannel(TmsTransportConstant.CTRIP_MESSAGE_CHANNEL);
            requestType.setMessageCode(310505);
            MessageBody messageBody = new MessageBody();
            messageBody.setChannelInfo(channelInfo);
            messageBody.setContent(content);
            requestType.setMsgBody(messageBody);
            requestType.setEid(String.valueOf(drvId));
            SendMessageResponseType responseType = infrastructureServiceClientProxy.sendMessage(requestType);
            if (responseType == null || !Objects.equals(responseType.responseResult.getReturnCode(), TmsTransportConstant.ResultStatusTypeEnum.SUCCESS_CODE.getCode())) {
                return Result.Builder.<Boolean>newResult()
                        .fail()
                        .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withData(Boolean.FALSE)
                        .build();
            }
        }catch (Exception e){
            return Result.Builder.<Boolean>newResult()
                    .fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgSendEmailError))
                    .withData(Boolean.FALSE)
                    .build();
        }
        return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
    }

    @Override
    public Result<QueryDrvCardInfoSOAResponseType> queryDrvCardInfo(String name, String drvCardNo) {
        try{
            if(StringUtils.isEmpty(name) || StringUtils.isEmpty(drvCardNo)){
                return Result.Builder.<QueryDrvCardInfoSOAResponseType>newResult().success().withData(null).build();
            }
            QueryDrvCardInfoSOARequestType soaRequestType = new QueryDrvCardInfoSOARequestType();
            soaRequestType.setName(name);
            soaRequestType.setDrvCardNo(drvCardNo);
            QueryDrvCardInfoSOAResponseType soaResponseType = connectServiceClientProxy.queryDrvCardInfo(soaRequestType);
            if (!ServiceResponseConstants.ResStatus.SUCCESS_CODE.equals(soaResponseType.getResponseResult().getReturnCode())) {
                return Result.Builder.<QueryDrvCardInfoSOAResponseType>newResult().success().withData(null).build();
            }
            return Result.Builder.<QueryDrvCardInfoSOAResponseType>newResult().success().withData(soaResponseType).build();
        }catch (Exception e){
            return Result.Builder.<QueryDrvCardInfoSOAResponseType>newResult().success().withData(null).build();
        }
    }

    @Override
    public Result<QueryVehicleCardInfoSOAResponseType> queryVehicleCardInfo(String plateNumber, String plateType) {
        try{
            if(StringUtils.isEmpty(plateNumber)){
                return Result.Builder.<QueryVehicleCardInfoSOAResponseType>newResult().success().withData(null).build();
            }
            QueryVehicleCardInfoSOARequestType soaRequestType = new QueryVehicleCardInfoSOARequestType();
            soaRequestType.setPlateNumber(plateNumber);
            soaRequestType.setPlateType(plateType);
            QueryVehicleCardInfoSOAResponseType soaResponseType = connectServiceClientProxy.queryVehicleCardInfo(soaRequestType);
            if (!ServiceResponseConstants.ResStatus.SUCCESS_CODE.equals(soaResponseType.getResponseResult().getReturnCode())) {
                return Result.Builder.<QueryVehicleCardInfoSOAResponseType>newResult().success().withData(null).build();
            }
            return Result.Builder.<QueryVehicleCardInfoSOAResponseType>newResult().success().withData(soaResponseType).build();
        }catch (Exception e){
            return Result.Builder.<QueryVehicleCardInfoSOAResponseType>newResult().success().withData(null).build();
        }
    }

    @Override
    public Result<QueryNetDrvCardInfoSOAResponseType> queryNetDrvCardInfo(String drvCardNo) {
        try{
            if(StringUtils.isEmpty(drvCardNo)){
                return Result.Builder.<QueryNetDrvCardInfoSOAResponseType>newResult().success().withData(null).build();
            }
            QueryNetDrvCardInfoSOARequestType soaRequestType = new QueryNetDrvCardInfoSOARequestType();
            soaRequestType.setDrvCardNo(drvCardNo);
            QueryNetDrvCardInfoSOAResponseType soaResponseType = connectServiceClientProxy.queryNetDrvCardInfo(soaRequestType);
            if (!ServiceResponseConstants.ResStatus.SUCCESS_CODE.equals(soaResponseType.getResponseResult().getReturnCode())) {
                return Result.Builder.<QueryNetDrvCardInfoSOAResponseType>newResult().success().withData(null).build();
            }
            return Result.Builder.<QueryNetDrvCardInfoSOAResponseType>newResult().success().withData(soaResponseType).build();
        }catch (Exception e){
            return Result.Builder.<QueryNetDrvCardInfoSOAResponseType>newResult().success().withData(null).build();
        }
    }

    @Override
    public Result<QueryNetCertResultDTO> queryNetDrvCardInfo(QueryNetDrvCardInfoParamDTO paramDTO) {
        try{
            //参数校验
            if(StringUtils.isEmpty(paramDTO.getDrvCardNo())){
                return Result.Builder.<QueryNetCertResultDTO>newResult().fail().withData(null).build();
            }
            if(StringUtils.isEmpty(paramDTO.getCityId())){
                return Result.Builder.<QueryNetCertResultDTO>newResult().fail().withData(null).build();
            }
            if(StringUtils.isEmpty(paramDTO.getDriverName())){
                return Result.Builder.<QueryNetCertResultDTO>newResult().fail().withData(null).build();
            }
            //封装查询参数 调用29747接口查询合规信息
            QueryNetDriverCertInfoSOARequestType soaRequestType = new QueryNetDriverCertInfoSOARequestType();
            soaRequestType.setDrvCardNo(paramDTO.getDrvCardNo());
            soaRequestType.setCityId(paramDTO.getCityId());
            soaRequestType.setDriverName(paramDTO.getDriverName());
            QueryNetDriverCertInfoSOAResponseType soaResponseType = connectServiceClientProxy.queryNetDriverCertInfo(soaRequestType);
            //请求处理失败 返回空结果
            if (!ServiceResponseConstants.ResStatus.SUCCESS_CODE.equals(soaResponseType.getResponseResult().getReturnCode())) {
                return Result.Builder.<QueryNetCertResultDTO>newResult().fail().withData(null).build();
            }
            //封装返回参数
            QueryNetCertResultDTO queryNetCertResultDTO = new QueryNetCertResultDTO();
            queryNetCertResultDTO.setResponseCode(soaResponseType.getResponseCode());
            queryNetCertResultDTO.setResponseMsg(soaResponseType.getResponseMsg());
            queryNetCertResultDTO.setThirdResponseMsg(soaResponseType.getThirdResponseMsg());
            return Result.Builder.<QueryNetCertResultDTO>newResult().success().withData(queryNetCertResultDTO).build();
        }catch (Exception e){
            return Result.Builder.<QueryNetCertResultDTO>newResult().fail().withData(null).build();
        }
    }

    @Override
    public Result<QueryNetVehicleCardInfoSOAResponseType> queryNetVehicleCardInfo(String vehicleLicense) {
        try{
            if(StringUtils.isEmpty(vehicleLicense)){
                return Result.Builder.<QueryNetVehicleCardInfoSOAResponseType>newResult().success().withData(null).build();
            }
            QueryNetVehicleCardInfoSOARequestType soaRequestType = new QueryNetVehicleCardInfoSOARequestType();
            soaRequestType.setVehicleLicense(vehicleLicense);
            QueryNetVehicleCardInfoSOAResponseType soaResponseType = connectServiceClientProxy.queryNetVehicleCardInfo(soaRequestType);
            if (!ServiceResponseConstants.ResStatus.SUCCESS_CODE.equals(soaResponseType.getResponseResult().getReturnCode())) {
                return Result.Builder.<QueryNetVehicleCardInfoSOAResponseType>newResult().success().withData(null).build();
            }
            return Result.Builder.<QueryNetVehicleCardInfoSOAResponseType>newResult().success().withData(soaResponseType).build();
        }catch (Exception e){
            return Result.Builder.<QueryNetVehicleCardInfoSOAResponseType>newResult().success().withData(null).build();
        }
    }

    @Override
    public Result<QueryNetCertResultDTO> queryNetVehicleCardInfo(QueryNetVehicleCardInfoParamDTO paramDTO) {
        try{
            //参数校验
            if(StringUtils.isEmpty(paramDTO.getVehicleLicense()) || StringUtils.isEmpty(paramDTO.getCityId())){
                return Result.Builder.<QueryNetCertResultDTO>newResult().fail().withData(null).build();
            }
            //封装查询参数 调用29747接口查询合规信息
            QueryNetVehicleCertInfoSOARequestType soaRequestType = new QueryNetVehicleCertInfoSOARequestType();
            soaRequestType.setVehicleLicense(paramDTO.getVehicleLicense());
            soaRequestType.setCityId(paramDTO.getCityId());
            QueryNetVehicleCertInfoSOAResponseType soaResponseType = connectServiceClientProxy.queryNetVehicleCertInfo(soaRequestType);
            //请求处理失败 返回空结果
            if (!ServiceResponseConstants.ResStatus.SUCCESS_CODE.equals(soaResponseType.getResponseResult().getReturnCode())) {
                return Result.Builder.<QueryNetCertResultDTO>newResult().fail().withData(null).build();
            }
            //封装返回参数
            QueryNetCertResultDTO queryNetCertResultDTO = new QueryNetCertResultDTO();
            queryNetCertResultDTO.setResponseCode(soaResponseType.getResponseCode());
            queryNetCertResultDTO.setResponseMsg(soaResponseType.getResponseMsg());
            queryNetCertResultDTO.setThirdResponseMsg(soaResponseType.getThirdResponseMsg());
            return Result.Builder.<QueryNetCertResultDTO>newResult().success().withData(queryNetCertResultDTO).build();
        }catch (Exception e){
            return Result.Builder.<QueryNetCertResultDTO>newResult().fail().withData(null).build();
        }
    }

    @Override
    public DrvDriverPO drivingLicenseOCR(Long drvId, String imageUrl) {
        try {
            DrivingLicenseRequestType soaRequestType = new DrivingLicenseRequestType();
            soaRequestType.setImageUrl(imageUrl);
            DrivingLicenseResponseType soaResponseType = infrastructureServiceClientProxy.drivingLicense(soaRequestType);
            if (soaResponseType == null || soaResponseType.getResponseResult() == null || !ServiceResponseConstants.ResStatus.SUCCESS_CODE.equals(soaResponseType.getResponseResult().getReturnCode()) ||
                soaResponseType.getDrivingLicense() == null || Strings.isNullOrEmpty(soaResponseType.getDrivingLicense().getFirstGetDate())) {
                return null;
            }
            DrvDriverPO driverPO = new DrvDriverPO();
            driverPO.setDrvId(drvId);
            driverPO.setCertiDate(Date.valueOf(soaResponseType.getDrivingLicense().getFirstGetDate()));
            return driverPO;
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Boolean judgeSupplierIsZY(Long supplierId) {
        Boolean resultFlag = Boolean.FALSE;
        List<Long> serviceProviderIds = enumRepository.queryServiceProviderIds(supplierId);
        List<SelfServiceProviderModel> selfServiceProviders = selfServiceProviderConfig.getSelfServiceProviders();
        for (SelfServiceProviderModel selfServiceProvider : selfServiceProviders) {
            if (serviceProviderIds.contains(selfServiceProvider.getSelfServiceProviderId())) {
                resultFlag = Boolean.TRUE;
                break;
            }
        }
        return resultFlag;
    }

    @Override
    public VehVehiclePO vehicleDrivingLicenseImgOCR(Long vehicleId, String imageUrl) {
        try {
            VehicleLicenseRequestType soaRequestType = new VehicleLicenseRequestType();
            soaRequestType.setFrontImageUrl(imageUrl);
            VehicleLicenseResponseType soaResponseType = infrastructureServiceClientProxy.vehicleLicense(soaRequestType);
            if (soaResponseType == null || soaResponseType.getResponseResult() == null || !ServiceResponseConstants.ResStatus.SUCCESS_CODE.equals(soaResponseType.getResponseResult().getReturnCode()) ||
                    soaResponseType.getVehicleLicense() == null || Strings.isNullOrEmpty(soaResponseType.getVehicleLicense().getRegistDate())) {
                return null;
            }
            VehVehiclePO vehiclePO = new VehVehiclePO();
            vehiclePO.setVehicleId(vehicleId);
            vehiclePO.setRegstDate(Date.valueOf(soaResponseType.getVehicleLicense().getRegistDate()));
            return vehiclePO;
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Boolean judgeSupplierIsZYById(Long supplierId) {
        if (supplierId == null) {
            return false;
        }
        Boolean resultFlag = Boolean.FALSE;
        List<SelfServiceProviderModel> selfServiceProviders = selfServiceProviderConfig.getSelfServiceProviders();
        for (SelfServiceProviderModel selfServiceProvider : selfServiceProviders) {
            if (selfServiceProvider.getSelfSupplierId().longValue() == supplierId.longValue()) {
                resultFlag = Boolean.TRUE;
                break;
            }
        }
        return resultFlag;
    }

    @Override
    public QueryTodayOrderCountDTO queryTodayOrderCount(Long cityId, Long vehicleTypeId, String useVehicleDate) {
        String callUrl = exernalCallUrlQconfig.getCarinnnerHost() + exernalCallUrlQconfig.getQueryBroadcastStatisticsNumUrl();
        logger.info("queryTodayOrderCount","params,cityId:{},vehicleTypeId:{},useVehicleDate:{}", callUrl, cityId, vehicleTypeId,useVehicleDate);
        int tryCount = 0;
        HashMap<String, String> params = Maps.newHashMap();
        params.put("cityId", cityId.toString());
        params.put("carTypeId", vehicleTypeId.toString());
        params.put("sysExpectBookTime", useVehicleDate);
        while (tryCount <= tryCountLimit) {
            try {
                tryCount++;
                String result = HttpClientUtils.doGet(callUrl, params);
                logger.info("queryTodayOrderCount","result={}",result);
                if (Strings.isNullOrEmpty(result)) {
                    continue;
                }
                QueryTodayOrderCountResultDto qunarAcctResDto = JsonUtil.fromJson(result, new TypeReference<QueryTodayOrderCountResultDto>() {
                });
                if (qunarAcctResDto != null && qunarAcctResDto.getBstatus() != null && qunarAcctResDto.getBstatus().getCode() == 0) {
                    return qunarAcctResDto.getData();
                }
            } catch (Exception e) {
                logger.warn("queryTodayOrderCount_Fail","result:{}", e);
            }
        }
        return null;
    }

    /**
     * 查询q侧司机得账号信息
     * @param drvId
     * @return
     */
    private QunarUserDto getQIdByDriverId(Long drvId) {
        String callUrl = exernalCallUrlQconfig.getCarinnnerHost() + exernalCallUrlQconfig.getQbUserCenterFetchUserIdUrl();
        logger.info("Account To getQIdByDriverId","getQIdByDriverId call={}, param={}", callUrl, drvId);
        Map<String, String> param = queryParam(drvId);
        if(CollectionUtils.isEmpty(param)){
            logger.info("getQIdByDriverId_param_null",drvId==null?"":drvId.toString());
            return null;
        }
        logger.info("Account_To_getQIdByDriverId_token","getQIdByDriverId call={}, param={}", callUrl, JsonUtil.toJson(param));
        int tryCount = 0;
        while (tryCount <= tryCountLimit) {
            try {
                tryCount++;
                String result = HttpClientUtils.doGet(callUrl,param);
                logger.info("Account To getQIdByDriverId","{}call: call={}, result={}", tryCount, callUrl, result);
                if (Strings.isNullOrEmpty(result)) {
                    continue;
                }
                QunarUserResultDto qunarUserResultDto = JsonUtil.fromJson(result, new TypeReference<QunarUserResultDto>() {
                });
                if (qunarUserResultDto == null || qunarUserResultDto.getBstatus() == null || qunarUserResultDto.getData() == null ||
                        (qunarUserResultDto.getBstatus().getCode() != 0)) {
                    continue;
                }
                return qunarUserResultDto.getData();
            } catch (Exception e) {
                logger.warn("fail to get drv qunar account, drvId={}", drvId, e);
            }
        }
        logger.error("fail to get drv qunar account, drvId={}", drvId);
        return null;
    }

    /**
     * 司机id加密
     * @return
     */
    private Map<String, String> queryParam(Long drvId){
        if(drvId == null){
            return new HashMap<>();
        }
        Map<String, String> param = new HashMap<>();
        param.put("driverId",drvId.toString());
        //增加md5token参数
        Boolean tokenSwitch = tmsTransportQconfig.getQueryQIdByDriverIdTokenSwitch();
        if(tokenSwitch != null && tokenSwitch){
            String md5Token = MD5Util.getMD5Str(drvId.toString(),MD5Util.SALT);
            if(!org.springframework.util.StringUtils.isEmpty(md5Token)){
                param.put("token",md5Token);
            }
        }
        return param;
    }


    private String createCtripIMAccount(DrvDriverPO driverPO) {
        String callUrl = exernalCallUrlQconfig.getImAccountCtripCreateUrl();
        logger.info("Account To IMAccount","createCtrip call={}, driverPO:{}", callUrl, driverPO);
        Map<String, String> params = Maps.newLinkedHashMapWithExpectedSize(8);
        params.put("driverId", driverPO.getDrvId().toString());
        params.put("driverName", SharkUtils.getSharkValue(SharkKeyConstant.transportDrvDrvier) +":"+ driverPO.getDrvName());
        params.put("driverIcon", driverPO.getDrvHeadImg());
        params.put("channelType", exernalCallUrlQconfig.getImChannelType());
        params.put("vendorId", driverPO.getSupplierId().toString());
        params.put("vendorName", enumRepository.getSupplierName(driverPO.getSupplierId()));
        int tryCount = 0;
        while (tryCount <= tryCountLimit) {
            try {
                tryCount++;
                String result = HttpClientUtils.doPostJson(callUrl,JsonUtil.toJson(params));
                logger.info("Account To IMAccount","{}call: call={}, result={}", tryCount, callUrl, result);
                if (Strings.isNullOrEmpty(result)) {
                    continue;
                }
                CtripIMAccountCreateResponseDto responseDto = JsonUtil.fromJson(result, new TypeReference<CtripIMAccountCreateResponseDto>() {
                });
                if (responseDto != null && responseDto.getResponseResult() != null && responseDto.getResponseResult().isSuccess()) {
                    return responseDto.getCtripUid();
                }
            } catch (Exception e) {
                logger.warn("Account To IMAccount Fail","fail to createCtrip call={} driverPO:{}", callUrl, driverPO, e);
            }
        }
        logger.error("Account To IMAccount Fail","fail to createCtrip call={} driverPO:{}", callUrl, driverPO);
        return null;
    }

    private void updateCtripIMAccount(DrvDriverPO driverPO) {
        String callUrl = exernalCallUrlQconfig.getImAccountCtripUpdateUrl();
        logger.info("updateCtrip call={}, driverPO:{}", callUrl, driverPO);
        Map<String, String> params = Maps.newLinkedHashMapWithExpectedSize(4);
        params.put("driverId", driverPO.getDrvId().toString());
        params.put("driverName", driverPO.getDrvName());
        params.put("driverIcon", driverPO.getDrvHeadImg());
        params.put("channelType", exernalCallUrlQconfig.getImChannelType());
        int tryCount = 0;
        while (tryCount <= tryCountLimit) {
            try {
                tryCount++;
                String result = HttpClientUtils.doPostJson(callUrl,JsonUtil.toJson(params));
                logger.info("{}call: call={}, result={}", tryCount, callUrl, result);
                if (Strings.isNullOrEmpty(result)) {
                    continue;
                }
                CtripIMAccountUpdateResponseDto responseDto = JsonUtil.fromJson(result, new TypeReference<CtripIMAccountUpdateResponseDto>() {
                });
                if (responseDto != null && responseDto.getResponseResult() != null && responseDto.getResponseResult().isSuccess()) {
                    logger.info("update Ctrip IM Account success!");
                    break;
                }
            } catch (Exception e) {
                logger.warn("fail to updateCtrip call={} driverPO:{}", callUrl, driverPO, e);
            }
        }
    }

    @Override
    public Result<List<QueryCityNetConfigListSOADTO>> queryCityNetConfigList(QueryCityNetConfigListSOARequestType requestType) {
        try {
            List<QueryCityNetConfigListSOADTO> resultList = Lists.newArrayList();
            List<TmsCityNetConfigPO> configPOList = tmsCityNetConfigRepository.queryConfigList(requestType.getCityId(), requestType.getConfigItem(), requestType.getConfigType());
            if (CollectionUtils.isEmpty(configPOList)) {
                return Result.Builder.<List<QueryCityNetConfigListSOADTO>>newResult().success().withData(resultList).build();
            }
            for (TmsCityNetConfigPO tmsCityNetConfigPO : configPOList) {
                QueryCityNetConfigListSOADTO configListSOADTO = new QueryCityNetConfigListSOADTO();
                BeanUtils.copyProperties(tmsCityNetConfigPO, configListSOADTO);
                resultList.add(configListSOADTO);
            }
            return Result.Builder.<List<QueryCityNetConfigListSOADTO>>newResult().success().withData(resultList).build();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Result<String> sendVoiceCode(String mobilePhone, String countryCode, String site) {
        Cat.logEvent(CatEventType.VOICE_CODE, "method_entry");
        // 手机号维度一天24小时内只能发送验证码，次数限制从配置获取
        String countKey = VOICE_CODE_COUNT_KEY_PREFIX + mobilePhone;
        Integer todayCount = RedisUtils.get(countKey);


        try {
            if (StringUtils.equals(AreaScopeTypeEnum.OVERSEAS.name(), site)) {
                if (todayCount != null && todayCount >= tmsTransportQconfig.getMaxVoiceCodePerDay()) {
                    Cat.logEvent(CatEventType.VOICE_CODE, "rate_limit_exceeded");
                    return Result.Builder.<String>newResult()
                            .fail()
                            .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                            .withMsg(SharkUtils.getSharkValue(ErrorCodeEnum.SEND_SMS_OVER_LIMIT.getMessage()))
                            .withData(ErrorCodeEnum.SEND_SMS_OVER_LIMIT.getCode())
                            .build();
                }
            }

            Cat.logEvent(CatEventType.VOICE_CODE, "get_code_by_phone_request");
            GetCodeByPhoneRequestType getCodeByPhoneRequestType = new GetCodeByPhoneRequestType();
            getCodeByPhoneRequestType.setMessageCode(getMessageCode(site));
            getCodeByPhoneRequestType.setCountryCode(getCountryCode(countryCode));
            getCodeByPhoneRequestType.setMobilePhone(mobilePhone);
            GetCodeByPhoneResponseType codeByPhone = accountsMessageServiceProxy.getCodeByPhone(getCodeByPhoneRequestType);
            if (!Objects.equals(codeByPhone.getReturnCode(), 0)) {
                Cat.logEvent(CatEventType.VOICE_CODE, "get_code_by_phone_failed", "ERROR", "ReturnCode:" + codeByPhone.getReturnCode());
                return SendCodeErrorResult();
            }
            String code = codeByPhone.getCode();
            if (StringUtils.isEmpty(code)) {
                Cat.logEvent(CatEventType.VOICE_CODE, "empty_code_received", "ERROR", (String) null);
                return SendCodeErrorResult();
            }

            Cat.logEvent(CatEventType.VOICE_CODE, "notify_verification_code_request");
            NotifyVerificationCodeByCallPhoneRequestType notifyVerificationCodeByCallPhoneRequestType = new NotifyVerificationCodeByCallPhoneRequestType();
            notifyVerificationCodeByCallPhoneRequestType.setPhoneNumber(TmsTransUtil.encrypt(mobilePhone, KeyType.Phone));
            notifyVerificationCodeByCallPhoneRequestType.setVerificationCode(code);
            notifyVerificationCodeByCallPhoneRequestType.setLocale(enumRepositoryHelper.getLocaleCode());
            notifyVerificationCodeByCallPhoneRequestType.setCountryCode(countryCode);
            notifyVerificationCodeByCallPhoneRequestType.setChannel("tms_driver_register");
            NotifyVerificationCodeByCallPhoneResponseType responseType = driverDomainService.notifyVerificationCodeByCallPhone(notifyVerificationCodeByCallPhoneRequestType);
            if (responseType == null || !Objects.equals(responseType.responseResult.getReturnCode(), TmsTransportConstant.ResultStatusTypeEnum.SUCCESS_CODE.getCode())) {
                Cat.logEvent(CatEventType.VOICE_CODE, "notify_verification_code_failed", "ERROR", responseType == null ? "null_response" : "ReturnCode:" + responseType.responseResult.getReturnCode());
                return SendCodeErrorResult();
            }

            // 更新计数器
            Cat.logEvent(CatEventType.VOICE_CODE, "update_counter");
            if (todayCount == null) {
                RedisUtils.set(countKey, RedisUtils.ONE_DAY, 1);
            } else {
                RedisUtils.set(countKey, RedisUtils.ONE_DAY, todayCount + 1);
            }
        } catch (Exception e) {
            logger.error("sendVoiceCOde error", e);
            return SendCodeErrorResult();
        }

        Cat.logEvent(CatEventType.VOICE_CODE, "success");
        return Result.Builder.<String>newResult().success().build();
    }

    @NotNull
    private static Result<String> SendCodeErrorResult() {
        Cat.logEvent(CatEventType.VOICE_CODE, "send_code_error_result");
        return Result.Builder.<String>newResult()
                .fail()
                .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportSendMsgError))
                .withData("-1")
                .build();
    }
}
