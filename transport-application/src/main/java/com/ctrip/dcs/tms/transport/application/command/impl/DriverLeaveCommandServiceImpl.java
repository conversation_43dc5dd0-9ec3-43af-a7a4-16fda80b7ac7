package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.constant.*;
import com.ctrip.platform.dal.dao.annotation.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2020/3/17 15:06
 */
@Service
public class DriverLeaveCommandServiceImpl implements DriverLeaveCommandService {

    private static final Result<Boolean> result = Result.Builder.<Boolean>newResult().fail().
            withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withData(false).build();

    @Autowired
    private DrvDriverLeaveRepository drvDriverLeaveRepository;

    @Autowired
    private TmsQmqProducerCommandService tmsQmqProducerCommandService;

    @Autowired
    private DrvDrvierRepository drvierRepository;

    @Autowired
    private DriverQueryService driverQueryService;

    @Autowired
    private TmsTransportQconfig qconfig;
    @Autowired
    private ApprovalProcessAuthQconfig approvalProcessAuthQconfig;
    @Autowired
    CommonCommandService commandService;
    @Autowired
    TspTransportGroupDriverRelationRepository relationRepository;

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> addDrvLeave(DrvDriverLeavePO driverLeavePO) {
        Result.Builder<Boolean> result = Result.Builder.<Boolean>newResult();
        //校验请假时段是否重复
        if (drvDriverLeaveRepository.checkDrvLeaveTime(driverLeavePO) > 0) {
            return result.fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg("[" + driverLeavePO.getLeaveBeginTime() + "~" + driverLeavePO.getLeaveEndTime() + "]" + SharkUtils.getSharkValue(SharkKeyConstant.transportLeaveTtimeRepeatPleaseCheck))
                    .withData(false)
                    .build();
        }

        if (checkLeaveValidity(driverLeavePO, drvDriverLeaveRepository.queryDrvLeaveDetailForDsp(Lists.newArrayList(driverLeavePO.getDrvId())))) {
            return result.fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportDrvLeaveTimeExceedLimit))
                    .withData(false)
                    .build();
        }

        int count = drvDriverLeaveRepository.addDrvLeave(driverLeavePO);
        if (count <= 0) {
            return result.fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withData(false)
                    .build();
        }
        tmsQmqProducerCommandService.sendDrvLeaveQmq(driverLeavePO.getDrvId(), DateUtil.timestampToString(driverLeavePO.getLeaveBeginTime(), DateUtil.YYYYMMDDHHMMSS),
                DateUtil.timestampToString(driverLeavePO.getLeaveEndTime(), DateUtil.YYYYMMDDHHMMSS));
        //发送请假短信
        this.senDriverLeaveSms(driverLeavePO);
        return result.success()
                .withData(true)
                .build();
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> closeDrvLeave(DrvDriverLeavePO driverLeavePO) {
        if (Objects.isNull(driverLeavePO) || Objects.isNull(driverLeavePO.getDrvId())) {
            return result;
        }
        DrvDriverPO driverPO = drvierRepository.queryByPk(driverLeavePO.getDrvId());
        if (Objects.isNull(driverPO)) {
            return result;
        }
        List<Long> vehicleIs = Lists.newArrayList();
        if (driverPO.getVehicleId() != null && driverPO.getVehicleId() > 0) {
            vehicleIs.add(driverPO.getVehicleId());
        }
        if (qconfig.getDrvLeaveEpidemicSwitch()) {
            //新增按城市限制操作
            Result<Boolean> disableFlagResult = driverQueryService.judgeDrvOnlinePermission(Objects.equals(driverPO.getDrvStatus(), TmsTransportConstant.DrvStatusEnum.ONLINE.getCode()), Arrays.asList(driverPO.getCityId()), vehicleIs);
            if (!disableFlagResult.isSuccess()) {
                return disableFlagResult;
            }
        }
        int count = drvDriverLeaveRepository.closeDrvLeave(driverLeavePO);
        if (count > 0) {
            tmsQmqProducerCommandService.sendDrvCloseLeaveQmq(driverLeavePO.getDrvId());
            //报名制司机-报名状态已剔除置为已报名
            relationRepository.updatEeliminateStatusByDrvId(Arrays.asList(driverLeavePO.getDrvId()),driverLeavePO.getModifyUser());
            return Result.Builder.<Boolean>newResult()
                    .success()
                    .withData(true)
                    .build();
        }
        return result;
    }

    public void senDriverLeaveSms(DrvDriverLeavePO leavePO) {
        if (Objects.isNull(leavePO) || leavePO.getDrvId() == null || leavePO.getDrvId() <= 0) {
            return;
        }
        DrvDriverPO drvDriverPO = drvierRepository.queryByPk(leavePO.getDrvId());
        if (Objects.isNull(drvDriverPO)) {
            return;
        }
        Map<String, String> params = Maps.newHashMap();
        params.put("DriverName", drvDriverPO.getDrvName());
        params.put("leaveBeginTime", DateUtil.timestampToString(leavePO.getLeaveBeginTime(), DateUtil.YYYYMMDDHHMMSS));
        params.put("leaveEndTime", DateUtil.timestampToString(leavePO.getLeaveEndTime(), DateUtil.YYYYMMDDHHMMSS));
        params.put("leaveReason", leavePO.getLeaveReason());
        String smsCode = approvalProcessAuthQconfig.getDriverLeaveSmsCode();
        commandService.sendMessageByPhone(drvDriverPO.getIgtCode(), TmsTransUtil.decrypt(drvDriverPO.getDrvPhone(), KeyType.Phone), smsCode, params);
    }

    /**
     * 1.[  |  ] [   ] [  ]
     * 2.| [  ] [  ] [  ]
     * 3.|
     */
    private boolean checkLeaveValidity(DrvDriverLeavePO driverLeavePO, List<DrvLeaveDetailPO> detailPOS) {
        if (CollectionUtils.isEmpty(detailPOS)) {
            return false;
        }
        boolean leaving = false;
        Date now = new Date();
        Long leaveMinutes = 0L;
        for (DrvLeaveDetailPO po : detailPOS) {
            if (po.getLeaveBeginTime().before(now) && po.getLeaveEndTime().after(now)) {
                leaving = true;
                leaveMinutes += DateUtil.getMinOfDate(now, po.getLeaveEndTime());
            } else {
                leaveMinutes += DateUtil.getMinOfDate(po.getLeaveBeginTime(), po.getLeaveEndTime());
            }
        }
        if (driverLeavePO.getLeaveEndTime().after(now)) {
            leaveMinutes += DateUtil.getMinOfDate(driverLeavePO.getLeaveBeginTime().before(now) ? now : driverLeavePO.getLeaveBeginTime(), driverLeavePO.getLeaveEndTime());
        }
        Long limitMinutes = (leaving ? 1 : 0) + 525600L * qconfig.getMaximumNumberYearsOfLeave();
        return leaveMinutes > limitMinutes;
    }
}
