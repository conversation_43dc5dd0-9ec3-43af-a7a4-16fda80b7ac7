package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.dcs.tms.transport.application.query.INetCertNoCheckRuleService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.NetCertCheckRuleDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.NetCertNoCheckRuleDetailDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.DriverVehicleNetCertCheckRuleConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.TmsTransUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.regex.Pattern;

@Component
public class NetCertNoCheckRuleService implements INetCertNoCheckRuleService {
    private Logger logger = LoggerFactory.getLogger(DriverVehicleNetCertCheckRuleConfig.class);
    @Resource
    private DriverVehicleNetCertCheckRuleConfig driverVehicleNetCertCheckRuleConfig;
    @Autowired
    private DrvDrvierRepository drvDrvierRepository;
    @Override
    public NetCertCheckRuleDTO queryNetCertNoCheckRule(String cityId) {
        try{
            if(StringUtils.isEmpty(cityId)){
                return null;
            }
            NetCertCheckRuleDTO netCertCheckRuleDTO = driverVehicleNetCertCheckRuleConfig.getRuleByCity(cityId);
            return netCertCheckRuleDTO;
        }catch (Exception e){
            logger.error("NetCertNoCheckRuleService_queryCheckRule_ex",e);
            return null;
        }
    }

    @Override
    public boolean checkDriverNetCertNo(String cityId, String netCertNo) {
        try{
            //必传参数校验
            if(StringUtils.isEmpty(netCertNo) || StringUtils.isEmpty(cityId)){
                return false;
            }
            //有效规则校验
            NetCertCheckRuleDTO netCertCheckRuleDTO = queryNetCertNoCheckRule(cityId);
            if(netCertCheckRuleDTO == null || CollectionUtils.isEmpty(netCertCheckRuleDTO.getDriverNetCertNoCheckRule())){
                return true;
            }
            //规则校验
            List<NetCertNoCheckRuleDetailDTO> netCertNoCheckRuleDetailDTOList = netCertCheckRuleDTO.getDriverNetCertNoCheckRule();
            for (NetCertNoCheckRuleDetailDTO netCertNoCheckRuleDetailDTO : netCertNoCheckRuleDetailDTOList) {
                if(check(netCertNoCheckRuleDetailDTO,netCertNo)){
                    return true;
                }
            }
            return false;
        }catch (Exception e){
            //发生异常不阻碍流程判定为通过
            logger.info("NetCertNoCheckRuleService_checkDriverNetCertNo_ex",e);
            return true;
        }
    }

    @Override
    public boolean checkVehicleNetCertNo(String cityId, String netCertNo) {
        try{
            //必传参数校验
            if(StringUtils.isEmpty(netCertNo) || StringUtils.isEmpty(cityId)){
                return false;
            }
            //有效规则校验
            NetCertCheckRuleDTO netCertCheckRuleDTO = queryNetCertNoCheckRule(cityId);
            if(netCertCheckRuleDTO == null || CollectionUtils.isEmpty(netCertCheckRuleDTO.getVehicleNetCertNoCheckRule())){
                return true;
            }
            //规则校验
            List<NetCertNoCheckRuleDetailDTO> netCertNoCheckRuleDetailDTOList = netCertCheckRuleDTO.getVehicleNetCertNoCheckRule();
            for (NetCertNoCheckRuleDetailDTO netCertNoCheckRuleDetailDTO : netCertNoCheckRuleDetailDTOList) {
                if(check(netCertNoCheckRuleDetailDTO,netCertNo)){
                    return true;
                }
            }
            return false;
        }catch (Exception e){
            //发生异常不阻碍流程判定为通过
            logger.info("NetCertNoCheckRuleService_checkVehicleNetCertNo_ex",e);
            return true;
        }
    }

    @Override
    public boolean checkDriverNetCertNo(String cityId, String netCertNo, Long driverId) {
        //规则校验失败
        if(!checkDriverNetCertNo(cityId,netCertNo)){
            return false;
        }
        //规则校验成功 不需要与身份证比对
        if(!isEqualsDriverIdCard(cityId)){
            return true;
        }
        //规则校验成功 与身份证比对
        //从数据库查询司机的身份证号密文
        DrvDriverPO drvDriverPO = drvDrvierRepository.queryByPk(driverId);
        //数据库无值则校验失败
        if(drvDriverPO == null || StringUtils.isEmpty(drvDriverPO.getDrvIdcard())){
            return false;
        }
        //网约车驾驶证号解密
        String result = TmsTransUtil.decrypt(drvDriverPO.getDrvIdcard(), KeyType.Identity_Card);
        //网约车驾驶证号密文与身份证号明文比对 成功
        if(!StringUtils.isEmpty(result) && result.equals(netCertNo)){
            return true;
        }
        //网约车驾驶证号密文与身份证号明文比对 失败
        return false;
    }

    /**
     * 根据城市判断是否需要与身份证号进行比对
     * @param cityId
     * @return
     */
    public boolean isEqualsDriverIdCard(String cityId){
        //必传参数校验
        if(StringUtils.isEmpty(cityId)){
            return false;
        }
        //有效规则校验
        NetCertCheckRuleDTO netCertCheckRuleDTO = queryNetCertNoCheckRule(cityId);
        if(netCertCheckRuleDTO == null || StringUtils.isEmpty(netCertCheckRuleDTO.getEqualsDriverIdCardFlag())){
            return false;
        }
        if("true".equals(netCertCheckRuleDTO.getEqualsDriverIdCardFlag())){
            return true;
        }
        return false;
    }
    /**
     * 校验证件号
     * @param rule
     * @param netCertNo
     * @return
     */
    public boolean check(NetCertNoCheckRuleDetailDTO rule,String netCertNo){
        //校验字符个数
        boolean checkLengthResult = checkLength(rule.getTotalLength(),netCertNo);
        if(!checkLengthResult){
            return false;
        }
        //校验前缀
        boolean checkPrefixResult = checkPrefix(rule.getPrefix(),netCertNo);
        if(!checkPrefixResult){
            return false;
        }
        //校验后缀
        boolean checkSuffixResult = checkSuffix(rule,netCertNo);
        if(!checkSuffixResult){
            return false;
        }
        //返回结果
        return true;
    }

    /**
     *校验字符个数
     * @param length
     * @param netCertNo
     * @return
     */
    public boolean checkLength(String length,String netCertNo){
        if(StringUtils.isEmpty(length)){
            return true;
        }
        return netCertNo.length() == Integer.valueOf(length).intValue();
    }

    /**
     *校验前缀
     * @param prefix
     * @param netCertNo
     * @return
     */
    public boolean checkPrefix(String prefix,String netCertNo){
        if(StringUtils.isEmpty(prefix)){
            return true;
        }
        return netCertNo.startsWith(prefix);
    }

    /**
     * 校验后缀
     * @param rule
     * @param netCertNo
     * @return
     */
    public boolean checkSuffix(NetCertNoCheckRuleDetailDTO rule,String netCertNo){
        //查询后缀
        String netCertNoSuffix = getNetCertNoSuffix(rule.getPrefix(),netCertNo);
        if(netCertNoSuffix == null){
            return true;
        }
        if(StringUtils.isEmpty(rule.getSuffixType()) || StringUtils.isEmpty(rule.getSuffixLength())){
            return true;
        }
        boolean checkLengthResult = netCertNoSuffix.length() == Integer.valueOf(rule.getSuffixLength());
        if(!checkLengthResult){
            return false;
        }
        if("STRING".equals(rule.getSuffixType())){
            return true;
        }
        if("NUMBER".equals(rule.getSuffixType())){
            Pattern pattern = Pattern.compile("[0-9]*");
            return pattern.matcher(netCertNoSuffix).matches();
        }
        return true;
    }

    /**
     * 获取证件号的后缀
     * @param prefix
     * @param netCertNo
     * @return
     */
    public String getNetCertNoSuffix(String prefix,String netCertNo){
        if(StringUtils.isEmpty(prefix)){
            return netCertNo;
        }
        return netCertNo.substring(prefix.length());
    }
}
