package com.ctrip.dcs.tms.transport.task.application.type;

import com.ctrip.dcs.tms.transport.api.model.IsNeedCreateTaskRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryTaskConfigRequestType;
import com.ctrip.dcs.tms.transport.api.model.SaveIsNeedCreateTaskRequestType;
import com.ctrip.dcs.tms.transport.task.infrastructure.common.enums.NeedCreateTaskEnum;
import com.ctrip.igt.framework.common.result.Result;

/**
 * <AUTHOR>
 */
public interface TaskManageService {
    Result<String> queryTaskConfig(QueryTaskConfigRequestType requestType);

    Result<NeedCreateTaskEnum> isNeedCreateTask(IsNeedCreateTaskRequestType requestType);

    Result<Boolean> saveIsNeedCreateTask(SaveIsNeedCreateTaskRequestType requestType);
}
