package com.ctrip.dcs.tms.transport.application.command;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.common.result.*;

/**
 * <AUTHOR>
 * @Description  审批
 * @Date 10:39 2020/11/5
 * @Param 
 * @return 
 **/
public interface TmsTransportApproveCommandService<T> {

    /**
     * 修改审批状态
     * @param requestType
     * @return
     */
    Result<Boolean> updateApproveStatus(TransportApproveStatusUpdateSOARequestType requestType);

    /**
     * 进入审批流
     * @param
     * @return
     */
    Result<Long> insertApprove(AddApproveDTO<T> addApproveDTO, TmsTransportConstant.EnentTypeEnum eventTypeEnum, Long cityId);

    /**
     * 查询已修改的字段是否在待审批流中
     * @param addApproveDTO
     * @return
     */
    Boolean checkColumnApproveIng(AddApproveDTO<T> addApproveDTO);

    /**
     * 保持唯一申请
     * */
    Boolean keepOnlyApprove(DrvDriverPO driverPO, Boolean isNucleicAcidDetectionType);
}
