package com.ctrip.dcs.tms.transport.application.convert;

import com.ctrip.dcs.tms.transport.api.model.OverseasOCRRecognitionSOARequestType;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant;
import com.ctrip.igt.infrastructureservice.executor.contract.CarOCRRequestType;
import com.ctrip.igt.infrastructureservice.executor.contract.DrivingLicenseRequestType;

/**
 * SOA 请求 转换器
 *
 * <AUTHOR> Zhang<PERSON>hen
 * @create 2023/6/29 14:54
 */
public class SOAReqConverter {

    public static DrivingLicenseRequestType buildDrvOCRReq(OverseasOCRRecognitionSOARequestType req) {
        DrivingLicenseRequestType resReq = new DrivingLicenseRequestType();
        resReq.setCityId(req.getCityId());
        resReq.setImageUrl(req.getOcrImgUrl());
        return resReq;
    }

    public static CarOCRRequestType buildCarOCRReq(OverseasOCRRecognitionSOARequestType req) {
        CarOCRRequestType resReq = new CarOCRRequestType();
        resReq.setCityId(req.getCityId());
        resReq.setImageUrl(req.getOcrImgUrl());
        return resReq;
    }

}