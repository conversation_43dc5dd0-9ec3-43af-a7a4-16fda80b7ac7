package com.ctrip.dcs.tms.transport.application.query;

import com.ctrip.dcs.tms.transport.application.dto.DriverLeaveDTO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.common.base.*;
import com.ctrip.igt.framework.common.result.*;

import java.time.*;
import java.util.*;

/**
 * 司机请假查询类接口
 * 
 * <AUTHOR>
 * @Date 2020/3/17 15:03
 */
public interface DriverLeaveQueryService {

  /**
   * 司机请假历史查询
   * 
   * @param driverLeavePO
   * @param pageInfo
   * @return
   */
  Result<PageHolder<DrvLeaveDetailPO>> queryDrvLeaveDetail(DrvDriverLeavePO driverLeavePO, PaginatorDTO pageInfo);

  /**
   * 司机请假历史查询（给派发使用）
   * 
   * @param drvIds
   * @param useCache true 走缓存；false 不走
   * @return
   */
  Result<List<DrvLeaveDetailPO>> queryDrvLeaveDetailForDsp(List<Long> drvIds, Boolean useCache);
  
  /**
   *
   * @param driverId
   * @param checkTime
   * @return
   */
  Result<String> checkDrvLeave(Long driverId, LocalDateTime checkTime);

  /**
   * 查询生效中的请假记录
   * */
  Optional<DrvLeaveDetailPO> getDrvLeaveDetail(Long driverId, LocalDateTime checkTime);

  /**
   * 查询一个司机最近请假开始时间及结束时间 （处理连续）
   * */
  DrvLeaveDetailPO getDrvLeaveLately(Long drvId);

  /**
   * 查询司机请假信息-运力库存使用
   * @param drvId
   * @param id
   * @return
   */
  DriverLeaveDTO queryDriverLeaveById(Long drvId, Long id);
}
