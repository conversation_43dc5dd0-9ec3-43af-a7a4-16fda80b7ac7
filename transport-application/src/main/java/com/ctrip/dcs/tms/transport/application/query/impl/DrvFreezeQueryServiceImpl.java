package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctriposs.baiji.exception.*;
import com.dianping.cat.utils.*;
import com.fasterxml.jackson.core.type.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang.time.*;
import org.springframework.beans.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;
import java.util.stream.*;


/**
 * 司机冻结
 */
@Service
public class DrvFreezeQueryServiceImpl implements DrvFreezeQueryService {

    @Autowired
    DrvDrvierRepository repository;
    @Autowired
    EnumRepository enumRepository;
    @Autowired
    TspTransportGroupDriverRelationRepository relationRepository;
    @Autowired
    VehicleRepository vehicleRepository;
    @Autowired
    TmsDrvFreezeRepository freezeRepository;
    @Autowired
    private SensitiveDataControl control;

    @Override
    public Result<QueryDrvFreezeDetailDTOSOA> queryDrvFreezeDetail(Long drvId) {
        TmsDrvFreezePO drvFreezePO =  freezeRepository.queryByPk(drvId);
        if(drvFreezePO == null){
            return Result.Builder.<QueryDrvFreezeDetailDTOSOA>newResult().success() .withData(null) .build();
        }
        DrvDriverPO drvDriverPO = repository.queryByPk(drvId);
        if(drvDriverPO == null){
            return Result.Builder.<QueryDrvFreezeDetailDTOSOA>newResult().success() .withData(null) .build();
        }
        if(!Objects.equals(drvDriverPO.getDrvId(),drvFreezePO.getDrvId())){
            return Result.Builder.<QueryDrvFreezeDetailDTOSOA>newResult().fail() .withData(null) .build();
        }
        QueryDrvFreezeDetailDTOSOA detailDTOSOA = new QueryDrvFreezeDetailDTOSOA();
        BeanUtils.copyProperties(drvFreezePO,detailDTOSOA);
        detailDTOSOA.setDrvPhone(control.getSensitiveData(drvDriverPO.getDrvPhone(),KeyType.Phone));
        detailDTOSOA.setSupplierId(drvDriverPO.getSupplierId());
        detailDTOSOA.setDrvName(drvDriverPO.getDrvName());
        detailDTOSOA.setDrvStatus(drvDriverPO.getDrvStatus());
        detailDTOSOA.setFirstFreezeTime(DateUtil.timestampToString(drvFreezePO.getFirstFreezeTime(),DateUtil.YYYYMMDDHHMMSS));
        detailDTOSOA.setSupplierName(enumRepository.getSupplierName(drvDriverPO.getSupplierId()));
        detailDTOSOA.setFrozenTime(DateUtil.getAlreadyFreezeShow(drvFreezePO.getFirstFreezeTime()));
        detailDTOSOA.setAccumulationFreezeTime(DateUtil.getFreezeDayHourShow(drvFreezePO.getFreezeHour()));
        detailDTOSOA.setRemainingFreezeTime(DateUtil.getRemainingFreezeTime(drvFreezePO.getFirstFreezeTime(),drvFreezePO.getFreezeHour()));
        if(StringUtils.isNotEmpty(drvFreezePO.getFreezeReason())){
            List<FreezeReasonDTO>  reasonData =  JsonUtil.fromJson(drvFreezePO.getFreezeReason(), new TypeReference<List<FreezeReasonDTO>>() {});
            detailDTOSOA.setReasonData(reasonData);
        }else{
            detailDTOSOA.setReasonData(Lists.newArrayList());
        }
        detailDTOSOA.setOriginFreezeTime(DateUtil.getEndfreezeTime(drvFreezePO.getFirstFreezeTime(),drvFreezePO.getFreezeHour()));
        return Result.Builder.<QueryDrvFreezeDetailDTOSOA>newResult().success() .withData(detailDTOSOA) .build();
    }

    @Override
    public Result<Boolean> checkSupplierFreezePerm(Long drvId,Integer accountType) {
        TmsDrvFreezePO drvFreezePO =  freezeRepository.queryByPk(drvId);
        if(drvFreezePO == null){
            return Result.Builder.<Boolean>newResult().success() .withData(Boolean.TRUE) .build();
        }

        //如果登录账号是BD，跳过判断
        if(Objects.equals(TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue(),accountType)){
            return Result.Builder.<Boolean>newResult().success() .withData(Boolean.TRUE) .build();
        }

        Boolean flag = Boolean.TRUE;
        if(StringUtils.isNotEmpty(drvFreezePO.getTotalFreezeFrom())){
            List<Integer> fromLists = Arrays.stream(drvFreezePO.getTotalFreezeFrom().split(TmsTransportConstant.SPLIT))
                    .map(s -> Integer.parseInt(s.trim()))
                    .collect(Collectors.toList());
            for(Integer integer : fromLists){
                if(Objects.equals(integer, TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue()) || Objects.equals(integer, CommonEnum.FreezeOPFromEnum.PUNISH.getValue())) {
                    flag = Boolean.FALSE;
                    break;
                }
            }
        }
        return Result.Builder.<Boolean>newResult().success() .withData(flag) .build();
    }

    @Override
    public Result<List<QueryDoDrvFreezeSOADTO>> queryDoDrvFreeze(List<Long> drvIds, Integer freezeFrom) {
        try{
            List<DrvDriverPO> drvDriverPOList = repository.queryDrvList(drvIds);
            if(CollectionUtils.isEmpty(drvDriverPOList)){
                return Result.Builder.<List<QueryDoDrvFreezeSOADTO>>newResult().success().withData(Lists.newArrayList()).build();
            }

            Map<Long,String> drvMap = drvDriverPOList.stream().collect(Collectors.toMap(DrvDriverPO::getDrvId,DrvDriverPO::getDrvName));
            Set<Long> onLineDrvList = Sets.newHashSet();
            Set<Long> freezeDrvList = Sets.newHashSet();
            List<QueryDoDrvFreezeSOADTO> resultList = Lists.newArrayList();
            //已冻结司机有冻结记录
            Set<Long> freezeDrv = Sets.newHashSet();
            //属于”冻结“和”已上线“的司机
            for(DrvDriverPO drvDriverPO : drvDriverPOList){
                if(Objects.equals(TmsTransportConstant.DrvStatusEnum.ONLINE.getCode(),drvDriverPO.getDrvStatus())){
                    onLineDrvList.add(drvDriverPO.getDrvId());
                }
                if(Objects.equals(TmsTransportConstant.DrvStatusEnum.FREEZE.getCode(),drvDriverPO.getDrvStatus())){
                    freezeDrvList.add(drvDriverPO.getDrvId());
                }
            }
            if(CollectionUtils.isEmpty(onLineDrvList) && CollectionUtils.isEmpty(freezeDrvList)){
                return Result.Builder.<List<QueryDoDrvFreezeSOADTO>>newResult().success().withData(Lists.newArrayList()).build();
            }

            if(CollectionUtils.isNotEmpty(freezeDrvList)){
                List<TmsDrvFreezePO> freezePOList =  freezeRepository.queryDrvFreezeByDrvIds(freezeDrvList);
                if(CollectionUtils.isEmpty(freezeDrvList)){
                    return Result.Builder.<List<QueryDoDrvFreezeSOADTO>>newResult().success().withData(Lists.newArrayList()).build();
                }
                for(Iterator<TmsDrvFreezePO> iterator = freezePOList.iterator();iterator.hasNext();){
                    TmsDrvFreezePO freezePO = iterator.next();
                    //供应商角色下，判断如果该司机运营操作过冻结,则供应商无权操作
                    if(Objects.equals(TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue(),freezeFrom)){
                        if(StringUtils.isNotEmpty(freezePO.getTotalFreezeFrom())){
                            List<Integer> fromLists = Arrays.stream(freezePO.getTotalFreezeFrom().split(TmsTransportConstant.SPLIT))
                                    .map(s -> Integer.parseInt(s.trim()))
                                    .collect(Collectors.toList());
                            Boolean flag = Boolean.TRUE;
                            for(Integer integer : fromLists){
                                if(Objects.equals(integer, TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue())){
                                    flag = Boolean.FALSE;
                                    break;
                                }
                            }
                            if(!flag){
                                iterator.remove();
                                continue;
                            }
                        }
                    }
                    //累计冻结时长“＜90天
                    if(freezePO.getFreezeHour()/24 > 90){
                        iterator.remove();
                        continue;
                    }
                    freezeDrv.add(freezePO.getDrvId());
                }
            }

            List<Long> totalDrvList = (List<Long>) CollectionUtils.disjunction(new ArrayList(onLineDrvList),new ArrayList(freezeDrv));
            if(CollectionUtils.isEmpty(totalDrvList)){
                return Result.Builder.<List<QueryDoDrvFreezeSOADTO>>newResult().success().withData(Lists.newArrayList()).build();
            }

            Iterator<Long> iterator = totalDrvList.iterator();
            while (iterator.hasNext()){
                Long drvId = iterator.next();
                QueryDoDrvFreezeSOADTO freezeDTO = new QueryDoDrvFreezeSOADTO();
                freezeDTO.setDrvId(drvId);
                freezeDTO.setDrvName(drvMap.get(drvId));
                resultList.add(freezeDTO);
            }
            return Result.Builder.<List<QueryDoDrvFreezeSOADTO>>newResult().success().withData(resultList).build();
        }catch (Exception e){
            throw new BaijiRuntimeException("queryDoDrvFreeze error", e);
        }
    }

    @Override
    public List<DrvFreezeInfoSOADTO> queryDrvFreezeInfoList(Set<Long> drvIdSet) {
        List<DrvDriverPO> drvDriverPOList = repository.queryDrvList(Lists.newArrayList(drvIdSet));
        if (CollectionUtils.isEmpty(drvDriverPOList)) {
            return Collections.emptyList();
        }
        List<TmsDrvFreezePO> freezePOList = freezeRepository.queryDrvFreezeByDrvIds(drvDriverPOList.stream().map(driverPO -> driverPO.getDrvId()).collect(Collectors.toSet()));
        Map<Long, TmsDrvFreezePO> drvFreezePOMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(freezePOList)) {
            drvFreezePOMap = freezePOList.stream().collect(Collectors.toMap(TmsDrvFreezePO::getDrvId, a -> a, (k1, k2) -> k1));
        }
        List<DrvFreezeInfoSOADTO> res = Lists.newArrayListWithExpectedSize(drvDriverPOList.size());
        for (DrvDriverPO driverPO : drvDriverPOList) {
            DrvFreezeInfoSOADTO infoDTOSOA = new DrvFreezeInfoSOADTO();
            infoDTOSOA.setCityId(driverPO.getCityId());
            infoDTOSOA.setDriverId(driverPO.getDrvId());
            infoDTOSOA.setCarTypeId(driverPO.getVehicleTypeId());
            dealFreezeInfo(infoDTOSOA, drvFreezePOMap.get(driverPO.getDrvId()));
            res.add(infoDTOSOA);
        }
        return res;
    }

    private void dealFreezeInfo(DrvFreezeInfoSOADTO infoDTOSOA, TmsDrvFreezePO drvFreezePO) {
        if (drvFreezePO == null || !Objects.equals(drvFreezePO.getFreezeStatus(), TmsTransportConstant.FreezeStatusEnum.FREEZE.getValue()) || drvFreezePO.getFirstFreezeTime() == null || drvFreezePO.getFreezeHour() == null) {
            infoDTOSOA.setIsFreeze(false);
            infoDTOSOA.setFrozenEndTime("");
            infoDTOSOA.setFrozenBeginTime("");
            return;
        }
        infoDTOSOA.setIsFreeze(true);
        Date startTime = new Date(drvFreezePO.getFirstFreezeTime().getTime());
        infoDTOSOA.setFrozenBeginTime(DateUtil.dateToString(startTime, DateUtil.YYYYMMDDHHMMSS));
        infoDTOSOA.setFrozenEndTime(DateUtil.dateToString(DateUtils.addHours(startTime, drvFreezePO.getFreezeHour()), DateUtil.YYYYMMDDHHMMSS));
    }
}
