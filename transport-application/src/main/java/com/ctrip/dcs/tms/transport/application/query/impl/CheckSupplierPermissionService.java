package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.application.query.ICheckSupplierPermissionService;
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CommonEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OverseasQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.SupplierTransportManagementPermissionQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.dcs.scm.sdk.domain.serviceprovider.ServiceProvider;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.result.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 供应商权限校验
 * 如果是供应商的服务商id是1001000
 * 不允许新增 编辑 解绑运力组sku
 */
@Component
public class CheckSupplierPermissionService implements ICheckSupplierPermissionService {
    private static final Logger logger = LoggerFactory.getLogger(CheckSupplierPermissionService.class);
    @Autowired
    private TransportGroupQueryService transportGroupQueryService;
    @Autowired
    private EnumRepository enumRepository;
    @Autowired
    private OverseasQconfig overseasQconfig;

    @Autowired
    private SupplierTransportManagementPermissionQconfig supplierTransportManagementPermissionQconfig;

    @Autowired
    private ProductionLineUtil productionLineUtil;

    @Override
    public boolean check(Long transportGroupId) {
        Result<TspTransportGroupPO> orgTransportGroupPO = transportGroupQueryService.queryTransportGroupDetail(transportGroupId);
        if (!orgTransportGroupPO.isSuccess()) {
            logger.info("queryTransportGroupDetail_fail",transportGroupId.toString());
            return true;
        }
        TspTransportGroupPO tspTransportGroupPO = orgTransportGroupPO.getData();
        if(tspTransportGroupPO == null){
            logger.info("tspTransportGroupPO_null",transportGroupId.toString());
            return true;
        }
        return checkByContractId(tspTransportGroupPO.getContractId(), tspTransportGroupPO.getCategorySynthesizeCode(), tspTransportGroupPO.getPointCityId());
    }

    @Override
    public boolean checkByContractId(Long contractId, Integer productLine, Long cityId) {
        if(contractId == null){
            logger.info("contractId_null","contractId_null");
            return true;
        }
        //如果是供应商操作编辑运力组并且运力组对应的服务商id是1001000，则不允许编辑运力组
        if(Objects.equals(TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue().toString(), SessionHolder.getRestSessionAccountType())){
            ServiceProvider serviceProvider =  enumRepository.getServiceProviderByContractId(contractId);
            Long serviceProviderId = Objects.isNull(serviceProvider) ? 0L : serviceProvider.getId() == null ? 0L : serviceProvider.getId();
            if(overseasQconfig.getFiltrationServiceprovideridList().contains(serviceProviderId)){
                //特定城市的供应商，有权限
                CommonEnum.UseProductionLineEnum category =
                    CommonEnum.UseProductionLineEnum.getEnumByCode(productLine);
                if (supplierTransportManagementPermissionQconfig.hasPermission(
                    category == null ? "" : category.getText(), String.valueOf(serviceProviderId), cityId)) {
                    logger.info("supplierCheckPermissionTrue",contractId.toString());
                    return true;
                }
                logger.info("checkPermissionFalse",contractId.toString());
                return false;
            }
        }
        logger.info("checkPermissionTrue",contractId.toString());
        return true;
    }
}
