package com.ctrip.dcs.tms.transport.task.application.command;

import com.ctrip.dcs.tms.transport.api.model.SaveIsNeedCreateTaskRequestType;
import com.ctrip.dcs.tms.transport.api.model.UploadVehicleDispatchPhotoListRequestType;
import com.ctrip.igt.framework.common.result.Result;

/**
 * <AUTHOR>
 */
public interface VehicleDispatchPhotoCommandService {
  Result<Boolean> uploadVehicleDispatchPhotoList(UploadVehicleDispatchPhotoListRequestType requestType);

}
